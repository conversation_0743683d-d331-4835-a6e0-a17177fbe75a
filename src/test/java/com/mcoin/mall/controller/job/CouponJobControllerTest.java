package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.SynVoucherCode2PlatformRequest;
import com.mcoin.mall.util.JodaTimeUtil;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 CouponJobController
 * 使用真实服务和数据库，测试优惠券任务相关功能
 */
class CouponJobControllerTest extends BaseUnitTest {

    @Resource
    private CouponJobController couponJobController;

    @Test
    void testSynCoupon() {
        // 创建请求对象
        SynVoucherCode2PlatformRequest request = new SynVoucherCode2PlatformRequest();
        request.setCurrentTime(new Date());

        // 直接调用控制器方法
        Response<String> response = couponJobController.synCoupon(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 