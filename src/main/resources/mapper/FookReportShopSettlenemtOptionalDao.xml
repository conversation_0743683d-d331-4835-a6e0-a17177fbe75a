<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportShopSettlenemtOptionalDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportShopSettlenemtOptional">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="optionalid" jdbcType="INTEGER" property="optionalid" />
    <result column="seller_id" jdbcType="INTEGER" property="sellerId" />
    <result column="shop_id" jdbcType="INTEGER" property="shopId" />
    <result column="bank_account" jdbcType="VARCHAR" property="bankAccount" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="momecoins" jdbcType="DECIMAL" property="momecoins" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, optionalid, seller_id, shop_id, bank_account, account_name, bank, start_time, 
    end_time, amount, settlement_amount, commission, momecoins, settlement_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_shop_settlenemt_optional
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_shop_settlenemt_optional
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportShopSettlenemtOptional">
    insert into fook_report_shop_settlenemt_optional
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="optionalid != null">
        optionalid,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="bank != null">
        bank,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="momecoins != null">
        momecoins,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="optionalid != null">
        #{optionalid,jdbcType=INTEGER},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=INTEGER},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportShopSettlenemtOptional">
    update fook_report_shop_settlenemt_optional
    <set>
      <if test="optionalid != null">
        optionalid = #{optionalid,jdbcType=INTEGER},
      </if>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=INTEGER},
      </if>
      <if test="bankAccount != null">
        bank_account = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        bank = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        momecoins = #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportShopSettlenemtOptional">
    update fook_report_shop_settlenemt_optional
    set optionalid = #{optionalid,jdbcType=INTEGER},
      seller_id = #{sellerId,jdbcType=INTEGER},
      shop_id = #{shopId,jdbcType=INTEGER},
      bank_account = #{bankAccount,jdbcType=VARCHAR},
      account_name = #{accountName,jdbcType=VARCHAR},
      bank = #{bank,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      amount = #{amount,jdbcType=DECIMAL},
      settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      momecoins = #{momecoins,jdbcType=DECIMAL},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>