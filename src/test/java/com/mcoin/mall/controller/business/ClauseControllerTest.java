package com.mcoin.mall.controller.business;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.model.*;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.annotation.Resource;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doReturn;

/**
 * 集成测试 ClauseController
 * 使用真实服务和数据库，测试条款相关功能
 */
class ClauseControllerTest extends BaseUnitTest {

    @Resource
    private ClauseController clauseController;
    
    @SpyBean
    private ContextHolder contextHolder;
    
    private static final int TEST_USER_ID = 99999;

    @BeforeEach
    void setUp() {
        // 模拟Locale
        doReturn(Locale.CHINESE).when(contextHolder).getLocale();
        
        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");
        
        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();
    }

    @Test
    void testGetClause() throws Exception {
        // 直接调用控制器方法
        Response<GetClauseResponse> response = clauseController.getClause();
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testSetClause() throws Exception {
        // 创建请求对象
        SetClauseRequest request = new SetClauseRequest();
        request.setClauseid("1");
        request.setStatus("1"); // 1 = agreed
        
        // 直接调用控制器方法
        Response<SetClauseResponse> response = clauseController.setClause(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 