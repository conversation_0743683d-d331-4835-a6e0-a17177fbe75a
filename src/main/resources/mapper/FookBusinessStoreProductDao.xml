<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessStoreProductDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessStoreProduct">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="productid" jdbcType="INTEGER" property="productid" />
    <result column="storeid" jdbcType="INTEGER" property="storeid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, productid, storeid, `type`, businessid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_store_product
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_store_product
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessStoreProduct">
    insert into fook_business_store_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productid != null">
        productid,
      </if>
      <if test="storeid != null">
        storeid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productid != null">
        #{productid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        #{storeid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessStoreProduct">
    update fook_business_store_product
    <set>
      <if test="productid != null">
        productid = #{productid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        storeid = #{storeid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessStoreProduct">
    update fook_business_store_product
    set productid = #{productid,jdbcType=INTEGER},
      storeid = #{storeid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      businessid = #{businessid,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>