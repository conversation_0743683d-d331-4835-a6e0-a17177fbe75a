INSERT INTO fooku.fook_platform_order (id,areaid,userid,sellerid,`type`,order_no,create_time,payment_type,payment_transaction,status,complete_time,order_transaction,mpayintegral,score,platform,order_amount,point_ratio,currency,total_amount,total_amount_currency,transaction_amount,transaction_amount_currency,payment_amount,payment_amount_currency,payment_time,query_mark,account_suffix,expiration_month,expiration_year,card_type,credit_cardid,reason_code,process_type,bank_charges,bank_settlement_amount,ordercode_id,refundid,ordercode_status,refund_status,is_mpay,is_member,memberintegral,is_voucher,mpay_coupons_status,mpay_coupons_code,subsidy_amount,session_id) VALUES
	 (4217669,1,36721,4,1,'20240808151334100000013','2024-08-08 15:13:34',4,NULL,2,NULL,'2024080815133643249649',150,0.50,4,2.00,300,'MOP',1.50,NULL,1.50,'MOP',1.50,'MOP','2024-08-08 15:13:40',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0.02,NULL,NULL,NULL,0,0,'1',0,NULL,0,1,'****************',0.00,NULL);

INSERT INTO fook_platform_order (id,areaid,userid,sellerid,`type`,order_no,create_time,payment_type,payment_transaction,status,complete_time,order_transaction,mpayintegral,score,platform,order_amount,point_ratio,currency,total_amount,total_amount_currency,transaction_amount,transaction_amount_currency,payment_amount,payment_amount_currency,payment_time,query_mark,account_suffix,expiration_month,expiration_year,card_type,credit_cardid,reason_code,process_type,bank_charges,bank_settlement_amount,ordercode_id,refundid,ordercode_status,refund_status,is_mpay,is_member,memberintegral,is_voucher,mpay_coupons_status,mpay_coupons_code,session_id,subsidy_amount) VALUES
	 (5600369,1,36940,823,1,'20240812181014100000038','2024-08-12 18:10:15',4,NULL,2,NULL,'2024081218101508679180',300,1.00,4,10.00,300,'MOP',9.00,NULL,9.00,'MOP',9.00,'MOP','2024-08-12 18:10:32',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0.09,NULL,NULL,NULL,0,0,'1',0,NULL,0,0,NULL,NULL,0.00);

INSERT INTO fook_platform_order (id,areaid,userid,sellerid,`type`,order_no,create_time,payment_type,payment_transaction,status,complete_time,order_transaction,mpayintegral,score,platform,order_amount,point_ratio,currency,total_amount,total_amount_currency,transaction_amount,transaction_amount_currency,payment_amount,payment_amount_currency,payment_time,query_mark,account_suffix,expiration_month,expiration_year,card_type,credit_cardid,reason_code,process_type,bank_charges,bank_settlement_amount,ordercode_id,refundid,ordercode_status,refund_status,is_mpay,is_member,memberintegral,is_voucher,mpay_coupons_status,mpay_coupons_code,subsidy_amount,session_id) VALUES
	 (********,1,36721,2,1,'20250311115213100000005','2025-03-11 11:52:14',4,NULL,2,NULL,'2025031111521443645889',150,0.50,4,30.00,300,'MOP',29.50,NULL,29.50,'MOP',29.50,'MOP','2025-03-11 11:52:19',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0.30,NULL,NULL,NULL,0,0,'1',0,NULL,0,0,NULL,0.00,NULL);

INSERT INTO fook_platform_orderinfo (id,orderid,prodcutid,product_price,image_snapshots,title_snapshots,desc_snapshots,vaild_start_time,vaild_end_time,details_snapshots,`number`,is_weekend,no_useday,`type`,img,retail_price,tnc,is_vacation,is_allow_refund,fee_rate,buy_start_time,buy_end_time,valid_mode,day_num,applicable_shops,is_hot,momecoins_option,threshold,is_great,is_redeen,is_momecoin,ordercode_id,refundid,order_amount,score,mpayintegral,memberintegral,miles_first,miles_name,miles_member,miles_milage,is_voucher,is_settlement,subsidy_amount) VALUES
	 (4219391,********,2324,30.00,'product/a8IsmeM2VsjFarPsHyJ0.jpg','椒盐小鸡腿',NULL,'2025-03-11 11:52:14','2025-06-08 23:59:59',NULL,1,1,NULL,2,'product/a8IsmeM2VsjFarPsHyJ0.jpg',250.00,'椒盐小鸡腿',1,1,2.00,NULL,NULL,2,90,NULL,0,'5',1,0,0,0,NULL,NULL,30.00,0,150,NULL,NULL,NULL,NULL,0.00,0,1,0.00);

INSERT INTO fook_platform_order (id,areaid,userid,sellerid,`type`,order_no,create_time,payment_type,payment_transaction,status,complete_time,order_transaction,mpayintegral,score,platform,order_amount,point_ratio,currency,total_amount,total_amount_currency,transaction_amount,transaction_amount_currency,payment_amount,payment_amount_currency,payment_time,query_mark,account_suffix,expiration_month,expiration_year,card_type,credit_cardid,reason_code,process_type,bank_charges,bank_settlement_amount,ordercode_id,refundid,ordercode_status,refund_status,is_mpay,is_member,memberintegral,is_voucher,mpay_coupons_status,mpay_coupons_code,subsidy_amount,session_id) VALUES
	 (270130,1,36679,519,1,'20231012184100797963392146','2023-10-12 18:41:00',4,NULL,2,NULL,'2023101218410112893594',150,0.00,4,300.00,300,'MOP',299.50,NULL,299.50,'MOP',299.50,'MOP','2025-03-10 14:19:13',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,3.00,NULL,NULL,NULL,0,3,'1',0,NULL,0,0,NULL,0.00,NULL),
	 (270133,1,36679,810,1,'20231012184504671922915640','2023-10-12 18:45:04',4,NULL,4,'2023-10-12 20:01:10','2023101218450612893600',150,0.00,4,15.00,300,'MOP',14.50,NULL,14.50,'MOP',14.50,'MOP','2025-03-10 14:19:13',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0.15,NULL,NULL,NULL,0,3,'1',0,NULL,0,0,NULL,0.00,NULL);



INSERT INTO fook_platform_order (id, areaid, userid, sellerid, type, order_no, create_time, payment_type, payment_transaction, status, complete_time, order_transaction, mpayintegral, score, platform, order_amount, point_ratio, currency, total_amount, total_amount_currency, transaction_amount, transaction_amount_currency, payment_amount, payment_amount_currency, payment_time, query_mark, account_suffix, expiration_month, expiration_year, card_type, credit_cardid, reason_code, process_type, bank_charges, bank_settlement_amount, ordercode_id, refundid, ordercode_status, refund_status, is_mpay, is_member, memberintegral, is_voucher, mpay_coupons_status, mpay_coupons_code, subsidy_amount, session_id)
VALUES (4216756, 1, 1, 681, 1, '20240422073501100000032', DATE_SUB(NOW(), INTERVAL 1 DAY), 4, null, 2, '2024-04-24 09:01:24', '2024042207350104588694', 400, 1.33, 4, 1.00, 300, 'MOP', 0.00, null, 0.00, 'MOP', 0.00, 'MOP', '2024-04-22 07:35:10', null, null, null, null, null, null, null, null, 0.00, null, null, null, 3, 0, '1', 0, null, 0, 0, null, 0.00, null);



















INSERT INTO fook_platform_order (id,areaid,userid,sellerid,`type`,order_no,create_time,payment_type,payment_transaction,status,complete_time,order_transaction,mpayintegral,score,platform,order_amount,point_ratio,currency,total_amount,total_amount_currency,transaction_amount,transaction_amount_currency,payment_amount,payment_amount_currency,payment_time,query_mark,account_suffix,expiration_month,expiration_year,card_type,credit_cardid,reason_code,process_type,bank_charges,bank_settlement_amount,ordercode_id,refundid,ordercode_status,refund_status,is_mpay,is_member,memberintegral,is_voucher,mpay_coupons_status,mpay_coupons_code,subsidy_amount,session_id) VALUES
	 (********,1,36727,557,1,'20250325172036100000003','2025-03-25 17:20:36',4,NULL,2,NULL,'2025032517203743673285',450,1.50,4,18.00,300,'MOP',16.50,NULL,16.50,'MOP',16.50,'MOP','2025-03-25 17:20:46',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0.17,NULL,NULL,NULL,0,0,'1',0,NULL,0,0,NULL,0.00,NULL);
