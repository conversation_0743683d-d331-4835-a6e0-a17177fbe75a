<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresTypeClassDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookStoresTypeClass">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="stores_id" jdbcType="INTEGER" property="storesId" />
    <result column="stores_type_id" jdbcType="INTEGER" property="storesTypeId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, stores_id, stores_type_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_stores_type_class
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_stores_type_class
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookStoresTypeClass">
    insert into fook_stores_type_class
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="storesId != null">
        stores_id,
      </if>
      <if test="storesTypeId != null">
        stores_type_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="storesId != null">
        #{storesId,jdbcType=INTEGER},
      </if>
      <if test="storesTypeId != null">
        #{storesTypeId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookStoresTypeClass">
    update fook_stores_type_class
    <set>
      <if test="storesId != null">
        stores_id = #{storesId,jdbcType=INTEGER},
      </if>
      <if test="storesTypeId != null">
        stores_type_id = #{storesTypeId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookStoresTypeClass">
    update fook_stores_type_class
    set stores_id = #{storesId,jdbcType=INTEGER},
      stores_type_id = #{storesTypeId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>