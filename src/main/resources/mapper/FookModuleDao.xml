<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookModuleDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookModule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="banner_name" jdbcType="VARCHAR" property="bannerName" />
    <result column="banner_style" jdbcType="INTEGER" property="bannerStyle" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="valid" jdbcType="BIT" property="valid" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    id, banner_name, banner_style, sort, `enable`, created_at, updated_at, `valid`, created_by, 
    updated_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_module
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fook_module
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookModule">
    insert into fook_module
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bannerName != null">
        banner_name,
      </if>
      <if test="bannerStyle != null">
        banner_style,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="valid != null">
        `valid`,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bannerName != null">
        #{bannerName,jdbcType=VARCHAR},
      </if>
      <if test="bannerStyle != null">
        #{bannerStyle,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookModule">
    update fook_module
    <set>
      <if test="bannerName != null">
        banner_name = #{bannerName,jdbcType=VARCHAR},
      </if>
      <if test="bannerStyle != null">
        banner_style = #{bannerStyle,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        `valid` = #{valid,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookModule">
    update fook_module
    set banner_name = #{bannerName,jdbcType=VARCHAR},
      banner_style = #{bannerStyle,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      `enable` = #{enable,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      `valid` = #{valid,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>