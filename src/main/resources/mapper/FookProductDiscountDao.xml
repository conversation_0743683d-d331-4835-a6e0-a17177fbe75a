<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookProductDiscountDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookProductDiscount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="integral" jdbcType="INTEGER" property="integral" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_mpay" jdbcType="TINYINT" property="isMpay" />
    <result column="is_mcoin" jdbcType="TINYINT" property="isMcoin" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, product_id, integral, price, `status`, start_time, end_time, is_mpay, is_mcoin,
    created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_product_discount
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_product_discount
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookProductDiscount">
    insert into fook_product_discount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="integral != null">
        integral,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="isMpay != null">
        is_mpay,
      </if>
      <if test="isMcoin != null">
        is_mcoin,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="integral != null">
        #{integral,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isMpay != null">
        #{isMpay,jdbcType=TINYINT},
      </if>
      <if test="isMcoin != null">
        #{isMcoin,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookProductDiscount">
    update fook_product_discount
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="integral != null">
        integral = #{integral,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isMpay != null">
        is_mpay = #{isMpay,jdbcType=TINYINT},
      </if>
      <if test="isMcoin != null">
        is_mcoin = #{isMcoin,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookProductDiscount">
    update fook_product_discount
    set product_id = #{productId,jdbcType=INTEGER},
      integral = #{integral,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      is_mpay = #{isMpay,jdbcType=TINYINT},
      is_mcoin = #{isMcoin,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>