-- SQL file for OrderRefundTimeoutControllerTest
-- Contains test data for fook_platform_orderrefund_record table and related tables

-- =============================================
-- Clean up existing test data
-- =============================================
DELETE FROM `fook_platform_orderrefund_record` WHERE `id` IN (20001, 20002, 20003, 20004);
DELETE FROM `fook_platform_orderrefund` WHERE `id` IN (20001, 20002, 20003, 20004);
DELETE FROM `fook_platform_order` WHERE `id` IN (20001, 20002, 20003, 20004);
DELETE FROM `fook_pay_log` WHERE `id` IN (20001, 20002, 20003, 20004);
DELETE FROM `fook_mq_local` WHERE `resource_type` = 'REFUND' AND `resource_id` IN ('20001', '20002', '20003', '20004');

-- =============================================
-- fook_mq_local table - ensure it exists
-- =============================================
CREATE TABLE IF NOT EXISTS `fook_mq_local` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` varchar(64) DEFAULT NULL COMMENT '来源id，可以用于关联某个业务表',
  `resource_type` varchar(32) DEFAULT NULL COMMENT '来源id的类型，用于指明哪种业务表',
  `template_name` varchar(64) DEFAULT NULL COMMENT 'rabbit template spring bean name',
  `try_count` int(11) DEFAULT '0' COMMENT '已尝试次数',
  `status` int(11) DEFAULT '0' COMMENT '0-待处理，1-已完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `next_retry` datetime DEFAULT NULL,
  `max_try_count` int(11) DEFAULT '3' COMMENT '最大尝试次数',
  `mq_config` text COMMENT 'mq的其他发送配置信息',
  `message_body` text COMMENT 'MQ的消息体，过长建议只存此表id，消费时查表使用attachment',
  `attachment` text COMMENT '消费消息时需要的参数变量等等',
  PRIMARY KEY (`id`),
  KEY `idx_resource` (`resource_id`,`resource_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='本地消息表';

-- =============================================
-- fook_platform_order table - base data
-- =============================================
-- 订单表基础数据
INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    20001, 1, 1, 1, 1, 'TEST_ORDER_NO_20001',
    DATE_SUB(NOW(), INTERVAL 1 DAY), 1, 'TEST_PAYMENT_TRANSACTION_20001', 2,
    NULL, 'TEST_ORDER_TRANSACTION_20001', 0, 0,
    2, 100.00, 0.00, 'MOP',
    100.00, 'MOP', 100.00,
    'MOP', 100.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 23 HOUR), 2
);

INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    20002, 1, 1, 1, 1, 'TEST_ORDER_NO_20002',
    DATE_SUB(NOW(), INTERVAL 2 DAY), 1, 'TEST_PAYMENT_TRANSACTION_20002', 2,
    NULL, 'TEST_ORDER_TRANSACTION_20002', 0, 0,
    2, 200.00, 0.00, 'MOP',
    200.00, 'MOP', 200.00,
    'MOP', 200.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 47 HOUR), 2
);

INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    20003, 1, 1, 1, 1, 'TEST_ORDER_NO_20003',
    DATE_SUB(NOW(), INTERVAL 2 DAY), 1, 'TEST_PAYMENT_TRANSACTION_20003', 2,
    NULL, 'TEST_ORDER_TRANSACTION_20003', 0, 0,
    2, 300.00, 0.00, 'MOP',
    300.00, 'MOP', 300.00,
    'MOP', 300.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 47 HOUR), 2
);

INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    20004, 1, 1, 1, 1, 'TEST_ORDER_NO_20004',
    DATE_SUB(NOW(), INTERVAL 5 DAY), 1, 'TEST_PAYMENT_TRANSACTION_20004', 2,
    NULL, 'TEST_ORDER_TRANSACTION_20004', 0, 0,
    2, 400.00, 0.00, 'MOP',
    400.00, 'MOP', 400.00,
    'MOP', 400.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 119 HOUR), 2
);

-- =============================================
-- fook_platform_orderrefund table - base data
-- =============================================
INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    20001, 20001, 100.00, '测试退款原因', '前台退款原因',
    '客服备注', 2, 1, 1,
    1, 0.00, 100.00, 0,
    NULL, DATE_SUB(NOW(), INTERVAL 1 DAY), NULL, 20001
);

INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    20002, 20002, 200.00, '测试退款原因2', '前台退款原因2',
    '客服备注2', 6, 3, 3,
    1, 0.00, 200.00, 0,
    NOW(), DATE_SUB(NOW(), INTERVAL 2 DAY), 'TEST_REFUND_TRANSACTION_20002', 20002
);

INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    20003, 20003, 300.00, '测试退款原因3', '前台退款原因3',
    '客服备注3', 2, 1, 1,
    1, 0.00, 300.00, 0,
    NULL, DATE_SUB(NOW(), INTERVAL 2 DAY), NULL, 20003
);

INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    20004, 20004, 400.00, '测试退款原因4', '前台退款原因4',
    '客服备注4', 2, 1, 1,
    1, 0.00, 400.00, 0,
    NULL, DATE_SUB(NOW(), INTERVAL 5 DAY), NULL, 20004
);

-- =============================================
-- fook_platform_orderrefund_record table - test data
-- =============================================
-- 测试数据1：状态为2，在30分钟前到72小时之内的时间范围内(应该被处理)
INSERT INTO `fook_platform_orderrefund_record` (
    `id`, `refundid`, `old_status`, `new_status`, 
    `operation_time`, `operation_type`, `operation_id`, 
    `remark`, `out_request_no`
) VALUES (
    20001, 20001, 1, 2,
    DATE_SUB(NOW(), INTERVAL 60 MINUTE), 1, 1,
    '测试退款记录1', 'TEST_OUT_REQUEST_NO_20001'
);

-- 测试数据2：状态为6，在时间范围内(不应该被处理，因为已经退款成功)
INSERT INTO `fook_platform_orderrefund_record` (
    `id`, `refundid`, `old_status`, `new_status`,
    `operation_time`, `operation_type`, `operation_id`,
    `remark`, `out_request_no`
) VALUES (
    20002, 20002, 2, 6,
    DATE_SUB(NOW(), INTERVAL 120 MINUTE), 1, 1,
    '测试退款记录2', 'TEST_OUT_REQUEST_NO_20002'
);

-- 测试数据3：状态为2，在30分钟内(不应该被处理，因为太新)
INSERT INTO `fook_platform_orderrefund_record` (
    `id`, `refundid`, `old_status`, `new_status`,
    `operation_time`, `operation_type`, `operation_id`,
    `remark`, `out_request_no`
) VALUES (
    20003, 20003, 2, 2,
    DATE_SUB(NOW(), INTERVAL 15 MINUTE), 1, 1,
    '测试退款记录3', 'TEST_OUT_REQUEST_NO_20003'
);

-- 测试数据4：状态为2，在72小时前(不应该被处理，因为太老)
INSERT INTO `fook_platform_orderrefund_record` (
    `id`, `refundid`, `old_status`, `new_status`,
    `operation_time`, `operation_type`, `operation_id`,
    `remark`, `out_request_no`
) VALUES (
    20004, 20004, 2, 2,
    DATE_SUB(NOW(), INTERVAL 5 DAY), 1, 1,
    '测试退款记录4', 'TEST_OUT_REQUEST_NO_20004'
);

-- =============================================
-- fook_mq_local table - test data for refund messages
-- =============================================
-- 为refundId=20001的退款记录创建一条MQ本地消息
INSERT INTO `fook_mq_local` (
    `resource_id`, `resource_type`, `template_name`, 
    `try_count`, `status`, `create_time`, 
    `update_time`, `next_retry`, `max_try_count`, 
    `message_body`
) VALUES (
    '20001', 'REFUND', 'refundTemplate',
    0, 0, NOW(),
    NOW(), DATE_SUB(NOW(), INTERVAL 30 MINUTE), 3,
    '{"refundId":20001,"outRequestNo":"TEST_OUT_REQUEST_NO_20001"}'
);