package com.mcoin.mall.service.business.impl;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.mcoin.mall.service.common.CacheService.CACHE_ACTIVE_ZONE;
import static com.mcoin.mall.service.common.CacheService.CACHE_INDEX;
import static com.mcoin.mall.service.common.CacheService.CACHE_INDEX_BANNER;
import static com.mcoin.mall.service.common.CacheService.CACHE_INDEX_POPULAR_SEARCHES;
import static com.mcoin.mall.service.common.CacheService.CACHE_STORES_TYPE;
import static com.mcoin.mall.service.common.CacheService.CACHE_STORE_KEY_WORDS_V1;
import static com.mcoin.mall.service.common.CacheService.CACHE_STORE_KEY_WORDS_V2;
import static com.mcoin.mall.util.McoinMall.FEEDS_HARMONY_SUFFIX;
import static com.mcoin.mall.util.McoinMall.PRODUCT_CREATE_TIME_KEY_PREFIX;
import static com.mcoin.mall.util.McoinMall.PRODUCT_RECOMMEND_KEY_PREFIX;
import static com.mcoin.mall.util.McoinMall.STORE_GEO_PRODUCT_CREATE_TIME_KEY_PREFIX;
import static java.lang.Integer.parseInt;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.mcoin.mall.model.ShopIntervalResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.GeoOrder;
import org.redisson.api.GeoUnit;
import org.redisson.api.RGeo;
import org.redisson.api.RMap;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookActiveZone;
import com.mcoin.mall.bean.FookBanner;
import com.mcoin.mall.bean.FookModule;
import com.mcoin.mall.bean.FookPlatformSuggest;
import com.mcoin.mall.bean.FookStoresKeyword;
import com.mcoin.mall.bean.FookStoresType;
import com.mcoin.mall.bo.ActiveZoneProductBo;
import com.mcoin.mall.bo.ActiveZoneStoreBo;
import com.mcoin.mall.bo.ProductsCollectBo;
import com.mcoin.mall.bo.SnapUpItemBo;
import com.mcoin.mall.bo.StoreDistanceBo;
import com.mcoin.mall.bo.StoreTypeProductsBo;
import com.mcoin.mall.bo.StoreTypeProductsPageBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookActiveZoneDao;
import com.mcoin.mall.dao.FookBannerDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.dao.FookModuleDao;
import com.mcoin.mall.dao.FookPlatformSuggestDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.dao.FookStoresKeywordDao;
import com.mcoin.mall.dao.FookStoresTypeDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Page;
import com.mcoin.mall.model.RecommendZoneRequest;
import com.mcoin.mall.model.RecommendZoneResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.ShopTypeRequest;
import com.mcoin.mall.model.ShopTypeResponse;
import com.mcoin.mall.model.SnapUpMoreRequest;
import com.mcoin.mall.model.SnapUpMoreResponse;
import com.mcoin.mall.model.SnapUpNextRequest;
import com.mcoin.mall.model.SnapUpNextResponse;
import com.mcoin.mall.model.SnapUpRequest;
import com.mcoin.mall.model.SnapUpResponse;
import com.mcoin.mall.model.StoreTypeResponse;
import com.mcoin.mall.model.ZoneTypeResponse;
import com.mcoin.mall.service.business.IndexService;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.service.core.SnapUpConvertService;
import com.mcoin.mall.service.job.FeedsJobService;
import com.mcoin.mall.util.BusinessProductUtil;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.DistanceUtil;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.MiniProgramDisplayUtil;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.util.PageUtil;
import com.mcoin.mall.util.StoreTypeUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class IndexServiceImpl implements IndexService {

    @Resource
    private SnapUpConvertService snapUpConvertService;

    @Resource
    private FookBannerDao fookBannerDao;

    @Resource
    private FookActiveZoneDao fookActiveZoneDao;

    @Resource
    private FookStoresTypeDao fookStoresTypeDao;

    @Resource
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;
    @Resource
    private SettingsDao settingsDao;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;


    @Resource
    private FookStoresKeywordDao fookStoresKeywordDao;

    @Resource
    private FookPlatformSuggestDao fookPlatformSuggestDao;

    @Resource
    private ContextHolder contextHolder;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CollectService collectService;

    @Resource
    private FookModuleDao fookModuleDao;
    @Resource
    private FeedsJobService feedsJobService;
    @Resource
    private MessageSource messageSource;
    @Resource
    private FookStoresDao fookStoresDao;

    /**
     * 首页广告
     *
     * @return
     */
    @Cacheable(value = CACHE_INDEX_BANNER, key = "@cacheService.CACHE_DEFAULT_KEY", unless = "#result.isEmpty()")
    public List<FookBanner> getIndexPoster() {
        List<FookBanner> result = Lists.newArrayList();
        List<FookBanner> banners = fookBannerDao.getIndexBanners();
        for (FookBanner banner : banners) {
            Date broadcastEndTime = banner.getBroadcastEndTime();
            Date broadcastStartTime = banner.getBroadcastStartTime();
            if (null != broadcastEndTime && 1 == banner.getIsBroadcast()) {
                long now = System.currentTimeMillis();
                long br = broadcastEndTime.getTime();
                long start = broadcastStartTime.getTime();
                if (br > now && start < now) {
                    result.add(banner);
                }
            } else {
                result.add(banner);
            }
        }
        return result;
    }

    /**
     * 门店著数，过滤掉srcType
     *
     * @return
     */
    @Cacheable(value = CACHE_STORE_KEY_WORDS_V1, key = "@cacheService.CACHE_DEFAULT_KEY", unless = "#result.isEmpty()")
    public List<FookStoresKeyword> getStoreKeyWords() {
        List<FookStoresKeyword> storeKeyWords = fookStoresKeywordDao.getStoreKeyWords();
        return storeKeyWords.stream().filter(x -> null != x.getSrcType() && (x.getSrcType() == 5 || x.getSrcType() == 6)).collect(Collectors.toList());
    }

    /**
     * 门店著数
     *
     * @param harmonySwitch 是否为HARMONY设备且开关开启
     * @return 门店著数列表
     */
    @Cacheable(value = CACHE_STORE_KEY_WORDS_V2, key = "@cacheService.getStoreKeyWordsV2Key(#harmonySwitch)", unless = "#result.isEmpty()")
    public List<FookStoresKeyword> getStoreKeyWordsV2(Boolean harmonySwitch) {
        // 直接在SQL中过滤数据，而不是在内存中过滤
        return fookStoresKeywordDao.getStoreKeyWordsWithFilter(harmonySwitch);
    }


    /**
     * 热门搜索
     *
     * @return
     */
    public List<FookPlatformSuggest> getPopularSearches() {
        return fookPlatformSuggestDao.getPopularSearches();
    }

    /**
     * 主页获取热词，随机排序
     *
     * @return
     */
    @Cacheable(value = CACHE_INDEX_POPULAR_SEARCHES, key = "@cacheService.CACHE_DEFAULT_KEY", unless = "#result.isEmpty()")
    public List<FookPlatformSuggest> getIndexPopularSearches() {
        List<FookPlatformSuggest> indexPopularSearches = fookPlatformSuggestDao.getIndexPopularSearches();
        //shuffle indexPopularSearches
        if (CollectionUtils.isNotEmpty(indexPopularSearches)) {
            Collections.shuffle(indexPopularSearches);
        }
        return indexPopularSearches;
    }

    /**
     * 專區類別
     *
     * @return
     */
    @Cacheable(value = CACHE_ACTIVE_ZONE, key = "@cacheService.getLanguage()", unless = "#result.isEmpty()")
    public List<ZoneTypeResponse> getActiveZone() {

        Locale local = contextHolder.getLocale();
        String language = local.getLanguage();
        List<ZoneTypeResponse> list = new ArrayList<>();
        List<FookActiveZone> listZone = fookActiveZoneDao.getActiveZones();
        for (FookActiveZone activeZone : listZone) {
            ZoneTypeResponse response = new ZoneTypeResponse();
            response.setName(activeZone.getName());
            if (language.equals(McoinMall.LANG_EN) && StringUtils.isNotEmpty(activeZone.getNameEn())) {
                response.setName(activeZone.getNameEn());
            }
            response.setType(activeZone.getType().toString());
            response.setStatus(activeZone.getStatus().toString());
            response.setZoneId(activeZone.getId().toString());
            list.add(response);
        }

        return list;

    }

    /**
     * 專區類別福利
     *
     * @return
     */
    public List<RecommendZoneResponse> getActiveZoneProduct(RecommendZoneRequest request, String clientType) {

        List<ActiveZoneProductBo> productList;
        try {
            Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
            productList = fookActiveZoneDao.getActiveZoneProducts(request.getType(), request.getZonne_id(), request.isShowLikeText(), harmonySwitch);
        } catch (Exception e) {
            log.error("查询未知异常", e);
            productList = Collections.emptyList();
        }
        List<RecommendZoneResponse> list = new ArrayList<>();

        if (!productList.isEmpty()) {
            List<Integer> productIds = productList.stream().map(ActiveZoneProductBo::getPid).collect(Collectors.toList());
            // 处理收藏
            ProductsCollectBo collectBo = collectService.getProductsCollect(productIds);
            Map<Integer, ActiveZoneStoreBo> storeBoMap = fookActiveZoneDao.getActiveZoneStores(productIds);
            for (ActiveZoneProductBo p : productList) {
                ActiveZoneStoreBo storeBo = storeBoMap.get(p.getPid());
                if (storeBo == null) {
                    // 无门店则跳过
                    continue;
                }

                RecommendZoneResponse response = new RecommendZoneResponse();
                int ratio = p.getPointRatio();
                int point0 = p.getMinPoint() != null && p.getMinPoint() > 0 ? p.getMinPoint() : (ratio / 2);//混合支付
                Object point1 = p.getPrice() * ratio;//纯积分
                Object point = p.getSystemType() == 3 ? p.getMaximumPoints() :
                        (p.getOnlyPoint() == 0 ? point0 : p.getOnlyPoint() == 1 ? point1 : 0);
                Double pointPrice = Double.parseDouble(point.toString()) / (double) ratio;
                Object preferential = p.getOnlyPoint() == 0 ? p.getPrice() - pointPrice : p.getOnlyPoint() == 1 ? 0 : p.getPrice();

                response.setId(p.getPid().toString());
                response.setName(p.getTitle());
                response.setImg(OssUtil.initOssImage(p.getImg()));
                response.setPoint(point.toString());
                response.setPrice(p.getPrice().toString());
                response.setRetailPrice(p.getRetailPrice());
                response.setPreferential(preferential.toString());
                if (isNotBlank(request.getLot()) && isNotBlank(request.getLat())) {
                    List<StoreDistanceBo> storeDistanceList = fookBusinessStoreProductDao.getStoreDistanceListByProductId(p.getPid());
                    if (!storeDistanceList.isEmpty()) {
                        // 取最近的门店名
                        storeDistanceList.forEach((e)->{
                            e.setDistance(DistanceUtil.getDistance(request.getLot(),request.getLat(), e.getLongitude(),e.getDimension()));
                        });
                        Optional<StoreDistanceBo> first = storeDistanceList.stream().sorted(Comparator.comparingDouble(StoreDistanceBo::getDistance)).limit(1).findFirst();
                        if (first.isPresent()) {
                            response.setStoreName(first.get().getStoreName());
                        } else {
                            response.setStoreName(storeBo.getStoreName());
                        }
                    } else {
                        response.setStoreName(storeBo.getStoreName());
                    }
                } else {
                    response.setStoreName(storeBo.getStoreName());
                }
                String romoteUrl = ConfigUtils.getProperty("macaupass.romote_url", "");
                response.setRedirectUrl(romoteUrl.replace("product_id", p.getPid().toString()));
                response.setType(p.getOnlyPoint().toString());
                response.setProductType(p.getType());
                response.setHrefUrl(BusinessProductUtil.fillHrefUrlBy(p.getPid(), p.getType(), p.getHrefUrl(), p.getGoodsId()));
                // 处理收藏
                collectService.setCollectNumber(collectBo, p.getPid(), response, request.isShowLikeText());
                list.add(response);
            }
        }
        return list;

    }

    /**
     * 门店分类
     *
     * @return
     */
    @Cacheable(value = CACHE_STORES_TYPE, key = "@cacheService.getLanguage()",
            unless = "#result.getSnatchList().isEmpty()")
    public StoreTypeResponse getStoresType() {
        Locale local = contextHolder.getLocale();
        String language = local.getLanguage();
        List<FookStoresType> storesTypeList = fookStoresTypeDao.getStoresTypes();
        StoreTypeResponse response = new StoreTypeResponse();
        List<StoreTypeResponse.SnatchListItem> snatchList = new ArrayList<StoreTypeResponse.SnatchListItem>();
        for (FookStoresType fookStoresType : storesTypeList) {
            StoreTypeResponse.SnatchListItem item = new StoreTypeResponse.SnatchListItem();
            item.setImg(OssUtil.initOssImage(fookStoresType.getIcon()));
            item.setId(fookStoresType.getId());
            item.setName(StringEscapeUtils.unescapeJava(fookStoresType.getName()));
            if (language.equals(McoinMall.LANG_EN) && isNotBlank(fookStoresType.getEnglishName())) {
                item.setName(StringEscapeUtils.unescapeJava(fookStoresType.getEnglishName()));
            }
            item.setSort(fookStoresType.getSort());
            snatchList.add(item);
        }
        response.setSnatchList(snatchList);

        int recommendTabSwitch = Integer.parseInt(defaultIfBlank(
                settingsDao.getValueDirect("site.feeds_recommend_tab_switch"), "0"));
        if (recommendTabSwitch == 1) {
            response.setRecommend(feedsJobService.getRecommendTabDataSize("") > 0);
        } else {
            response.setRecommend(false);
        }
        return response;
    }

    /**
     * 门店分类福利
     *
     * @return
     */
    public ShopTypeResponse getShopType(ShopTypeRequest request, String clientType) {

        ShopTypeResponse response = new ShopTypeResponse();

        StoreTypeProductsPageBo pageBo = getStoreTypeFromRedis(request, clientType);
        List<StoreTypeProductsBo> storesList;
        int totalCount;
        if (pageBo != null) {
            storesList = pageBo.getStoreTypeProductsBos();
            totalCount = pageBo.getTotalCount();
        } else {
            storesList = Collections.emptyList();
            totalCount = 0;
        }
        List<ShopTypeResponse.SnatchListItem> snatchList = new ArrayList<ShopTypeResponse.SnatchListItem>();
        int showStock = Integer.parseInt(defaultIfBlank(settingsDao.getValue("site.show_sales_num"), "0"));

        List<Integer> productIds = storesList.stream().map(e -> Integer.parseInt(e.getPid()))
                .distinct().collect(Collectors.toList());
        // 处理收藏
        ProductsCollectBo collectBo = collectService.getProductsCollect(productIds);
        for (StoreTypeProductsBo bo : storesList) {
            if (isBlank(bo.getPreferential())) {
                int ratio = bo.getPointRatio();
                int point0 = StringUtils.isNotEmpty(bo.getMinPoint()) && parseInt(bo.getMinPoint()) > 0 ? parseInt(bo.getMinPoint()) : (ratio / 2);//混合支付
                Object point1 = Double.parseDouble(bo.getPrice()) * ratio;//纯积分
                Object point = parseInt(bo.getOnlyPoint()) == 0 ? point0 : parseInt(bo.getOnlyPoint()) == 1 ? point1 : 0;
                Double pointPrice = Double.parseDouble(point.toString()) / (double) ratio;
                Object preferential = parseInt(bo.getOnlyPoint()) == 0 ? Double.parseDouble(bo.getPrice()) - pointPrice : parseInt(bo.getOnlyPoint()) == 1 ? 0 : bo.getPrice();
                bo.setPreferential(preferential.toString());
                bo.setPoint(point.toString());
            }
            double distanceKm = DistanceUtil.getDistance(request.getLot(),request.getLat(), bo.getLongitude(),bo.getDimension());
            String distance =DistanceUtil.getDistanceStr(distanceKm);

            String imgs = "";
            if (isNotBlank(bo.getImg())) {
                String[] img_arr = bo.getImg().split(",");
                if (img_arr.length > 0) {
                    imgs = img_arr[0];
                }
            }

            ShopTypeResponse.SnatchListItem item = new ShopTypeResponse.SnatchListItem();
            item.setId(bo.getPid());
            item.setStoreName(bo.getStoreName());
            item.setProductName(bo.getTitle());
            item.setImg(OssUtil.initOssImage(imgs));
            item.setPrice(bo.getPrice());
            item.setRetailPrice(bo.getRetailPrice());
            item.setSales(bo.getSales());
            item.setIsShowSlaes(Integer.parseInt(bo.getSales()) > showStock ? "1" : "0");
            item.setPoint(bo.getPoint());
            item.setPreferential(bo.getPreferential());
            item.setType(bo.getOnlyPoint());
            item.setDistance(distance);
            item.setProductType(bo.getType());
            item.setHrefUrl(BusinessProductUtil.fillHrefUrlBy(Integer.valueOf(bo.getPid()), bo.getType(), bo.getHrefUrl(), bo.getGoodsId()));
            // 处理收藏
            collectService.setCollectNumber(collectBo, Integer.parseInt(bo.getPid()), item);
            snatchList.add(item);
        }
        Page respPage = new Page();
        respPage.setPerPage(McoinMall.DEFAULT_PAGE_SIZE);
        respPage.setTotal(totalCount);
        respPage.setLastPage(PageUtil.getPageNum(totalCount, McoinMall.DEFAULT_PAGE_SIZE));
        respPage.setCurrentPage(request.getPage());
        response.setPage(respPage);
        response.setSnatchList(snatchList);
        return response;
    }

    private String getHarmonyCacheSuffix(String clientType){
        boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
        return harmonySwitch ? FEEDS_HARMONY_SUFFIX : "";
    }

    private StoreTypeProductsPageBo getStoreTypeFromRedis(ShopTypeRequest request, String clientType) {
        String categoryKey = "";
        Integer category = request.getCategory();
        if (category != null) {
            categoryKey = String.valueOf(category);
            // category == 0 代表“综合”
            if (category == 0) {
                categoryKey = "";
            }
        }
        int totalCount;
        Collection<String> cacheIds;
        String idType = "store";
        String type = defaultIfBlank(request.getType(), "time");
        boolean isRecommend = false;

        if ("time".equals(type)) {
            String zset_key = PRODUCT_CREATE_TIME_KEY_PREFIX + getHarmonyCacheSuffix(clientType) + categoryKey;
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(zset_key);
            Integer start = request.getStart();
            int end = request.getStart() + (McoinMall.DEFAULT_PAGE_SIZE - 1);
            cacheIds = sortedSet.valueRangeReversed(start, end);
            idType = "product";
            totalCount = sortedSet.size();
            log.info("类别门店 time 查询参数: key = {}，start = {}, end = {}, totalCount: {}", zset_key, start, end, totalCount);
        } else if ("distance".equals(type)) {
            String zset_key = "stores_geo_" + getHarmonyCacheSuffix(clientType) + categoryKey;
            RGeo<String> rGeo = redissonClient.getGeo(zset_key);
            double lot = Double.parseDouble(defaultIfBlank(request.getLot(), "0"));
            double lat = Double.parseDouble(defaultIfBlank(request.getLat(), "0"));
            List<String> geo_list = rGeo.radius(lot, lat, 100000000, GeoUnit.KILOMETERS, GeoOrder.ASC);
            totalCount = geo_list.size();
            cacheIds = pageArray(request.getPage(), McoinMall.DEFAULT_PAGE_SIZE, totalCount, geo_list);
            log.info("类别门店 distance 查询参数: key = {}，lot = {}, lat = {}, totalCount: {}", zset_key, lot, lat, totalCount);
        } else if ("sales".equals(type)) {
            String zset_key = "zset_stores_sales_" + getHarmonyCacheSuffix(clientType) + categoryKey;
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(zset_key);
            Integer start = request.getStart();
            int end = request.getStart() + (McoinMall.DEFAULT_PAGE_SIZE - 1);
            cacheIds = sortedSet.valueRangeReversed(start, end);
            totalCount = sortedSet.size();
            log.info("类别门店 sales 查询参数: key = {}，start = {}, end = {}, totalCount: {}", zset_key, start, end, totalCount);
        } else if ("price".equals(type)) {
            double start_price = Double.parseDouble(defaultIfBlank(request.getStart_price_interval(), "0"));
            double end_price;
            if (isBlank(request.getEnd_price_interval())) {
                end_price = Double.POSITIVE_INFINITY;
            } else {
                end_price = Double.parseDouble(request.getEnd_price_interval());
            }
            String zset_key = "zset_stores_price_" + getHarmonyCacheSuffix(clientType) + categoryKey;
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(zset_key);
            totalCount = sortedSet.count(start_price, true, end_price, true);
            cacheIds = sortedSet.valueRange(start_price, true, end_price, true,
                    request.getStart(), (McoinMall.DEFAULT_PAGE_SIZE - 1));
            log.info("类别门店 price 查询参数: key = {}，start = {}, end = {}, totalCount: {}", zset_key, start_price, end_price, totalCount);
        } else if ("point".equals(type)) {
            double start_point = Double.parseDouble(defaultIfBlank(request.getStart_point_interval(), "0"));
            double end_point;
            if (isBlank(request.getEnd_point_interval())) {
                end_point = Double.POSITIVE_INFINITY;
            } else {
                end_point = Double.parseDouble(request.getEnd_point_interval());
            }
            String zset_key = "zset_stores_point_" + getHarmonyCacheSuffix(clientType) + categoryKey;
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(zset_key);
            totalCount = sortedSet.count(start_point, true, end_point, true);
            cacheIds = sortedSet.valueRange(start_point, true, end_point, true,
                    request.getStart(), (McoinMall.DEFAULT_PAGE_SIZE - 1));
            log.info("类别门店 point 查询参数: key = {}，start = {}, end = {}, totalCount: {}", zset_key, start_point, end_point, totalCount);
        } else if ("recommend".equals(type) && category != null && category == 0) {
            // "综合"下“推荐”tab
            String zset_key = PRODUCT_RECOMMEND_KEY_PREFIX + getHarmonyCacheSuffix(clientType) + categoryKey;
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(zset_key);
            Integer start = request.getStart();
            int end = request.getStart() + (McoinMall.DEFAULT_PAGE_SIZE - 1);
            cacheIds = sortedSet.valueRangeReversed(start, end);
            idType = "product";
            isRecommend = true;
            totalCount = sortedSet.size();
            log.info("类别门店 recommend 查询参数: key = {}，start = {}, end = {}, totalCount: {}", zset_key, start, end, totalCount);
        } else {//默认没传参数按 时间排序
            String zset_key = PRODUCT_CREATE_TIME_KEY_PREFIX + getHarmonyCacheSuffix(clientType) + categoryKey;
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(zset_key);
            Integer start = request.getStart();
            int end = request.getStart() + (McoinMall.DEFAULT_PAGE_SIZE - 1);
            cacheIds = sortedSet.valueRangeReversed(start, end);
            totalCount = sortedSet.size();
            log.info("类别门店 other 查询参数: key = {}，start = {}, end = {}, totalCount: {}", zset_key, start, end, totalCount);
        }
        int showSalesNum = Integer.parseInt(defaultIfBlank(settingsDao.getValue("site.show_sales_num"), "0"));
        if (cacheIds != null && cacheIds.size() > 0) {
            StoreTypeProductsPageBo pageBo = new StoreTypeProductsPageBo();
            List<StoreTypeProductsBo> storeTypeProductsBos = new ArrayList<>();
            for (String cacheId : cacheIds) {
                String key;
                if ("store".equals(idType)) {
                    key = "mcoin:indexstore:" + cacheId;
                } else if ("product".equals(idType)) {
                    key = "mcoin:indexproduct:" + cacheId;
                } else {
                    String msg = messageSource.getMessage("message.basic.nodata", null, contextHolder.getLocale());
                    throw new BusinessException(Response.Code.BAD_REQUEST, msg);
                }
                RMap<String, String> productRemote = redissonClient.getMap(key);
                Map<String, String> product = productRemote.readAllMap();
                StoreTypeProductsBo bo = null;
                if (!isRecommend && product.size() > 0) {
                    bo = new StoreTypeProductsBo();
                    bo.setPid(product.get("pid"));
                    bo.setTypeId(product.get("type_id"));
                    bo.setBusinessId(product.get("businessid"));
                    bo.setTitle(product.get("title"));
                    bo.setImg(product.get("img"));
                    bo.setStoreId(product.get("store_id"));
                    bo.setStoreName(product.get("store_name"));
                    bo.setSales(product.get("sales"));
                    bo.setIsShowSlaes(product.get("is_show_slaes"));
                    bo.setPrice(product.get("price"));
                    bo.setRetailPrice(product.get("retail_price"));
                    bo.setPoint(product.get("point"));
                    bo.setPreferential(product.get("preferential"));
                    bo.setMinPoint(product.get("min_point"));
                    bo.setOnlyPoint(product.get("only_point"));
                    bo.setCreatedAt(product.get("created_at"));
                    bo.setBuyStartTime(product.get("buy_start_time"));
                    bo.setBuyEndTime(product.get("buy_end_time"));
                    bo.setLongitude(product.get("longitude"));
                    bo.setDimension(product.get("dimension"));
                    if (product.containsKey("type")) {
                        bo.setType(Integer.valueOf(product.get("type")));
                    }
                    bo.setHrefUrl(product.get("href_url"));
                    bo.setDistance(DistanceUtil.getDistance(request.getLot(), request.getLat(), bo.getLongitude(), bo.getDimension()));
                    storeTypeProductsBos.add(bo);
                } else if ("product".equals(idType)) {
                    bo = fookBusinessProductDao.selectStoreTypeProductById(Integer.valueOf(cacheId));
                    if (bo != null) {
                        bo.setDistance(DistanceUtil.getDistance(request.getLot(), request.getLat(), bo.getLongitude(), bo.getDimension()));
                        bo.setPreferential(defaultIfNull(StoreTypeUtils.getPreferential(bo), BigDecimal.ZERO).toString());
                        bo.setPoint(String.valueOf(StoreTypeUtils.getPoint(bo)));
                        bo.setIsShowSlaes(parseInt(defaultIfBlank(bo.getSales(), "0")) > showSalesNum ? "1" : "0");
                        storeTypeProductsBos.add(bo);
                    }
                }

                // 判断是否存在一个商品多个门店的情况，如果有则取最新的一个门店返回
                if ("product".equals(idType) && bo != null && isNotBlank(request.getLot()) && isNotBlank(request.getLat())) {
                    String storeGeoByProductId = STORE_GEO_PRODUCT_CREATE_TIME_KEY_PREFIX +
                            getHarmonyCacheSuffix(clientType) + bo.getPid() +
                            (isBlank(categoryKey)? "":"_" + categoryKey);
                    RGeo<String> rGeo = redissonClient.getGeo(storeGeoByProductId);
                    double lot = Double.parseDouble(defaultIfBlank(request.getLot(), "0"));
                    double lat = Double.parseDouble(defaultIfBlank(request.getLat(), "0"));
                    List<String> geo_list = rGeo.radius(lot, lat, 100000000, GeoUnit.KILOMETERS, GeoOrder.ASC);
                    int storeCount = geo_list.size();
                    log.info("商品key：{} 下存在多个门店，数量：{}", storeGeoByProductId, storeCount);
                    if (storeCount > 0) {
                        String storeId = geo_list.get(0);
                        StoreDistanceBo distanceBo = fookStoresDao.selectStoreDistanceById(parseInt(storeId));
                        if (distanceBo != null) {
                            bo.setStoreId(storeId);
                            bo.setStoreName(distanceBo.getStoreName());
                            bo.setLongitude(distanceBo.getLongitude());
                            bo.setDimension(distanceBo.getDimension());
                            bo.setDistance(DistanceUtil.getDistance(request.getLot(), request.getLat(),
                                    distanceBo.getLongitude(), distanceBo.getDimension()));
                        }
                    }
                }
            }
            pageBo.setStoreTypeProductsBos(storeTypeProductsBos);
            pageBo.setTotalCount(totalCount);
            return pageBo;
        }
        return null;
    }

    public List<String> pageArray(Integer page, int pageCount, int allNum, List<String> data) {
        if (page == null || page <= 0) {
            page = 1;
        }
        int pageNum = PageUtil.getPageNum(allNum, pageCount);
        if (page >= pageNum) {
            page = pageNum;
        }
        int start = (page - 1) * pageCount; // Starting index
        return data.stream().skip(start).limit(pageCount).collect(Collectors.toList());
    }

    @Override
    public SnapUpNextResponse getSnapUpNext(SnapUpNextRequest request, String clientType) {
        String language = contextHolder.getLocale().getLanguage();
        Date nextTime = JodaTimeUtil.plusDayToDate(JodaTimeUtil.getDateWithoutTime(), 1);
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
        List<SnapUpItemBo> snapUpItemBos = fookBusinessProductDao.getSnapUpListByNextTime(nextTime, McoinMall.DEFAULT_PAGE_SIZE, harmonySwitch);

        SnapUpNextResponse response = new SnapUpNextResponse();
        response.setSnatchList(getSnatchList(request, language, snapUpItemBos, true));
        return response;
    }

    @Override
    public SnapUpResponse getSnapUp(SnapUpRequest request, String clientType) {
        String language = contextHolder.getLocale().getLanguage();
        Date now = new Date();
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
        List<SnapUpItemBo> snapUpItemBos = fookBusinessProductDao.getSnapUpListByNow(now, McoinMall.DEFAULT_PAGE_SIZE, harmonySwitch);
        SnapUpResponse response = new SnapUpResponse();
        response.setSnatchList(getSnatchList(request, language, snapUpItemBos, false));
        response.setSysTime(JodaTimeUtil.getStandardTime());
        return response;
    }

    @Override
    public SnapUpMoreResponse getSnapUpMore(SnapUpMoreRequest request, String clientType) {
        String language = contextHolder.getLocale().getLanguage();
        Date now = new Date();
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
        int dataCount = fookBusinessProductDao.getSnapUpListCount(now, harmonySwitch);
        int page = request.getPage();
        int offset = PageUtil.getOffset(page, McoinMall.DEFAULT_PAGE_SIZE);
        List<SnapUpItemBo> snapUpItemBos = fookBusinessProductDao.getSnapUpListByPage(now, McoinMall.DEFAULT_PAGE_SIZE, offset, harmonySwitch);
        SnapUpMoreResponse response = new SnapUpMoreResponse();
        response.setSnatchList(getSnatchList(request, language, snapUpItemBos,Boolean.FALSE));
        response.setSysTime(JodaTimeUtil.getStandardTime());

        Page rPage = new Page();
        rPage.setPerPage(McoinMall.DEFAULT_PAGE_SIZE);
        rPage.setTotal(dataCount);
        rPage.setLastPage(PageUtil.getPageNum(dataCount, McoinMall.DEFAULT_PAGE_SIZE));
        rPage.setCurrentPage(page);
        response.setPage(rPage);
        return response;
    }

    private List<SnapUpResponse.SnatchListItem> getSnatchList(SnapUpRequest request, String language, List<SnapUpItemBo> snapUpItemBos, Boolean next) {
        return snapUpConvertService.getSnatchList(request, language, snapUpItemBos, next);
    }

    @Override
    @Cacheable(value = CACHE_INDEX, key = "@cacheService.CACHE_DEFAULT_KEY", unless = "#result.isEmpty()")
    public List<FookModule> getIndex() {
        List<FookModule> fookModules = this.fookModuleDao.selectList(new LambdaQueryWrapper<FookModule>()
                .eq(FookModule::getValid, 1)
                .eq(FookModule::getEnable, 1)
                .orderByAsc(FookModule::getSort));
       return fookModules;
    }
    
    @Override
    public ShopIntervalResponse getShopInterval() {
        ShopIntervalResponse response = new ShopIntervalResponse();
        List<String> point = new ArrayList<>();
        
        // 积分
        String startPoint = (String) redissonClient.getBucket("index_shop_start_point_interval").get();
        String endPoint = (String) redissonClient.getBucket("index_shop_end_point_interval").get();
        String pointInterval = (String) redissonClient.getBucket("index_shop_point_interval").get();
        
        point.add(StringUtils.isNotEmpty(startPoint) ? startPoint : "150");
        point.add(StringUtils.isNotEmpty(endPoint) ? endPoint : "1200");
        point.add(StringUtils.isNotEmpty(pointInterval) ? pointInterval : "150");
        response.setPointInterval(point);
        
        // 价格
        List<String> price = new ArrayList<>();
        String startPrice = (String) redissonClient.getBucket("index_shop_start_price_interval").get();
        String endPrice = (String) redissonClient.getBucket("index_shop_end_price_interval").get();
        String priceInterval = (String) redissonClient.getBucket("index_shop_price_interval").get();
        
        price.add(StringUtils.isNotEmpty(startPrice) ? startPrice : "0");
        price.add(StringUtils.isNotEmpty(endPrice) ? endPrice : "1000");
        price.add(StringUtils.isNotEmpty(priceInterval) ? priceInterval : "10");
        response.setPriceInterval(price);
        
        return response;
    }
}
