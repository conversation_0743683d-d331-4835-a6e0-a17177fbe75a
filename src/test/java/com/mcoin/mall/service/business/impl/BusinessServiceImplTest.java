package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bo.StoreDistanceBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.ShareProductResponse;
import com.mcoin.mall.util.ConfigUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BusinessServiceImplTest{

    @InjectMocks
    private BusinessServiceImpl businessService;

    private String key;

    @Mock
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Mock
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;

    @Mock
    private FookBusinessDao fookBusinessDao;

    private Locale locale;

    private String id;

    private FookBusinessProduct product;

    private FookBusiness business;

    @Mock
    private FookBusinessProductDao fookBusinessProductDao;

    @Mock
    private ContextHolder contextHolder;

    @Mock
    private MessageSource messageSource;


    MockedStatic<ConfigUtils> configUtilsStatic;


    @Test
    public void testGetShareProduct_BusinessNotFound() {


        when(contextHolder.getLocale()).thenReturn(locale);
        when(ConfigUtils.getProperty("security.product.share.secret")).thenReturn(key);
        when(messageSource.getMessage("message.order.platform", null, locale)).thenReturn("Platform is not available");
        when(fookBusinessProductDao.selectByPrimaryKey(product.getId())).thenReturn(product);

        when(fookBusinessDao.selectByPrimaryKey(product.getBusinessid())).thenReturn(null);

        BusinessException exception = assertThrows(BusinessException.class, () -> {
            businessService.getShareProduct(id);
        });
    
        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("Platform is not available", exception.getMessage());
    }

    @Test
    public void testGetShareProduct_ProductNotAvailable() {
        when(contextHolder.getLocale()).thenReturn(locale);
        when(ConfigUtils.getProperty("security.product.share.secret")).thenReturn(key);
        when(messageSource.getMessage("message.order.platform", null, locale)).thenReturn("Platform is not available");
        when(fookBusinessProductDao.selectByPrimaryKey(product.getId())).thenReturn(null);
//        when(fookBusinessStoreProductDao.getStoreDistanceListByProductId(product.getId())).thenReturn(Collections.emptyList());

        BusinessException exception = assertThrows(BusinessException.class, () -> {
            businessService.getShareProduct(id);
        });
    
        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("Platform is not available", exception.getMessage());
    }

    @BeforeEach
    public void setUp() {

        configUtilsStatic = mockStatic(ConfigUtils.class);
        MockitoAnnotations.openMocks(this);

        locale = Locale.ENGLISH;
        key = "SitZuhHSRgAYblyG";
        id = "54796c2f537a5756524862774b612f655a644d4449773d3d";
    
        product = new FookBusinessProduct();
        product.setId(1);
        product.setPrice(BigDecimal.valueOf(100));
        product.setPointRatio(2);
        product.setMinPoint(10);
        product.setMaximumPoints(100);
        product.setOnlyPoint(1);
        product.setTitle("Test Product");
        product.setImg("test-img.jpg");
        product.setRetailPrice(BigDecimal.valueOf(90));
        product.setSnapUp(2);
    
        business = new FookBusiness();
        business.setSystemType(3);
    }


    @AfterEach
    void tearDown() {
        configUtilsStatic.close();
    }


    @Test
    public void testGetShareProduct_Success() {

        when(contextHolder.getLocale()).thenReturn(locale);
        when(ConfigUtils.getProperty("security.product.share.secret")).thenReturn(key);
        when(fookBusinessProductDao.selectByPrimaryKey(product.getId())).thenReturn(product);
        when(fookBusinessDao.selectByPrimaryKey(product.getBusinessid())).thenReturn(business);
        when(fookBusinessProductTranslationsDao.getTranslationsById(product.getId(), locale.getLanguage())).thenReturn(null);

        StoreDistanceBo storeDistanceBo = new StoreDistanceBo();
        storeDistanceBo.setStoreName("Store Name");
        when(fookBusinessStoreProductDao.getStoreDistanceListByProductId(product.getId())).thenReturn(Collections.singletonList(storeDistanceBo));
//        when(OssUtil.getProductImg(product.getImg())).thenReturn("product-img.jpg");
        when(ConfigUtils.getProperty("macaupass.romote_url", "")).thenReturn("http://example.com/product_id");
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl")).thenReturn("http://oss.mpay.com/");

        ShareProductResponse response = businessService.getShareProduct(id);
        assertNotNull(response);
        assertEquals("http://oss.mpay.com/test-img.jpg", response.getImg());
        assertEquals("Test Product", response.getName());
        assertEquals(BigDecimal.valueOf(90), response.getRetailPrice());
        assertEquals(100, response.getPoint());
        assertEquals(BigDecimal.ZERO, response.getPreferential());
        assertEquals("Store Name", response.getStoreName());
        assertTrue(response.getSnapUp() == 2);
        assertEquals("http://example.com/" + product.getId(), response.getRedirectUrl());
    }

    @Test
    public void testGetShareProduct_DecryptionFailure() {

        when(contextHolder.getLocale()).thenReturn(locale);
        when(ConfigUtils.getProperty("security.product.share.secret")).thenReturn(key);
        when(messageSource.getMessage("message.basic.parameter", null, locale)).thenReturn("Invalid parameter");

        BusinessException exception = assertThrows(BusinessException.class, () -> {
            businessService.getShareProduct("xxxx");
        });

        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("Invalid parameter", exception.getMessage());
    }

}