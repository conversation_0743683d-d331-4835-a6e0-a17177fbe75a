package com.mcoin.mall.mp.controller.api;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.client.model.MpayPayRefundRequest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.api.ConfirmRefundRequest;
import com.mcoin.mall.mp.model.api.ConfirmRefundResponse;
import com.mcoin.mall.mp.security.UserInfo;
import com.mcoin.mall.mp.util.ConfigUtils;
import com.mcoin.mall.mp.util.SignUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Transactional
class ApiAfterSaleControllerTest extends BaseUnitTest {

    @Autowired
    private ApiAfterSaleController apiAfterSaleController;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        try {
            when(maslClient.refund(any(MpayPayRefundRequest.class))).thenReturn("{\"data\":{\"result_code\":\"0055\",\"result_msg\":\"當前訂單已全部退款\",\"result_msg_en\":\"The order has been fully refunded.\"},\"is_success\":\"T\",\"sign_type\":\"MD5\",\"sign\":\"AFFA21464E1FBCAAAD6FB0C43604910C\"}");
        } catch (BlockException e) {
            e.printStackTrace();
        }
    }

    @Test
    void confirmRefund_ShouldSucceed_WhenSignatureIsValid() {
        // Arrange
        ConfirmRefundRequest request = new ConfirmRefundRequest();
        request.setId(1L);
        request.setSid(65);
        request.setAdminId(1);
        request.setRefundTotalAmount("10.00");
        request.setRefundWay("ORIGINAL");

        // 生成签名 - 使用小写参数名
        Map<String, String> params = new HashMap<>();
        params.put("id", "1");
        params.put("sid", "65");
        params.put("adminid", "1");
        params.put("refundtotalamount", "10.00");
        params.put("refundway", "ORIGINAL");
        String signature = SignUtil.phpSign(ConfigUtils.getProperty("mcoinMall.mcoinSecret"), params);
        request.setSignature(signature);

        // Act
        Response<ConfirmRefundResponse> response = apiAfterSaleController.confirmRefund(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void confirmRefund_ShouldFail_WhenSignatureIsInvalid() {
        // Arrange
        ConfirmRefundRequest request = new ConfirmRefundRequest();
        request.setId(1L);
        request.setSid(65);
        request.setAdminId(1);
        request.setRefundTotalAmount("100.00");
        request.setRefundWay("ORIGINAL");
        request.setSignature("invalid_signature");

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            apiAfterSaleController.confirmRefund(request);
        });
    }
}