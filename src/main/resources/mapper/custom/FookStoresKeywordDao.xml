<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresKeywordDao">
    <select id="getStoreKeyWords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_stores_keyword
        WHERE
        enable = 1
        AND sort <![CDATA[ <> ]]> 0
        order by sort asc
    </select>

    <select id="getStoreKeyWordsWithFilter" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_stores_keyword
        WHERE
        enable = 1
        AND sort <![CDATA[ <> ]]>  0
        <if test="filterMiniProgram == true">
            AND (src_type IS NULL OR src_type <![CDATA[ <> ]]> 4)
        </if>
        order by sort asc
    </select>

    <select id="getStoreKeyWordsByStoreIds" resultType="com.mcoin.mall.bean.StoresKeywordWithStoreId">
        select
        fsk.id, fsk.pid, fsk.`name`, fsk.english_name, fsk.sort, fsk.`enable`, fsk.icon, fsk.if_index_show, fsk.created_at, fsk.updated_at,
        fsk.src,fskc.stores_id
        from fook_stores_keyword_class fskc
        left join fook_stores_keyword fsk
        on fsk.id = fskc.stores_keyword_id
        where
        fsk.enable = 1
        AND fskc.stores_id in
        <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
    </select>
</mapper>