{"singleOrderInfo": {"id": 1, "orderid": 1, "prodcutid": 3003, "productPrice": 99.99, "imageSnapshots": "https://example.com/image.jpg", "titleSnapshots": "Amazing Product", "descSnapshots": "This is a great product that does amazing things.", "vaildStartTime": "2023-04-21T00:00:00Z", "vaildEndTime": "2123-05-21T23:59:59Z", "detailsSnapshots": "Detailed information about the product...", "number": 2, "isWeekend": 1, "noUseday": "2019-03-19 00:00:00 - 2019-04-30 00:00:00", "type": 1, "img": "https://example.com/product.jpg", "retailPrice": 199.99, "isVacation": 0, "isAllowRefund": 1, "feeRate": 2.5, "buyStartTime": "2023-04-22T08:00:00Z", "buyEndTime": "2023-04-30T20:00:00Z", "validMode": 1, "dayNum": 30, "applicableShops": "Shop A, Shop B, Shop C", "isHot": 1, "momecoinsOption": "Option1, Option2", "threshold": 1000, "isGreat": 0, "isRedeen": 0, "isMomecoin": 1, "ordercodeId": 4004, "refundid": 5005, "orderAmount": 299.98, "score": 88, "mpayintegral": 100, "memberintegral": 50.0, "milesFirst": "First Class", "milesName": "Frequent Flyer", "milesMember": 1234567890123, "milesMilage": 1234.56, "isVoucher": 0, "isSettlement": 1, "tnc": "Terms and Conditions text here..."}}