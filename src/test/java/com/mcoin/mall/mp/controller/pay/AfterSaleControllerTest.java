package com.mcoin.mall.mp.controller.pay;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.pay.AfterSaleDetailRequest;
import com.mcoin.mall.mp.model.pay.AfterSaleDetailResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@Transactional
class AfterSaleControllerTest extends BaseUnitTest {

    @Autowired
    private AfterSaleController afterSaleController;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
    }

//    @Test
//    void apply_ShouldReturnApplyResponse() {
//        // Arrange
//        AfterSaleApplyRequest request = new AfterSaleApplyRequest();
//        request.setOrderGoodsId("123");
//        request.setReceiptStatus(1);
//        request.setRefundMethod(1);
//        request.setRefundReason("商品有问题");
//        request.setRefundRemark("请尽快处理");
//        request.setVoucher(Arrays.asList("image1.jpg", "image2.jpg"));
//
//        // Act
//        Response<AfterSaleApplyResponse> response = afterSaleController.apply(request);
//
//        // Assert
//        assertNotNull(response);
//        assertEquals(Response.Code.OK.getCode(), response.getCode());
//        assertNotNull(response.getData());
//    }

    @Test
    void detail_ShouldReturnDetailResponse() {
        // Arrange
        AfterSaleDetailRequest request = new AfterSaleDetailRequest();
        request.setId(1);

        // Act
        Response<AfterSaleDetailResponse> response = afterSaleController.detail(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }
} 