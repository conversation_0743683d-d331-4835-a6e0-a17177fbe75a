<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessInformationDao">
  <select id="getBusinessInformationByIds" resultType="java.lang.Integer">
    SELECT id
    FROM fook_business_information
    WHERE id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="selectInformation"  resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List" />
  from fook_business_information
  where enable = 1
  </select>
</mapper>