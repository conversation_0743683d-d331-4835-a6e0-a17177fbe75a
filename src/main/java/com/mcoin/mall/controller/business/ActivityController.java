package com.mcoin.mall.controller.business;

import com.mcoin.mall.model.*;
import com.mcoin.mall.service.business.ActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
public class ActivityController {

	@Autowired
	private ActivityService activityService;
    /**
     * 获取优惠与活动列表
     * @return
     */
    @GetMapping(value = "/api/business/daygainlist", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<DaygainListResponse> dayGainList(@Valid DaygainListRequest request) {
        DaygainListResponse response = activityService.dayGainList(request);
        return Responses.ok(response);
    }
    /**
     * 获取优惠活动商家列表
     * @return
     */
    @GetMapping(value = "/api/business/merchantlist", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ActiveMerchantListResponse> activeMerchantList(@Valid ActiveMerchantListRequest request) {
    	Response<ActiveMerchantListResponse> response = activityService.merchantList(request);
        return response;
    }
}
