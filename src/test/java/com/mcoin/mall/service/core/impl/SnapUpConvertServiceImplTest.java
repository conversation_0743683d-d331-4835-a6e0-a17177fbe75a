package com.mcoin.mall.service.core.impl;

import com.mcoin.mall.bo.ProductsCollectBo;
import com.mcoin.mall.bo.SnapUpItemBo;
import com.mcoin.mall.bo.StoreDistanceBo;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.model.BaseDistanceRequest;
import com.mcoin.mall.model.SnapUpRequest;
import com.mcoin.mall.model.SnapUpSessionProductItem;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.util.BusinessProductUtil;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.DistanceUtil;
import com.mcoin.mall.util.JsonTestUtils;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.util.SnapUpUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

public class SnapUpConvertServiceImplTest {

    @Mock
    private CollectService collectService;

    @Mock
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @InjectMocks
    private SnapUpConvertServiceImpl snapUpConvertService;

    private List<SnapUpItemBo> snapUpItemBos;
    private List<StoreDistanceBo> storeDistanceBos;
    private ProductsCollectBo productsCollectBo;
    private MockedStatic<ConfigUtils> mockedStatic;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        snapUpItemBos = new ArrayList<>();
        storeDistanceBos = new ArrayList<>();
        productsCollectBo = new ProductsCollectBo();
    }
    
    @AfterEach
    void cleanup() {
        if (mockedStatic != null) {
            mockedStatic.close();
        }
    }

    @Test
    void testConvertToSessionSnatchList() {
        // Arrange
        BaseDistanceRequest request = new BaseDistanceRequest();
        request.setLat("123.456");
        request.setLot("789.012");
        SnapUpItemBo item1 = new SnapUpItemBo();
        item1.setId(1);
        item1.setTitle("Test Product 1");
        item1.setImg("https://test_img_1.jpg");
        item1.setZipImg("https://test_zip_img_1.jpg");
        item1.setPrice(new BigDecimal("100.00"));
        item1.setRetailPrice(new BigDecimal("200.00"));
        item1.setBuyEndTime(Date.from(LocalDate.now().plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        item1.setPointRatio(10);
        item1.setMinPoint(100);
        item1.setOnlyPoint(1);
        item1.setActualSales(5);
        item1.setStock(10);
        item1.setBusinessName("Test Business");
        item1.setHrefUrl("https://example.com/product/1");
        snapUpItemBos.add(item1);

        SnapUpItemBo item2 = new SnapUpItemBo();
        item2.setId(2);
        item2.setTitle("Test Product 2");
        item2.setImg("https://test_img_2.jpg");
        item2.setPrice(new BigDecimal("200.00"));
        item2.setRetailPrice(new BigDecimal("300.00"));
        item2.setBuyEndTime(Date.from(LocalDate.now().plusDays(2).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        item2.setPointRatio(20);
        item2.setMinPoint(200);
        item2.setOnlyPoint(2);
        item2.setActualSales(0);
        item2.setStock(10);
        item2.setBusinessName("Another Test Business");
        item2.setHrefUrl("https://example.com/product/2");
        snapUpItemBos.add(item2);

        storeDistanceBos.add(new StoreDistanceBo());
        storeDistanceBos.get(0).setDimension("116.301944");
        storeDistanceBos.get(0).setLongitude("39.904204");

        when(fookBusinessStoreProductDao.getStoreDistanceListByProductId(item1.getId())).thenReturn(storeDistanceBos);
        when(fookBusinessStoreProductDao.getStoreDistanceListByProductId(item2.getId())).thenReturn(new ArrayList<>());
        when(collectService.getProductsCollect(anyList())).thenReturn(productsCollectBo);
        doAnswer(invocation -> {
            SnapUpSessionProductItem item = invocation.getArgument(2);
            item.setCollect(1);
            item.setCollectCount("1");
            return null;
        }).when(collectService).setCollectNumber(any(ProductsCollectBo.class), anyInt(), any(SnapUpSessionProductItem.class));
        // Act
        List<SnapUpSessionProductItem> result = snapUpConvertService.convertToSessionSnatchList(request, snapUpItemBos);

        // Assert
        Assertions.assertEquals(2, result.size());
        Assertions.assertEquals(item1.getId(), result.get(0).getId());
        Assertions.assertEquals(item1.getTitle(), result.get(0).getTitle());
        Assertions.assertEquals(OssUtil.initOssImage(item1.getImg()), result.get(0).getImage());
        Assertions.assertEquals(OssUtil.initOssImage(item1.getZipImg()), result.get(0).getImg());
        Assertions.assertEquals(item1.getPrice(), result.get(0).getPrice());
        Assertions.assertEquals(item1.getRetailPrice(), result.get(0).getRetailPrice());
        Assertions.assertEquals(item1.getBuyEndTime(), result.get(0).getBuyEndTime());
        Assertions.assertEquals(item1.getBusinessId(), result.get(0).getBoom());
        Assertions.assertEquals(item1.getOnlyPoint(), result.get(0).getPayType());
        Assertions.assertEquals(DistanceUtil.getDistanceStr(DistanceUtil.getDistance(request.getLot(), request.getLat(),
                storeDistanceBos.get(0).getLongitude(), storeDistanceBos.get(0).getDimension())), result.get(0).getDistance());
        Assertions.assertEquals(item1.getType(), result.get(0).getType());
        Assertions.assertEquals(BusinessProductUtil.fillHrefUrlBy(item1.getId(), item1.getType(), item1.getHrefUrl(), item1.getGoodsId()),
                result.get(0).getHrefUrl());
        Assertions.assertEquals(SnapUpUtils.getPoint(item1), result.get(0).getPoint());
        Assertions.assertEquals(SnapUpUtils.getPreferential(item1), result.get(0).getPreferential());
        Assertions.assertEquals(item1.getSnapUp(), result.get(0).getSnapUp());
        Assertions.assertEquals(1, result.get(0).getCollect());
        Assertions.assertEquals("1", result.get(0).getCollectCount());
        Assertions.assertEquals(item1.getBusinessName(), result.get(0).getBusinessName());
        Assertions.assertEquals(item1.getStock(), result.get(0).getStock());
        Assertions.assertEquals(item1.getActualSales(), result.get(0).getActualSales());
        Assertions.assertEquals((int) (item1.getActualSales() * 100.0 / (item1.getActualSales() + item1.getStock())), result.get(0).getOccupy());

        Assertions.assertEquals(item2.getId(), result.get(1).getId());
        Assertions.assertEquals(item2.getTitle(), result.get(1).getTitle());
        Assertions.assertEquals(OssUtil.initOssImage(item2.getImg()), result.get(1).getImage());
        Assertions.assertEquals(OssUtil.initOssImage(item2.getImg()), result.get(1).getImg());
        Assertions.assertEquals(item2.getPrice(), result.get(1).getPrice());
        Assertions.assertEquals(item2.getRetailPrice(), result.get(1).getRetailPrice());
        Assertions.assertEquals(item2.getBuyEndTime(), result.get(1).getBuyEndTime());
        Assertions.assertEquals(item2.getBusinessId(), result.get(1).getBoom());
        Assertions.assertEquals(item2.getOnlyPoint(), result.get(1).getPayType());
        Assertions.assertEquals("", result.get(1).getDistance());
        Assertions.assertEquals(item2.getType(), result.get(1).getType());
        Assertions.assertEquals(BusinessProductUtil.fillHrefUrlBy(item2.getId(), item2.getType(),item2.getHrefUrl(), item2.getGoodsId()), result.get(1).getHrefUrl());
        Assertions.assertEquals(SnapUpUtils.getPoint(item2), result.get(1).getPoint());
        Assertions.assertEquals(SnapUpUtils.getPreferential(item2), result.get(1).getPreferential());
        Assertions.assertEquals(item2.getSnapUp(), result.get(1).getSnapUp());
        Assertions.assertEquals(1, result.get(1).getCollect());
        Assertions.assertEquals("1", result.get(1).getCollectCount());
        Assertions.assertEquals(item2.getBusinessName(), result.get(1).getBusinessName());
        Assertions.assertEquals(item2.getStock(), result.get(1).getStock());
        Assertions.assertEquals(item2.getActualSales(), result.get(1).getActualSales());
        Assertions.assertEquals((int) (item2.getActualSales() * 100.0 / (item2.getActualSales() + item2.getStock())), result.get(1).getOccupy());
    }

    @Test
    void testGetSnatchList() {
        // given
        when(fookBusinessStoreProductDao.getStoreDistanceListByProductId(anyInt())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/storeDistanceBo.json", "singleObj", StoreDistanceBo.class))
        );
        when(collectService.getProductsCollect(anyList())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/productsCollectBo.json", "singleObj", ProductsCollectBo.class)
        );
        mockedStatic = Mockito.mockStatic(ConfigUtils.class);
        mockedStatic.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("http://127.0.0.1/");

        // when & then
        assertEquals("7737.9km", 
                     snapUpConvertService.getSnatchList(getSnapUpRequest(), "language", Collections.singletonList(JsonTestUtils.getObjectFromJson("json/snapUpItemBo.json", "singleObj", SnapUpItemBo.class)), true)
                                         .get(0).getDistance());
    }

    private static SnapUpRequest getSnapUpRequest() {
        SnapUpRequest request = new SnapUpRequest();
        request.setLat(String.valueOf(22.139438213686102));
        request.setLot(String.valueOf(113.52259101699902));
        return request;
    }
}
