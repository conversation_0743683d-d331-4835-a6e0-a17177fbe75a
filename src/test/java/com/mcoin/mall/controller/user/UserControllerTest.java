package com.mcoin.mall.controller.user;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookPlatformUsercollectionDao;
import com.mcoin.mall.model.CollectionListRequest;
import com.mcoin.mall.model.CollectionListResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

/**
 * 集成测试 UserController
 * 使用真实服务和数据库，测试用户收藏列表功能
 */
@Slf4j
class UserControllerTest extends BaseUnitTest {

    @Resource
    private UserController userController;


    @Resource
    private FookPlatformUsercollectionDao fookPlatformUsercollectionDao;

    private static final int TEST_USER_ID = 99999;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        log.info("UserController instance: {}" , (userController != null ? "injected" : "null"));

        // 模拟Locale
        doReturn(Locale.CHINESE).when(contextHolder).getLocale();

        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");

        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();

        // 加载测试数据
        try {
            // 加载SQL文件，包含清理和初始化测试数据
            DB.getDB().source("db/tempData/test_user_collections_data.sql", "fooku");
        } catch (Exception e) {
            throw new RuntimeException("Failed to load test data for UserController collections tests", e);
        }
    }

    @Test
    void testCollections() {
        // 创建请求对象
        CollectionListRequest request = new CollectionListRequest();
        request.setType(2); // 设置福利类型为优惠券
        request.setPage(1); // 设置页码

        log.info("UserController: {}", userController);
        log.info("ContextHolder: {}", contextHolder);
        log.info("ContextHolder.getAuthUserInfo(): {}", (contextHolder != null ? contextHolder.getAuthUserInfo() : "null"));

        // 直接调用控制器方法
        Response<CollectionListResponse> response;
        try {
            response = userController.collections(request);
            log.info("Response: {}", response);

            // 验证响应结构
            assertNotNull(response, "Response should not be null");
            assertEquals(Response.Status.SUCCESS.ordinal(), response.getStatus(), "Response status should be SUCCESS");
            assertNotNull(response.getData(), "Response data should not be null");
        } catch (Exception e) {
            log.error("Exception occurred: {}", e.getMessage(), e);
            throw e;
        }

        // 验证收藏列表数据
        CollectionListResponse collectionListResponse = response.getData();
        assertNotNull(collectionListResponse, "Collection list response should not be null");

        // 打印调试信息
        log.info("Collection list response: {}", collectionListResponse);

        // 验证收藏列表
        assertNotNull(collectionListResponse.getSnatchList(), "Collection list should not be null");
        assertFalse(collectionListResponse.getSnatchList().isEmpty(), "Collection list should not be empty");

        // 验证收藏列表中包含我们的测试数据
        List<CollectionListResponse.SnatchListItem> items = collectionListResponse.getSnatchList();

        // 打印所有项目的ID
        log.info("Found items with IDs: ");
        for (CollectionListResponse.SnatchListItem item : items) {
            log.info("Item ID: {}, Title: {}", item.getId(), item.getTitle());
        }

        boolean foundTestProduct1 = false;
        boolean foundTestProduct2 = false;

        for (CollectionListResponse.SnatchListItem item : items) {
            if (item.getId() != null && item.getId() == 30001) {
                foundTestProduct1 = true;
                assertEquals("Collection Test Product 1", item.getTitle(), "Product title should match");
                assertEquals(1, item.getCollect(), "Product should be marked as collected");
            } else if (item.getId() != null && item.getId() == 30002) {
                foundTestProduct2 = true;
                assertEquals("Collection Test Product 2", item.getTitle(), "Product title should match");
                assertEquals(1, item.getCollect(), "Product should be marked as collected");
            }
        }

        assertTrue(foundTestProduct1, "Should find test product 1 in collection list");
        assertTrue(foundTestProduct2, "Should find test product 2 in collection list");
    }
}
