<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformUsercollectionDao">
    <select id="selectForCollection" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fook_platform_usercollection
        where userid = #{userid,jdbcType=INTEGER}
        and `type` = #{type,jdbcType=INTEGER}
        and collectionid = #{collectionid,jdbcType=INTEGER}
    </select>

    <select id="countCollection" resultType="java.lang.Integer">
        select
        count(1)
        from fook_platform_usercollection
        where enable = #{enable,jdbcType=INTEGER}
        and `type` = #{type,jdbcType=INTEGER}
        and collectionid = #{collectionid,jdbcType=INTEGER}
    </select>

    <select id="getUserEnabledByIds" resultType="com.mcoin.mall.bean.FookPlatformUsercollection">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_platform_usercollection
        WHERE collectionid IN
        <foreach item="item" index="index" collection="productIds" open="(" separator=", " close=")">
            #{item}
        </foreach>
        AND userid = #{userId,jdbcType=INTEGER}
        AND type = 2
        AND enable = 1
    </select>
    <select id="getCountByIds" resultType="com.mcoin.mall.bean.UsercollectionCnt">
        select collectionid,count(*) as cnt from fook_platform_usercollection
        where collectionid in
        <foreach item="item" index="index" collection="productIds" open="(" separator=", " close=")">
            #{item}
        </foreach>
        and enable = 1 and type =2 group by collectionid Order by collectionid;

    </select>
    <delete id="deleteByUsercollection" parameterType="com.mcoin.mall.bean.FookPlatformUsercollection">
        DELETE FROM fook_platform_usercollection
        where userid = #{userid,jdbcType=INTEGER}
          and `type` = #{type,jdbcType=INTEGER}
          and collectionid = #{collectionid,jdbcType=INTEGER}
    </delete>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fook_platform_usercollection
        where userid = #{userid,jdbcType=INTEGER}
        and `type` = #{type,jdbcType=INTEGER}
        and EXISTS (
        SELECT id FROM fook_business_product fbp WHERE
        fbp.id = collectionid
        AND fbp.shelf_status = 1
        AND fbp.enable = 1
        AND fbp.is_redeem = 0
        AND fbp.platform_type = 1
        <if test="productType == 12">
            AND fbp.`type` = 12
        </if>
        <if test="productType != 12">
            AND fbp.`type` <![CDATA[ <> ]]> 12
        </if>
        )
        order by create_time DESC
        limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>
    <select id="selectListCnt" resultType="java.lang.Integer">
        select
        COUNT(*)
        from fook_platform_usercollection
        where userid = #{userid,jdbcType=INTEGER}
        and `type` = #{type,jdbcType=INTEGER}
        and EXISTS (
        SELECT id FROM fook_business_product fbp WHERE
        fbp.id = collectionid
        AND fbp.shelf_status = 1
        AND fbp.enable = 1
        AND fbp.is_redeem = 0
        AND fbp.platform_type = 1
        <if test="productType == 12">
            AND fbp.`type` = 12
        </if>
        <if test="productType != 12">
            AND fbp.`type` <![CDATA[ <> ]]> 12
        </if>
        )
    </select>
    <select id="selectCollections" resultType="com.mcoin.mall.bean.FookPlatformUsercollection">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_platform_usercollection fpu
        WHERE fpu.userid  = #{userId,jdbcType=INTEGER}
        AND fpu.collectionid in
        <foreach item="item" index="index" collection="productIds" open="(" separator=", " close=")">
            #{item}
        </foreach>
        and type =2
    </select>
</mapper>