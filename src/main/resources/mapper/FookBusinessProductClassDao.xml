<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductClassDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessProductClass">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="valid" jdbcType="TINYINT" property="valid" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, `name`, `level`, parent_code, `valid`, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_product_class
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_product_class
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.mcoin.mall.bean.FookBusinessProductClass">
    insert into fook_business_product_class (id, code, `name`, 
      `level`, parent_code, `valid`, 
      created_at, updated_at)
    values (#{id,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{level,jdbcType=TINYINT}, #{parentCode,jdbcType=VARCHAR}, #{valid,jdbcType=TINYINT}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessProductClass">
    insert into fook_business_product_class
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="parentCode != null">
        parent_code,
      </if>
      <if test="valid != null">
        `valid`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="parentCode != null">
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessProductClass">
    update fook_business_product_class
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=TINYINT},
      </if>
      <if test="parentCode != null">
        parent_code = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        `valid` = #{valid,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessProductClass">
    update fook_business_product_class
    set code = #{code,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=TINYINT},
      parent_code = #{parentCode,jdbcType=VARCHAR},
      `valid` = #{valid,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>