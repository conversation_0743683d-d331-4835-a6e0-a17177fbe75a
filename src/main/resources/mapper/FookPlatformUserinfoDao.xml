<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformUserinfoDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformUserinfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="wechat_userid" jdbcType="INTEGER" property="wechatUserid" />
    <result column="areaid" jdbcType="INTEGER" property="areaid" />
    <result column="registered_phone" jdbcType="VARCHAR" property="registeredPhone" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="sex" jdbcType="INTEGER" property="sex" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
    <result column="growth_value" jdbcType="INTEGER" property="growthValue" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="comment_count" jdbcType="INTEGER" property="commentCount" />
    <result column="question" jdbcType="VARCHAR" property="question" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="question2" jdbcType="VARCHAR" property="question2" />
    <result column="answer2" jdbcType="VARCHAR" property="answer2" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="map" jdbcType="INTEGER" property="map" />
    <result column="first_authorized_time" jdbcType="TIMESTAMP" property="firstAuthorizedTime" />
    <result column="macaupass_id" jdbcType="INTEGER" property="macaupassId" />
    <result column="credit_card_pay" jdbcType="INTEGER" property="creditCardPay" />
    <result column="pay_by_credit_card" jdbcType="INTEGER" property="payByCreditCard" />
    <result column="bing" jdbcType="TINYINT" property="bing" />
    <result column="new_phone" jdbcType="VARCHAR" property="newPhone" />
    <result column="bing_time" jdbcType="TIMESTAMP" property="bingTime" />
    <result column="member_id" jdbcType="INTEGER" property="memberId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.FookPlatformUserinfo">
    <result column="last_login_time" jdbcType="VARBINARY" property="lastLoginTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, wechat_userid, areaid, registered_phone, `password`, nick_name, sex, avatar,
    email, `enable`, city, address, birthday, growth_value, score, comment_count, question,
    answer, question2, answer2, create_time, `map`, first_authorized_time, macaupass_id,
    credit_card_pay, pay_by_credit_card, bing, new_phone, bing_time, member_id
  </sql>
  <sql id="Blob_Column_List">
    last_login_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fook_platform_userinfo
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_userinfo
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformUserinfo">
    insert into fook_platform_userinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="wechatUserid != null">
        wechat_userid,
      </if>
      <if test="areaid != null">
        areaid,
      </if>
      <if test="registeredPhone != null">
        registered_phone,
      </if>
      <if test="password != null">
        `password`,
      </if>
      <if test="nickName != null">
        nick_name,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="growthValue != null">
        growth_value,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="commentCount != null">
        comment_count,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="answer != null">
        answer,
      </if>
      <if test="question2 != null">
        question2,
      </if>
      <if test="answer2 != null">
        answer2,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="map != null">
        `map`,
      </if>
      <if test="firstAuthorizedTime != null">
        first_authorized_time,
      </if>
      <if test="macaupassId != null">
        macaupass_id,
      </if>
      <if test="creditCardPay != null">
        credit_card_pay,
      </if>
      <if test="payByCreditCard != null">
        pay_by_credit_card,
      </if>
      <if test="bing != null">
        bing,
      </if>
      <if test="newPhone != null">
        new_phone,
      </if>
      <if test="bingTime != null">
        bing_time,
      </if>
      <if test="memberId != null">
        member_id,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="wechatUserid != null">
        #{wechatUserid,jdbcType=INTEGER},
      </if>
      <if test="areaid != null">
        #{areaid,jdbcType=INTEGER},
      </if>
      <if test="registeredPhone != null">
        #{registeredPhone,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=INTEGER},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="growthValue != null">
        #{growthValue,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="commentCount != null">
        #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="question != null">
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=VARCHAR},
      </if>
      <if test="question2 != null">
        #{question2,jdbcType=VARCHAR},
      </if>
      <if test="answer2 != null">
        #{answer2,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="map != null">
        #{map,jdbcType=INTEGER},
      </if>
      <if test="firstAuthorizedTime != null">
        #{firstAuthorizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="macaupassId != null">
        #{macaupassId,jdbcType=INTEGER},
      </if>
      <if test="creditCardPay != null">
        #{creditCardPay,jdbcType=INTEGER},
      </if>
      <if test="payByCreditCard != null">
        #{payByCreditCard,jdbcType=INTEGER},
      </if>
      <if test="bing != null">
        #{bing,jdbcType=TINYINT},
      </if>
      <if test="newPhone != null">
        #{newPhone,jdbcType=VARCHAR},
      </if>
      <if test="bingTime != null">
        #{bingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberId != null">
        #{memberId,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=VARBINARY},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformUserinfo">
    update fook_platform_userinfo
    <set>
      <if test="wechatUserid != null">
        wechat_userid = #{wechatUserid,jdbcType=INTEGER},
      </if>
      <if test="areaid != null">
        areaid = #{areaid,jdbcType=INTEGER},
      </if>
      <if test="registeredPhone != null">
        registered_phone = #{registeredPhone,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        nick_name = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=INTEGER},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=INTEGER},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="growthValue != null">
        growth_value = #{growthValue,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="commentCount != null">
        comment_count = #{commentCount,jdbcType=INTEGER},
      </if>
      <if test="question != null">
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        answer = #{answer,jdbcType=VARCHAR},
      </if>
      <if test="question2 != null">
        question2 = #{question2,jdbcType=VARCHAR},
      </if>
      <if test="answer2 != null">
        answer2 = #{answer2,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="map != null">
        `map` = #{map,jdbcType=INTEGER},
      </if>
      <if test="firstAuthorizedTime != null">
        first_authorized_time = #{firstAuthorizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="macaupassId != null">
        macaupass_id = #{macaupassId,jdbcType=INTEGER},
      </if>
      <if test="creditCardPay != null">
        credit_card_pay = #{creditCardPay,jdbcType=INTEGER},
      </if>
      <if test="payByCreditCard != null">
        pay_by_credit_card = #{payByCreditCard,jdbcType=INTEGER},
      </if>
      <if test="bing != null">
        bing = #{bing,jdbcType=TINYINT},
      </if>
      <if test="newPhone != null">
        new_phone = #{newPhone,jdbcType=VARCHAR},
      </if>
      <if test="bingTime != null">
        bing_time = #{bingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=VARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.FookPlatformUserinfo">
    update fook_platform_userinfo
    set wechat_userid = #{wechatUserid,jdbcType=INTEGER},
      areaid = #{areaid,jdbcType=INTEGER},
      registered_phone = #{registeredPhone,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      nick_name = #{nickName,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=INTEGER},
      avatar = #{avatar,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=INTEGER},
      city = #{city,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=TIMESTAMP},
      growth_value = #{growthValue,jdbcType=INTEGER},
      score = #{score,jdbcType=DECIMAL},
      comment_count = #{commentCount,jdbcType=INTEGER},
      question = #{question,jdbcType=VARCHAR},
      answer = #{answer,jdbcType=VARCHAR},
      question2 = #{question2,jdbcType=VARCHAR},
      answer2 = #{answer2,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      `map` = #{map,jdbcType=INTEGER},
      first_authorized_time = #{firstAuthorizedTime,jdbcType=TIMESTAMP},
      macaupass_id = #{macaupassId,jdbcType=INTEGER},
      credit_card_pay = #{creditCardPay,jdbcType=INTEGER},
      pay_by_credit_card = #{payByCreditCard,jdbcType=INTEGER},
      bing = #{bing,jdbcType=TINYINT},
      new_phone = #{newPhone,jdbcType=VARCHAR},
      bing_time = #{bingTime,jdbcType=TIMESTAMP},
      member_id = #{memberId,jdbcType=INTEGER},
      last_login_time = #{lastLoginTime,jdbcType=VARBINARY}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformUserinfo">
    update fook_platform_userinfo
    set wechat_userid = #{wechatUserid,jdbcType=INTEGER},
      areaid = #{areaid,jdbcType=INTEGER},
      registered_phone = #{registeredPhone,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      nick_name = #{nickName,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=INTEGER},
      avatar = #{avatar,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=INTEGER},
      city = #{city,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=TIMESTAMP},
      growth_value = #{growthValue,jdbcType=INTEGER},
      score = #{score,jdbcType=DECIMAL},
      comment_count = #{commentCount,jdbcType=INTEGER},
      question = #{question,jdbcType=VARCHAR},
      answer = #{answer,jdbcType=VARCHAR},
      question2 = #{question2,jdbcType=VARCHAR},
      answer2 = #{answer2,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      `map` = #{map,jdbcType=INTEGER},
      first_authorized_time = #{firstAuthorizedTime,jdbcType=TIMESTAMP},
      macaupass_id = #{macaupassId,jdbcType=INTEGER},
      credit_card_pay = #{creditCardPay,jdbcType=INTEGER},
      pay_by_credit_card = #{payByCreditCard,jdbcType=INTEGER},
      bing = #{bing,jdbcType=TINYINT},
      new_phone = #{newPhone,jdbcType=VARCHAR},
      bing_time = #{bingTime,jdbcType=TIMESTAMP},
      member_id = #{memberId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>