<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookDayDiscountDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookDayDiscount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="gain_id" jdbcType="INTEGER" property="gainId" />
    <result column="discount" jdbcType="VARCHAR" property="discount" />
    <result column="discount_en" jdbcType="VARCHAR" property="discountEn" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, gain_id, discount, discount_en, `status`, sort, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_day_discount
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_day_discount
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookDayDiscount">
    insert into fook_day_discount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gainId != null">
        gain_id,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="discountEn != null">
        discount_en,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="gainId != null">
        #{gainId,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=VARCHAR},
      </if>
      <if test="discountEn != null">
        #{discountEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookDayDiscount">
    update fook_day_discount
    <set>
      <if test="gainId != null">
        gain_id = #{gainId,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=VARCHAR},
      </if>
      <if test="discountEn != null">
        discount_en = #{discountEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookDayDiscount">
    update fook_day_discount
    set gain_id = #{gainId,jdbcType=INTEGER},
      discount = #{discount,jdbcType=VARCHAR},
      discount_en = #{discountEn,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      sort = #{sort,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>