<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductcategoryDao">
  <select id="selectBusinessProductcategorys" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fook_business_productcategory
    where mcoin_platform = #{mcoinPlatform,jdbcType=INTEGER}
  </select>
</mapper>