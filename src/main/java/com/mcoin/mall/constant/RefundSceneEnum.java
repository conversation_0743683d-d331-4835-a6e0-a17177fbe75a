package com.mcoin.mall.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款场景枚举
 */
@Getter
@AllArgsConstructor
public enum RefundSceneEnum {
    
    /**
     * 无效订单
     */
    INVALID_ORDER("INVALID_ORDER", "无效订单"),
    
    /**
     * 用户取消
     */
    APPROVAL_REFUND("APPROVAL_REFUND", "审批退款"),

    /**
     * 自动审批退款
     */
    AUTO_APPROVAL_REFUND("AUTO_APPROVAL_REFUND", "自动审批退款"),
    
    /**
     * 其他
     */
    OTHER("OTHER", "其他");
    
    /**
     * 场景编码
     */
    private final String code;
    
    /**
     * 场景描述
     */
    private final String desc;
    
    /**
     * 根据编码获取枚举
     */
    public static RefundSceneEnum getByCode(String code) {
        for (RefundSceneEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return OTHER;
    }
} 