<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingDataClassDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookTradingDataClass">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="trading_id" jdbcType="INTEGER" property="tradingId" />
    <result column="trading_data_id" jdbcType="INTEGER" property="tradingDataId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, trading_id, trading_data_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_trading_data_class
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_trading_data_class
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookTradingDataClass">
    insert into fook_trading_data_class
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tradingId != null">
        trading_id,
      </if>
      <if test="tradingDataId != null">
        trading_data_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="tradingId != null">
        #{tradingId,jdbcType=INTEGER},
      </if>
      <if test="tradingDataId != null">
        #{tradingDataId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookTradingDataClass">
    update fook_trading_data_class
    <set>
      <if test="tradingId != null">
        trading_id = #{tradingId,jdbcType=INTEGER},
      </if>
      <if test="tradingDataId != null">
        trading_data_id = #{tradingDataId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookTradingDataClass">
    update fook_trading_data_class
    set trading_id = #{tradingId,jdbcType=INTEGER},
      trading_data_id = #{tradingDataId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>