package com.mcoin.mall.service.business.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.model.AsiaMilesUserInfoListResponse;
import com.mcoin.mall.service.business.AsiaMilesService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.mcoin.mall.service.common.CacheService.CACHE_ASIA_MILES_USERS;

@Service
public class AsiaMilesServiceImpl implements AsiaMilesService {

    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Resource
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Resource
    private ContextHolder contextHolder;

    @Cacheable(value = CACHE_ASIA_MILES_USERS, key = "@cacheService.getUserId()",
            unless = "#result == null || #result.getSnatchList().isEmpty()")
    @Override
    public AsiaMilesUserInfoListResponse getAsiaMilesList() {
        Integer userId = contextHolder.getAuthUserInfo().getUserId();
        AsiaMilesUserInfoListResponse result = new AsiaMilesUserInfoListResponse();
        String startTime = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -15), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(DateUtil.endOfDay(DateUtil.date()), "yyyy-MM-dd HH:mm:ss");
        List<Integer> orderIds = this.fookPlatformOrderDao.getOrderIdByQuery(startTime, endTime, userId, 2);
        List<AsiaMilesUserInfoListResponse.SnatchListItem> snatchList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orderIds)) {
            List<FookPlatformOrderinfo> orders = this.fookPlatformOrderinfoDao.getAsiaMilesInfo(orderIds);
            for (FookPlatformOrderinfo order : orders) {
                AsiaMilesUserInfoListResponse.SnatchListItem item = new AsiaMilesUserInfoListResponse.SnatchListItem();
                item.setId(order.getId());
                item.setMilesMember(Convert.toStr(order.getMilesMember()));
                item.setMilesFirst(order.getMilesFirst());
                item.setMilesName(order.getMilesName());
                snatchList.add(item);
            }
        }
        result.setSnatchList(snatchList);
        return result;
    }

}
