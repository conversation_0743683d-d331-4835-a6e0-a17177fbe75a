package com.mcoin.mall.service.job.utils;

import com.mcoin.mall.util.AESUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class AESUtilTest {

    private final String key = "SitZuhHSRgAYblyG"; // 16位密钥
    private final String originalText = "Hello, World!";
    private final String encryptedText = "Tyl/SzWVRHbwKa/eZdMDIw=="; // 预期的加密结果

    @Test
    public void testEncryptToBase64() {
        String encrypted = AESUtil.encryptToBase64(key, originalText);
        assertNotNull(encrypted);
        assertNotEquals(originalText, encrypted); // 加密后结果不应与原文相同
    }

    @Test
    public void testDecrypt() {
        String decrypted = AESUtil.decrypt(key, encryptedText);
        assertNotNull(decrypted);
        assertEquals("1", decrypted); // 解密后结果应与原文相同
    }

    @Test
    public void testEncryptToHex() {
        String hex = AESUtil.encryptToHex(key, originalText);
        assertNotNull(hex);
        assertFalse(hex.isEmpty()); // Hex 结果不应为空
    }

    @Test
    public void testDecryptFromHex() {
        String hex = AESUtil.encryptToHex(key, originalText);
        String decrypted = AESUtil.decryptFromHex(key, hex);
        assertNotNull(decrypted);
        assertEquals(originalText, decrypted); // 解密后结果应与原文相同
    }

    @Test
    public void testEncryptAndDecrypt() {
        String encrypted = AESUtil.encryptToBase64(key, originalText);
        String decrypted = AESUtil.decrypt(key, encrypted);
        assertEquals(originalText, decrypted); // 加密后再解密应还原为原文
    }

    @Test
    public void testInvalidKey() {
        String invalidKey = "shortkey"; // 不足16位的密钥
        Exception exception = assertThrows(RuntimeException.class, () -> {
            AESUtil.encryptToBase64(invalidKey, originalText);
        });
        assertNotNull(exception.getMessage());
    }
}
