package com.mcoin.mall.service.api.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Collections;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookMacaupassCode;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrdercodeLog;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookMacaupassCodeDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeLogDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.model.api.CheckvoucherResponse;
import com.mcoin.mall.util.McoinMall;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class OrderApiServiceImplTest{

    @Mock
    private FookBusinessDao fookBusinessDao;

    @Mock
    private FookBusinessProductDao fookBusinessProductDao;

    @Mock
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Mock
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;

    @Mock
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @InjectMocks
    private OrderApiServiceImpl orderApiService;

    @Mock
    private FookMacaupassCodeDao fookMacaupassCodeDao;

    @Mock
    private FookPlatformOrdercodeLogDao fookPlatformOrdercodeLogDao;

    @Mock
    private FookStoresDao fookStoresDao;


    @Test
    void testGetOrderDetailSingleCode() {
        String code = "test-code";
        FookPlatformOrdercode orderCode = new FookPlatformOrdercode();
        orderCode.setId(1);
        orderCode.setCode(code);
        orderCode.setOrderinfoId(1);
        orderCode.setShopid(1);
    
        FookPlatformOrderinfo orderInfo = new FookPlatformOrderinfo();
        orderInfo.setProdcutid(1);
    
        FookStores fookStores = new FookStores();
        fookStores.setId(1);
    
        FookPlatformOrdercodeLog codeLog = new FookPlatformOrdercodeLog();
        codeLog.setOrdercodeid(1);
    
        FookMacaupassCode mc = new FookMacaupassCode();
    
        when(fookPlatformOrdercodeDao.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.singletonList(orderCode));
        when(fookPlatformOrderinfoDao.selectByPrimaryKey(orderCode.getOrderinfoId()))
                .thenReturn(orderInfo);
        when(fookStoresDao.getApplyStores(McoinMall.LANG_EN, orderInfo.getProdcutid(), 1))
                .thenReturn(Collections.singletonList(fookStores));
        when(fookPlatformOrdercodeLogDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(codeLog);
        when(fookMacaupassCodeDao.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.singletonList(mc));
    
        CheckvoucherResponse response = orderApiService.getOrderDetail(code);
    
        assertEquals(orderCode.getCode(), response.getVoucherCode());
        assertEquals(fookStores.getId(), response.getStoreId());
        assertEquals(mc.getMacaupassBusinesscode(), response.getMerchantCode());
    }

    @Test
    void testGetOrderDetailMultipleCodes() {
        String code = "test-code";
        FookPlatformOrdercode orderCode1 = new FookPlatformOrdercode();
        orderCode1.setId(1);
        orderCode1.setCode(code);
        orderCode1.setOrderinfoId(1);
        orderCode1.setStatus(1);
    
        FookPlatformOrdercode orderCode2 = new FookPlatformOrdercode();
        orderCode2.setId(2);
        orderCode2.setCode(code);
        orderCode2.setOrderinfoId(1);
        orderCode2.setStatus(2);
    
        when(fookPlatformOrdercodeDao.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Lists.newArrayList(orderCode1, orderCode2));
    
        CheckvoucherResponse response = orderApiService.getOrderDetail(code);
    
        assertEquals(orderCode1.getCode(), response.getVoucherCode());
    }

    @Test
    void testGetOrderDetailNoCode() {
        String code = "non-existent-code";
        when(fookPlatformOrdercodeDao.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
    
        CheckvoucherResponse response = orderApiService.getOrderDetail(code);
    
        assertNull( response.getVoucherCode());
    }

    @Test
    void testGetPassCheckTrue() {
        String code = "test-code";
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setId(1);
        ordercode.setCode(code);
        ordercode.setOrderid(1);
        ordercode.setStatus(1);
    
        FookPlatformOrder order = new FookPlatformOrder();
        order.setSellerid(1);
    
        FookBusiness fookBusiness = new FookBusiness();
        fookBusiness.setSystemType(2);
    
        FookPlatformOrderinfo orderInfo = new FookPlatformOrderinfo();
        orderInfo.setProdcutid(1);
    
        FookBusinessProduct businessProduct = new FookBusinessProduct();
        businessProduct.setCouponType(0);
    
        when(fookPlatformOrdercodeDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(ordercode.getOrderid()))
                .thenReturn(order);
        when(fookBusinessDao.selectByPrimaryKey(order.getSellerid()))
                .thenReturn(fookBusiness);
        when(fookPlatformOrderinfoDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(orderInfo);
        when(fookBusinessProductDao.selectByPrimaryKey(orderInfo.getProdcutid()))
                .thenReturn(businessProduct);
    
        boolean result = orderApiService.getPassCheck(code);
    
        assertTrue(result);
    }

    @Test
    void testGetPassCheckFalseSystemTypeNot2() {
        String code = "test-code";
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setId(1);
        ordercode.setCode(code);
        ordercode.setOrderid(1);
        ordercode.setStatus(1);
    
        FookPlatformOrder order = new FookPlatformOrder();
        order.setSellerid(1);
    
        FookBusiness fookBusiness = new FookBusiness();
        fookBusiness.setSystemType(1); // SystemType is not 2
    
        when(fookPlatformOrdercodeDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(ordercode.getOrderid()))
                .thenReturn(order);
        when(fookBusinessDao.selectByPrimaryKey(order.getSellerid()))
                .thenReturn(fookBusiness);
        FookBusinessProduct product = new FookBusinessProduct();
        product.setCouponType(0);
        when(fookBusinessProductDao.selectByPrimaryKey(any())).thenReturn(product);
        when(this.fookPlatformOrderinfoDao.selectOne(any())).thenReturn(new FookPlatformOrderinfo());
    
        boolean result = orderApiService.getPassCheck(code);
    
        assertFalse(result);
    }

    @Test
    void testGetPassCheckFalseCouponTypeNot0() {
        String code = "test-code";
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setId(1);
        ordercode.setCode(code);
        ordercode.setOrderid(1);
        ordercode.setStatus(1);
    
        FookPlatformOrder order = new FookPlatformOrder();
        order.setSellerid(1);
    
        FookBusiness fookBusiness = new FookBusiness();
        fookBusiness.setSystemType(2);
    
        FookPlatformOrderinfo orderInfo = new FookPlatformOrderinfo();
        orderInfo.setProdcutid(1);
    
        FookBusinessProduct businessProduct = new FookBusinessProduct();
        businessProduct.setCouponType(1); // CouponType is not 0
    
        when(fookPlatformOrdercodeDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(ordercode.getOrderid()))
                .thenReturn(order);
        when(fookBusinessDao.selectByPrimaryKey(order.getSellerid()))
                .thenReturn(fookBusiness);
        when(fookPlatformOrderinfoDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(orderInfo);
        when(fookBusinessProductDao.selectByPrimaryKey(orderInfo.getProdcutid()))
                .thenReturn(businessProduct);
    
        boolean result = orderApiService.getPassCheck(code);
    
        assertFalse(result);
    }

    @Test
    void testGetPassCheckFalseOrdercodeNotFound() {
        String code = "test-code";
    
        when(fookPlatformOrdercodeDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);
    
        boolean result = orderApiService.getPassCheck(code);
    
        assertFalse(result);
    }

}