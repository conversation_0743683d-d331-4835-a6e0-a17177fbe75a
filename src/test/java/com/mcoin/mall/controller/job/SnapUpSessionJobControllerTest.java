package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.client.model.MiniUserAddressEditHttpRequest;
import com.mcoin.mall.client.model.mp.management.ActivityTimeToGoodsBuyTimeRequest;
import com.mcoin.mall.client.model.mp.management.MpMcoinMallResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.CheckSessionTimeAndProductBuyTimeJobRequest;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.amqp.core.Message;

import javax.annotation.Resource;

import java.util.Date;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * 集成测试 SnapUpSessionJobController
 * 使用真实服务和数据库，测试抢购场次相关功能
 */
class SnapUpSessionJobControllerTest extends BaseUnitTest {

    @Resource
    private SnapUpSessionJobController snapUpSessionJobController;

    @BeforeEach
    public void setUp() {
        // Mock locale
        Mockito.when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        Mockito.when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
        MpMcoinMallResponse response = new MpMcoinMallResponse();
        response.setCode(1);
        Mockito.when(managementClient.activityTimeToGoodsBuyTime(any(ActivityTimeToGoodsBuyTimeRequest.class)))
                .thenReturn(response);
    }

    @Test
    void testCheckSessionTimeAndProductBuyTime() {
        // 创建请求对象
        CheckSessionTimeAndProductBuyTimeJobRequest request = new CheckSessionTimeAndProductBuyTimeJobRequest();
        request.setCurrentTime(new Date());
        // 直接调用控制器方法
        Response<String> response = snapUpSessionJobController.checkSessionTimeAndProductBuyTime(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 