<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrderinfoDao">
  <select id="getOrderInfoByOrderId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from fook_platform_orderinfo
    WHERE orderid in
    <foreach item="orderId" open="(" close=")" collection="orderIds" separator=",">
      #{orderId}
    </foreach>
  </select>
  <select id="getAsiaMilesInfo" resultMap="BaseResultMap">
    SELECT
    id,miles_first,miles_name,miles_member
    from fook_platform_orderinfo
    WHERE orderid in
    <foreach item="orderId" collection="orderIds" open="(" close=")" separator=",">
      #{orderId}
    </foreach>
    and type = 5
    and miles_member is not null
    group by id,miles_first,miles_name,miles_member
    order by id desc
    limit 2
  </select>
  <select id="getOneOrderInfoByOrderId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from fook_platform_orderinfo
    WHERE orderid = #{orderId,jdbcType=INTEGER}
    limit 1
  </select>
</mapper>