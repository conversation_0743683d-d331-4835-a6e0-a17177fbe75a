<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportProfitDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportProfit">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="momecoins" jdbcType="DECIMAL" property="momecoins" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="final_amount" jdbcType="DECIMAL" property="finalAmount" />
    <result column="wechat_amount" jdbcType="DECIMAL" property="wechatAmount" />
    <result column="wechat_fee" jdbcType="DECIMAL" property="wechatFee" />
    <result column="alipay" jdbcType="DECIMAL" property="alipay" />
    <result column="alipay_fee" jdbcType="DECIMAL" property="alipayFee" />
    <result column="mppay_amount" jdbcType="DECIMAL" property="mppayAmount" />
    <result column="mppay_fee" jdbcType="DECIMAL" property="mppayFee" />
    <result column="cybersource_amount" jdbcType="DECIMAL" property="cybersourceAmount" />
    <result column="cybersource_fee" jdbcType="DECIMAL" property="cybersourceFee" />
    <result column="bank_amount" jdbcType="DECIMAL" property="bankAmount" />
    <result column="settlement_amount_total" jdbcType="DECIMAL" property="settlementAmountTotal" />
    <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="wait_settlement_amount" jdbcType="DECIMAL" property="waitSettlementAmount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="settlement_commission" jdbcType="DECIMAL" property="settlementCommission" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="report_profit_id" jdbcType="INTEGER" property="reportProfitId" />
    <result column="statistics_report" jdbcType="VARCHAR" property="statisticsReport" />
  </resultMap>
  <sql id="Base_Column_List">
    id, amount, momecoins, mpayintegral, final_amount, wechat_amount, wechat_fee, alipay, 
    alipay_fee, mppay_amount, mppay_fee, cybersource_amount, cybersource_fee, bank_amount, 
    settlement_amount_total, settlement_amount, wait_settlement_amount, commission, settlement_commission, 
    settlement_time, start_time, end_time, report_profit_id, statistics_report
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_profit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_profit
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportProfit">
    insert into fook_report_profit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="momecoins != null">
        momecoins,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="finalAmount != null">
        final_amount,
      </if>
      <if test="wechatAmount != null">
        wechat_amount,
      </if>
      <if test="wechatFee != null">
        wechat_fee,
      </if>
      <if test="alipay != null">
        alipay,
      </if>
      <if test="alipayFee != null">
        alipay_fee,
      </if>
      <if test="mppayAmount != null">
        mppay_amount,
      </if>
      <if test="mppayFee != null">
        mppay_fee,
      </if>
      <if test="cybersourceAmount != null">
        cybersource_amount,
      </if>
      <if test="cybersourceFee != null">
        cybersource_fee,
      </if>
      <if test="bankAmount != null">
        bank_amount,
      </if>
      <if test="settlementAmountTotal != null">
        settlement_amount_total,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="waitSettlementAmount != null">
        wait_settlement_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="settlementCommission != null">
        settlement_commission,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="reportProfitId != null">
        report_profit_id,
      </if>
      <if test="statisticsReport != null">
        statistics_report,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="finalAmount != null">
        #{finalAmount,jdbcType=DECIMAL},
      </if>
      <if test="wechatAmount != null">
        #{wechatAmount,jdbcType=DECIMAL},
      </if>
      <if test="wechatFee != null">
        #{wechatFee,jdbcType=DECIMAL},
      </if>
      <if test="alipay != null">
        #{alipay,jdbcType=DECIMAL},
      </if>
      <if test="alipayFee != null">
        #{alipayFee,jdbcType=DECIMAL},
      </if>
      <if test="mppayAmount != null">
        #{mppayAmount,jdbcType=DECIMAL},
      </if>
      <if test="mppayFee != null">
        #{mppayFee,jdbcType=DECIMAL},
      </if>
      <if test="cybersourceAmount != null">
        #{cybersourceAmount,jdbcType=DECIMAL},
      </if>
      <if test="cybersourceFee != null">
        #{cybersourceFee,jdbcType=DECIMAL},
      </if>
      <if test="bankAmount != null">
        #{bankAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmountTotal != null">
        #{settlementAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="waitSettlementAmount != null">
        #{waitSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="settlementCommission != null">
        #{settlementCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportProfitId != null">
        #{reportProfitId,jdbcType=INTEGER},
      </if>
      <if test="statisticsReport != null">
        #{statisticsReport,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportProfit">
    update fook_report_profit
    <set>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        momecoins = #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="finalAmount != null">
        final_amount = #{finalAmount,jdbcType=DECIMAL},
      </if>
      <if test="wechatAmount != null">
        wechat_amount = #{wechatAmount,jdbcType=DECIMAL},
      </if>
      <if test="wechatFee != null">
        wechat_fee = #{wechatFee,jdbcType=DECIMAL},
      </if>
      <if test="alipay != null">
        alipay = #{alipay,jdbcType=DECIMAL},
      </if>
      <if test="alipayFee != null">
        alipay_fee = #{alipayFee,jdbcType=DECIMAL},
      </if>
      <if test="mppayAmount != null">
        mppay_amount = #{mppayAmount,jdbcType=DECIMAL},
      </if>
      <if test="mppayFee != null">
        mppay_fee = #{mppayFee,jdbcType=DECIMAL},
      </if>
      <if test="cybersourceAmount != null">
        cybersource_amount = #{cybersourceAmount,jdbcType=DECIMAL},
      </if>
      <if test="cybersourceFee != null">
        cybersource_fee = #{cybersourceFee,jdbcType=DECIMAL},
      </if>
      <if test="bankAmount != null">
        bank_amount = #{bankAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmountTotal != null">
        settlement_amount_total = #{settlementAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="waitSettlementAmount != null">
        wait_settlement_amount = #{waitSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="settlementCommission != null">
        settlement_commission = #{settlementCommission,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportProfitId != null">
        report_profit_id = #{reportProfitId,jdbcType=INTEGER},
      </if>
      <if test="statisticsReport != null">
        statistics_report = #{statisticsReport,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportProfit">
    update fook_report_profit
    set amount = #{amount,jdbcType=DECIMAL},
      momecoins = #{momecoins,jdbcType=DECIMAL},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      final_amount = #{finalAmount,jdbcType=DECIMAL},
      wechat_amount = #{wechatAmount,jdbcType=DECIMAL},
      wechat_fee = #{wechatFee,jdbcType=DECIMAL},
      alipay = #{alipay,jdbcType=DECIMAL},
      alipay_fee = #{alipayFee,jdbcType=DECIMAL},
      mppay_amount = #{mppayAmount,jdbcType=DECIMAL},
      mppay_fee = #{mppayFee,jdbcType=DECIMAL},
      cybersource_amount = #{cybersourceAmount,jdbcType=DECIMAL},
      cybersource_fee = #{cybersourceFee,jdbcType=DECIMAL},
      bank_amount = #{bankAmount,jdbcType=DECIMAL},
      settlement_amount_total = #{settlementAmountTotal,jdbcType=DECIMAL},
      settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      wait_settlement_amount = #{waitSettlementAmount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      settlement_commission = #{settlementCommission,jdbcType=DECIMAL},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      report_profit_id = #{reportProfitId,jdbcType=INTEGER},
      statistics_report = #{statisticsReport,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>