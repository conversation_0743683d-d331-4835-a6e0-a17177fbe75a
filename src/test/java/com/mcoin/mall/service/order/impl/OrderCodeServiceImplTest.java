package com.mcoin.mall.service.order.impl;

import com.mcoin.mall.constant.BusinessProductIsExportEnum;
import com.mcoin.mall.constant.BusinessProductTypeEnum;
import com.mcoin.mall.constant.CouponSynStatus;
import com.mcoin.mall.constant.PaymentType;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.mq.model.CouponSyncMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.mockito.InjectMocks;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.service.chennel.MPayChannelService;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import java.util.Locale;
import static org.mockito.ArgumentMatchers.any;
import com.mcoin.mall.component.ContextHolder;
import org.junit.jupiter.api.BeforeEach;
import java.math.BigDecimal;
import com.mcoin.mall.bean.*;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class OrderCodeServiceImplTest{

    @Mock
    private MessageSource messageSource;

    @Mock
    private FookExternalVcodeDao fookExternalVcodeDao;

    @Mock
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Mock
    private FookMacaupassUserDao fookMacaupassUserDao;

    private FookPlatformOrder order;

    @Mock
    private MPayChannelService mPayChannelService;

    @Mock
    private ContextHolder contextHolder;

    @Mock
    private FookBusinessProductDao fookBusinessProductDao;

    @InjectMocks
    private OrderCodeServiceImpl orderCodeService;

    private FookPlatformOrderinfo orderinfo;

    @BeforeEach
    public void setUp() {
        order = new FookPlatformOrder();
        order.setId(1);
        order.setUserid(1001);
        order.setOrderAmount(BigDecimal.valueOf(1000));
        order.setTotalAmount(BigDecimal.valueOf(1000));
        order.setScore(BigDecimal.valueOf(100));
        order.setMpayintegral(100);
        order.setMemberintegral(BigDecimal.valueOf(100));
        order.setIsVoucher(1);
        order.setPaymentType(PaymentType.MPAY.getType());
    
        orderinfo = new FookPlatformOrderinfo();
        orderinfo.setId(1001);
        orderinfo.setProdcutid(1);
        orderinfo.setNumber(10);
        orderinfo.setMilesMember(1001L);
    }

    @Test
    public void testCreateOrderCode_Success() {

        FookBusinessProduct product = new FookBusinessProduct();
        product.setId(1);
        product.setFeeRate(new BigDecimal("1"));
        product.setType(BusinessProductTypeEnum.COIN_CASH_COUPON.getTypeId());
        product.setIsexport(BusinessProductIsExportEnum.MANUAL_IMPORT.getType());

        FookExternalVcode code = new FookExternalVcode();
        code.setVcode("1234567890");


        when(fookMacaupassUserDao.updateByPrimaryKeySelective(any(FookMacaupassUser.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(fookBusinessProductDao.selectByPrimaryKey(any(Integer.class))).thenReturn(product);
        when(fookPlatformOrdercodeDao.countByOrderInfoId(any(Integer.class))).thenReturn(0);
        when(fookPlatformOrdercodeDao.insert(any(FookPlatformOrdercode.class))).thenReturn(1);
        when(fookBusinessProductDao.updateIncrSales(any(Integer.class), any(Integer.class))).thenReturn(1);
        when(fookExternalVcodeDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(code);
        when(fookExternalVcodeDao.update(any(FookExternalVcode.class), any(LambdaQueryWrapper.class))).thenReturn(1);
        when(mPayChannelService.insertCouponSync(eq(1), eq(null),
                eq(CouponSyncMessage.Operation.OrderCode), eq(CouponSynStatus.FAILED), eq(""))).thenReturn(1);

        // Execute the method
        orderCodeService.createOrderCode(order, orderinfo);
    
        // Verify the interactions and assertions
        verify(fookMacaupassUserDao, times(1)).updateByPrimaryKeySelective(any(FookMacaupassUser.class));
        verify(fookPlatformOrdercodeDao, times(11)).selectOne(any(LambdaQueryWrapper.class));
        verify(fookBusinessProductDao, times(1)).selectByPrimaryKey(any(Integer.class));
        verify(fookPlatformOrdercodeDao, times(10)).countByOrderInfoId(any(Integer.class));
        verify(fookPlatformOrdercodeDao, times(10)).insert(any(FookPlatformOrdercode.class));
        verify(fookBusinessProductDao, times(10)).updateIncrSales(any(Integer.class), any(Integer.class));
        verify(fookExternalVcodeDao, times(10)).update(any(FookExternalVcode.class), any(LambdaQueryWrapper.class));
        verify(mPayChannelService, times(10)).insertCouponSync(eq(1), eq(null),
                eq(CouponSyncMessage.Operation.OrderCode), eq(CouponSynStatus.FAILED), eq(""));
    }

    @Test
    public void testCreateOrderCode_ThrowsBusinessException_WhenOrderCodeAlreadyExists() {
        when(fookPlatformOrdercodeDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(new FookPlatformOrdercode());
    
        Locale locale = Locale.getDefault();
        when(contextHolder.getLocale()).thenReturn(locale);
        when(messageSource.getMessage(eq("message.orderRepository.verification_code_data"), any(), eq(locale))).thenReturn("Verification code data already exists");
    
        assertThrows(BusinessException.class, () -> orderCodeService.createOrderCode(order, orderinfo));
    
        verify(contextHolder, times(1)).getLocale();
        verify(messageSource, times(1)).getMessage(eq("message.orderRepository.verification_code_data"), any(), eq(locale));
    }

    @Test
    public void testCreateOrderCode_ThrowsBusinessException_WhenOrderCodeGenerationFails() {

        FookBusinessProduct product = new FookBusinessProduct();
        product.setId(1);
        product.setFeeRate(new BigDecimal("1"));
        product.setType(BusinessProductTypeEnum.COIN_CASH_COUPON.getTypeId());
        product.setIsexport(BusinessProductIsExportEnum.MANUAL_IMPORT.getType());

        when(fookPlatformOrdercodeDao.countByOrderInfoId(any(Integer.class))).thenReturn(0);
        when(fookBusinessProductDao.selectByPrimaryKey(any())).thenReturn(product);
    
        assertThrows(BusinessException.class, () -> orderCodeService.createOrderCode(order, orderinfo));
    
        verify(fookPlatformOrdercodeDao, times(1)).countByOrderInfoId(any(Integer.class));
    }

}