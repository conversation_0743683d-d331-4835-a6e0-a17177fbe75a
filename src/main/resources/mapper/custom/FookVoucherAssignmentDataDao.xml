<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookVoucherAssignmentDataDao">

  <select id="selectByVoucherId" resultType="com.mcoin.mall.bean.FookVoucherAssignmentData">
    select
    <include refid="Base_Column_List" />
    from fook_voucher_assignment_data
    where voucher_id = #{voucherId,jdbcType=INTEGER}
    and status = '1'
    limit #{batchSize,jdbcType=INTEGER}
  </select>

  <select id="sumSuccessByVoucherId" resultType="integer">
    select sum(number)
    from fook_voucher_assignment_data
    where voucher_id = #{voucherId,jdbcType=INTEGER}
    and status = '2'
  </select>
</mapper>