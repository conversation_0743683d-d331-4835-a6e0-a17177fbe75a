<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductTranslationsDao">
  <select id="getTranslationsByIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from fook_business_product_translations
    where business_product_id in
    <foreach item="item" collection="businessProductIds" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and locale = #{language,jdbcType=VARCHAR}
  </select>

  <select id="getTranslationsById" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from fook_business_product_translations
    where business_product_id = #{businessProductId,jdbcType=INTEGER}
    and locale = #{language,jdbcType=VARCHAR}
  </select>
</mapper>