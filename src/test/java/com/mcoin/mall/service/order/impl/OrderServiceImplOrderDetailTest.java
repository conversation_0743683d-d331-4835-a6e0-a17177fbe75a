package com.mcoin.mall.service.order.impl;

import com.mcoin.mall.bean.*;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.model.OrderDetailRequest;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.chennel.MPayChannelService;
import com.mcoin.mall.service.common.AtomicSeqService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;

import java.util.Collections;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class OrderServiceImplOrderDetailTest {
    private MockedStatic<ConfigUtils> mockedStatic;

    @Mock
    FookPlatformOrdercodeDao fookPlatformOrdercodeDao;
    @Mock
    FookPlatformOrderDao fookPlatformOrderDao;
    @Mock
    FookStoresDao fookStoresDao;
    @Mock
    FookBusinessProductDao fookBusinessProductDao;
    @Mock
    FookBusinessDao fookBusinessDao;
    @Mock
    FookExternalVcodeDao fookExternalVcodeDao;
    @Mock
    FookReportOrdercodeSettlementDao fookReportOrdercodeSettlementDao;
    @Mock
    FookAFeeTypeDao fookAFeeTypeDao;
    @Mock
    FookPlatformOrdercodeLogDao fookPlatformOrdercodeLogDao;
    @Mock
    FookPlatformOrderrefundDao fookPlatformOrderrefundDao;
    @Mock
    FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Mock
    SettingsDao settingsDao;
    @Mock
    FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;
    @Mock
    FookBusinessStoreProductDao fookBusinessStoreProductDao;
    @Mock
    FookPlatformOrderrefundRecordDao fookPlatformOrderrefundRecordDao;
    @Mock
    MessageSource messageSource;
    @Mock
    ContextHolder contextHolder;
    @Mock
    AtomicSeqService atomicSeqService;
    @Mock
    FookProductSnappingRecordDao fookProductSnappingRecordDao;
    @Mock
    FookBusinessProductstockDao fookBusinessProductstockDao;
    @Mock
    FookProductStockLogDao fookProductStockLogDao;
    @Mock
    FookPayLogDao fookPayLogDao;
    @Mock
    MPayChannelService mPayChannelService;
    @Mock
    ApplicationContext applicationContext;
    @Mock
    FookMiniOrderSettlementDao fookMiniOrderSettlementDao;
    @Mock
    RabbitTemplate refundTemplate;
    @InjectMocks
    OrderServiceImpl orderServiceImpl;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        mockedStatic = Mockito.mockStatic(ConfigUtils.class);
    }

    @AfterEach
    void cleanup() {
        if (mockedStatic != null) {
            mockedStatic.close();
        }
    }

    @Test
    void getOrderDetail_ShouldReturnExpectedCode() {
        // Given
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(anyInt())).thenReturn(JsonTestUtils.getObjectFromJson("json/fookPlatformOrdercodes.json", "singleCode", FookPlatformOrdercode.class));
        when(fookPlatformOrdercodeDao.selectByOrderId(anyInt())).thenReturn(Collections.singletonList(JsonTestUtils.getObjectFromJson("json/fookPlatformOrdercodes.json", "singleCode", FookPlatformOrdercode.class)));
        when(fookPlatformOrderDao.selectByPrimaryKey(anyInt())).thenReturn(JsonTestUtils.getObjectFromJson("json/fookPlatformOrders.json", "singleOrder", FookPlatformOrder.class));
        when(fookStoresDao.getApplyStores(anyString(), anyInt(), anyInt())).thenReturn(Collections.singletonList(JsonTestUtils.getObjectFromJson("json/stores.json", "singleStores", FookStores.class)));
        when(fookStoresDao.selectByPrimaryKeyWithTranslations(anyString(), anyInt())).thenReturn(JsonTestUtils.getObjectFromJson("json/stores.json", "singleStores", FookStores.class));
        when(fookBusinessProductDao.selectByPrimaryKeyWithTranslations(anyInt(), anyString())).thenReturn(JsonTestUtils.getObjectFromJson("json/businessProduct.json", "singleBp", FookBusinessProduct.class));
        when(fookBusinessDao.selectByPrimaryKey(anyInt())).thenReturn(JsonTestUtils.getObjectFromJson("json/fookBusiness.json", "singleBusiness", FookBusiness.class));
        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(JsonTestUtils.getObjectFromJson("json/fookPlatformOrderrefunds.json", "singleRefund", FookPlatformOrderrefund.class));
        when(fookPlatformOrderinfoDao.selectByPrimaryKey(anyInt())).thenReturn(JsonTestUtils.getObjectFromJson("json/orderinfo.json", "singleOrderInfo", FookPlatformOrderinfo.class));
        when(settingsDao.getValue(anyString())).thenReturn("1");
        when(fookBusinessProductTranslationsDao.getTranslationsById(anyInt(), anyString())).thenReturn(JsonTestUtils.getObjectFromJson("json/businessProductTranslations.json", "businessProductTl", FookBusinessProductTranslations.class));
        when(messageSource.getMessage(anyString(), any(Object[].class), any(Locale.class))).thenReturn("getMessageResponse");
        when(contextHolder.getLocale()).thenReturn(new Locale("language", "country", "variant"));
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(0);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(applicationContext.getMessage(anyString(), any(Object[].class), any(Locale.class))).thenReturn("getMessageResponse");
        mockedStatic.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("http://127.0.0.1/");

        OrderDetailRequest request = new OrderDetailRequest();
        request.setCodeid(1);

        // When
        String result = orderServiceImpl.getOrderDetail(request).getCode();

        // Then
        assertEquals("code_c3381e1a9bae", result);
    }
} 