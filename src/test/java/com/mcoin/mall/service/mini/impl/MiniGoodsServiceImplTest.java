package com.mcoin.mall.service.mini.impl;

import cn.hutool.core.date.DateUtil;
import com.mcoin.mall.client.MiniGoodsClient;
import com.mcoin.mall.client.MpMcoinMallManagementClient;
import com.mcoin.mall.client.model.MiniGoodsHttpRequest;
import com.mcoin.mall.component.ExplicitTransaction;
import com.mcoin.mall.component.ThrowableRunnable;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.job.SyncProductListRequest;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.SentinelUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

class MiniGoodsServiceImplTest {

    private MockedStatic<ConfigUtils> configUtilsMockedStatic;
    private MockedStatic<SentinelUtils> sentinelUtilsMockedStatic;

    @Mock
    FookBusinessProductDao fookBusinessProductDao;

    @Mock
    MiniGoodsClient miniGoodsClient;
    
    @Mock
    ExplicitTransaction batchTransaction;
    
    @Mock
    MpMcoinMallManagementClient mpMcoinMallManagementClient;

    @InjectMocks
    MiniGoodsServiceImpl miniGoodsServiceImpl;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        configUtilsMockedStatic = Mockito.mockStatic(ConfigUtils.class);
        sentinelUtilsMockedStatic = Mockito.mockStatic(SentinelUtils.class);
        
        // Mock SentinelUtils to return the callable result directly without throwing exceptions
        sentinelUtilsMockedStatic.when(() -> SentinelUtils.handleBlockException(any(Callable.class), any()))
            .thenAnswer(invocation -> {
                Callable<?> callable = invocation.getArgument(0);
                return callable.call();
            });
    }

    @AfterEach
    void cleanup() {
        if (configUtilsMockedStatic != null) {
            configUtilsMockedStatic.close();
        }
        if (sentinelUtilsMockedStatic != null) {
            sentinelUtilsMockedStatic.close();
        }
    }

    @Test
    void testSyncProducts() {
        // given
        when(fookBusinessProductDao.updateMiniGoodsByUuId(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(mpMcoinMallManagementClient.getMiniGoods(any(MiniGoodsHttpRequest.class))).thenReturn(
                "{\"code\":1,\"show\":0,\"msg\":\"获取成功\",\"data\":{\"goods_list\":[" +
                "{\"uuid\":\"MCAB000001\",\"goods_id\":20,\"name\":\"mCoin實物福利-1\",\"price\":\"15.00\",\"retail_price\":\"20.00\",\"stock\":219,\"sales\":109}," +
                "{\"uuid\":\"MCAB655193BD7B692\",\"goods_id\":40,\"name\":\"实物混合）-1113\",\"price\":\"20.00\",\"retail_price\":\"22.00\",\"stock\":195,\"sales\":138}," +
                "{\"uuid\":\"MCAB6551946859D80\",\"goods_id\":41,\"name\":\"实物纯积分-1113\",\"price\":\"30.00\",\"retail_price\":\"33.00\",\"stock\":74,\"sales\":33}," +
                "{\"uuid\":\"MCAB655194E58E37C\",\"goods_id\":42,\"name\":\"实物纯金额-1113\",\"price\":\"8.00\",\"retail_price\":\"10.00\",\"stock\":81,\"sales\":23}," +
                "{\"uuid\":\"MCAB65B1D5889A271\",\"goods_id\":54,\"name\":\"测试商品\",\"price\":\"1.00\",\"retail_price\":\"2.00\",\"stock\":100,\"sales\":0}" +
                "]}}"
        );
        configUtilsMockedStatic.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("1234567");
        
        // Ensure that transaction executes the provided runnable
        lenient().doAnswer(invocation -> {
            ThrowableRunnable<BusinessException> runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(batchTransaction).invokeWithNewTransaction(any(ThrowableRunnable.class));
        
        SyncProductListRequest request = new SyncProductListRequest();
        request.setStartTime(DateUtil.parse("2020-01-01 00:00:00"));
        request.setEndTime(DateUtil.parse("2024-06-11 23:59:59"));
        
        // when & then
        assertDoesNotThrow(() -> miniGoodsServiceImpl.syncProducts(request));
    }
} 