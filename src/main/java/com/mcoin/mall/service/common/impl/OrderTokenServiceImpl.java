package com.mcoin.mall.service.common.impl;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.mcoin.mall.constant.OrderTokenStatus;
import com.mcoin.mall.security.TokenManager;
import com.mcoin.mall.service.common.OrderTokenService;
import com.mcoin.mall.util.CacheNameUtils;
import com.mcoin.mall.util.ConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class OrderTokenServiceImpl implements OrderTokenService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TokenManager tokenManager;

    @Override
    public String createOrderToken(Integer userId) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        long ttlMinutes = Long.parseLong(ConfigUtils.getProperty("order_token_ttl_minutes", "120"));
        String token = tokenManager.createOrderJwtToken(uuid, ttlMinutes * 60);
        String key = CacheNameUtils.getCreateOrderToken(userId);
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(token, ttlMinutes, TimeUnit.MINUTES);
        return token;
    }

    @Override
    public boolean removeOrderToken(Integer userId) {
        String key = CacheNameUtils.getCreateOrderToken(userId);
        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.delete();
    }

    @Override
    public OrderTokenStatus checkOrderToken(Integer userId, String token) {
        if (StringUtils.isBlank(token)) {
            return OrderTokenStatus.EXPIRED;
        }
        DecodedJWT decodedJWT = tokenManager.getJwtToken(token);
        if (decodedJWT == null) {
            return OrderTokenStatus.EXPIRED;
        }
        String key = CacheNameUtils.getCreateOrderToken(userId);
        RBucket<String> bucket = redissonClient.getBucket(key);
        String orderToken = bucket.get();
        if (orderToken != null && orderToken.equals(token)){
            return OrderTokenStatus.VALID;
        }
        return OrderTokenStatus.INVALID;
    }
}
