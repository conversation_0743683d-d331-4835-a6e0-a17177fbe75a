<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresKeywordDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookStoresKeyword">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="english_name" jdbcType="VARCHAR" property="englishName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="if_index_show" jdbcType="BIT" property="ifIndexShow" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="src" jdbcType="VARCHAR" property="src" />
    <result column="src_type" jdbcType="INTEGER" property="srcType" />
    <result column="extend_info" jdbcType="VARCHAR" property="extendInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pid, `name`, english_name, sort, `enable`, icon, if_index_show, created_at, updated_at, 
    src, src_type,  extend_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_stores_keyword
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_stores_keyword
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookStoresKeyword">
    insert into fook_stores_keyword
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="englishName != null">
        english_name,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="ifIndexShow != null">
        if_index_show,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="src != null">
        src,
      </if>
      <if test="srcType != null">
        src_type,
      </if>
      <if test="extendInfo != null">
        extend_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="ifIndexShow != null">
        #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="src != null">
        #{src,jdbcType=VARCHAR},
      </if>
      <if test="srcType != null">
        #{srcType,jdbcType=INTEGER},
      </if>
      <if test="extendInfo != null">
        #{extendInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookStoresKeyword">
    update fook_stores_keyword
    <set>
      <if test="pid != null">
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        english_name = #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="ifIndexShow != null">
        if_index_show = #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="src != null">
        src = #{src,jdbcType=VARCHAR},
      </if>
      <if test="srcType != null">
        src_type = #{srcType,jdbcType=INTEGER},
      </if>
      <if test="extendInfo != null">
        extend_info = #{extendInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookStoresKeyword">
    update fook_stores_keyword
    set pid = #{pid,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      english_name = #{englishName,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      `enable` = #{enable,jdbcType=BIT},
      icon = #{icon,jdbcType=VARCHAR},
      if_index_show = #{ifIndexShow,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      src = #{src,jdbcType=VARCHAR},
      src_type = #{srcType,jdbcType=INTEGER},
      extend_info = #{extendInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>