package com.mcoin.mall.constant;

public enum SystemType {
    MCOIN(1, "mCoin"),
    MEMBER_CARD(2, "會員卡"),
    ASIA_MILES(3, "亞洲萬裡通商戶");

    private final int value;
    private final String description;

    SystemType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据枚举值的int类型获取对应的枚举对象
     *
     * @param value int类型的枚举值
     * @return 对应的枚举对象，如果不存在则返回null
     */
    public static SystemType getByValue(int value) {
        for (SystemType type : SystemType.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的枚举对象
     *
     * @param description 枚举的描述
     * @return 对应的枚举对象，如果不存在则返回null
     */
    public static SystemType getByDescription(String description) {
        for (SystemType type : SystemType.values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        return null;
    }
}
