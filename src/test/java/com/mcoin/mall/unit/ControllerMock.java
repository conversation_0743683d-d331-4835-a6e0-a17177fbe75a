package com.mcoin.mall.unit;

import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.util.ContextUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.context.MessageSource;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * This class provides common mocks for controller layer tests
 * Child classes don't need to mock the static class again
 */
public class ControllerMock {

    protected MockedStatic<ContextUtils> mockedStatic;

    @BeforeEach
    public void setup() {
        openMocks(this);
    }

    @AfterEach
    public void cleanup() {
        if (mockedStatic != null) {
            mockedStatic.close();
        }
    }

    protected void openMocks(Object object) {
        MockitoAnnotations.openMocks(object);
        ContextHolder contextHolder = new ContextHolder();
        MessageSource messageSource = Mockito.mock(MessageSource.class);
        contextHolder.setResponseMessage("");
        mockedStatic = Mockito.mockStatic(ContextUtils.class);
        mockedStatic.when(() -> ContextUtils.getBean(ContextHolder.class)).thenReturn(contextHolder);
        mockedStatic.when(() -> ContextUtils.getBean("messageSource", MessageSource.class)).thenReturn(messageSource);
        when(messageSource.getMessage(any(), any(), any())).thenAnswer(invocation -> invocation.getArguments()[0]);
    }
} 