package com.mcoin.mall.controller.business;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.model.CheckSearchValueRequest;
import com.mcoin.mall.model.PopularSearchesResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.SearchProductRequest;
import com.mcoin.mall.model.SearchProductResponse;
import com.mcoin.mall.model.SearchStoreRequest;
import com.mcoin.mall.model.SearchStoreResponse;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.annotation.Resource;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doReturn;

/**
 * 集成测试 SearchController
 * 使用真实服务和数据库，测试搜索相关功能
 */
class SearchControllerTest extends BaseUnitTest {

    @Resource
    private SearchController searchController;

    @SpyBean
    private ContextHolder contextHolder;

    private static final int TEST_USER_ID = 99999;

    @BeforeEach
    void setUp() {
        // 模拟Locale
        doReturn(Locale.CHINESE).when(contextHolder).getLocale();

        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");

        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();
    }

    /**
     * 测试福利搜索功能 - HARMONY客户端
     * 验证搜索结果包含预期的数据结构和内容
     */
    @Test
    @DisplayName("测试福利搜索 - HARMONY客户端")
    void testSearchProductWithHarmonyClient() {
        // 创建请求对象
        SearchProductRequest request = new SearchProductRequest();
        request.setSearchTerm("test");
        request.setPage(1);
        request.setSort("1"); // 按相关性排序

        // 直接调用控制器方法，使用HARMONY客户端
        Response<SearchProductResponse> response = (Response<SearchProductResponse>) searchController.searchProduct(request, ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");

        // 验证响应数据
        SearchProductResponse data = response.getData();
        assertNotNull(data, "Response data should not be null");
        assertNotNull(data.getPage(), "Page info should not be null");

        // 验证分页信息
        assertEquals(1, data.getPage().getCurrentPage(), "Current page should be 1");

        // 验证搜索结果结构
        if (data.getSnatchList() != null && !data.getSnatchList().isEmpty()) {
            // 如果有搜索结果，验证商品属性
            SearchProductResponse.SnatchListItem product = data.getSnatchList().get(0);
            assertNotNull(product.getId(), "Product ID should not be null");
            assertNotNull(product.getTitle(), "Product title should not be null");
        }
    }

    /**
     * 测试福利搜索功能 - 非HARMONY客户端
     * 验证不同客户端类型的响应差异
     */
    @Test
    @DisplayName("测试福利搜索 - 非HARMONY客户端")
    void testSearchProductWithNonHarmonyClient() {
        // 创建请求对象
        SearchProductRequest request = new SearchProductRequest();
        request.setSearchTerm("test");
        request.setPage(1);
        request.setSort("1"); // 按相关性排序

        // 直接调用控制器方法，使用非HARMONY客户端
        Response<SearchProductResponse> response = (Response<SearchProductResponse>) searchController.searchProduct(request, "ANDROID");

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");

        // 验证响应数据
        SearchProductResponse data = response.getData();
        assertNotNull(data, "Response data should not be null");
        assertNotNull(data.getPage(), "Page info should not be null");

        // 验证分页信息
        assertEquals(1, data.getPage().getCurrentPage(), "Current page should be 1");

        // 验证搜索结果结构
        if (data.getSnatchList() != null && !data.getSnatchList().isEmpty()) {
            // 如果有搜索结果，验证商品属性
            SearchProductResponse.SnatchListItem product = data.getSnatchList().get(0);
            assertNotNull(product.getId(), "Product ID should not be null");
            assertNotNull(product.getTitle(), "Product title should not be null");
        }
    }

    /**
     * 测试店铺搜索功能
     * 验证搜索结果包含预期的数据结构和内容
     */
    @Test
    @DisplayName("测试店铺搜索")
    void testSearchStore() {
        // 创建请求对象
        SearchStoreRequest request = new SearchStoreRequest();
        request.setSearchTerm("test");
        request.setPage(1);

        // 直接调用控制器方法
        Response<SearchStoreResponse> response = (Response<SearchStoreResponse>) searchController.searchStore(request, "ANDROID");

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");

        // 验证响应数据
        SearchStoreResponse data = response.getData();
        assertNotNull(data, "Response data should not be null");
        assertNotNull(data.getPage(), "Page info should not be null");

        // 验证分页信息
        assertEquals(1, data.getPage().getCurrentPage(), "Current page should be 1");

        // 验证搜索结果结构
        if (data.getSnatchList() != null && !data.getSnatchList().isEmpty()) {
            // 如果有搜索结果，验证店铺属性
            SearchStoreResponse.SnatchListItem store = data.getSnatchList().get(0);
            assertNotNull(store.getId(), "Store ID should not be null");
            assertNotNull(store.getName(), "Store name should not be null");
        }
    }

    /**
     * 测试店铺搜索功能 - 使用分类筛选
     * 验证使用分类筛选的搜索结果
     */
    @Test
    @DisplayName("测试店铺搜索 - 使用分类筛选")
    void testSearchStoreWithCategory() {
        // 创建请求对象
        SearchStoreRequest request = new SearchStoreRequest();
        request.setSearchTerm("test");
        request.setPage(1);
        request.setCategory(new String[]{"1"}); // 添加分类筛选

        // 直接调用控制器方法
        Response<SearchStoreResponse> response = (Response<SearchStoreResponse>) searchController.searchStore(request, "ANDROID");

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");

        // 验证响应数据
        SearchStoreResponse data = response.getData();
        assertNotNull(data, "Response data should not be null");
        assertNotNull(data.getPage(), "Page info should not be null");

        // 验证分页信息
        assertEquals(1, data.getPage().getCurrentPage(), "Current page should be 1");

        // 验证搜索结果结构
        if (data.getSnatchList() != null && !data.getSnatchList().isEmpty()) {
            // 如果有搜索结果，验证店铺属性
            SearchStoreResponse.SnatchListItem store = data.getSnatchList().get(0);
            assertNotNull(store.getId(), "Store ID should not be null");
            assertNotNull(store.getName(), "Store name should not be null");
        }
    }

    /**
     * 测试获取热门关键字功能
     * 验证返回的热门关键字列表包含预期的数据结构和内容
     */
    @Test
    @DisplayName("测试获取热门关键字")
    void testPopularSearches() {
        // 直接调用控制器方法
        Response<PopularSearchesResponse> response = (Response<PopularSearchesResponse>) searchController.popularSearches();

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");

        // 验证响应数据
        PopularSearchesResponse data = response.getData();
        assertNotNull(data, "Response data should not be null");

        // 验证热门关键字列表结构
        if (data.getSnatchList() != null && !data.getSnatchList().isEmpty()) {
            // 如果有热门关键字，验证关键字属性
            PopularSearchesResponse.PopularSearchItem keyword = data.getSnatchList().get(0);
            assertNotNull(keyword.getLabel(), "Keyword label should not be null");
            assertNotNull(keyword.getPopularity(), "Keyword popularity should not be null");
        }
    }

    /**
     * 测试检查搜索关键字功能 - HARMONY客户端
     * 验证返回的搜索关键字检查结果包含预期的数据结构和内容
     */
    @Test
    @DisplayName("测试检查搜索关键字 - HARMONY客户端")
    void testCheckSearchValueWithHarmonyClient() {
        // 创建请求对象
        CheckSearchValueRequest request = new CheckSearchValueRequest();
        request.setSearchTerm("test");

        // 直接调用控制器方法，使用HARMONY客户端
        Response<?> response = searchController.checkSearchValue(request, ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");
    }

    /**
     * 测试检查搜索关键字功能 - 非HARMONY客户端
     * 验证不同客户端类型的响应差异
     */
    @Test
    @DisplayName("测试检查搜索关键字 - 非HARMONY客户端")
    void testCheckSearchValueWithNonHarmonyClient() {
        // 创建请求对象
        CheckSearchValueRequest request = new CheckSearchValueRequest();
        request.setSearchTerm("test");

        // 直接调用控制器方法，使用非HARMONY客户端
        Response<?> response = searchController.checkSearchValue(request, "ANDROID");

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");
    }

    /**
     * 测试检查搜索关键字功能 - 空关键字
     * 验证使用空关键字的搜索结果
     */
    @Test
    @DisplayName("测试检查搜索关键字 - 空关键字")
    void testCheckSearchValueWithEmptyKeyword() {
        // 创建请求对象
        CheckSearchValueRequest request = new CheckSearchValueRequest();
        request.setSearchTerm(""); // 空关键字

        // 直接调用控制器方法
        Response<?> response = searchController.checkSearchValue(request, "HARMONY");

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(200, response.getCode(), "Response code should be 200");
    }
}