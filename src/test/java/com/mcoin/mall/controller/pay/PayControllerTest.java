package com.mcoin.mall.controller.pay;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.*;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

class PayControllerTest extends BaseUnitTest {

    @Resource
    private PayController payController;
    @Resource
    private ContextHolder contextHolder;

    @BeforeEach
    public void setUp() {
        // 只模拟认证信息，这对于集成测试也是必要的
        Mockito.when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        Mockito.when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
        
        // 在集成测试中，可以先准备好测试数据
        // 例如确保数据库中存在ID为123的商品，以及相关的订单记录
        prepareTestData();
    }
    
    /**
     * 准备测试数据
     * 在集成测试中，我们需要确保数据库中有测试所需的数据
     */
    private void prepareTestData() {
        // 这里可以通过DAO层插入测试数据
        // 如果数据已存在，可以不做任何操作
        // 实际实现取决于你的DAO层接口
    }

    @Test
    void testObtainPay() {
        ObtainPayRequest request = new ObtainPayRequest();
        request.setBusinessId(2877);

        Response<ObtainPayResponse> response = payController.obtainPay(request);

        assertNotNull(response);
        // 集成测试中断言基本结构和状态码
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void testCreateOrder() {
        CreateOrderRequest request = new CreateOrderRequest();
        // 根据CreateOrderRequest的定义填充必要字段
        request.setId(2207); // 产品ID
        request.setNumber(1); // 购买数量
        request.setMcurrency(100); // M币金额
        
        assertThrows(BusinessException.class, () -> {
            payController.createOrder(request);
        });
    }

    @Test
    void testProductProtocol() {
        ProductProtocolRequest request = new ProductProtocolRequest();
        request.setProductId(2877);

        Response<ProductProtocolResponse> response = payController.productProtocol(request);

        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void testPopUpCashier() {
        // 需要使用已存在的订单ID
        Integer id = 1; // 假设测试环境中存在此订单ID

        assertThrows(BusinessException.class, () -> {
            payController.popUpCashier(id);
        });
    }

    @Test
    void testCancelPay() {
        Integer id = 1; // 假设测试环境中存在此订单ID

        Response<CancelPayResponse> response = payController.cancelPay(id);

        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertNotNull(response.getData().getOrderToken());
    }

    @Test
    void testQueryPay() {
        Integer id = 1; // 假设测试环境中存在此订单ID

        Response<QueryPayResponse> response = payController.queryPay(id);

        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void testPayCallback() {
        // 准备符合验签要求的回调数据
        MPayCallbackRequest request = new MPayCallbackRequest();
        request.setTrade_no("123456");
        request.setOut_trade_no("ORDER123");
        request.setTrade_status("success");
        // 添加其他必要字段，确保能通过签名验证
        assertThrows(BusinessException.class, () -> {
            payController.payCallback(request);
        });
    }

    @Test
    void testApplyBuy() {
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(2877); // 使用正确的字段名
        request.setNumber(1);
        request.setSnap_up(1);

        assertThrows(BusinessException.class, () -> {
            payController.applyBuy(request);
        });
    }

    @Test
    void testSnapUpSuccess() {
        SnapUpSuccessRequest request = new SnapUpSuccessRequest();
        request.setProduct_id(2877);
        request.setNumber(1);
        assertThrows(BusinessException.class, () -> {
            payController.snapUpSuccess(request);
        });
    }
}