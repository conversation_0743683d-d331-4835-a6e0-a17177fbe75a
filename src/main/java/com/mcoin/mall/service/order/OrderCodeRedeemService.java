package com.mcoin.mall.service.order;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.SettlementCtx;
import com.mcoin.mall.model.api.MpayRedeemVoucherResponse;
import com.mcoin.mall.model.api.RedeemVoucherResponse;

import java.util.List;

/**
 * 券码核销
 */
public interface OrderCodeRedeemService {

    Integer verifyPass(List<Integer> storesId, String writeNumber);

    Response.Code settlement(SettlementCtx ctx);

    Response<RedeemVoucherResponse> settlementWithDetail(SettlementCtx ctx);

    MpayRedeemVoucherResponse mpaySettlementWithDetail(SettlementCtx ctx);
}
