package com.mcoin.mall.mp.controller.config;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.entity.LsConfig;
import com.mcoin.mall.mp.mapper.LsConfigMapper;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.common.GetConfigResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Transactional
public class ConfigControllerTest extends BaseUnitTest {

    @Autowired
    private ConfigController configController;

    @Autowired
    private LsConfigMapper lsConfigMapper;

    @BeforeEach
    public void setUp() {
        // 准备测试配置数据
        List<LsConfig> configs = Arrays.asList(
            createConfig("config", "register_way", "[\"1\"]"),
            createConfig("config", "login_way", "[\"1\",\"2\"]"),
            createConfig("config", "is_mobile_register_code", "1"),
            createConfig("config", "coerce_mobile", "0"),
            createConfig("config", "h5_wechat_auth", "1"),
            createConfig("config", "h5_auto_wechat_auth", "1"),
            createConfig("config", "mnp_wechat_auth", "1"),
            createConfig("config", "mnp_auto_wechat_auth", "1"),
            createConfig("config", "toutiao_auth", "1"),
            createConfig("config", "toutiao_auto_auth", "1"),
            createConfig("config", "app_wechat_auth", "1"),
            createConfig("shop", "mobile_logo", "test_logo.png"),
            createConfig("shop", "share_page", "1"),
            createConfig("shop", "share_title", "Test Shop"),
            createConfig("shop", "share_intro", "Test Shop Description"),
            createConfig("shop", "share_image", "test_share.png"),
            createConfig("shop", "status", "1"),
            createConfig("shop", "name", "Test Shop"),
            createConfig("h5", "status", "1"),
            createConfig("pc", "status", "1")
        );

        // 保存测试配置
        for (LsConfig config : configs) {
            lsConfigMapper.insert(config);
        }
    }

    private LsConfig createConfig(String type, String name, String value) {
        LsConfig config = new LsConfig();
        config.setType(type);
        config.setName(name);
        config.setValue(value);
        config.setSid(1);
        return config;
    }

    @Test
    public void getConfig_ShouldReturnValidConfig() {
        // 执行请求
        Response<GetConfigResponse> response = configController.getConfig();

        // 验证响应
        assertNotNull(response);
        assertEquals(1, response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    public void getConfig_ShouldHandleMissingConfigs() {
        // 删除所有配置
        lsConfigMapper.delete(null);

        // 执行请求
        Response<GetConfigResponse> response = configController.getConfig();

        // 验证响应
        assertNotNull(response);
    }
} 