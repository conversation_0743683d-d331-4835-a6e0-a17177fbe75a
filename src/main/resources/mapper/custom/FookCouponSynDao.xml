<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookCouponSynDao">
    <update id="updateCouponSynToSuc">
        update fook_coupon_syn
        SET
        message = #{message,jdbcType=VARCHAR},
        `status` = 1,
        updated_at = #{updatedAt,jdbcType=TIMESTAMP}
        where code_id = #{codeId,jdbcType=INTEGER}
        AND operation = #{operation,jdbcType=VARCHAR}
        AND type = 2
        AND `status` = 0
    </update>
    <select id="selectFookCouponWaitSyn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fook_coupon_syn
        where operation = 'OrderCode'
        and type = 2
        and status = 0
        and created_at >= #{startDate,jdbcType=TIMESTAMP}
        <![CDATA[
        and created_at < #{endDate,jdbcType=TIMESTAMP}
          ]]>
        order by created_at
        limit #{num,jdbcType=INTEGER}
    </select>
</mapper>