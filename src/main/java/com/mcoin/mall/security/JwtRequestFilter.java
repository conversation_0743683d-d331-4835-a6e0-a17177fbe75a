package com.mcoin.mall.security;

import java.io.IOException;
import java.util.Base64;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSON;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.mcoin.mall.bo.UserInfoBo;
import com.mcoin.mall.service.user.UserService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtRequestFilter extends OncePerRequestFilter {

    private static final String TOKEN_PREFIX = "bearer ";

    @Resource
    private TokenManager tokenManager;
    @Resource
    private ThirdPartyTokenManager thirdPartyTokenManager;
    @Resource
    private UserService userService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String jwtTokenString = request.getHeader(HttpHeaders.AUTHORIZATION);
        log.debug("jwtTokenString:{}", jwtTokenString);
        if (SecurityContextHolder.getContext().getAuthentication() == null && !ObjectUtils.isEmpty(jwtTokenString) &&
                jwtTokenString.startsWith(TOKEN_PREFIX)) {
            // 去除头
            jwtTokenString = jwtTokenString.replace(TOKEN_PREFIX, "");

            if (request.getRequestURI().endsWith("/api/third/party/recommendZone")) {
                DecodedJWT decodedJWT = thirdPartyTokenManager.verifyToken(jwtTokenString);
                if (decodedJWT != null) {
                    String payload = new String(Base64.getUrlDecoder().decode(decodedJWT.getPayload()));
                    ThirdPartyJwtPayload jwtPayload = JSON.parseObject(payload, ThirdPartyJwtPayload.class);
                    if (jwtPayload != null && jwtPayload.getUserId() != null) {

                        UserInfoBo userInfoBo = userService.getUserInfoByCustomId(jwtPayload.getUserId());
                        if (userInfoBo != null) {
                            UserInfo userInfo = new UserInfo();
                            userInfo.setUserId(userInfoBo.getUserId());
                            userInfo.setUsername(userInfoBo.getName());

                            UsernamePasswordAuthenticationToken userAuthentication = new UsernamePasswordAuthenticationToken(userInfo, null, null);
                            userAuthentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(userAuthentication);
                        }
                    }
                }
            } else {
                DecodedJWT decodedJWT = tokenManager.getJwtToken(jwtTokenString);
                if (decodedJWT != null) {
                    String payload = new String(Base64.getUrlDecoder().decode(decodedJWT.getPayload()));
                    JwtPayload jwtPayload = JSON.parseObject(payload, JwtPayload.class);
                    if (jwtPayload != null && jwtPayload.getData() != null) {
                        UserInfo userInfo = new UserInfo();
                        userInfo.setUserId(jwtPayload.getData().getId());
                        userInfo.setUsername(jwtPayload.getData().getUsername());

                        UsernamePasswordAuthenticationToken userAuthentication = new UsernamePasswordAuthenticationToken(userInfo, null, null);
                        userAuthentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(userAuthentication);
                    }
                }
            }
        }
        filterChain.doFilter(request, response);
    }
}