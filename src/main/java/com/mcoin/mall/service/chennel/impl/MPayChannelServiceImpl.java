package com.mcoin.mall.service.chennel.impl;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.mcoin.mall.util.SentinelUtils.handleBlockException;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessProductTranslations;
import com.mcoin.mall.bean.FookCouponSyn;
import com.mcoin.mall.bean.FookMacaupassOrder;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.bean.FookMqLocal;
import com.mcoin.mall.bean.FookPayLog;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookPlatformOrderrefund;
import com.mcoin.mall.bean.FookPlatformUnusualorder;
import com.mcoin.mall.bean.FookProductSnappingRecord;
import com.mcoin.mall.bean.FookReportOrdercodeSettlement;
import com.mcoin.mall.bean.FookScoreLog;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.bean.FookStoresTranslations;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.MPayCouponAccessClient;
import com.mcoin.mall.client.MPayCouponClient;
import com.mcoin.mall.client.MPayCouponClient.MD5Utils;
import com.mcoin.mall.client.model.MPayCouponCancelRequest;
import com.mcoin.mall.client.model.MPayCouponPayRequest;
import com.mcoin.mall.client.model.MPayCouponQueryRequest;
import com.mcoin.mall.client.model.MPayCouponUpdateCodeRequest;
import com.mcoin.mall.client.model.MPayQueryRefundRequest;
import com.mcoin.mall.client.model.MPayQueryStatusRequest;
import com.mcoin.mall.client.model.MPayRefundRequest;
import com.mcoin.mall.constant.CouponSynStatus;
import com.mcoin.mall.constant.CouponSynType;
import com.mcoin.mall.constant.MqLocalResourceType;
import com.mcoin.mall.constant.Numbers;
import com.mcoin.mall.constant.OrderCodeStatusEnum;
import com.mcoin.mall.constant.OrdercodeRefundStatusEnum;
import com.mcoin.mall.constant.PayLogStatus;
import com.mcoin.mall.constant.PaymentType;
import com.mcoin.mall.constant.PlatformOrderStatusEnum;
import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.constant.ReportOrdercodeSettlementStatusEnum;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookCouponSynDao;
import com.mcoin.mall.dao.FookMacaupassOrderDao;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.dao.FookPayLogDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundDao;
import com.mcoin.mall.dao.FookPlatformUnusualorderDao;
import com.mcoin.mall.dao.FookProductSnappingRecordDao;
import com.mcoin.mall.dao.FookReportOrdercodeSettlementDao;
import com.mcoin.mall.dao.FookScoreLogDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.dao.FookStoresTranslationsDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.mq.model.CouponSyncMessage;
import com.mcoin.mall.mq.model.RefundMessage;
import com.mcoin.mall.service.base.MqLocalService;
import com.mcoin.mall.service.chennel.MPayChannelService;
import com.mcoin.mall.service.chennel.RefundService;
import com.mcoin.mall.service.chennel.vo.MPayPaySuccessVo;
import com.mcoin.mall.service.chennel.vo.MPayRefundVo;
import com.mcoin.mall.service.common.AtomicSeqService;
import com.mcoin.mall.service.order.OrderCodeService;
import com.mcoin.mall.service.pay.PayBaseService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.MoneyUtil;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.util.SignUtil;
import com.mcoin.mall.util.TimeIntervalUtil;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MPayChannelServiceImpl implements MPayChannelService {
    @Resource
    private MPayCouponClient mPayCouponClient;
    @Resource
    private MPayCouponAccessClient mPayCouponAccessClient;
    @Resource
    private MPayClient mPayClient;
    @Resource
    private FookMacaupassOrderDao fookMacaupassOrderDao;
    @Resource
    private FookBusinessProductDao fookBusinessProductDao;
    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;
    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;
    @Resource
    private FookCouponSynDao fookCouponSynDao;
    @Resource
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;
    @Resource
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Resource
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;
    @Resource
    private FookStoresDao fookStoresDao;
    @Resource
    private FookStoresTranslationsDao fookStoresTranslationsDao;
    @Resource
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;
    @Resource
    private FookPayLogDao fookPayLogDao;
    @Resource
    private FookReportOrdercodeSettlementDao fookReportOrdercodeSettlementDao;
    @Resource
    private AtomicSeqService atomicSeqService;
    @Resource
    private ObjectMapper jacksonObjectMapper;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private FookPlatformUnusualorderDao fookPlatformUnusualorderDao;
    @Resource
    private SettingsDao settingsDao;
    @Resource
    private FookScoreLogDao fookScoreLogDao;
    @Resource
    private OrderCodeService orderCodeService;
    @Resource
    private FookProductSnappingRecordDao fookProductSnappingRecordDao;
    @Resource
    private FookBusinessDao fookBusinessDao;

    @Resource
    private PayBaseService payBaseService;

    @Resource
    private RefundService refundService;

    @Resource
    private MqLocalService mqLocalService;

    @Override
    public String getCoupon(FookPlatformOrder order, FookPlatformOrderinfo orderinfo) {

        FookMacaupassOrder macaupassOrder = fookMacaupassOrderDao.selectOne(new LambdaQueryWrapper<FookMacaupassOrder>()
                .eq(FookMacaupassOrder::getOrderId, order.getId())
                .orderByDesc(FookMacaupassOrder::getId)
                .last("limit 1")
        );
        if (macaupassOrder == null) {
            log.info("没有查询到FookMacaupassOrder， orderId： {}", order.getId());
            return null;
        }
        if (macaupassOrder.getMpayintegral() <= 0) {
            log.info("积分金额：{} 小于等于0， orderId： {}", macaupassOrder.getMpayintegral(), order.getId());
            return null;
        }
        int price = 0;
        String coupons_code_id = "0";
        if (orderinfo != null) {
            FookBusinessProduct product = fookBusinessProductDao.selectByPrimaryKey(orderinfo.getProdcutid());
            coupons_code_id = product.getMpayCouponsCodeId();
            price = product.getRetailPrice().multiply(BigDecimal.valueOf(100)).intValue();
        }
        FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, order.getUserid())
                .last("limit 1")
        );
        if (user == null) {
            log.info("没有查询到FookMacaupassUser， orderId： {}", order.getId());
            return null;
        }

        MPayCouponPayRequest couponPayRequest = new MPayCouponPayRequest();
        couponPayRequest.setRuleId(coupons_code_id);
        couponPayRequest.setMobile(user.getPhone());
        couponPayRequest.setUserId(user.getCustomid());
        couponPayRequest.setChannelOrderId(order.getOrderNo());
        couponPayRequest.setPrice(String.valueOf(price));
        couponPayRequest.setSign(MD5Utils.getMD5(couponPayRequest));
        try {
            String couponPayResponse = mPayCouponClient.pay(couponPayRequest);
            // TODO 改为实体
            log.info("MPay coupon pay response: {}", couponPayResponse);
            return couponPayResponse;
        } catch (Exception e) {
            log.error("领券-连接券平台未知异常", e);
            return null;
        }
    }

    @Override
    public String queryCoupon(Integer userId, String orderNo) {
        FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, userId)
                .last("limit 1")
        );
        if (user == null) {
            log.info("没有查询到FookMacaupassUser， orderNo： {}", orderNo);
            return null;
        }
        MPayCouponQueryRequest couponQueryRequest = new MPayCouponQueryRequest();
        couponQueryRequest.setUserId(user.getCustomid());
        couponQueryRequest.setOrderId(orderNo);
        couponQueryRequest.setSign(MD5Utils.getMD5(couponQueryRequest));
        try {
            String couponQueryResponse = mPayCouponClient.query(couponQueryRequest);
            log.info("MPay coupon query response: {}", couponQueryResponse);
            return couponQueryResponse;
        } catch (Exception e) {
            log.error("查询-连接券平台未知异常", e);
            return null;
        }
    }

    @Override
    public void updSyncCouponOrderCode(CouponSyncMessage message) {
        FookPlatformOrdercode ordercode = fookPlatformOrdercodeDao.selectByPrimaryKey(message.getOrderCodeId());
        if (ordercode == null) {
            log.info("未查询到ordercode订单，orderCodeId: {}", message.getOrderCodeId());
            return;
        }
        Integer orderId = ordercode.getOrderid();
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(orderId);
        if (order == null) {
            log.info("未查询到订单，orderId: {}", orderId);
            return;
        }
        if (order.getStatus() != 2 && order.getStatus() != 4) {
            log.info("訂單狀態不是支付成功或者退款訂單返回, orderId: {}", orderId);
            String msg = "訂單狀態非2已付款/4退款訂單";
            insertCouponSync(message.getOrderId(), message.getOrderCodeId(), CouponSyncMessage.Operation.OrderCode, msg);
            return;
        }
        if (!StringUtils.equals("1", order.getIsMpay()) || 0 == ObjectUtil.defaultIfNull(order.getUserid(), 0)) {
            log.info("非Mpay订单不同步券平台，orderId: {}", orderId);
            insertCouponSync(orderId, ordercode.getId(), CouponSyncMessage.Operation.OrderCode,
                    CouponSynStatus.SUCCESS, "非Mpay订单，不同步券平台");
            return;
        }
        FookCouponSyn couponSyn = fookCouponSynDao.selectOne(new LambdaQueryWrapper<FookCouponSyn>()
                .eq(FookCouponSyn::getOrderId, message.getOrderId())
                .eq(FookCouponSyn::getCodeId, message.getOrderCodeId())
                .eq(FookCouponSyn::getType, CouponSynType.UPDATE.getType())
                .orderByDesc(FookCouponSyn::getId)
                .last("limit 1")
        );
        if (couponSyn == null || CouponSynStatus.SUCCESS.getStatus() == couponSyn.getStatus()) {
            log.info("同步券平台日志为空或者已经同步成功，orderCodeId: {}", message.getOrderCodeId());
            return;
        }

        FookPlatformOrderinfo orderinfo = fookPlatformOrderinfoDao.selectOne(new LambdaQueryWrapper<FookPlatformOrderinfo>()
                .eq(FookPlatformOrderinfo::getOrderid, orderId).last("limit 1"));
        try {
            MPayCouponUpdateCodeRequest request = this.getUpdateCodeRequest(order, orderinfo, ordercode);
            String updateCodedResponse = handleBlockException(()->mPayCouponAccessClient.updateCode(request), null);
            if (StringUtils.isBlank(updateCodedResponse)) {
                // 重试
                throw new RetryException("更新Coupon券码，重试,次数:" + message.getDelayCount());
            }
            log.info("更新Coupon券码，返回: {}", updateCodedResponse);
            JSONObject codeRespJson = JSON.parseObject(updateCodedResponse);
            if (!"200".equals(codeRespJson.getString("code"))) {
                // 重试
                throw new RetryException("更新Coupon券码，重试,次数:" + message.getDelayCount());
            } else {
                log.info("更新Coupon券码，成功，orderCodeId: {}", message.getOrderCodeId());
                insertCouponSync(message.getOrderId(), ordercode.getId(), CouponSyncMessage.Operation.OrderCode,
                        CouponSynStatus.SUCCESS, codeRespJson.getString("msg"));
            }
        } catch (RetryException retry) {
            throw retry;
        } catch (Exception e) {
            log.error("更新Coupon券码， 未知异常", e);
            // 重试
            throw new RetryException("更新Coupon券码，重试,次数:" + message.getDelayCount());
        }
    }

    public int insertCouponSync(Integer orderId, Integer codeId, CouponSyncMessage.Operation operation, String msg) {
        return insertCouponSync(orderId, codeId, operation, CouponSynStatus.SUCCESS, msg);
    }

    public int insertCouponSync(Integer orderId, Integer codeId, CouponSyncMessage.Operation operation,
                                CouponSynStatus status, String msg) {
        FookCouponSyn couponSyn = fookCouponSynDao.selectOne(new LambdaQueryWrapper<FookCouponSyn>()
                .eq(FookCouponSyn::getOrderId, orderId)
                .eq(FookCouponSyn::getCodeId, codeId)
                .eq(FookCouponSyn::getType, CouponSynType.UPDATE.getType())
                .orderByDesc(FookCouponSyn::getId)
                .last("limit 1")
        );
        couponSyn = ObjectUtil.defaultIfNull(couponSyn, new FookCouponSyn());
        couponSyn.setOrderId(orderId);
        couponSyn.setCodeId(codeId);
        couponSyn.setOperation(operation.name());
        couponSyn.setType(CouponSynType.UPDATE.getType());//由於保存接口去掉，目前只有更新接口
        // 狀態1成功、0失敗'
        couponSyn.setStatus(status.getStatus());
        couponSyn.setMessage(msg);
        int rt;
        if (couponSyn.getId() != null) {
            couponSyn.setUpdatedAt(new Date());
            rt = fookCouponSynDao.updateByPrimaryKeySelective(couponSyn);
        } else {
            couponSyn.setCreatedAt(new Date());
            rt = fookCouponSynDao.insert(couponSyn);
        }
        log.info("保存同步Coupon日志，rt: {}, orderId: {}, codeId: {}, operation: {}, msg: {}",
                rt, orderId, codeId, operation, msg);
        if (CouponSynStatus.FAILED == status) {
            CouponSyncMessage syncMessage = new CouponSyncMessage();
            String delayMax = ConfigUtils.getProperty("coupon_sync_delay_count_max", "30");
            syncMessage.setDelayMax(Integer.parseInt(delayMax));
            syncMessage.setOperation(CouponSyncMessage.Operation.OrderCode);
            syncMessage.setOrderId(orderId);
            syncMessage.setOrderCodeId(codeId);
            applicationContext.publishEvent(syncMessage);
        }
        return rt;
    }

    private MPayCouponUpdateCodeRequest getUpdateCodeRequest(FookPlatformOrder order, FookPlatformOrderinfo orderinfo,
                                                             FookPlatformOrdercode ordercode) {

        String status = "";
        //購買時間 or 退款申請時間 or 使用時間 or 到賬時間;
        Date time = order.getPaymentTime();
        int coupontype = 0;
        //亞洲萬裡通的狀態碼單獨出來
        if (orderinfo.getType() == 5 && orderinfo.getMilesMember() != null) {
            //亞洲萬裡通的券類型為1;
            coupontype = 1;
            if (ordercode.getStatus() == 1) {
                if (ordercode.getRefundStatus() == OrdercodeRefundStatusEnum.REFUNDING.getCode()) {
                    //退款待審核
                    status = "06";
                    FookPlatformOrderrefund orderrefund = fookPlatformOrderrefundDao.selectByPrimaryKey(ordercode.getRefundid());
                    orderrefund = Optional.ofNullable(orderrefund).orElse(new FookPlatformOrderrefund());
                    //退款申請時間
                    time = orderrefund.getApplicationTime() != null ? orderrefund.getApplicationTime() : time;
                } else if (new Date().getTime() > orderinfo.getVaildEndTime().getTime()) {
                    //已過期
                    status = "04";
                } else {
                    //兌換中
                    status = "07";
                }
            } else {
                if (ordercode.getStatus() == 2) {
                    //使用時間
                    time = ordercode.getUserTime() != null ? ordercode.getUserTime() : time;
                } else if (ordercode.getStatus() == 3) {
                    FookPlatformOrderrefund orderrefund = fookPlatformOrderrefundDao.selectByPrimaryKey(ordercode.getRefundid());
                    orderrefund = Optional.ofNullable(orderrefund).orElse(new FookPlatformOrderrefund());
                    //退款到賬時間
                    time = orderrefund.getRefundTime() != null ? orderrefund.getRefundTime() : time;
                }
                //前面补0
                status = "0" + ordercode.getStatus();
            }
        } else {
            if (ordercode.getStatus() == 1) {
                if (ordercode.getRefundStatus() == OrdercodeRefundStatusEnum.REFUNDING.getCode()) {
                    //退款待審核
                    status = "06";
                    //退款申請時間
                    FookPlatformOrderrefund orderrefund = fookPlatformOrderrefundDao.selectByPrimaryKey(ordercode.getRefundid());
                    orderrefund = Optional.ofNullable(orderrefund).orElse(new FookPlatformOrderrefund());
                    //退款申請時間
                    time = orderrefund.getApplicationTime() != null ? orderrefund.getApplicationTime() : time;
                } else if (new Date().getTime() > orderinfo.getVaildEndTime().getTime()) {
                    //已過期
                    status = "04";
                } else {
                    //前面补0
                    status = "0" + ordercode.getStatus();
                }
            } else {
                if (ordercode.getStatus() == 2) {
                    //使用時間
                    time = ordercode.getUserTime() != null ? ordercode.getUserTime() : time;
                } else if (ordercode.getStatus() == 3) {
                    FookPlatformOrderrefund orderrefund = fookPlatformOrderrefundDao.selectByPrimaryKey(ordercode.getRefundid());
                    orderrefund = Optional.ofNullable(orderrefund).orElse(new FookPlatformOrderrefund());
                    //退款到賬時間
                    time = orderrefund.getRefundTime() != null ? orderrefund.getRefundTime() : time;
                }
                //前面补0
                status = "0" + ordercode.getStatus();
            }
        }
        //獲取商戶下第一家門店狀態是正常營業的
        FookStores stores = fookStoresDao.selectOne(new LambdaQueryWrapper<FookStores>()
                .eq(FookStores::getBusinessId, order.getSellerid())
                .eq(FookStores::getEnable, 1)
                .last("limit 1")
        );
        if (stores == null) {
            stores = fookStoresDao.selectOne(new LambdaQueryWrapper<FookStores>()
                    .eq(FookStores::getBusinessId, order.getSellerid())
                    .last("limit 1")
            );
        }
        stores = Optional.ofNullable(stores).orElse(new FookStores());

        //將（）或()替換成@符號
        String stores_name = defaultIfBlank(stores.getName(), "")
                .replace("（", "@")
                .replace("）", "@")
                .replace("(", "@")
                .replace(")", "@");
        //獲取第一次出現@符號的位置
        int strpos = stores_name.indexOf("@");

        if (strpos != -1) {
            //截取字符串0,$strpos
            stores_name = stores_name.substring(0, strpos);
        }

        FookStoresTranslations translations = fookStoresTranslationsDao.getTranslationsByStoreId(stores.getId(), McoinMall.LANG_EN);
        String stores_name_en = "";
        if (translations != null) {
            stores_name_en = isNotBlank(translations.getTName()) ? translations.getTName() : "";
        }

        stores_name_en = defaultIfBlank(stores_name_en, "")
                .replace("（", "@")
                .replace("）", "@")
                .replace("(", "@")
                .replace(")", "@");

        //獲取第一次出現@符號的位置
        int strpos_en = stores_name_en.indexOf("@");
        if (strpos_en != -1) {
            //截取字符串0,$strpos
            stores_name_en = stores_name_en.substring(0, strpos_en);
        }

        String img = stores.getImg() != null ? stores.getImg().split(",")[0] : "";

        FookBusinessProductTranslations productTranslations = fookBusinessProductTranslationsDao
                .getTranslationsById(orderinfo.getProdcutid(), McoinMall.LANG_EN);
        productTranslations = Optional.ofNullable(productTranslations).orElse(new FookBusinessProductTranslations());

        String couponNameEn = productTranslations.getTTitle();

        FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, order.getUserid())
                .last("limit 1")
        );

        MPayCouponUpdateCodeRequest request = new MPayCouponUpdateCodeRequest();
        request.setStoreId(order.getSellerid());// 門店id  //實際拿的是商戶id
        request.setStoreName(stores_name.trim());
        request.setStoreNameEn(isNotBlank(stores_name_en) ? stores_name_en : null);
        request.setStoreIconUrl(OssUtil.getStoreImg(img));// //門店icon地址
        request.setCouponRuleId(orderinfo.getProdcutid() + "");//券規則ID
        request.setCouponName(orderinfo.getTitleSnapshots());//券名稱
        request.setCouponNameEn(isNotBlank(couponNameEn) ? couponNameEn : null);//券英文名稱
        request.setCouponType(coupontype + "");
        request.setIconUrl(orderinfo.getImageSnapshots());
        //域名
        String url = ConfigUtils.getProperty("APP_URL");
        String detailUrl = url + "mod#/mcoin_order_details/" + ordercode.getId() +
                "-" + user.getCustomid() + "-" + order.getOrderNo();
        request.setCouponDetailUrl(detailUrl);// 券详情页地址
        request.setUserId(user.getCustomid());//用户id
        request.setMobile(user.getPhone());
        request.setCouponTNC(orderinfo.getTnc());//使用須知
        request.setCouponCode(ordercode.getCode());//券碼
        request.setCouponStatus(status);//券狀態
        request.setCouponStatusTime(JodaTimeUtil.format(time != null ? time : new Date()));
        request.setMcoinOrderId(order.getOrderNo());//外部訂單號
        request.setIntegral(ordercode.getApportionMpayintegral());//使用積分
        request.setAmount(ordercode.getApportionBillFinalAmount());//金額
        request.setVaildStartTime(JodaTimeUtil.format(orderinfo.getVaildStartTime()));//有效開始時間
        request.setVaildEndTime(JodaTimeUtil.format(orderinfo.getVaildEndTime()));//有效截止時間
        request.setCouponId(ordercode.getId());
        String secret = ConfigUtils.getProperty("macaupass.coupon.secret");
        request.setSignature(SignUtil.couponSign(secret, request));

        return request;
    }

    @Override
    public int updRefund(RefundMessage message) {
        FookPlatformOrderrefund orderrefund = fookPlatformOrderrefundDao.selectByPrimaryKey(message.getRefundId());
        if (orderrefund == null) {
            log.info("未查询到退款订单，refundId: {}", message.getRefundId());
            return -1;
        }
        // 支付平台處理狀態(0-审核中 1-處理 2-不成功 3-成功)
        if (orderrefund.getPlatformDealStatus() == 0 || orderrefund.getPlatformDealStatus() == 3) {
            log.info("支付平台處理狀態，PlatformDealStatus: {} in (0,3)", orderrefund.getPlatformDealStatus());
            return -2;
        }
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(orderrefund.getOrderid());
        if (order == null) {
            log.info("未查询到订单，orderId: {}", orderrefund.getOrderid());
            return -3;
        }
        FookPayLog payLog = fookPayLogDao.selectOne(new LambdaQueryWrapper<FookPayLog>()
                .eq(FookPayLog::getOrderid, order.getId())
                .eq(FookPayLog::getUid, order.getUserid())
                .eq(FookPayLog::getOparemtion, "pay")
                .eq(FookPayLog::getType, "macaupay")
                .orderByDesc(FookPayLog::getId)
                .last("limit 1")
        );
        boolean pay_log_status = payLog != null && defaultIfNull(payLog.getPoints(), 0) > 0;
        //如果有退款金額
        if (orderrefund.getRefundAmount().compareTo(BigDecimal.ZERO) > 0 || pay_log_status) {
            FookPayLog refundLog = fookPayLogDao.selectOne(new LambdaQueryWrapper<FookPayLog>()
                    .eq(FookPayLog::getOrderid, order.getId())
                    .eq(FookPayLog::getUid, order.getUserid())
                    .eq(FookPayLog::getRefundId, message.getRefundId())
                    .orderByDesc(FookPayLog::getId)
                    .last("limit 1"));
            refundLog = ObjectUtil.defaultIfNull(refundLog, new FookPayLog());
            String outRequestNo = message.getOutRequestNo();
            if (StringUtils.isNotBlank(refundLog.getOutRequestNo())) {
                outRequestNo = refundLog.getOutRequestNo();
            }
            if (StringUtils.isBlank(outRequestNo)) {
                outRequestNo = atomicSeqService.getOutTradeNo();
            }
            MPayRefundVo refundResp = refund(order, orderrefund, outRequestNo);
            if (refundResp == null) {
                refundResp = queryRefund(payLog, orderrefund, outRequestNo);
            }
            if (refundResp == null) {
                throw new RetryException("退款重试");
            }
            // 退款日志
            refundLog.setOrderid(order.getId());
            refundLog.setUid(order.getUserid());
            refundLog.setUuid("");
            refundLog.setOutRequestNo(outRequestNo);
            refundLog.setCreatetime(new Date());
            refundLog.setType("macaupay");
            refundLog.setTradeno(orderrefund.getRefundTransacation());
            refundLog.setRefundId(orderrefund.getId());
            int refundResult = 1;

            FookPlatformOrderrefund updRefund = new FookPlatformOrderrefund();
            refundLog.setContent(refundResp.getRequest());
            refundLog.setUuid(refundResp.getOutTradeNo());
            refundLog.setAmount(refundResp.getCurRefundFee());
            refundLog.setCurrency("MOP");
            refundLog.setOparemtion("refund");
            refundLog.setStatus(1);
            refundLog.setReasoncontent(refundResp.getResponse());
            refundLog.setUpdatetime(refundResp.getGmtRefundPay());
            refundLog.setReason(refundResp.getFundChange());
            refundLog.setTradeno(refundResp.getTradeNo());
            //退款狀態 6已到賬
            updRefund.setStatus(6);
            //支付平台處理狀態(钱) 3成功
            updRefund.setPlatformDealStatus(3);
            //支付平台處理狀態(積分) 3成功
            updRefund.setMpayintegralStatus(3);
            updRefund.setRefundTime(new Date());
            updRefund.setActualRefundAmount(refundResp.getCurRefundFee());

            //判斷是否劵平台的福利退款
            if (StringUtils.isNotBlank(order.getMpayCouponsCode())) {
                FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                        .eq(FookMacaupassUser::getUserId, order.getUserid())
                        .last("limit 1")
                );
                if (user != null) {
                    MPayCouponCancelRequest couponCancelRequest = new MPayCouponCancelRequest();
                    couponCancelRequest.setUserId(user.getCustomid());
                    couponCancelRequest.setOrderId(order.getOrderNo());
                    couponCancelRequest.setCpConsumeId(order.getMpayCouponsCode());
                    couponCancelRequest.setSign(MD5Utils.getMD5(couponCancelRequest));
                    log.info("撤销券：{}", couponCancelRequest);
                    String couponCancel = handleBlockException(()->mPayCouponClient.cancel(couponCancelRequest), null);
                    log.info("撤销券, 返回：{}", couponCancel);
                    JSONObject couponResp = JSON.parseObject(couponCancel);
                    if (couponResp != null && "200".equals(couponResp.getString("code"))) {
                        // 已退劵
                        updRefund.setRefundCouponsStatus(1);
                    } else {
                        // 退卷失敗
                        updRefund.setRefundCouponsStatus(2);
                    }
                } else {
                    log.error("没有查询到FookMacaupassUser， orderId： {}", order.getId());
                }
            }
            LambdaQueryWrapper<FookPlatformOrdercode> lquery = new LambdaQueryWrapper<FookPlatformOrdercode>()
                            .eq(FookPlatformOrdercode::getUserid, order.getUserid())
                            .eq(FookPlatformOrdercode::getOrderid, order.getId())
                            .eq(FookPlatformOrdercode::getRefundStatus, OrdercodeRefundStatusEnum.REFUNDING.getCode())
                            .eq(FookPlatformOrdercode::getRefundid, message.getRefundId());
            if(!RefundSceneEnum.APPROVAL_REFUND.getCode().equals(message.getRefundScene())){
                            lquery.eq(FookPlatformOrdercode::getStatus, OrderCodeStatusEnum.UNUSED.getType());
            }
            // 退款时的核销记录更新
            List<FookPlatformOrdercode> ordercodes = fookPlatformOrdercodeDao.selectList(lquery);
            if (!ordercodes.isEmpty()) {
                log.info("核销记录记录为数量：{}，orderId: {}", ordercodes.size(), order.getId());
                for (FookPlatformOrdercode ordercode : ordercodes) {
                    FookPlatformOrdercode updCode = new FookPlatformOrdercode();
                    updCode.setStatus(3);
                    updCode.setRefundStatus(OrdercodeRefundStatusEnum.REFUNDED.getCode());
                    fookPlatformOrdercodeDao.update(updCode, new LambdaQueryWrapper<FookPlatformOrdercode>()
                            .eq(FookPlatformOrdercode::getId, ordercode.getId())
                    );
                }
            } else {
                log.warn("核销记录记录为空，orderId: {}", order.getId());
            }
            log.info("退款操作 获取 ordercode_id = {}", orderrefund.getOrdercodeId());
            if (StringUtils.isNotBlank(orderrefund.getOrdercodeId())) {
                String[] ordercode_id_ary = orderrefund.getOrdercodeId().split(",");
                for (String codeId : ordercode_id_ary) {
                    FookReportOrdercodeSettlement os = fookReportOrdercodeSettlementDao.selectOne(
                            new LambdaQueryWrapper<FookReportOrdercodeSettlement>()
                                    .eq(FookReportOrdercodeSettlement::getOrdercodeid, codeId)
                                    .gt(FookReportOrdercodeSettlement::getBillamount, 0)
                                    .last("limit 1")
                    );
                    if (os != null) {
                        FookReportOrdercodeSettlement updOs = new FookReportOrdercodeSettlement();
                        updOs.setBusinessid(os.getBusinessid());
                        updOs.setStoreid(os.getStoreid());
                        updOs.setOrdercodeid(os.getOrdercodeid());
                        updOs.setBusinessname(os.getBusinessname());
                        updOs.setStorename(os.getStorename());
                        updOs.setBankaccount(os.getBankaccount());
                        updOs.setBankname(os.getBankname());
                        updOs.setBank(os.getBank());
                        updOs.setCreatetime(new Date());
                        updOs.setUsetime(new Date());
                        updOs.setBillamount(os.getBillamount().negate());
                        updOs.setUserpaymentamount(os.getUserpaymentamount().negate());
                        updOs.setMomecoinsamount(os.getMomecoinsamount().negate());
                        updOs.setCommission(os.getCommission().negate());
                        updOs.setMerchantsettleamount(os.getMerchantsettleamount().negate());
                        updOs.setVouchercode(os.getVouchercode());
                        updOs.setVouchername(os.getVouchername());
                        updOs.setOrdertransaction(os.getOrdertransaction());
                        //结算时才设置status和settlementbusinessid
                        if(StringUtils.isNotBlank(os.getStorename()) && ReportOrdercodeSettlementStatusEnum.NO_SETTLEMENT.getValue().equals(os.getStatus())) {
                            updOs.setStatus(os.getStatus());
                        } else {
                            updOs.setStatus(ReportOrdercodeSettlementStatusEnum.UNSETTLED.getValue());
                        }
                        updOs.setMpaysettlement(os.getMpaysettlement());
                        updOs.setMpayintegral(os.getMpayintegral());
                        updOs.setIsMpay(os.getIsMpay());
                        updOs.setIsMember(os.getIsMember());
                        updOs.setMemberintegral(os.getMemberintegral());
                        updOs.setIsVoucher(os.getIsVoucher());
                        updOs.setIsSettlement(os.getIsSettlement());
                        updOs.setStoremid(os.getStoremid());
                        updOs.setIsAOpen(os.getIsAOpen());
                        updOs.setThirdPartySettlementPrice(os.getThirdPartySettlementPrice());
                        updOs.setAFeeType(os.getAFeeType());
                        updOs.setAFeeRate(os.getAFeeRate());
                        updOs.setDiscountAmount(os.getDiscountAmount());
                        updOs.setThirdPartyPay(os.getThirdPartyPay());
                        updOs.setChannelCommission(os.getChannelCommission());
                        updOs.setRetailPrice(os.getRetailPrice());
                        fookReportOrdercodeSettlementDao.insert(updOs);
                    }
                }
            }
//            else {
//                refundLog.setCurrency("MOP");
//                refundLog.setOparemtion("refund");
//                refundLog.setStatus(-1);
//                refundLog.setReason(refundData.getString("respMsg"));
//                refundLog.setReasoncontent("");
//                refundResult = 0;
//
//                //退錢失敗
//                updRefund.setStatus(7);
//                updRefund.setPlatformDealStatus(2);
//            }
            int rt = 0;
            if (refundLog.getId() != null) {
                rt = fookPayLogDao.updateByPrimaryKeySelective(refundLog);
            } else {
                rt = fookPayLogDao.insert(refundLog);
            }
            log.info("插入退款日志, rt：{}, 退款记录：{}", rt, refundLog);

            updRefund.setRefundTransacation(defaultIfBlank(refundLog.getTradeno(), order.getOrderTransaction()));
            updRefund.setPayId(refundLog.getId());
            rt = fookPlatformOrderrefundDao.update(updRefund, new LambdaQueryWrapper<FookPlatformOrderrefund>()
                    .eq(FookPlatformOrderrefund::getId, orderrefund.getId()));

            log.info("更新退款信息, rt：{}, 退款记录：{}", rt, updRefund);
            //退款成功，退库存
            this.refundService.automaticRefunds(orderrefund.getId());
            //上报互动任务中心
            String refundSuccessSwitch = ConfigUtils.getProperty("task.upload.refundSuccess.switch", "true");
            if (Boolean.parseBoolean(refundSuccessSwitch)) {
                this.payBaseService.uploadRefundSuccess(refundLog, message.getRefundScene());
            } else {
                log.warn("上报互动任务中心开关关闭，本次退款成功不上报！");
            }
            return refundResult;
        } else {
            log.info("退款金額小于零 orderId: {}", orderrefund.getOrderid());
        }
        return -4;
    }

    @Override
    public void updRefundForInvalidOrder(FookPlatformOrder order, FookPayLog payLog, String tradeNo) {
        log.info("订单已失效的处理, tradeNo:{}", tradeNo);
        FookPlatformOrder updOrder = new FookPlatformOrder();
        updOrder.setId(order.getId());
        updOrder.setPaymentType(PaymentType.MPAY.getType());
        updOrder.setOrderTransaction(tradeNo);

        BigDecimal refund_score = BigDecimal.ZERO; //退款返還積分匯總
        int mpayintegral = 0;

        //判斷是否混合支付&支持舊記錄純積分支付退款
        if (defaultIfNull(payLog.getPoints(), 0) > 0) {
            mpayintegral = payLog.getPoints();
            int mpay_val = order.getPointRatio();
            refund_score = MoneyUtil.divide(BigDecimal.valueOf(mpayintegral), BigDecimal.valueOf(mpay_val));
            FookMacaupassOrder macaupassOrder = new FookMacaupassOrder();
            macaupassOrder.setStatus(2);
            fookMacaupassOrderDao.update(macaupassOrder, new LambdaQueryWrapper<FookMacaupassOrder>()
                    .eq(FookMacaupassOrder::getOrderId, order.getId())
            );
        }
        //退款表退回途徑處理
        int return_route = 0;
        switch (ObjectUtil.defaultIfNull(order.getPaymentType(), PaymentType.MPAY.getType())) {
            //訂單表-付款方式(0M幣,1支付寶支付,2微信支付,3信用卡支付,4MPay澳門錢包)
            //退款表-退回途徑(0.M幣 1.MPay 2.信用卡 3.微信 4.支付寶)
            case 1:
                return_route = 4;
                break;
            case 2:
                return_route = 3;
                break;
            case 3:
                return_route = 2;
                break;
            case 4:
                return_route = 1;
                break;
            default:
                return_route = 0;
                break;
        }
        String refund_reason = "MPay系統自動退款";
        //积分或者金额都为 0 不创建退款
        if (mpayintegral > 0 || order.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            //退款表
            FookPlatformOrderrefund orderrefund = new FookPlatformOrderrefund();
            orderrefund.setAreaid(1);
            orderrefund.setBusinessid(order.getSellerid());
            orderrefund.setUserid(order.getUserid());
            orderrefund.setOrderid(order.getId());
            orderrefund.setOrdercodeId("");//訂單核銷碼記錄Ids
            orderrefund.setRefundOrderno(atomicSeqService.getOrderNo());
            orderrefund.setRefundAmount(order.getTotalAmount());//退款金額
            orderrefund.setCurrency(order.getCurrency());
            orderrefund.setApplicationTime(new Date());//申請退款時間
            //狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，
            // 6、已到賬，7、到賬失敗，8取消退款，9轉入人工退款，10人工退款成功）
            orderrefund.setStatus(2);
            //支付平台處理狀態(0-审核中 1-處理 2-不成功 3-成功)
            orderrefund.setPlatformDealStatus(1);
            orderrefund.setRefundRessonFront("");//前台退款原因
            orderrefund.setCustomerRemark("");//客服備註
            orderrefund.setReturnRoute(return_route);//退回途徑(0.M幣 1.MPay 2.信用卡 3.微信 4.支付寶)
            orderrefund.setRefundScore(refund_score);//退款返還積分
            orderrefund.setActualRefundAmount(order.getPaymentAmount());//實際退款金額
            orderrefund.setMpayintegral(Long.valueOf(mpayintegral));//澳門通積分
            orderrefund.setRefundReason(refund_reason);//退款原因

            int rtRefund = fookPlatformOrderrefundDao.insert(orderrefund);
            log.info("插入退款订单， rt: {}", rtRefund);

            if (orderrefund.getId() != null) {
                FookPlatformOrdercode updOrderCode = new FookPlatformOrdercode();
                updOrderCode.setRefundStatus(OrdercodeRefundStatusEnum.REFUNDING.getCode());
                updOrderCode.setRefundid(orderrefund.getId());
                int rtCode = fookPlatformOrdercodeDao.update(updOrderCode, new LambdaQueryWrapper<FookPlatformOrdercode>()
                        .eq(FookPlatformOrdercode::getOrderid, order.getId())
                );
                log.info("更新ordercode refund_status = 2，rt: {}", rtCode);
                // 设置订单退款状态
//                updOrder.setRefundStatus(3);
//                updOrder.setStatus(4);

                FookProductSnappingRecord record = fookProductSnappingRecordDao.selectOne(new LambdaQueryWrapper<FookProductSnappingRecord>()
                        .eq(FookProductSnappingRecord::getOrderId, order.getId())
                        .eq(FookProductSnappingRecord::getStatus, 2)
                        .last("limit 1")
                );
                if (record != null) {
                    FookProductSnappingRecord updRecord = new FookProductSnappingRecord();
                    updRecord.setStatus(3);
                    updRecord.setUpdatedAt(new Date());
                    int rt = fookProductSnappingRecordDao.update(updRecord, new LambdaUpdateWrapper<FookProductSnappingRecord>()
                            .eq(FookProductSnappingRecord::getOrderId, order.getId())
                            .eq(FookProductSnappingRecord::getStatus, 2)
                    );
                    log.info("更新SnappingRecord status = 3，rt: {}", rt);
                }
                // 退款
                RefundMessage refundMessage = new RefundMessage();
                refundMessage.setRefundId(orderrefund.getId());
                refundMessage.setOutRequestNo(atomicSeqService.getOutTradeNo());
                refundMessage.setRefundScene(RefundSceneEnum.INVALID_ORDER.getCode());


                String gradientRetryInterval = ConfigUtils.getProperty("mcoin.gradient.interval." + MqLocalResourceType.REFUND.getType(),"1m,10m,1h,4h,12h,24h,48h");
                String[] intervals = gradientRetryInterval.split(",");
                FookMqLocal mqRecord = new FookMqLocal();
                mqRecord.setResourceId(Convert.toStr(orderrefund.getId()));
                mqRecord.setResourceType(MqLocalResourceType.REFUND.getType());
                mqRecord.setTryCount(Numbers.ZERO.getIntValue());
                mqRecord.setNextRetry(DateUtil.offsetMillisecond(new Date(), TimeIntervalUtil.parseIntervalToMillis(intervals[Numbers.ZERO.getIntValue()])));
                mqRecord.setTemplateName("refundTemplate");
                mqRecord.setMessageBody(JSON.toJSONString(refundMessage));
                mqLocalService.saveMqLocal(mqRecord);
                refundMessage.setMqLocalId(mqRecord.getId());

                applicationContext.publishEvent(refundMessage);
            }
        }
        int rtUpdCode = fookPlatformOrderDao.updateByPrimaryKeySelective(updOrder);
        log.info("更新订单：{} OrderTransaction = {} rt: {}",
                order.getId(), tradeNo, rtUpdCode);
    }

    private MPayRefundVo refund(FookPlatformOrder order, FookPlatformOrderrefund orderrefund, String outRequestNo) {
        MPayRefundRequest.Content content = new MPayRefundRequest.Content();
        content.setOut_trade_no("");
        content.setTrade_no(order.getOrderTransaction());
        content.setRefund_amount(orderrefund.getRefundAmount());
        content.setRefund_point_amount(orderrefund.getRefundScore());
        content.setRefund_reason(orderrefund.getRefundReason());
        content.setOut_request_no(outRequestNo);
        content.setRefund_point(orderrefund.getMpayintegral());
        String priKey = ConfigUtils.getProperty("pay.Macau.key.private");
        String contentString = null;
        try {
            contentString = jacksonObjectMapper.writeValueAsString(content);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        MPayRefundRequest refundRequest = new MPayRefundRequest(ConfigUtils.getProperty("pay.Macau.appid"));
        refundRequest.setBiz_content(contentString);
        String signature = SignUtil.mPaySign(priKey, contentString);
        log.info("签名报文：{}, 签名：{}", contentString, signature);
        refundRequest.setSign(signature);

        try {
            String refundResp = mPayClient.refund(refundRequest);
            String pubKey = ConfigUtils.getProperty("pay.Macau.key.public");
            boolean verifySign = SignUtil.mPayVerifySign(pubKey, refundResp);
            log.info("退款返回：{}, 验签：{}", refundResp, verifySign);
            if (!verifySign) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "验签失败");
            }
            JSONObject refundData = JSON.parseObject(refundResp);
            if ("0000".equals(refundData.getString("respCode")) && refundData.containsKey("Data")) {
                JSONObject data = refundData.getJSONObject("Data");
                MPayRefundVo mPayRefundVo = new MPayRefundVo();
                mPayRefundVo.setRespCode(refundData.getString("respCode"));
                mPayRefundVo.setRespMsg(refundData.getString("respMsg"));
                mPayRefundVo.setResponse(refundResp);
                mPayRefundVo.setRequest(contentString);
                mPayRefundVo.setOutTradeNo(data.getString("out_trade_no"));
                mPayRefundVo.setCurRefundFee(data.getBigDecimal("cur_refund_fee"));
                mPayRefundVo.setGmtRefundPay(JodaTimeUtil.parse(data.getString("gmt_refund_pay"), JodaTimeUtil.PATTERN));
                mPayRefundVo.setFundChange(data.getString("fund_change"));
                mPayRefundVo.setTradeNo(data.getString("trade_no"));
                return mPayRefundVo;
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.info("请求退款异常:{}", e.getMessage());
        }
        return null;
    }

    private MPayRefundVo queryRefund(FookPayLog payLog, FookPlatformOrderrefund orderrefund, String outRequestNo) {
        MPayQueryRefundRequest.Content queryContent = new MPayQueryRefundRequest.Content();

        queryContent.setOut_trade_no(payLog != null ? payLog.getUuid() : "");
        queryContent.setTrade_no(orderrefund.getRefundTransacation());
        queryContent.setOut_request_no(outRequestNo);

        MPayQueryRefundRequest queryRefundRequest = new MPayQueryRefundRequest(ConfigUtils.getProperty("pay.Macau.appid"));
        String queryRefundContent = null;
        try {
            queryRefundContent = jacksonObjectMapper.writeValueAsString(queryContent);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        queryRefundRequest.setBiz_content(queryRefundContent);
        String priKey = ConfigUtils.getProperty("pay.Macau.key.private");
        String queryRefundSignature = SignUtil.mPaySign(priKey, queryRefundContent);
        log.info("查询退款签名报文：{}, 签名：{}", queryRefundContent, queryRefundSignature);
        queryRefundRequest.setSign(queryRefundSignature);
        try {
            String queryRefundResp = mPayClient.queryRefund(queryRefundRequest);
            String pubKey = ConfigUtils.getProperty("pay.Macau.key.public");
            boolean verifySign = SignUtil.mPayVerifySign(pubKey, queryRefundResp);
            log.info("查询退款返回：{}, 验签：{}", queryRefundResp, verifySign);
            if (!verifySign) {
                throw new BusinessException(Response.Code.BAD_REQUEST, "验签失败");
            }
            JSONObject refundData = JSON.parseObject(queryRefundResp);
            if ("0000".equals(refundData.getString("respCode")) && refundData.containsKey("Data")) {
                JSONObject data = refundData.getJSONObject("Data");
                if (outRequestNo.equals(data.getString("out_request_no"))) {
                    MPayRefundVo mPayRefundVo = new MPayRefundVo();
                    mPayRefundVo.setRespCode(refundData.getString("respCode"));
                    mPayRefundVo.setRespMsg(refundData.getString("respMsg"));
                    mPayRefundVo.setResponse(queryRefundResp);
                    mPayRefundVo.setOutTradeNo(data.getString("out_trade_no"));
                    mPayRefundVo.setCurRefundFee(data.getBigDecimal("refund_amount"));
                    mPayRefundVo.setGmtRefundPay(new Date());
                    mPayRefundVo.setFundChange("Y");
                    mPayRefundVo.setTradeNo(data.getString("trade_no"));
                    return mPayRefundVo;
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.info("查询退款异常:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public void updPaySuccess(MPayPaySuccessVo successData) {
        FookPayLog payLog = fookPayLogDao.selectOne(new LambdaQueryWrapper<FookPayLog>()
                .eq(FookPayLog::getUuid, successData.getOutTradeNo())
                .eq(FookPayLog::getOparemtion, "pay")
                .eq(FookPayLog::getStatus, PayLogStatus.UNPAID.getStatus())
                .orderByDesc(FookPayLog::getId)
                .last("limit 1")
        );
        if (payLog != null) {
            log.info("pay log:{}", payLog);
            FookPayLog updPayLog = new FookPayLog();
            updPayLog.setUpdatetime(new Date());
            payLog.setUpdatetime(new Date());
            updPayLog.setReason(successData.getTradeStatus());
            payLog.setReason(successData.getTradeStatus());
            updPayLog.setTradeno(successData.getTradeNo());
            payLog.setTradeno(successData.getTradeNo());
            BigDecimal rate = new BigDecimal(defaultIfBlank(settingsDao.getValue("site.macaurate"), "0.014"));
            BigDecimal fee = payLog.getAmount().multiply(rate);
            updPayLog.setFee(fee);
            payLog.setFee(fee);
            updPayLog.setStatus(PayLogStatus.PAID.getStatus());
            payLog.setStatus(PayLogStatus.PAID.getStatus());
            updPayLog.setReasoncontent(JSON.toJSONString(successData.getRequest()));
            payLog.setReasoncontent(updPayLog.getReasoncontent());

            if (StringUtils.isNotBlank(successData.getNotifyTime())) {
                updPayLog.setPaytime(JodaTimeUtil.parse(successData.getNotifyTime()));
                payLog.setPaytime(JodaTimeUtil.parse(successData.getNotifyTime()));
            }

            FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(payLog.getOrderid());
            boolean isInvalidRefund =false;
            if (order != null) {
                if (order.getStatus() == PlatformOrderStatusEnum.UNPAID.getStatus()) {
                    log.info("更新支付状态=> orderId : {}", payLog.getOrderid());
                    FookPlatformOrder updOrder = new FookPlatformOrder();
                    updOrder.setPaymentTime(new Date());
                    updOrder.setBankCharges(payLog.getFee());
                    updOrder.setStatus(PlatformOrderStatusEnum.PAID.getStatus());  //更新支付状态
                    updOrder.setPaymentType(PaymentType.MPAY.getType());
                    updOrder.setOrderTransaction(successData.getTradeNo());
                    updOrder.setTransactionAmount(new BigDecimal(successData.getBuyerPayAmount()));
                    updOrder.setTransactionAmountCurrency("MOP");
                    updOrder.setPaymentAmount(new BigDecimal(successData.getBuyerPayAmount()));
                    updOrder.setPaymentAmountCurrency("MOP");


                    FookScoreLog scoreLog = new FookScoreLog();
                    scoreLog.setOrderid(order.getId());
                    scoreLog.setUid(payLog.getUid());
                    scoreLog.setUuid(payLog.getUuid());
                    scoreLog.setOparemtion(payLog.getOparemtion() + "_bak");
                    scoreLog.setScore(payLog.getPoints());
                    scoreLog.setType(payLog.getType());
                    scoreLog.setStatus(payLog.getStatus());
                    scoreLog.setCreatetime(new Date());
                    if (payLog.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        if (StringUtils.isNotBlank(successData.getPointTradeNo())) {
                            scoreLog.setTradeno(successData.getPointTradeNo());
                        }
                    } else {
                        scoreLog.setTradeno(successData.getTradeNo());
                    }
                    // 是否创建核销码
                    boolean isCreateOrderCode = false;
                    FookPlatformOrderinfo orderinfo = fookPlatformOrderinfoDao.selectOne(new LambdaQueryWrapper<FookPlatformOrderinfo>()
                            .eq(FookPlatformOrderinfo::getOrderid, order.getId()).last("limit 1"));
                    if (orderinfo.getType() == 9) {
                        log.info("Mpay 优惠券平台領劵, orderId: {}", order.getId());
                        updPayLog.setMpayCouponsType(1);
                        payLog.setMpayCouponsType(1);
                        // 领券
                        String couponResponse = getCoupon(order, orderinfo);

                        if (StringUtils.isBlank(couponResponse)) {
                            log.info("Mpay 优惠券平台領劵, 获取券返回空，转查询, orderId: {}", order.getId());
                            couponResponse = queryCoupon(order.getUserid(), order.getOrderNo());
                        }
                        if (StringUtils.isNotBlank(couponResponse)) {
                            JSONObject couponJson = JSON.parseObject(couponResponse);
                            if ("200".equals(couponJson.getString("code"))) {
                                JSONObject couponJsonData = couponJson.getJSONObject("Data");
                                if (couponJsonData != null) {
                                    updPayLog.setMpayCouponsCode(couponJsonData.getString("cpConsumeId"));
                                    payLog.setMpayCouponsCode(couponJsonData.getString("cpConsumeId"));
                                    updPayLog.setMpayCouponsReasoncontent(couponResponse);
                                    payLog.setMpayCouponsReasoncontent(couponResponse);

                                    updOrder.setMpayCouponsCode(couponJsonData.getString("cpConsumeId"));
                                    order.setMpayCouponsCode(couponJsonData.getString("cpConsumeId"));
                                    updOrder.setMpayCouponsStatus(1);
                                    order.setMpayCouponsStatus(1);
                                    // 领券成功创建核销码
                                    isCreateOrderCode = true;

                                    log.info("Mpay 优惠券平台領劵, 成功");
                                } else {
                                    log.info("Mpay 优惠券平台領劵, 失败, couponJsonData is null");
                                    updOrder.setMpayCouponsStatus(2);
                                    order.setMpayCouponsStatus(2);
                                }
                            } else {
                                updOrder.setMpayCouponsStatus(2);
                                order.setMpayCouponsStatus(2);
                                log.info("Mpay 优惠券平台領劵, 失败, code({}) != 200", couponJson.getString("code"));
                                // 插入异常订单
                                saveUnusualOrder(order, orderinfo, scoreLog, payLog);
                            }
                        } else {
                            updOrder.setMpayCouponsStatus(2);
                            order.setMpayCouponsStatus(2);
                            log.info("Mpay 优惠券平台領劵, 失败,若查詢為空則表示mpay接口有問題丟入異常單");
                            // 插入异常订单
                            saveUnusualOrder(order, orderinfo, scoreLog, payLog);
                        }
                    } else {
                        // 不需要领券默认创建核销码
                        isCreateOrderCode = true;
                    }
                    log.info("保存积分日记数据: {}", scoreLog);
                    fookScoreLogDao.insertSelective(scoreLog);

                    int rt = fookPlatformOrderDao.update(updOrder, new LambdaQueryWrapper<FookPlatformOrder>()
                            .eq(FookPlatformOrder::getId, order.getId())
                            .eq(FookPlatformOrder::getStatus, 1)
                    );
                    if (rt == 0) {
                        log.warn("更新订单状态为2失败，{}", updOrder);
                    } else if (isCreateOrderCode) {
                        log.info("創建核銷碼 start, orderId: {}, tradeNo: {}", order.getId(), successData.getTradeNo());

                        // 创建核销码（券码）
                        orderCodeService.createOrderCode(order, orderinfo);
                        log.info("創建核銷碼 end, orderId: {}, tradeNo: {}", order.getId(), successData.getTradeNo());
                    }
                } else if (order.getStatus() == 3) {
                    updRefundForInvalidOrder(order, payLog, successData.getTradeNo());
                    isInvalidRefund = true;
                }
            }
            // 更新payLog
            int rt = fookPayLogDao.update(updPayLog, new LambdaQueryWrapper<FookPayLog>()
                    .eq(FookPayLog::getId, payLog.getId())
                    .eq(FookPayLog::getOparemtion, "pay")
                    .eq(FookPayLog::getStatus, PayLogStatus.UNPAID.getStatus())
            );
            if (rt == 0) {
                log.warn("更新payLog失败， payLogId: {}, status from 0 to 1", payLog.getId());
                throw new RetryException("更新payLog失败， payLogId: " + payLog.getId() + ", status from 0 to 1");
            }
            //上报互动任务中心
            String paySuccessSwitch = ConfigUtils.getProperty("task.upload.paySuccess.switch", "true");
            if (Boolean.parseBoolean(paySuccessSwitch) && !isInvalidRefund) {
                this.payBaseService.uploadPaySuccess(payLog);
            } else {
                log.warn("上报互动任务中心开关关闭或订单已失效，本次交易成功不上报！{} {} {}",payLog.getOrderid(), paySuccessSwitch, isInvalidRefund);
            }
        } else {
            log.warn("支付通知重复或者无payLog， out_trade_no: {}", successData.getOutTradeNo());
        }
    }


    private void saveUnusualOrder(FookPlatformOrder order, FookPlatformOrderinfo orderinfo, FookScoreLog scoreLog, FookPayLog payLog) {

        FookMacaupassUser user = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getUserId, order.getUserid())
                .last("limit 1")
        );

        FookBusiness business = fookBusinessDao.selectByPrimaryKey(order.getSellerid());

        FookPlatformUnusualorder unusualorder = fookPlatformUnusualorderDao.selectOne(
                new LambdaQueryWrapper<FookPlatformUnusualorder>()
                        .eq(FookPlatformUnusualorder::getOrderid, order.getId())
                        .eq(FookPlatformUnusualorder::getUserid, order.getUserid())
                        .eq(FookPlatformUnusualorder::getType, 200)
                        .last("limit 1")
        );
        unusualorder = Optional.ofNullable(unusualorder).orElse(new FookPlatformUnusualorder());
        unusualorder.setSellerid(order.getSellerid());
        unusualorder.setOrderNo(order.getOrderNo());
        unusualorder.setCreateTime(new Date());
        unusualorder.setPaymentType(payLog != null ? PaymentType.MPAY.getType() : order.getPaymentType());
        unusualorder.setStatus(order.getStatus());
        unusualorder.setMpayintegral(BigDecimal.valueOf(order.getMpayintegral()));
        unusualorder.setOrderAmount(order.getOrderAmount());
        unusualorder.setCurrency(order.getCurrency());
        unusualorder.setTotalAmount(order.getTotalAmount());
        unusualorder.setPaymentAmount(order.getPaymentAmount());
        unusualorder.setPaymentTime(order.getPaymentTime());
        if (StringUtils.isNotBlank(order.getIsMpay())) {
            unusualorder.setIsMpay(Integer.parseInt(order.getIsMpay()));
        }
        unusualorder.setIsMember(order.getIsMember());
        unusualorder.setMemberintegral(order.getMemberintegral());
        unusualorder.setBusinessName(business.getName());
        unusualorder.setProductId(orderinfo.getProdcutid());
        unusualorder.setProductName(orderinfo.getTitleSnapshots());
        unusualorder.setPhone(user.getPhone());

        if (scoreLog != null) {
            unusualorder.setScoreTradeno(scoreLog.getTradeno());
            unusualorder.setScoreUuid(scoreLog.getUuid());
            unusualorder.setScoreStatus(scoreLog.getStatus());
        }
        unusualorder.setScoreTradeno(defaultIfNull(unusualorder.getScoreTradeno(), ""));
        unusualorder.setScoreUuid(defaultIfNull(unusualorder.getScoreUuid(), ""));

        if (payLog != null) {
            unusualorder.setPayTradeno(payLog.getTradeno());
            unusualorder.setPayUuid(payLog.getUuid());
            unusualorder.setPayStatus(payLog.getStatus());
        }

        unusualorder.setPayTradeno(defaultIfNull(unusualorder.getPayTradeno(), ""));
        unusualorder.setPayUuid(defaultIfNull(unusualorder.getPayUuid(), ""));
        unusualorder.setPayUuid(defaultIfNull(unusualorder.getPayUuid(), ""));

        unusualorder.setCreatedAt(new Date());

        if (unusualorder.getId() != null) {
            fookPlatformUnusualorderDao.updateByPrimaryKeySelective(unusualorder);
        } else {
            fookPlatformUnusualorderDao.insertSelective(unusualorder);
        }
    }

    @Override
    public String queryPay(String outTradeNo, String orderNo) {
        MPayQueryStatusRequest.Content content = new MPayQueryStatusRequest.Content();
        content.setOut_trade_no(outTradeNo);
        content.setTrade_no(orderNo);
        String priKey = ConfigUtils.getProperty("pay.Macau.key.private");
        String contentString = null;
        try {
            contentString = jacksonObjectMapper.writeValueAsString(content);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        String signature = SignUtil.mPaySign(priKey, contentString);
        log.info("签名报文：{}, 签名：{}", contentString, signature);
        MPayQueryStatusRequest request = new MPayQueryStatusRequest(ConfigUtils.getProperty("pay.Macau.appid"));
        request.setBiz_content(contentString);
        request.setSign(signature);
        String statusResp = handleBlockException(()->mPayClient.queryStatus(request), null);
        if (StringUtils.isBlank(statusResp)) {
            return null;
        }
        String pubKey = ConfigUtils.getProperty("pay.Macau.key.public");
        boolean verifySign = SignUtil.mPayVerifySign(pubKey, statusResp);
        log.info("查询状态返回：{}, 验签：{}", statusResp, verifySign);
        if (!verifySign) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "验签失败");
        }
        return statusResp;
    }
}
