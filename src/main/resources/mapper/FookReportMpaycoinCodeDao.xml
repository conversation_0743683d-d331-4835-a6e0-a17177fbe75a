<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMpaycoinCodeDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportMpaycoinCode">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="sum_code" jdbcType="DECIMAL" property="sumCode" />
    <result column="sum_mpaycoins" jdbcType="DECIMAL" property="sumMpaycoins" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="settlement_cycle" jdbcType="VARCHAR" property="settlementCycle" />
  </resultMap>
  <sql id="Base_Column_List">
    id, start_time, end_time, sum_code, sum_mpaycoins, settlement_time, settlement_cycle
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_mpaycoin_code
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_mpaycoin_code
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinCode">
    insert into fook_report_mpaycoin_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="sumCode != null">
        sum_code,
      </if>
      <if test="sumMpaycoins != null">
        sum_mpaycoins,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
      <if test="settlementCycle != null">
        settlement_cycle,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sumCode != null">
        #{sumCode,jdbcType=DECIMAL},
      </if>
      <if test="sumMpaycoins != null">
        #{sumMpaycoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementCycle != null">
        #{settlementCycle,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinCode">
    update fook_report_mpaycoin_code
    <set>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sumCode != null">
        sum_code = #{sumCode,jdbcType=DECIMAL},
      </if>
      <if test="sumMpaycoins != null">
        sum_mpaycoins = #{sumMpaycoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementCycle != null">
        settlement_cycle = #{settlementCycle,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportMpaycoinCode">
    update fook_report_mpaycoin_code
    set start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      sum_code = #{sumCode,jdbcType=DECIMAL},
      sum_mpaycoins = #{sumMpaycoins,jdbcType=DECIMAL},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      settlement_cycle = #{settlementCycle,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>