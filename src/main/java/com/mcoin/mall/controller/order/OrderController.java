package com.mcoin.mall.controller.order;

import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.*;
import com.mcoin.mall.service.order.OrderCodeRedeemService;
import com.mcoin.mall.service.order.OrderService;
import com.mcoin.mall.util.SignUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@RestController
public class OrderController {

    @Resource
    private OrderService orderService;

    @Resource
    private MessageSource messageSource;

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private OrderCodeRedeemService orderCodeRedeemService;

    /**
     * 获取订单详情
     *
     * @return
     */
    @GetMapping(value = "/api/auth/productwelfaredetails", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderDetailResponse> orderDetail(@Valid OrderDetailRequest request) {
        request.convertWsgToGsj();
        OrderDetailResponse response = orderService.getOrderDetail(request);
        return Responses.ok(response);
    }

    /**
     * 密码核销
     *
     * @return
     */
    @GetMapping(value = "/api/auth/redeemWrite", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<RedeemWriteResponse> redeemWrite(@Valid RedeemWriteRequest request,
                                                     @RequestHeader(value = "HTTP_APPHEAD", defaultValue = "") String app) {
        int userId = contextHolder.getAuthUserInfo().getUserId();
        RedeemWriteResponse response = new RedeemWriteResponse();
        FookPlatformOrderinfo orderinfo = this.orderService.getOrderInfoByCode(request.getCode(), userId);
        if (null == orderinfo) {
            throw getBusinessException("message.redeem.error_admin");
        }
        List<FookStores> stores = this.orderService.getStoresByProductId(orderinfo.getProdcutid());
        if (CollectionUtils.isEmpty(stores)) {
            throw getBusinessException("message.redeem.error_admin");
        }
        String writeNumber = request.getWritenumber();
        writeNumber = SignUtil.md5Pass(writeNumber);
        List<Integer> storesIds = stores.stream().map(FookStores::getId).collect(Collectors.toList());
        Integer verifyStoresId = this.orderCodeRedeemService.verifyPass(storesIds, writeNumber);
        if (null == verifyStoresId) {
            throw getBusinessException("message.redeem.verification_password");
        }
        int mode = 2;
        if (StringUtils.isNotBlank(app) && "App".equals(app)) {
            mode = 3;
        }
        SettlementCtx ctx = new SettlementCtx();
        ctx.setUserId(userId);
        ctx.setStoresId(verifyStoresId);
        ctx.setMode(mode);
        ctx.setCode(request.getCode());
        ctx.setWithDraw(false);
        Response.Code status = this.orderCodeRedeemService.settlement(ctx);
        if (Response.Code.SUCCESS.equals(status)) {
            return Responses.ok(response);
        }

        //将PHP版本的404拆成了以下4个错误
        if (Response.Code.ST_NOT_APPLICABLE.equals(status)
                || Response.Code.ST_FORBIDDEN.equals(status)
                || Response.Code.ST_NOT_MATCH.equals(status)
                || Response.Code.ST_STORE_NOT_EXIST.equals(status)) {
            throw getBusinessException("message.redeem.verification_failure");
        }

        //核销失败
        if (Response.Code.ST_VERIFICATION_UNSUCCESSFUL.equals(status)) {
            throw getBusinessException("message.redeem.verification_failure");
        }

        //該券碼已核銷
        if (Response.Code.ST_CONSUMED.equals(status)) {
            throw getBusinessException("message.redeem.code_verification");
        }

        //該券碼不存在
        if (Response.Code.ST_NOT_EXIST.equals(status)) {
            throw getBusinessException("message.redeem.the_code_does");
        }

        //系統出現錯誤,請聯繫管理員
        if (Response.Code.UNKNOWN_ERROR.equals(status)) {
            throw getBusinessException("message.redeem.system_error");
        }

        //福利券已過期
        if (Response.Code.ST_EXPIRED.equals(status)) {
            throw getBusinessException("message.redeem.product_code_overdue");
        }

        //未到可使用時間
        if (Response.Code.ST_NOT_STARTED.equals(status)) {
            throw getBusinessException("message.redeem.time_available");
        }

        //已过期
        if (Response.Code.ST_REFUND_STATUS.equals(status)) {
            throw getBusinessException("message.redeem.refunded");
        }

        //已失效
        if (Response.Code.ST_CODE_INVALID.equals(status)) {
            throw getBusinessException("message.redeem.status_invalid");
        }

        //當前時間不可用
        if (Response.Code.ST_NOT_AVAILABLE_TIME.equals(status)) {
            throw getBusinessException("message.redeem.time_notuse");
        }

        //券規則不可用
        if (Response.Code.ST_CODE_STOP_USE.equals(status)) {
            throw getBusinessException("message.redeem.code_stop_use");
        }

        return Responses.ok(response);
    }


    /**
     * 更新券码状态
     *
     * @return
     */
    @GetMapping(value = "/api/auth/updatecode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<List<UpdateCouponCodeResponse>> updateCode(@Valid UpdateCouponCodeRequest request) {
        return orderService.updateCode(request);
    }

    /**
     * 提交退款申请
     *
     * @return
     */
    @GetMapping(value = "/api/auth/refundHandling", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<RefundHandlingResponse> refundHandling(@Valid RefundHandlingRequest request) {
        orderService.updRefundHandling(request);
        return Responses.ok(new RefundHandlingResponse());
    }

    /**
     * 提交退款申请
     *
     * @return
     */
    @PostMapping(value = "/api/auth/refundHandling/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<RefundHandlingResponse> refundHandlingV2(@Valid @RequestBody RefundHandlingRequest request) {
        orderService.updRefundHandling(request);
        return Responses.ok(new RefundHandlingResponse());
    }

    /**
     * 获取退款券详情
     *
     * @return
     */
    @GetMapping(value = "/api/auth/refundCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<RefundCodeResponse> getRefundCode(@Valid RefundCodeRequest request) {
        request.convertWsgToGsj();
        RefundCodeResponse response = orderService.getRefundCode(request);
        return Responses.ok(response);
    }


    private BusinessException getBusinessException(String code) {
        Locale locale = LocaleContextHolder.getLocale();
        return new BusinessException(Response.Code.BAD_REQUEST, messageSource.getMessage(code, null, locale));
    }
}
