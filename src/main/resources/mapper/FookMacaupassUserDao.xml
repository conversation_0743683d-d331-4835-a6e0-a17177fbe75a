<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMacaupassUserDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMacaupassUser">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="openid" jdbcType="VARCHAR" property="openid" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken" />
    <result column="point" jdbcType="INTEGER" property="point" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="customid" jdbcType="VARCHAR" property="customid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, openid, access_token, refresh_token, point, phone, area, `status`, create_time,
    update_time, code, customid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_macaupass_user
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_macaupass_user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMacaupassUser">
    insert into fook_macaupass_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="openid != null">
        openid,
      </if>
      <if test="accessToken != null">
        access_token,
      </if>
      <if test="refreshToken != null">
        refresh_token,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="customid != null">
        customid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=INTEGER},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="customid != null">
        #{customid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMacaupassUser">
    update fook_macaupass_user
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        refresh_token = #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=INTEGER},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="customid != null">
        customid = #{customid,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMacaupassUser">
    update fook_macaupass_user
    set user_id = #{userId,jdbcType=INTEGER},
      openid = #{openid,jdbcType=VARCHAR},
      access_token = #{accessToken,jdbcType=VARCHAR},
      refresh_token = #{refreshToken,jdbcType=VARCHAR},
      point = #{point,jdbcType=INTEGER},
      phone = #{phone,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      code = #{code,jdbcType=VARCHAR},
      customid = #{customid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>