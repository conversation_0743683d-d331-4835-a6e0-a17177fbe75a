package com.mcoin.mall.constant;

/**
 * 退款响应码枚举
 */
public enum RefundResponseCodeEnum {

    /**
     * 退款失败
     */
    REFUND_FAILED(101, "退款失败"),
    
    /**
     * 退款成功
     */
    REFUND_SUCCESS(200, "退款成功");

    private final int code;
    private final String description;

    RefundResponseCodeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根據代碼獲取描述
     *
     * @param code 代碼
     * @return 描述
     */
    public static String getDescriptionByCode(int code) {
        for (RefundResponseCodeEnum status : RefundResponseCodeEnum.values()) {
            if (status.getCode() == code) {
                return status.getDescription();
            }
        }
        return "";
    }
}
