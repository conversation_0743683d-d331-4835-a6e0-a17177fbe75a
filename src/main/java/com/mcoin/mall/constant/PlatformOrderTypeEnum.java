package com.mcoin.mall.constant;


import lombok.Getter;

/***
 * 订单类型
 * @author: la<PERSON><PERSON><PERSON>
 * @date 2023-10-20
 */
@Getter
public enum PlatformOrderTypeEnum {

    /**
     * 订单类型枚举
     */
    WELFARE_ORDER(1, "招牌產品"),
    RESERVE_ORDER(2, "新優惠"),
    SPECIAL_BENEFITS(3, "現金券"),
    MINI_ORDER(4, "实物產品"),
    ;

    /**
     * 订单类型
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    PlatformOrderTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;

    }
}
