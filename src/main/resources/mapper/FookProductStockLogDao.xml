<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookProductStockLogDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookProductStockLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="pe" jdbcType="BIT" property="pe" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="now_stock" jdbcType="VARCHAR" property="nowStock" />
    <result column="stock" jdbcType="VARCHAR" property="stock" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, product_id, pe, num, now_stock, stock, ip, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_product_stock_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_product_stock_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookProductStockLog">
    insert into fook_product_stock_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="pe != null">
        pe,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="nowStock != null">
        now_stock,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="pe != null">
        #{pe,jdbcType=BIT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="nowStock != null">
        #{nowStock,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookProductStockLog">
    update fook_product_stock_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="pe != null">
        pe = #{pe,jdbcType=BIT},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="nowStock != null">
        now_stock = #{nowStock,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookProductStockLog">
    update fook_product_stock_log
    set user_id = #{userId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=INTEGER},
      pe = #{pe,jdbcType=BIT},
      num = #{num,jdbcType=VARCHAR},
      now_stock = #{nowStock,jdbcType=VARCHAR},
      stock = #{stock,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>