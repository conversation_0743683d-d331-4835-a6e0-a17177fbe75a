-- Insert test after-sale log
INSERT INTO `ls_after_sale_log` (
    `id`, `after_sale_id`, `operator_role`, `operator_id`, `content`,
    `create_time`, `update_time`, `sid`
) VALUES (
             1, 1, 3, 1, '买家申请退款',
             1741942325, 1741942325, 65
         );



-- 插入售后日志
INSERT INTO `ls_after_sale_log` (
    `id`, `after_sale_id`, `content`, `operator_id`, `operator_role`,
    `sid`, `create_time`, `update_time`
) VALUES (
             11, 11, '买家发起商品售后,等待卖家同意', 1, 1,
             65, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
         );