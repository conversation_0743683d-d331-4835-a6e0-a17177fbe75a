package com.mcoin.mall.controller.show;

import com.mcoin.mall.model.*;
import com.mcoin.mall.service.show.ShowZoneProductService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/***
 * @author: laijinjie
 * @date 2023-10-20
 */
@RestController
@RequestMapping("/api/show")
public class ShowZoneController {

    @Resource
    private ShowZoneProductService showZoneProductService;

    /**
     * 查询专场-专区-福利商品信息
     *
     * @return
     */
    /**
     * 查询专场-专区-福利商品信息
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 专场-专区-福利商品响应
     */
    @GetMapping(value = "/zone/product")
    @ApiOperation(value = "查询专场-专区-福利商品", notes = "根据专场ID查询关联的专区及福利商品信息")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<ShowResponse> queryByShowId(@Valid ShowZoneProductRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        ShowResponse showResponse = showZoneProductService.queryShowZoneProductByShowId(request,clientType);
        return Responses.ok(showResponse);
    }

    /**
     * 查询专场信息
     *
     * @return
     */
    @GetMapping(value = "/info")
    public Response<ShowInfoResponse> queryByShowInfo(@Valid String showId) {
        ShowInfoResponse showInfoResponse = showZoneProductService.queryShowInfoByShowId(showId);
        return Responses.ok(showInfoResponse);
    }



    /**
     * 查询H5专场本期搶購
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 搶購响应
     */
    @GetMapping(value = "indexsnapup", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取H5专场本期搶購", notes = "获取H5专场本期搶購商品列表")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<SnapUpResponse> snapUp(@Valid ShowSnapUpRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        SnapUpResponse response = showZoneProductService.getSnapUp(request,clientType);
        return Responses.ok(response);
    }

    /**
     * 查询H5专场搶購下期精选
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 搶購下期精选响应
     */
    @GetMapping(value = "snapupnext", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取H5专场搶購下期精选", notes = "获取H5专场下期搶購商品列表")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<SnapUpNextResponse> snapUpNext(@Valid ShowSnapUpNextRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        SnapUpNextResponse response = showZoneProductService.getSnapUpNext(request,clientType);
        return Responses.ok(response);
    }
}
