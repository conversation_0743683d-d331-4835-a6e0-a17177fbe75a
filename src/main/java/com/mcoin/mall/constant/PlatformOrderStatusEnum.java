package com.mcoin.mall.constant;


import lombok.Getter;

/***
 * 订单表状态枚举
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date 2023-10-20
 */
@Getter
public enum PlatformOrderStatusEnum {

    /**
     * 订单表状态枚举
     */
    UNPAID(1, "未付款"),
    PAID(2, "已付款"),
    INVALID_ORDER(3, "失效订单"),
    REFUND_ORDER(4, "退款订单"),
    ;

    /**
     * 支付状态
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;

    PlatformOrderStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
