package com.mcoin.mall.unit;

import com.mcoin.mall.controller.business.AsiaMilesController;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.AsiaMilesFromRequest;
import com.mcoin.mall.model.AsiaMilesUserInfoListResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.service.business.AsiaMilesService;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.context.MessageSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class AsiaMilesControllerUnitTest extends ControllerMock {
    @Mock
    AsiaMilesService asiaMilesService;
    
    @InjectMocks
    AsiaMilesController asiaMilesController;
    
    @Mock
    MessageSource messageSource;

    @Test
    void testGetUserInfo() {
        // given
        AsiaMilesUserInfoListResponse list = new AsiaMilesUserInfoListResponse();
        when(asiaMilesService.getAsiaMilesList()).thenReturn(list);

        // when
        Response<AsiaMilesUserInfoListResponse> result = asiaMilesController.getUserInfo();

        // then
        assertEquals(200, result.getCode());
    }

    @ParameterizedTest
    @MethodSource("judgeAsiaMilesTestCases")
    void testJudgeAsiaMiles(AsiaMilesFromRequest req, String expectedExceptionMsg) {
        // given
        when(messageSource.getMessage(any(), any(), any())).thenAnswer(invocation -> invocation.getArguments()[0]);
        
        // when/then
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> asiaMilesController.judgeAsiaMiles(req));
        assertEquals(expectedExceptionMsg, exception.getMessage());
    }
    
    static Stream<Arguments> judgeAsiaMilesTestCases() {
        return Stream.of(
            Arguments.of(getAsiaMilesFromRequest(), "message.ordersaveinfo.miles_member"),
            Arguments.of(getMilesMemberRepeat(), "message.ordersaveinfo.miles_member_repeat"),
            Arguments.of(getMilesMemberFromJson("no_miles_member"), "message.ordersaveinfo.no_miles_member"),
            Arguments.of(getMilesMemberFromJson("memberValid"), "message.ordersaveinfo.miles_member_verification")
        );
    }
    
    private static AsiaMilesFromRequest getAsiaMilesFromRequest() {
        return new AsiaMilesFromRequest();
    }
    
    private static AsiaMilesFromRequest getMilesMemberRepeat() {
        AsiaMilesFromRequest req = new AsiaMilesFromRequest();
        req.setMiles_member("1");
        return req;
    }
    
    private static AsiaMilesFromRequest getMilesMemberFromJson(String caseName) {
        return JsonTestUtils.getObjectFromJson("json/asiaMiles.json", caseName, AsiaMilesFromRequest.class);
    }
} 