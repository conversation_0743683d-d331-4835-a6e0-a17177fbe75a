package com.mcoin.mall.mp.service.selffechshop;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.mcoin.mall.mp.constant.DeliveryMethod;
import com.mcoin.mall.mp.constant.OrderStatus;
import com.mcoin.mall.mp.constant.YesNo;
import com.mcoin.mall.mp.entity.LsSelffetchShop;
import com.mcoin.mall.mp.mapper.LsSelffetchShopMapper;
import com.mcoin.mall.mp.util.DateTimeUtil;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

public class SelffechShopServiceImplTest{

    @Mock
    private LsSelffetchShopMapper selffechShopMapper;

    @InjectMocks
    private SelffechShopServiceImpl selffechShopService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetEstimatePickupDayReturnsNullWhenLsSelffetchShopIsNull() {
        Date orderDate = DateTimeUtil.parse("2023-01-01", "yyyy-MM-dd");
        Integer selffetchShopId = 1;
        Integer sid = 1;
    
        when(selffechShopMapper.selectOne(any())).thenReturn(null);
    
        Date result = selffechShopService.getEstimatePickupDay(DeliveryMethod.STORE_PICKUP.getCode(),
                OrderStatus.WAIT_DELIVERY.getCode(),
                orderDate, selffetchShopId, sid);
    
        assertNull(result);
        verify(selffechShopMapper, times(1)).selectOne(any());
    }

    @Test
    void testGetEstimatePickupDayReturnsNullWhenPickupDaysIsZero() {
        Date orderDate = DateTimeUtil.parse("2023-01-01", "yyyy-MM-dd");
        Integer selffetchShopId = 1;
        Integer sid = 1;
    
        LsSelffetchShop lsSelffetchShop = new LsSelffetchShop();
        lsSelffetchShop.setPickupDays(0);
    
        when(selffechShopMapper.selectOne(any())).thenReturn(lsSelffetchShop);
    
        Date result = selffechShopService.getEstimatePickupDay(DeliveryMethod.STORE_PICKUP.getCode(),
                OrderStatus.WAIT_DELIVERY.getCode(), orderDate, selffetchShopId, sid);
    
        assertNull(result);
        verify(selffechShopMapper, times(1)).selectOne(any());
    }

    @Test
    void testGetEstimatePickupDayReturnsPickupDay_InWeek() {
        Date orderDate = DateTimeUtil.parse("2025-01-01 14:00:00", DateTimeUtil.DEFAULT_PATTERN);
        Integer selffetchShopId = 1;
        Integer sid = 1;

        LsSelffetchShop lsSelffetchShop = new LsSelffetchShop();
        lsSelffetchShop.setPickupDays(3);
        lsSelffetchShop.setWeekdays("4,5,6");
        lsSelffetchShop.setBusinessStartTime("09:00:00");
        lsSelffetchShop.setStatus(YesNo.YES.getValue());

        when(selffechShopMapper.selectOne(any())).thenReturn(lsSelffetchShop);

        Date result = selffechShopService.getEstimatePickupDay(DeliveryMethod.STORE_PICKUP.getCode(),
                OrderStatus.WAIT_DELIVERY.getCode(), orderDate, selffetchShopId, sid);

        // Assuming the expected result is a date object
        // You can adjust the assertion based on the expected result
        assertNotNull(result);
        verify(selffechShopMapper, times(1)).selectOne(any());
        assertEquals(DateTimeUtil.format(result), "2025-01-04 09:00:00");
    }

    @Test
    void testGetEstimatePickupDayReturnsPickupDay_AcrossTwoWeek() {
        Date orderDate = DateTimeUtil.parse("2025-01-01 14:00:00", DateTimeUtil.DEFAULT_PATTERN);
        Integer selffetchShopId = 1;
        Integer sid = 1;
    
        LsSelffetchShop lsSelffetchShop = new LsSelffetchShop();
        lsSelffetchShop.setPickupDays(3);
        lsSelffetchShop.setWeekdays("1,2,3");
        lsSelffetchShop.setBusinessStartTime("09:00:00");
        lsSelffetchShop.setStatus(YesNo.YES.getValue());
    
        when(selffechShopMapper.selectOne(any())).thenReturn(lsSelffetchShop);
    
        Date result = selffechShopService.getEstimatePickupDay(DeliveryMethod.STORE_PICKUP.getCode(),
                OrderStatus.WAIT_DELIVERY.getCode(), orderDate, selffetchShopId, sid);
    
        // Assuming the expected result is a date object
        // You can adjust the assertion based on the expected result
        assertNotNull(result);
        verify(selffechShopMapper, times(1)).selectOne(any());
        assertEquals(DateTimeUtil.format(result), "2025-01-08 09:00:00");
    }


    @Test
    void testGetEstimatePickupDayReturnsPickupDay_AcrossTwoWeek2() {
        Date orderDate = DateTimeUtil.parse("2025-01-01 14:00:00", DateTimeUtil.DEFAULT_PATTERN);
        Integer selffetchShopId = 1;
        Integer sid = 1;

        LsSelffetchShop lsSelffetchShop = new LsSelffetchShop();
        lsSelffetchShop.setPickupDays(3);
        lsSelffetchShop.setWeekdays("1,3,4");
        lsSelffetchShop.setBusinessStartTime("09:00:00");
        lsSelffetchShop.setStatus(YesNo.YES.getValue());

        when(selffechShopMapper.selectOne(any())).thenReturn(lsSelffetchShop);

        Date result = selffechShopService.getEstimatePickupDay(DeliveryMethod.STORE_PICKUP.getCode(),
                OrderStatus.WAIT_DELIVERY.getCode(), orderDate, selffetchShopId, sid);

        // Assuming the expected result is a date object
        // You can adjust the assertion based on the expected result
        assertNotNull(result);
        verify(selffechShopMapper, times(1)).selectOne(any());
        assertEquals(DateTimeUtil.format(result), "2025-01-08 09:00:00");
    }

}