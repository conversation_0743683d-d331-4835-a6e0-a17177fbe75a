package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.*;
import com.mcoin.mall.bo.ActiveZoneProductBo;
import com.mcoin.mall.bo.ActiveZoneStoreBo;
import com.mcoin.mall.bo.StoreDistanceBo;
import com.mcoin.mall.bo.StoreTypeProductsBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.model.*;
import com.mcoin.mall.model.StoreTypeResponse.SnatchListItem;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.service.job.FeedsJobService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.OssUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.*;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class IndexServiceImplTest{

    @Mock
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Mock
    private FookActiveZoneDao fookActiveZoneDao;

    @Mock
    private FookPlatformSuggestDao fookPlatformSuggestDao;

    @Mock
    private FookStoresKeywordDao fookStoresKeywordDao;

    @Mock
    private FookBannerDao fookBannerDao;

    @Mock
    private FookStoresTypeDao fookStoresTypeDao;

    @Mock
    private SettingsDao settingsDao;

    @Mock
    private FeedsJobService feedsJobService;

    @InjectMocks
    private IndexServiceImpl indexService;

    private Locale locale;

    @Mock
    private ContextHolder contextHolder;
    private MockedStatic<ConfigUtils> configUtilsMock;

    @Mock private CollectService collectService;


    @BeforeEach
    public void setUp() {
        configUtilsMock = mockStatic(ConfigUtils.class);
    }

    @Mock private RedissonClient redissonClient;

    @Mock private FookStoresDao fookStoresDao;

    @AfterEach
    public void shutdown() {
        configUtilsMock.close();
    }

    @Test
    public void testGetStoresType_English() {
        locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);
        // Mock data
        FookStoresType type1 = new FookStoresType();
        type1.setId(1);
        type1.setIcon("icon1");
        type1.setName("Type 1");
        type1.setEnglishName("Type 1 English");
        type1.setSort(1);
    
        FookStoresType type2 = new FookStoresType();
        type2.setId(2);
        type2.setIcon("icon2");
        type2.setName("Type 2");
        type2.setEnglishName("Type 2 English");
        type2.setSort(2);
    
        when(fookStoresTypeDao.getStoresTypes()).thenReturn(Arrays.asList(type1, type2));
        when(settingsDao.getValueDirect("site.feeds_recommend_tab_switch")).thenReturn("1");
        when(feedsJobService.getRecommendTabDataSize("")).thenReturn(1);

        // Execute the method
        StoreTypeResponse response = indexService.getStoresType();
    
        // Verify the results
        List<StoreTypeResponse.SnatchListItem> snatchList = response.getSnatchList();
        assertEquals(2, snatchList.size());
    
        assertEquals(1, snatchList.get(0).getId());
        assertEquals("Type 1 English", snatchList.get(0).getName());
        assertEquals(OssUtil.initOssImage("icon1"), snatchList.get(0).getImg());
        assertEquals(1, snatchList.get(0).getSort());
    
        assertEquals(2, snatchList.get(1).getId());
        assertEquals("Type 2 English", snatchList.get(1).getName());
        assertEquals(OssUtil.initOssImage("icon2"), snatchList.get(1).getImg());
        assertEquals(2, snatchList.get(1).getSort());
    
        assertEquals(true, response.getRecommend());

    }

    @Test
    public void testGetIndexPoster() {
        // Mock data
        FookBanner banner1 = new FookBanner();
        banner1.setId(1);
        banner1.setBroadcastEndTime(new Date(System.currentTimeMillis() + 10000));
        banner1.setBroadcastStartTime(new Date(System.currentTimeMillis() - 10000));
        banner1.setIsBroadcast(1);
    
        FookBanner banner2 = new FookBanner();
        banner2.setId(2);
        banner2.setBroadcastEndTime(new Date(System.currentTimeMillis() + 10000));
        banner2.setBroadcastStartTime(new Date(System.currentTimeMillis() - 10000));
        banner2.setIsBroadcast(1);
    
        FookBanner banner3 = new FookBanner();
        banner3.setId(3);
        banner3.setBroadcastEndTime(new Date(System.currentTimeMillis() + 10000));
        banner3.setBroadcastStartTime(new Date(System.currentTimeMillis() - 10000));
        banner3.setIsBroadcast(0);
    
        when(fookBannerDao.getIndexBanners()).thenReturn(Arrays.asList(banner1, banner2, banner3));
    
        // Execute the method
        List<FookBanner> result = indexService.getIndexPoster();
    
        // Verify the results
        assertEquals(3, result.size());
        assertEquals(1, result.get(0).getId());
        assertEquals(2, result.get(1).getId());
    }

    @Test
    public void testGetStoreKeyWords() {
        // Mock data
        FookStoresKeyword keyword1 = new FookStoresKeyword();
        keyword1.setId(1);
        keyword1.setSrcType(5);
        keyword1.setName("keyword1");
    
        FookStoresKeyword keyword2 = new FookStoresKeyword();
        keyword2.setId(2);
        keyword2.setSrcType(6);
        keyword2.setName("keyword2");
    
        FookStoresKeyword keyword3 = new FookStoresKeyword();
        keyword3.setId(3);
        keyword3.setSrcType(null);
        keyword3.setName("keyword3");
    
        List<FookStoresKeyword> keywords = Arrays.asList(keyword1, keyword2, keyword3);
    
        // Mock the DAO layer
        when(fookStoresKeywordDao.getStoreKeyWords()).thenReturn(keywords);
    
        // Execute the method
        List<FookStoresKeyword> result = indexService.getStoreKeyWords();
    
        // Verify the results
        assertEquals(2, result.size());
        assertEquals("keyword1", result.get(0).getName());
        assertEquals("keyword2", result.get(1).getName());
    }

    @Test
    public void testGetIndexPopularSearchesEmptyList() {

        // Mock data
        when(fookPlatformSuggestDao.getIndexPopularSearches()).thenReturn(Collections.emptyList());
    
        // Execute the method
        List<FookPlatformSuggest> result = indexService.getIndexPopularSearches();
    
        // Verify the results
        assertEquals(0, result.size());
    }

    @Test
    public void testGetIndexPopularSearchesNonEmptyList() {

        // Mock data
        FookPlatformSuggest suggest1 = new FookPlatformSuggest();
        suggest1.setId(1);

        FookPlatformSuggest suggest2 = new FookPlatformSuggest();
        suggest2.setId(2);

        List<FookPlatformSuggest> expectedSuggests = Arrays.asList(suggest1, suggest2);
        when(fookPlatformSuggestDao.getIndexPopularSearches()).thenReturn(expectedSuggests);
    
        // Execute the method
        List<FookPlatformSuggest> result = indexService.getIndexPopularSearches();
    
        // Verify the results
        assertEquals(2, result.size());
        assertEquals(suggest1, result.stream().filter(suggest -> Objects.equals(suggest1.getId(), suggest.getId())).collect(Collectors.toList()).get(0));
        assertEquals(suggest2, result.stream().filter(suggest -> Objects.equals(suggest2.getId(), suggest.getId())).collect(Collectors.toList()).get(0));
    }

    @Test
    public void testGetIndexPopularSearchesShuffle() {

        // Mock data
        FookPlatformSuggest suggest1 = new FookPlatformSuggest();
        suggest1.setId(1);

        FookPlatformSuggest suggest2 = new FookPlatformSuggest();
        suggest2.setId(2);

        List<FookPlatformSuggest> expectedSuggests = Arrays.asList(suggest1, suggest2);
        when(fookPlatformSuggestDao.getIndexPopularSearches()).thenReturn(expectedSuggests);
    
        // Execute the method
        List<FookPlatformSuggest> result = indexService.getIndexPopularSearches();
    
        // Verify the results
        assertEquals(2, result.size());
        assertEquals(suggest1, result.stream().filter(suggest -> Objects.equals(suggest1.getId(), suggest.getId())).collect(Collectors.toList()).get(0));
        assertEquals(suggest2, result.stream().filter(suggest -> Objects.equals(suggest2.getId(), suggest.getId())).collect(Collectors.toList()).get(0));
    }

    @Test
    public void testGetActiveZoneEnglish() {
        Locale locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);
    
        // Mock data
        FookActiveZone zone1 = new FookActiveZone();
        zone1.setId(1);
        zone1.setName("Zone 1");
        zone1.setNameEn("Zone 1 English");
        zone1.setType(1);
        zone1.setStatus(1);
    
        FookActiveZone zone2 = new FookActiveZone();
        zone2.setId(2);
        zone2.setName("Zone 2");
        zone2.setNameEn("Zone 2 English");
        zone2.setType(2);
        zone2.setStatus(2);
    
        when(fookActiveZoneDao.getActiveZones()).thenReturn(Arrays.asList(zone1, zone2));
    
        // Execute the method
        List<ZoneTypeResponse> result = indexService.getActiveZone();
    
        // Verify the results
        assertEquals(2, result.size());
    
        ZoneTypeResponse response1 = result.get(0);
        assertEquals("1", response1.getZoneId());
        assertEquals("Zone 1 English", response1.getName());
        assertEquals("1", response1.getType());
        assertEquals("1", response1.getStatus());
    
        ZoneTypeResponse response2 = result.get(1);
        assertEquals("2", response2.getZoneId());
        assertEquals("Zone 2 English", response2.getName());
        assertEquals("2", response2.getType());
        assertEquals("2", response2.getStatus());
    }

    @Test
    public void testGetActiveZoneNonEnglish() {
        // Mock data
        Locale locale = new Locale("zh", "CN");
        when(contextHolder.getLocale()).thenReturn(locale);
    
        FookActiveZone zone1 = new FookActiveZone();
        zone1.setId(1);
        zone1.setName("Zone 1");
        zone1.setNameEn("Zone 1 English");
        zone1.setType(1);
        zone1.setStatus(1);
    
        FookActiveZone zone2 = new FookActiveZone();
        zone2.setId(2);
        zone2.setName("Zone 2");
        zone2.setNameEn("Zone 2 English");
        zone2.setType(2);
        zone2.setStatus(2);
    
        when(fookActiveZoneDao.getActiveZones()).thenReturn(Arrays.asList(zone1, zone2));
    
        // Execute the method
        List<ZoneTypeResponse> result = indexService.getActiveZone();
    
        // Verify the results
        assertEquals(2, result.size());
    
        ZoneTypeResponse response1 = result.get(0);
        assertEquals("1", response1.getZoneId());
        assertEquals("Zone 1", response1.getName());
        assertEquals("1", response1.getType());
        assertEquals("1", response1.getStatus());
    
        ZoneTypeResponse response2 = result.get(1);
        assertEquals("2", response2.getZoneId());
        assertEquals("Zone 2", response2.getName());
        assertEquals("2", response2.getType());
        assertEquals("2", response2.getStatus());
    }

    @Test
    public void testGetActiveZoneProductSuccess() {
        // Mock data
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setLot("123.456");
        request.setLat("789.012");
    
        ActiveZoneProductBo product1 = new ActiveZoneProductBo();
        product1.setPid(1);
        product1.setTitle("Product 1");
        product1.setImg("image1.jpg");
        product1.setPrice(100.0);
        product1.setRetailPrice("90.0");
        product1.setMinPoint(10);
        product1.setPointRatio(2);
        product1.setOnlyPoint(1);
        product1.setSystemType(1);
        product1.setHrefUrl("http://example.com/product_id=");
        product1.setGoodsId(1);
        product1.setType(12);
    
        List<ActiveZoneProductBo> productList = Arrays.asList(product1);
        when(fookActiveZoneDao.getActiveZoneProducts(request.getType(), request.getZonne_id(), request.isShowLikeText(), false)).thenReturn(productList);
    
        ActiveZoneStoreBo storeBo = new ActiveZoneStoreBo();
        storeBo.setStoreName("Store 1");
        Map<Integer, ActiveZoneStoreBo> storeBoMap = new HashMap<>();
        storeBoMap.put(1, storeBo);
        when(fookActiveZoneDao.getActiveZoneStores(anyList())).thenReturn(storeBoMap);
        StoreDistanceBo storeDistanceBo = new StoreDistanceBo();
        storeDistanceBo.setStoreName("Store 1");
        List<StoreDistanceBo> storeDistanceList = Arrays.asList(storeDistanceBo);
        when(fookBusinessStoreProductDao.getStoreDistanceListByProductId(anyInt())).thenReturn(storeDistanceList);
        when(ConfigUtils.getProperty("macaupass.romote_url", "")).thenReturn("http://example.com");
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl")).thenReturn("http://example.com/");

        // Execute the method
        List<RecommendZoneResponse> responseList = indexService.getActiveZoneProduct(request, ClientTypeEnum.HARMONY.toString());
    
        // Verify the results
        assertEquals(1, responseList.size());
        RecommendZoneResponse response = responseList.get(0);
        assertEquals("1", response.getId());
        assertEquals("Product 1", response.getName());
        assertEquals("http://example.com/image1.jpg", response.getImg());
        assertEquals("100.0", response.getPrice());
        assertEquals("90.0", response.getRetailPrice());
        assertEquals("0.0", response.getPreferential());
        assertEquals("Store 1", response.getStoreName());
        assertEquals("1", response.getType());
        assertEquals(12, response.getProductType());
        assertEquals("http://example.com/product_id=1", response.getHrefUrl());
    }

    @Test
    public void testGetActiveZoneProductEmptyProductList() {
        // Mock data
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setLot("123.456");
        request.setLat("789.012");
    
        when(fookActiveZoneDao.getActiveZoneProducts(request.getType(), request.getZonne_id(), request.isShowLikeText(), true)).thenReturn(Collections.emptyList());
    
        // Execute the method
        List<RecommendZoneResponse> responseList = indexService.getActiveZoneProduct(request, ClientTypeEnum.HARMONY.toString());
    
        // Verify the results
        assertEquals(0, responseList.size());
    }

    @Test
    public void testGetActiveZoneProductNoStoreName() {
        // Mock data
        RecommendZoneRequest request = new RecommendZoneRequest();
    
        ActiveZoneProductBo product1 = new ActiveZoneProductBo();
        product1.setPid(1);
        product1.setTitle("Product 1");
        product1.setImg("image1.jpg");
        product1.setPrice(100.0);
        product1.setRetailPrice("90.0");
        product1.setMinPoint(10);
        product1.setPointRatio(2);
        product1.setOnlyPoint(1);
        product1.setSystemType(1);
        product1.setType(1);
        product1.setHrefUrl("http://example.com/product_id=");
        product1.setGoodsId(1);
    
        List<ActiveZoneProductBo> productList = Arrays.asList(product1);
        when(fookActiveZoneDao.getActiveZoneProducts(request.getType(), request.getZonne_id(), request.isShowLikeText(), false)).thenReturn(productList);
    
        ActiveZoneStoreBo storeBo = new ActiveZoneStoreBo();
        storeBo.setStoreName("Store 1");
        Map<Integer, ActiveZoneStoreBo> storeBoMap = new HashMap<>();
        storeBoMap.put(1, storeBo);
        when(fookActiveZoneDao.getActiveZoneStores(anyList())).thenReturn(storeBoMap);
    
        when(collectService.getProductsCollect(any())).thenReturn(null);
        doNothing().when(collectService).setCollectNumber(any(), any(), any(), any(Boolean.class));
        when(ConfigUtils.getProperty("macaupass.romote_url", "")).thenReturn("http://example.com");
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl")).thenReturn("http://example.com/");

        // Execute the method
        List<RecommendZoneResponse> responseList = indexService.getActiveZoneProduct(request, ClientTypeEnum.HARMONY.toString());
    
        // Verify the results
        assertEquals(1, responseList.size());
        RecommendZoneResponse response = responseList.get(0);
        assertEquals("1", response.getId());
        assertEquals("Product 1", response.getName());
        assertEquals("http://example.com/image1.jpg", response.getImg());
        assertEquals("100.0", response.getPrice());
        assertEquals("90.0", response.getRetailPrice());
        assertEquals("0.0", response.getPreferential());
        assertEquals("Store 1", response.getStoreName());
        assertEquals("1", response.getType());
        assertEquals(1, response.getProductType());
        assertEquals("http://example.com/product_id=", response.getHrefUrl());
    }

    @Test
    public void testGetStoresTypeEnglish() {
        locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);

        // Mock data
        FookStoresType type1 = new FookStoresType();
        type1.setId(1);
        type1.setIcon("icon1");
        type1.setName("Type 1");
        type1.setEnglishName("Type 1 English");
        type1.setSort(1);
    
        FookStoresType type2 = new FookStoresType();
        type2.setId(2);
        type2.setIcon("icon2");
        type2.setName("Type 2");
        type2.setEnglishName("Type 2 English");
        type2.setSort(2);
    
        when(fookStoresTypeDao.getStoresTypes()).thenReturn(Arrays.asList(type1, type2));
        when(settingsDao.getValueDirect("site.feeds_recommend_tab_switch")).thenReturn("1");
        when(feedsJobService.getRecommendTabDataSize("")).thenReturn(1);
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl")).thenReturn("http://example.com/");
    
        // Execute the method
        StoreTypeResponse response = indexService.getStoresType();
    
        // Verify the results
        List<SnatchListItem> snatchList = response.getSnatchList();
        assertEquals(2, snatchList.size());
    
        assertEquals(1, snatchList.get(0).getId());
        assertEquals("Type 1 English", snatchList.get(0).getName());
        assertEquals(OssUtil.initOssImage("icon1"), snatchList.get(0).getImg());
        assertEquals(1, snatchList.get(0).getSort());
    
        assertEquals(2, snatchList.get(1).getId());
        assertEquals("Type 2 English", snatchList.get(1).getName());
        assertEquals(OssUtil.initOssImage("icon2"), snatchList.get(1).getImg());
        assertEquals(2, snatchList.get(1).getSort());
    
        assertEquals(true, response.getRecommend());
    }

    @Test
    public void testGetShopTypeCacheValid() {
        // Mock the request
        ShopTypeRequest request = new ShopTypeRequest();
        request.setLot("123.456");
        request.setLat("123.456");
        request.setPage(1);
        request.setStart(1);
    

        // Mock the behavior of fookStoresTypeDao
        List<StoreTypeProductsBo> storeTypeProducts = new ArrayList<>();
        StoreTypeProductsBo storeTypeProduct = new StoreTypeProductsBo();
        storeTypeProduct.setPid("1");
        storeTypeProduct.setStoreName("Store 1");
        storeTypeProduct.setTitle("Product 1");
        storeTypeProduct.setImg("img1,img2");
        storeTypeProduct.setPrice("100");
        storeTypeProduct.setRetailPrice("90");
        storeTypeProduct.setSales("100");
        storeTypeProduct.setPoint("50");
        storeTypeProduct.setPreferential("50");
        storeTypeProduct.setOnlyPoint("1");
        storeTypeProduct.setType(1);
        storeTypeProduct.setHrefUrl("href1");
        storeTypeProduct.setGoodsId(1);
        storeTypeProduct.setLongitude("123.456");
        storeTypeProduct.setDimension("123.456");
        storeTypeProducts.add(storeTypeProduct);

        Map<String, Object> productMap = new HashMap<>();

        // Mock PID
        productMap.put("pid", "1");

        // Mock Type ID
        productMap.put("type_id", "1");

        // Mock Business ID
        productMap.put("businessid", "12345");

        // Mock Title
        productMap.put("title", "Mock Product Title");

        // Mock Image URL
        productMap.put("img", "http://example.com/mock-product.jpg");

        // Mock Store ID
        productMap.put("store_id", "67890");

        // Mock Store Name
        productMap.put("store_name", "Mock Store");

        // Mock Sales
        productMap.put("sales", "100");

        // Mock Is Show Sales
        productMap.put("is_show_slaes", "true");

        // Mock Price
        productMap.put("price", "99.99");

        // Mock Retail Price
        productMap.put("retail_price", "129.99");

        // Mock Point
        productMap.put("point", "100");

        // Mock Preferential
        productMap.put("preferential", "Buy one get one free");

        // Mock Min Point
        productMap.put("min_point", "50");

        // Mock Only Point
        productMap.put("only_point", "false");

        // Mock Created At
        productMap.put("created_at", JodaTimeUtil.format(new Date()));

        // Mock Buy Start Time
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        productMap.put("buy_start_time", JodaTimeUtil.format(calendar.getTime()));

        // Mock Buy End Time
        calendar.add(Calendar.DAY_OF_YEAR, 7);
        productMap.put("buy_end_time", JodaTimeUtil.format(calendar.getTime()));

        // Mock Longitude
        productMap.put("longitude", "121.473701");

        // Mock Dimension
        productMap.put("dimension", "121.473701");

        // Mock Type (if needed)
        productMap.put("type", "1");

        // Mock Href URL
        productMap.put("href_url", "http://example.com/mock-product");


        RScoredSortedSet rScoredSortedSet = mock(RScoredSortedSet.class);
        when(redissonClient.getScoredSortedSet(anyString())).thenReturn(rScoredSortedSet);
        when(rScoredSortedSet.valueRangeReversed(any(Integer.class), any(Integer.class))).thenReturn(Arrays.asList("1", "2", "3"));
        when(settingsDao.getValue("site.show_sales_num")).thenReturn(null);
        RMap rMap = mock(RMap.class);
        when(redissonClient.getMap(anyString())).thenReturn(rMap);
        when(rMap.readAllMap()).thenReturn(productMap);
        RGeo rGeo = mock(RGeo.class);
        when(redissonClient.getGeo(anyString())).thenReturn(rGeo);
        when(rGeo.radius(any(Double.class), any(Double.class),  any(Double.class), any(GeoUnit.class), any(GeoOrder.class)))
                .thenReturn(Arrays.asList("1", "2", "3"));

        StoreDistanceBo storeDistanceBo = createStoreDistanceBo(1, "Store A", "121.473701",
                "121.473701", "image.zip", "http://example.com/image.jpg", 10.5);
        when(fookStoresDao.selectStoreDistanceById(anyInt())).thenReturn(storeDistanceBo);

        // Execute the method
        ShopTypeResponse response = indexService.getShopType(request, ClientTypeEnum.HARMONY.toString());

        // Verify the results
        assertEquals(3, response.getSnatchList().size());
    }
    public static StoreDistanceBo createStoreDistanceBo(int storeId, String storeName, String longitude, String dimension, String imgZip, String img, Double distance) {
        StoreDistanceBo storeDistanceBo = new StoreDistanceBo();
        storeDistanceBo.setStoreId(storeId);
        storeDistanceBo.setStoreName(storeName);
        storeDistanceBo.setLongitude(longitude);
        storeDistanceBo.setDimension(dimension);
        storeDistanceBo.setImgZip(imgZip);
        storeDistanceBo.setImg(img);
        storeDistanceBo.setDistance(distance);
        return storeDistanceBo;
    }
}