
# MYSQL
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=false
# master
spring.datasource.dynamic.datasource.master.url=***************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.master.type=com.zaxxer.hikari.HikariDataSource
# slave
spring.datasource.dynamic.datasource.slave.url=***************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.slave.type=com.zaxxer.hikari.HikariDataSource