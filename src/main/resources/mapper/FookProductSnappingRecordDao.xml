<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookProductSnappingRecordDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookProductSnappingRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="redis_id" jdbcType="VARCHAR" property="redisId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="redis_time" jdbcType="TIMESTAMP" property="redisTime" />
    <result column="stock_id" jdbcType="INTEGER" property="stockId" />
    <result column="session_id" jdbcType="INTEGER" property="sessionId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, redis_id, user_id, ip, product_id, `number`, `status`, `type`, created_at, updated_at, 
    complete_time, order_id, redis_time, stock_id, session_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_product_snapping_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_product_snapping_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookProductSnappingRecord">
    insert into fook_product_snapping_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="redisId != null">
        redis_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="redisTime != null">
        redis_time,
      </if>
      <if test="stockId != null">
        stock_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="redisId != null">
        #{redisId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="redisTime != null">
        #{redisTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stockId != null">
        #{stockId,jdbcType=INTEGER},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookProductSnappingRecord">
    update fook_product_snapping_record
    <set>
      <if test="redisId != null">
        redis_id = #{redisId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="redisTime != null">
        redis_time = #{redisTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stockId != null">
        stock_id = #{stockId,jdbcType=INTEGER},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookProductSnappingRecord">
    update fook_product_snapping_record
    set redis_id = #{redisId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      ip = #{ip,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      `number` = #{number,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      `type` = #{type,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      order_id = #{orderId,jdbcType=INTEGER},
      redis_time = #{redisTime,jdbcType=TIMESTAMP},
      stock_id = #{stockId,jdbcType=INTEGER},
      session_id = #{sessionId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>