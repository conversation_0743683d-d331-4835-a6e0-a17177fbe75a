<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductcategoryDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessProductcategory">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="english_name" jdbcType="VARCHAR" property="englishName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="mcoin_platform" jdbcType="TINYINT" property="mcoinPlatform" />
    <result column="mcard_platform" jdbcType="TINYINT" property="mcardPlatform" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_id, `name`, english_name, sort, icon, `enable`, `type`, mcoin_platform,
    mcard_platform
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_productcategory
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_productcategory
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessProductcategory">
    insert into fook_business_productcategory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="englishName != null">
        english_name,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="mcoinPlatform != null">
        mcoin_platform,
      </if>
      <if test="mcardPlatform != null">
        mcard_platform,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="mcoinPlatform != null">
        #{mcoinPlatform,jdbcType=TINYINT},
      </if>
      <if test="mcardPlatform != null">
        #{mcardPlatform,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessProductcategory">
    update fook_business_productcategory
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        english_name = #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="mcoinPlatform != null">
        mcoin_platform = #{mcoinPlatform,jdbcType=TINYINT},
      </if>
      <if test="mcardPlatform != null">
        mcard_platform = #{mcardPlatform,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessProductcategory">
    update fook_business_productcategory
    set parent_id = #{parentId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      english_name = #{englishName,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      icon = #{icon,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      mcoin_platform = #{mcoinPlatform,jdbcType=TINYINT},
      mcard_platform = #{mcardPlatform,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>