<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresTypeTranslationsDao">
  <select id="getTranslationsByStoreTypeIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from fook_stores_type_translations
    where stores_type_id in
    <foreach item="storeTypeId" collection="typeIds" open="(" separator="," close=")">
      #{storeTypeId}
    </foreach>
    and locale = #{language,jdbcType=VARCHAR}
  </select>
</mapper>