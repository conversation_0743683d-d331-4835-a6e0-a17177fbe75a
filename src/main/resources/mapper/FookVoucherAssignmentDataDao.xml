<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookVoucherAssignmentDataDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookVoucherAssignmentData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="voucher_id" jdbcType="INTEGER" property="voucherId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="product_title" jdbcType="VARCHAR" property="productTitle" />
    <result column="product_type" jdbcType="TINYINT" property="productType" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="arrive_time" jdbcType="TIMESTAMP" property="arriveTime" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="user_status" jdbcType="TINYINT" property="userStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, voucher_id, user_id, order_id, order_no, product_id, product_title, product_type, 
    `number`, `status`, area_code, phone, remark, upload_time, send_time, arrive_time, 
    created_at, updated_at, message, user_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_voucher_assignment_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_voucher_assignment_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookVoucherAssignmentData">
    insert into fook_voucher_assignment_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productTitle != null">
        product_title,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="arriveTime != null">
        arrive_time,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="userStatus != null">
        user_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productTitle != null">
        #{productTitle,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arriveTime != null">
        #{arriveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="userStatus != null">
        #{userStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookVoucherAssignmentData">
    update fook_voucher_assignment_data
    <set>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="productTitle != null">
        product_title = #{productTitle,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arriveTime != null">
        arrive_time = #{arriveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="userStatus != null">
        user_status = #{userStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookVoucherAssignmentData">
    update fook_voucher_assignment_data
    set voucher_id = #{voucherId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      product_title = #{productTitle,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=TINYINT},
      `number` = #{number,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      area_code = #{areaCode,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      arrive_time = #{arriveTime,jdbcType=TIMESTAMP},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      message = #{message,jdbcType=VARCHAR},
      user_status = #{userStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>