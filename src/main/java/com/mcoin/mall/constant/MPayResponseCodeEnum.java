package com.mcoin.mall.constant;

/**
 * MPay API 响应码枚举
 */
public enum MPayResponseCodeEnum {

    /**
     * 成功
     */
    SUCCESS("200", "成功"),
    
    /**
     * 失败
     */
    FAIL("500", "失败");

    private final String code;
    private final String description;

    MPayResponseCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根據代碼獲取描述
     *
     * @param code 代碼
     * @return 描述
     */
    public static String getDescriptionByCode(String code) {
        for (MPayResponseCodeEnum status : MPayResponseCodeEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return "";
    }
}
