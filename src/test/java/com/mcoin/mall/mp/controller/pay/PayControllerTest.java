package com.mcoin.mall.mp.controller.pay;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.client.model.MpayPayCreateRequest;
import com.mcoin.mall.mp.client.model.MpayPayQueryRequest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.pay.*;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@Transactional
class PayControllerTest extends BaseUnitTest {

    @Autowired
    private PayController payController;

    @BeforeEach
    public void setUp() {
        doNothing().when(orderSyncTemplate).convertAndSend(Mockito.any());
        try {
            when(maslClient.query(any(MpayPayQueryRequest.class))).thenReturn("{\"data\":{\"buyer_pay_amount\":\"0.00\",\"buyer_user_id\":\"7293246cf89d60e4ea70e15ba\",\"coupon_amount\":\"0\",\"currency\":\"MOP\",\"discount_amount\":\"0.00\",\"merchant_discount_amt\":\"0.00\",\"out_trans_id\":\"ORDER1234567\",\"pay_channel_type\":\"mpay\",\"pay_time\":\"\",\"platform_discount_amt\":\"0.00\",\"point_amount\":\"30.00\",\"points\":\"9000\",\"receipt_amount\":\"0.00\",\"result_code\":\"0000\",\"result_msg\":\"支付失敗\",\"trans_amount\":\"0.00\",\"trans_id\":\"2025031715420920000001\",\"trans_status\":\"FAILED\"},\"is_success\":\"T\",\"sign_type\":\"MD5\",\"sign\":\"65F33102A45BC2CFA1EBC54BF45A0410\"}");
            when(maslClient.create(any(MpayPayCreateRequest.class))).thenReturn("{\"data\":{\"result_code\":\"0051\",\"result_msg\":\"交易已關閉\",\"result_msg_en\":\"The order has been closed.\"},\"is_success\":\"T\",\"sign_type\":\"MD5\",\"sign\":\"D2DF3B95F76002FA23400712F96067F5\"}");
        } catch (BlockException e) {
            e.printStackTrace();
        }
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
    }

    @Test
    void payway_ShouldReturnPayWayResponse() {
        // Arrange
        Integer orderId = 1;
        Integer scene = 1;

        // Act
        Response<PayWayResponse> response = payController.payway(orderId, scene);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void pay_ShouldReturnPayResponse() {
        PayRequest request = new PayRequest();
        request.setOrderId("2");
        request.setPayWay(1);
        request.setFrom("mall");

        // Act
        Response<PayResponse> response = payController.pay(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void payStatus_ShouldReturnPayStatusResponse() {
        // Arrange
        String orderId = "123";
        String from = "mall";

        // Act
        Response<PayStatusResponse> response = payController.payStatus(orderId, from);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void payResult_ShouldReturnPayResultResponse() {
        // Arrange
        String orderId = "123";
        String from = "mall";

        // Act
        Response<PayResultResponse> response = payController.payResult(orderId, from);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }
} 