package com.mcoin.mall.service.chennel.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.*;
import com.mcoin.mall.constant.CouponSynStatus;
import com.mcoin.mall.constant.PlatformOrderStatusEnum;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.mq.model.CouponSyncMessage;
import com.mcoin.mall.service.chennel.MPayChannelService;
import com.mcoin.mall.service.chennel.RefundService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static java.lang.Integer.parseInt;

@Slf4j
@Service
public class RefundServiceImpl implements RefundService {


    @Resource
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Resource
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;
    @Resource
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;
    @Resource
    private FookExternalVcodeDao fookExternalVcodeDao;
    @Resource
    private FookBusinessProductDao fookBusinessProductDao;
    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;
    @Resource
    private FookBusinessProductstockDao fookBusinessProductstockDao;
    @Resource
    private FookProductStockLogDao fookProductStockLogDao;
    @Resource
    private FookPlatformOrderrefundRecordDao fookPlatformOrderrefundRecordDao;
    @Resource
    private MPayChannelService mPayChannelService;

    @Transactional(rollbackFor = Exception.class)
    public void automaticRefunds(Integer id) {
        Date now = new Date();
//php的automaticRefunds功能
        FookPlatformOrderrefund orderRefund = this.fookPlatformOrderrefundDao.selectByPrimaryKey(id);
        FookPlatformOrder order = this.fookPlatformOrderDao.selectByPrimaryKey(orderRefund.getOrderid());
        FookPlatformOrderinfo orderInfo = this.fookPlatformOrderinfoDao.selectOne(new LambdaQueryWrapper<FookPlatformOrderinfo>()
                .eq(FookPlatformOrderinfo::getOrderid, orderRefund.getOrderid()));
        FookBusinessProduct product = this.fookBusinessProductDao.selectByPrimaryKey(orderInfo.getProdcutid());
        List<FookPlatformOrdercode> allOrderCodes = this.fookPlatformOrdercodeDao.selectList(new LambdaQueryWrapper<FookPlatformOrdercode>()
                .eq(FookPlatformOrdercode::getOrderid, orderRefund.getOrderid()));
        Integer refundCodesCnt = 0;
        List<FookPlatformOrdercode> refundOrderCodes = allOrderCodes.stream().filter(o -> ObjectUtil.equals(o.getRefundid(), id)).collect(Collectors.toList());
        refundCodesCnt = refundOrderCodes.size();
        Integer invalidStatus = 3;
        List<FookPlatformOrdercode> invalidCodes = allOrderCodes.stream()
                .filter(o -> ObjectUtil.equals(o.getStatus(), invalidStatus)
                        && ObjectUtil.equals(o.getRefundStatus(), invalidStatus))
                .collect(Collectors.toList());
        //更新orderRefund route
        //券码修改为未绑定状态
        if (null != product.getIsexport() && CollectionUtils.isNotEmpty(refundOrderCodes)) {
            Integer productId = orderInfo.getProdcutid();
            List<String> codes = refundOrderCodes.stream().map(FookPlatformOrdercode::getCode).collect(Collectors.toList());
            log.info("Update the codes to unbind. codes: {}", JSONUtil.toJsonStr(codes));
            FookExternalVcode externalVcode = new FookExternalVcode();
            externalVcode.setIsascription(0);
            externalVcode.setEnable(1);
            this.fookExternalVcodeDao.update(externalVcode, new LambdaQueryWrapper<FookExternalVcode>()
                    .eq(FookExternalVcode::getProductId, productId)
                    .in(FookExternalVcode::getVcode, codes));
        }
        int rt = 0;
        Integer stockCnt = 0;
        //库存
        if (refundCodesCnt > 0) {
            //訂單狀態（1、未付款，2、已付款,3、失效訂單，4、退款訂單）
            Integer status = 2;
            //退款狀態(0 沒退款，1已退款，2部分退款，3子訂單全退款、4異常單退款)
            Integer refundStatus = 2;
            stockCnt = refundCodesCnt;
            if (ObjectUtil.equals(orderInfo.getNumber(), invalidCodes.size())) {
                //全退情况 STATUS=4(退款订单),refundStatus=3(子订单全退)
                status = 4;
                refundStatus = 3;
            }
            //获取订单支付状态，用于判断退款是否增加库存，防止失效订单也退回库存
            FookPlatformOrder updOrder = new FookPlatformOrder();
            updOrder.setStatus(status);
            updOrder.setRefundStatus(refundStatus);
            updOrder.setCompleteTime(now);
            rt = fookPlatformOrderDao.update(updOrder, new LambdaQueryWrapper<FookPlatformOrder>()
                    .eq(FookPlatformOrder::getId, orderRefund.getOrderid())
                    .eq(FookPlatformOrder::getStatus, PlatformOrderStatusEnum.PAID.getStatus())
            );
            if (rt == 1) {
                this.fookBusinessProductDao.updateIncrStock(orderInfo.getProdcutid(), stockCnt);
                this.fookBusinessProductDao.updateDecrSales(orderInfo.getProdcutid(), stockCnt);
                this.fookBusinessProductDao.updateDecrActualSales(orderInfo.getProdcutid(),stockCnt);
            } else if(rt ==0) {
                int invalid_rt = fookPlatformOrderDao.update(updOrder, new LambdaQueryWrapper<FookPlatformOrder>()
                        .eq(FookPlatformOrder::getId, orderRefund.getOrderid())
                        .eq(FookPlatformOrder::getStatus, PlatformOrderStatusEnum.INVALID_ORDER.getStatus())
                );
                log.info("已失效状态变更已退款，但是不退库存,orderid {} ,更新结果 {}",orderRefund.getOrderid(),invalid_rt);
            }
        } else {
            //此情況是單邊賬情況
            //例如下单已付款了，但没领到券ordercode
            Integer status = 4;
            //4(异常单退款)
            Integer refundStatus = 4;
            stockCnt = orderInfo.getNumber();
            FookPlatformOrder updOrder = new FookPlatformOrder();
            updOrder.setStatus(status);
            updOrder.setRefundStatus(refundStatus);
            rt = fookPlatformOrderDao.update(updOrder, new LambdaQueryWrapper<FookPlatformOrder>()
                    .eq(FookPlatformOrder::getId, orderRefund.getOrderid())
                    .eq(FookPlatformOrder::getStatus, PlatformOrderStatusEnum.PAID.getStatus())
            );
            if (rt == 1) {
                this.fookBusinessProductDao.updateIncrStock(orderInfo.getProdcutid(),stockCnt);
            } else if(rt ==0) {
                int invalid_rt = fookPlatformOrderDao.update(updOrder, new LambdaQueryWrapper<FookPlatformOrder>()
                        .eq(FookPlatformOrder::getId, orderRefund.getOrderid())
                        .eq(FookPlatformOrder::getStatus, PlatformOrderStatusEnum.INVALID_ORDER.getStatus())
                );
                log.info("已失效状态变更已退款，但是不退库存,orderid {} ,更新结果 {}",orderRefund.getOrderid(),invalid_rt);
            }
        }
        if (rt == 1) {
            FookBusinessProductstock pstock = new FookBusinessProductstock();
            pstock.setProductid(orderInfo.getProdcutid());
            pstock.setNumber(orderInfo.getNumber());
            //理由Id(1新增初始化庫存,2加減庫存,3取消訂單補庫存,4刪除訂單補庫存,5下單扣庫存,6下單過程中異常補庫存,7取消訂單補庫存,8用戶更改訂單補庫存,9用戶更改訂單扣庫存,99失效訂單補庫存)
            pstock.setType(7);
            pstock.setOperationType(7);
            pstock.setOrderid(0);
            pstock.setUserid(orderRefund.getUserid());
            pstock.setOrderid(orderRefund.getOrderid());
            pstock.setCreateTime(now);
            //庫存是否補迴 1已補迴，0未補迴
            pstock.setStatus(0);
            this.fookBusinessProductstockDao.insertSelective(pstock);
            //防止上批庫存購買的申請退款加回庫存而前端顯示有庫存實際搶不到
            FookProductStockLog stockLog = fookProductStockLogDao.selectFirstOrderById(orderInfo.getProdcutid());
            log.info("防止上批庫存購買的申請退款加回庫存而前端顯示有庫存實際搶不到: {}", stockLog);
            if (stockLog != null) {
                if (order.getCreateTime() != null && stockLog.getCreatedAt() != null) {
                    if (order.getCreateTime().compareTo(stockLog.getCreatedAt()) < 0) {
                        int nowStock = parseInt(stockLog.getNowStock()) + stockCnt;
                        stockLog.setNowStock(nowStock + "");
                        int stk = parseInt(stockLog.getStock()) + stockCnt;
                        stockLog.setStock(stk + "");
                        fookProductStockLogDao.updateByPrimaryKeySelective(stockLog);
                    }
                }
            }
        }
        //插入退款记录表
        FookPlatformOrderrefundRecord oldRecord = fookPlatformOrderrefundRecordDao.selectOne(new LambdaQueryWrapper<FookPlatformOrderrefundRecord>()
                .eq(FookPlatformOrderrefundRecord::getRefundid, id)
                .orderByDesc(FookPlatformOrderrefundRecord::getOperationTime)
                .last("limit 1")
        );
        FookPlatformOrderrefundRecord record = new FookPlatformOrderrefundRecord();
        record.setRefundid(id);
        if (null != oldRecord) {
            record.setOldStatus(oldRecord.getNewStatus());
        }
        record.setNewStatus(6);
        record.setOperationTime(now);
        //已到账是系统操作，无操作管理员id
        record.setOperationType(1);
        record.setOperationId(0);
        this.fookPlatformOrderrefundRecordDao.insertSelective(record);

        //同步券到券平台
        for (FookPlatformOrdercode ordercode : refundOrderCodes) {
            // 插入同步日志
            mPayChannelService.insertCouponSync(order.getId(), ordercode.getId(),
                    CouponSyncMessage.Operation.OrderCode, CouponSynStatus.FAILED, "");
        }
    }
}
