package com.mcoin.mall.service.business.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import com.mcoin.mall.bean.FookClause;
import com.mcoin.mall.bean.FookClauseConfirm;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookClauseConfirmDao;
import com.mcoin.mall.dao.FookClauseDao;
import com.mcoin.mall.model.GetClauseResponse;
import com.mcoin.mall.model.SetClauseRequest;
import com.mcoin.mall.model.SetClauseResponse;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.Date;
import java.util.Locale;

@ExtendWith(MockitoExtension.class)
public class ClauseServiceImplTest{

    @Mock
    private MessageSource messageSource;

    @InjectMocks
    private ClauseServiceImpl clauseService;

    private UserInfo userInfo;

    private Locale locale;

    @Mock
    private ContextHolder contextHolder;

    @Mock
    private FookClauseConfirmDao fookClauseConfirmDao;

    @Mock
    private FookClauseDao fookClauseDao;

    @BeforeEach
    public void setUp() {
        userInfo = new UserInfo();
        userInfo.setUserId(1);
        locale = Locale.US;
    
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(contextHolder.getLocale()).thenReturn(locale);
    }

    @Test
    public void testGetClauseUserHasNotAgreed() {
        FookClause fookClause = new FookClause();
        fookClause.setId(1);
        fookClause.setContentEn("This is a clause");
        fookClause.setCreatedAt(new Date());
    
        when(fookClauseDao.selectLatestClause()).thenReturn(fookClause);
        when(fookClauseConfirmDao.selectClauseConfirm(userInfo.getUserId(), fookClause.getId(), 1)).thenReturn(null);
    
        GetClauseResponse response = clauseService.getClause();
    
        assertEquals(1, response.getStatus());
        assertEquals("Not agreed", response.getMsg());
        assertEquals(String.valueOf(fookClause.getId()), response.getClauseid());
        assertEquals(fookClause.getContentEn(), response.getContent());
        assertEquals(JodaTimeUtil.format4Mcoin(fookClause.getCreatedAt(), "en"), response.getCreate_at());
    }

    @Test
    public void testGetClauseUserHasAgreed() {
        FookClause fookClause = new FookClause();
        fookClause.setId(1);
        fookClause.setContentEn("This is a clause");
        fookClause.setCreatedAt(new Date());
    
        FookClauseConfirm fookClauseConfirm = new FookClauseConfirm();
        fookClauseConfirm.setUserid(userInfo.getUserId());
        fookClauseConfirm.setClauseid(fookClause.getId());
    
        when(fookClauseDao.selectLatestClause()).thenReturn(fookClause);
        when(fookClauseConfirmDao.selectClauseConfirm(userInfo.getUserId(), fookClause.getId(), 1)).thenReturn(fookClauseConfirm);
    
        GetClauseResponse response = clauseService.getClause();
    
        assertEquals(0, response.getStatus());
        assertEquals("agree", response.getMsg());
        assertEquals(String.valueOf(fookClause.getId()), response.getClauseid());
        assertEquals(fookClause.getContent(), response.getContent());
    }

    @Test
    public void testGetClauseNoClauseFound() {
        when(fookClauseDao.selectLatestClause()).thenReturn(null);
    
        GetClauseResponse response = clauseService.getClause();
    
        assertEquals(0, response.getStatus());
        assertEquals("agree", response.getMsg());
        assertEquals(null, response.getClauseid());
        assertEquals(null, response.getContent());
    }

    @Test
    public void testGetClauseLocaleIsChinese() {
        locale = Locale.CHINA;
        when(contextHolder.getLocale()).thenReturn(locale);
    
        FookClause fookClause = new FookClause();
        fookClause.setId(1);
        fookClause.setContent("This is a clause");
        fookClause.setContent("这是一个条款");
        fookClause.setCreatedAt(new Date());
    
        when(fookClauseDao.selectLatestClause()).thenReturn(fookClause);
        when(fookClauseConfirmDao.selectClauseConfirm(userInfo.getUserId(), fookClause.getId(), 1)).thenReturn(null);
    
        GetClauseResponse response = clauseService.getClause();
    
        assertEquals(1, response.getStatus());
        assertEquals("不同意", response.getMsg());
        assertEquals(String.valueOf(fookClause.getId()), response.getClauseid());
        assertEquals(fookClause.getContent(), response.getContent());
        assertEquals(JodaTimeUtil.format4Mcoin(fookClause.getCreatedAt(), "zh"), response.getCreate_at());
    }

    @Test
    public void testSaveClauseUserHasNotAgreed() {
        userInfo = new UserInfo();
        userInfo.setUserId(1);
        locale = Locale.US;
    
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(contextHolder.getLocale()).thenReturn(locale);
        when(messageSource.getMessage(anyString(), any(), any(Locale.class))).thenReturn("Successful");
    
        SetClauseRequest request = new SetClauseRequest();
        request.setStatus("0");
        request.setClauseid("1");
    
        FookClauseConfirm fookClauseConfirm = null;
        when(fookClauseConfirmDao.selectClauseConfirm(userInfo.getUserId(), 1, null)).thenReturn(fookClauseConfirm);
    
        SetClauseResponse response = clauseService.saveClause(request);
    
        verify(fookClauseConfirmDao, times(1)).insertSelective(any(FookClauseConfirm.class));
        assertEquals(McoinMall.RESPONSE_STATUS_SUCCESS, response.getStatus());
        assertEquals("Successful", response.getMsg());
    }

    @Test
    public void testSaveClauseUserHasAlreadyAgreed() {
        userInfo = new UserInfo();
        userInfo.setUserId(1);
        locale = Locale.US;
    
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(contextHolder.getLocale()).thenReturn(locale);
        when(messageSource.getMessage(anyString(), any(), any(Locale.class))).thenReturn("Successful");
    
        SetClauseRequest request = new SetClauseRequest();
        request.setStatus("1");
        request.setClauseid("1");
    
        FookClauseConfirm existingConfirm = new FookClauseConfirm();
        existingConfirm.setId(1);
        existingConfirm.setStatus(1);
        existingConfirm.setUpdatedAt(new Date());
        when(fookClauseConfirmDao.selectClauseConfirm(userInfo.getUserId(), 1, null)).thenReturn(existingConfirm);
    
        SetClauseResponse response = clauseService.saveClause(request);
    
        verify(fookClauseConfirmDao, times(1)).updateByPrimaryKeySelective(any(FookClauseConfirm.class));
        assertEquals(McoinMall.RESPONSE_STATUS_SUCCESS, response.getStatus());
        assertEquals("Successful", response.getMsg());
    }

}