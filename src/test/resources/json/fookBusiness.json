{"singleBusiness": {"id": 0, "areasId": 0, "code": "code_94febe702e41", "name": "name_08fb30d80767", "companyName": "companyName_93d75787404c", "account": "account_6cbcb1646ae7", "status": 0, "tel": "tel_ccb912891f1b", "address": "address_9893b9130ec0", "logo": "logo_ab86eb3c7680", "jsAccount": "jsAccount_5cebaede2426", "jsName": "jsName_49ff8e751644", "jsBank": "jsBank_f02b9f795f75", "headName": "headName_f306ab5fab0c", "headTel": "headTel_59c542814e56", "headEmail": "headEmail_0e9a44e438a2", "sex": 0, "enbale": 0, "loginPass": "loginPass_d025683b2444", "macauPassMerchantNumber": "macauPassMerchantNumber_d3e2ba48694c", "macauPassTerminalNumber": "macauPassTerminalNumber_7bb207a3d9d3", "createdAt": "2024-05-22 15:14:14", "updatedAt": "2024-05-22 15:14:14", "uid": 0, "ifAllowConnect": 0, "oaCodeId": 0, "businessNumber": "businessNumber_e2195a18319b", "fileNumber": "fileNumber_8a75642e50a3", "taxpayerNumber": "taxpayerNumber_f5d62c9d7529", "istemporary": 0, "governmentNumber": "governmentNumber_75bdd45097a1", "systemType": 0, "memberBusinessId": "memberBusinessId_7da25db9ebf5", "memberType": 0, "optionalType": 0, "isOldRedeem": 0}}