package com.mcoin.mall.service.job.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import com.mcoin.mall.bean.FookReportOrdercodeSettlement;
import com.mcoin.mall.bo.SettlementAmountBo;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class SettlementUtilTest{

    @Test
    public void testGetSettlementAmountBo() {
        // 创建测试数据
        FookReportOrdercodeSettlement settlement1 = new FookReportOrdercodeSettlement();
        settlement1.setBillamount(new BigDecimal("100.00"));
        settlement1.setMerchantsettleamount(new BigDecimal("50.00"));
        settlement1.setCommission(new BigDecimal("20.00"));
        settlement1.setMomecoinsamount(new BigDecimal("10.00"));
        settlement1.setSubsidyAmount(new BigDecimal("15.00"));
    
        FookReportOrdercodeSettlement settlement2 = new FookReportOrdercodeSettlement();
        settlement2.setBillamount(new BigDecimal("200.00"));
        settlement2.setMerchantsettleamount(new BigDecimal("100.00"));
        settlement2.setCommission(new BigDecimal("50.00"));
        settlement2.setMomecoinsamount(new BigDecimal("20.00"));
        settlement2.setSubsidyAmount(new BigDecimal("30.00"));
    
        List<FookReportOrdercodeSettlement> ordercodeSettlements = Arrays.asList(settlement1, settlement2);
    
        // 调用被测方法
        SettlementAmountBo result = SettlementUtil.getSettlementAmountBo(ordercodeSettlements);
    
        // 验证结果
        assertEquals(new BigDecimal("300.00"), result.getTotalAmount());
        assertEquals(new BigDecimal("150.00"), result.getTotalSettlement());
        assertEquals(new BigDecimal("70.00"), result.getTotalCommission());
        assertEquals(new BigDecimal("30.00"), result.getTotalMomecoins());
        assertEquals(new BigDecimal("45.00"), result.getTotalSubsidyAmount());
    }

    @Test
    public void testGetSettlementAmountBoWithEmptyList() {
        // 创建测试数据
        List<FookReportOrdercodeSettlement> ordercodeSettlements = Arrays.asList();
    
        // 调用被测方法
        SettlementAmountBo result = SettlementUtil.getSettlementAmountBo(ordercodeSettlements);
    
        // 验证结果
        assertEquals(BigDecimal.ZERO, result.getTotalAmount());
        assertEquals(BigDecimal.ZERO, result.getTotalSettlement());
        assertEquals(BigDecimal.ZERO, result.getTotalCommission());
        assertEquals(BigDecimal.ZERO, result.getTotalMomecoins());
        assertEquals(BigDecimal.ZERO, result.getTotalSubsidyAmount());
    }


    @Test
    public void testGetSettlementAmountBosGroupByStoreId() {
        // 创建测试数据
        FookReportOrdercodeSettlement settlement1 = new FookReportOrdercodeSettlement();
        settlement1.setBillamount(new BigDecimal("100.00"));
        settlement1.setMerchantsettleamount(new BigDecimal("50.00"));
        settlement1.setCommission(new BigDecimal("50.00"));
        settlement1.setMomecoinsamount(new BigDecimal("20.00"));
        settlement1.setSubsidyAmount(new BigDecimal("30.00"));
        settlement1.setStoreid(1);

        FookReportOrdercodeSettlement settlement1_2 = new FookReportOrdercodeSettlement();
        settlement1_2.setBillamount(new BigDecimal("50.00"));
        settlement1_2.setMerchantsettleamount(new BigDecimal("50.00"));
        settlement1_2.setCommission(new BigDecimal("20.00"));
        settlement1_2.setMomecoinsamount(new BigDecimal("10.00"));
        settlement1_2.setSubsidyAmount(new BigDecimal("15.00"));
        settlement1_2.setStoreid(1);

        FookReportOrdercodeSettlement settlement2 = new FookReportOrdercodeSettlement();
        settlement2.setBillamount(new BigDecimal("200.00"));
        settlement2.setMerchantsettleamount(new BigDecimal("100.00"));
        settlement2.setCommission(new BigDecimal("50.00"));
        settlement2.setMomecoinsamount(new BigDecimal("20.00"));
        settlement2.setSubsidyAmount(new BigDecimal("30.00"));
        settlement2.setStoreid(2);

        FookReportOrdercodeSettlement settlement2_2 = new FookReportOrdercodeSettlement();
        settlement2_2.setBillamount(new BigDecimal("50.00"));
        settlement2_2.setMerchantsettleamount(new BigDecimal("100.00"));
        settlement2_2.setCommission(new BigDecimal("50.00"));
        settlement2_2.setMomecoinsamount(new BigDecimal("20.00"));
        settlement2_2.setSubsidyAmount(new BigDecimal("30.00"));
        settlement2_2.setStoreid(2);

        List<FookReportOrdercodeSettlement> ordercodeSettlements = Arrays.asList(settlement1, settlement1_2, settlement2, settlement2_2);

        // 调用被测方法
        Map<Integer, SettlementAmountBo> result = SettlementUtil.getSettlementAmountBosGroupByStoreId(ordercodeSettlements);

        // 验证结果
        assertEquals(2, result.size());

        SettlementAmountBo store1AmountBo = result.get(1);
        assertEquals(new BigDecimal("150.00"), store1AmountBo.getTotalAmount());
        assertEquals(new BigDecimal("100.00"), store1AmountBo.getTotalSettlement());
        assertEquals(new BigDecimal("70.00"), store1AmountBo.getTotalCommission());
        assertEquals(new BigDecimal("30.00"), store1AmountBo.getTotalMomecoins());
        assertEquals(new BigDecimal("45.00"), store1AmountBo.getTotalSubsidyAmount());

        SettlementAmountBo store2AmountBo = result.get(2);
        assertEquals(new BigDecimal("250.00"), store2AmountBo.getTotalAmount());
        assertEquals(new BigDecimal("200.00"), store2AmountBo.getTotalSettlement());
        assertEquals(new BigDecimal("100.00"), store2AmountBo.getTotalCommission());
        assertEquals(new BigDecimal("40.00"), store2AmountBo.getTotalMomecoins());
        assertEquals(new BigDecimal("60.00"), store2AmountBo.getTotalSubsidyAmount());
    }

}