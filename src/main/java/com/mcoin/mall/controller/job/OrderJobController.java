package com.mcoin.mall.controller.job;

import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.model.job.EvcodeToOrderRequest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.OrderCloseRequest;
import com.mcoin.mall.model.job.OrderQueryStatusRequest;
import com.mcoin.mall.service.job.OrderJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/order")
@Slf4j
public class OrderJobController {

    @Resource
    private OrderJobService orderJobService;
    @Resource
    private SettingsDao settingsDao;

    /**
     * evcode转订单
     * 檢測是否需要創建19開頭券訂單
     * PHP(voucher:query)
     * @param request
     * @return
     */
    @PostMapping(value = "/evcodeToOrder")
    public Response<String> evcodeToOrder(@Valid EvcodeToOrderRequest request) {
        int number = Integer.parseInt(StringUtils.defaultIfBlank(settingsDao.getValue("site.evcodetoordernum"), "1000"));
        int batchSize = 1000;
        int start = 0;
        // 设置初始的end，如果number小于batchSize，则取number作为end
        int end = Math.min(number, batchSize);
        while (start < number) {
            request.setStart(start);
            request.setEnd(end);
            this.orderJobService.evcodeToOrder(request);
            start = end + 1;
            // 更新end，确保不超过number的范围
            end = Math.min(start + batchSize - 1, number);
        }
        return Responses.ok("success");
    }

    /**
     * 自动审批用户退款
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/approvalRefund")
    public Response<String> approvalRefund() {
        this.orderJobService.approvalRefund();
        return Responses.ok("success");
    }

    /**
     * 定时任务关单(PHP:order:cancel  撤消未付款訂單 OrderCancelTask)
     *
     * @return
     */
    @PostMapping(value = "/close")
    public Response<String> close(@Valid OrderCloseRequest request) {
        orderJobService.triggerClose(request);
        return Responses.ok("success");
    }

    /**
     * 查询订单状态（PHP:order:query 每分鐘檢測訂單的支付狀態 OrderQueryTask）
     *
     * @return
     */
    @PostMapping(value = "/queryStatus")
    public Response<String> queryStatus(@Valid OrderQueryStatusRequest request) {
        orderJobService.triggerQueryStatus(request);
        return Responses.ok("success");
    }


    /**
     * 捞取orderrefundrecord表中status=2的未退款数据
     *  T： 30m
     * @return Response response
     */
    @PostMapping(value = "/refundTimeoutRefundOrder")
    public Response<String> refundTimeoutRefundOrder() {
        orderJobService.triggerRefundTimeoutRefundOrder();
        return Responses.ok("success");
    }
}


