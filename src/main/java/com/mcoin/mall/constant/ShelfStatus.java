package com.mcoin.mall.constant;

public enum ShelfStatus {
    SHELVED(1, "上架"),
    UNSHELVED(2, "下架");

    private final int value;
    private final String description;

    ShelfStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据整数值获取对应的枚举对象
     *
     * @param value 整型值
     * @return 对应的枚举对象，如果不存在则返回null
     */
    public static ShelfStatus getByValue(int value) {
        for (ShelfStatus status : ShelfStatus.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的枚举对象
     *
     * @param description 描述字符串
     * @return 对应的枚举对象，如果不存在则返回null
     */
    public static ShelfStatus getByDescription(String description) {
        for (ShelfStatus status : ShelfStatus.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }
}
