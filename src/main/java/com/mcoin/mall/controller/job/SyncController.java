package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/job/sync")
@Slf4j
public class SyncController {
    /**
     * 综合分类-redis
     * @return
     */
    @PostMapping(value = "/storeToRedis")
    public Response<String> storeToRedis() {
        return Responses.ok("success");
    }
}
