#!/bin/bash
APP_NAME="mp-mcoin-mall"
APP_PORT="8080"
jar_name="$APP_NAME.jar"
echo "********************** start stop app **********************"
pid=`ps -ef | grep $jar_name | grep -v grep | awk '{print $2}'`
if [ -n "$pid" ]
then
   echo "$(date "+%Y-%m-%d %H:%M:%S") begin call nacos offline."
   nacos_result=`curl --connect-time 3 --max-time 5 -w "http_code=%{http_code}, time_total=%{time_total}" -X "POST" "http://localhost:$APP_PORT/$APP_NAME/actuator/service-registry?status=DOWN" -H "Content-Type: application/vnd.spring-boot.actuator.v2+json;charset=UTF-8"`
   echo "$(date "+%Y-%m-%d %H:%M:%S") end call nacos offline. result: "$nacos_result
   echo "$(date "+%Y-%m-%d %H:%M:%S") begin sleep 10s, wait ribbon server list refresh."
   sleep 10
   echo "$(date "+%Y-%m-%d %H:%M:%S") begin graceful down app, kill pid:" $pid
   kill -15 $pid
   echo "$(date "+%Y-%m-%d %H:%M:%S") wait graceful down."
   # shellcheck disable=SC2034
   totalSleep=0
   for i in {1..90} ; do
     pid2=`ps -ef | grep $jar_name | grep -v grep | awk '{print $2}'`
     if [ -n "$pid2" ]
     then
       echo "$(date "+%Y-%m-%d %H:%M:%S") total sleep $totalSleep, continue sleep 1s wait graceful down. pid:" $pid2
       sleep 1;
       totalSleep=$(($totalSleep+1))
     else
       echo "$(date "+%Y-%m-%d %H:%M:%S") after sleep $totalSleep s finish graceful down."
       break
     fi
   done
   pid2=`ps -ef | grep $jar_name | grep -v grep | awk '{print $2}'`
   if [ -n "$pid2" ]
   then
      echo "$(date "+%Y-%m-%d %H:%M:%S") after sleep 90s application is alive. kill -9 " $pid2
      kill -9 $pid2
   fi
else
  echo "$(date "+%Y-%m-%d %H:%M:%S") app is dead."
fi
echo "********************** end stop app **********************"