package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.model.HotDealsRequest;
import com.mcoin.mall.model.HotDealsResponse;
import com.mcoin.mall.service.business.HotDealsService;
import com.mcoin.mall.util.BusinessProductUtil;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.MiniProgramDisplayUtil;
import com.mcoin.mall.util.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HotDealsServiceImpl implements HotDealsService {
    @Autowired
    private FookBusinessProductDao fookBusinessProductDao;

    @Autowired
    private FookBusinessDao fookBusinessDao;

    @Resource
    private ContextHolder contextHolder;

    @Override
    public HotDealsResponse hotDeals(HotDealsRequest request, String clientType) {
        HotDealsResponse hotDealsResponse = new HotDealsResponse();

        // 检查是否为HARMONY设备且开关开启
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);

        // 获取商品列表，并传入harmonySwitch参数
        List<FookBusinessProduct> productList = fookBusinessProductDao.selectHotDeals(harmonySwitch);

        if(productList.isEmpty()){
             return hotDealsResponse;
        }

        List<HotDealsResponse.SnatchListItem> snatchListItemList = new ArrayList<>();
        Locale locale = contextHolder.getLocale();
        String lang = locale.getLanguage();

        for(FookBusinessProduct product: productList){
            FookBusiness business = fookBusinessDao.selectByPrimaryKeyTranslations(product.getBusinessid(), lang);
            if(business == null){
                log.error("productId:{} 商戶不存在", product.getId());
                continue;
            }

            if(McoinMall.LANG_EN.equals(lang)){
                FookBusinessProduct product1 = fookBusinessProductDao.selectByPrimaryKeyWithTranslations(product.getId(), lang);
                product.setTitle(product1.getTitle());
            }

            HotDealsResponse.SnatchListItem snatchListItem = new HotDealsResponse.SnatchListItem();
            snatchListItem.setId(product.getId());
            snatchListItem.setTitle(product.getTitle());
            snatchListItem.setImg(OssUtil.getProductImg(product.getZipImg()));

            if(StringUtils.isBlank(product.getZipImg())){
                snatchListItem.setImg(OssUtil.getProductImg(product.getImg()));
            }

            snatchListItem.setImage(OssUtil.getProductImg(product.getImg()));
            snatchListItem.setPreferential(ObjectUtils.defaultIfNull(product.getOnlyPoint(),0) == 1 ? BigDecimal.ZERO : product.getPrice().subtract(new BigDecimal("0.5")));//TODO 待確認
            snatchListItem.setPrice(String.valueOf(product.getPrice()));
            snatchListItem.setRetailPrice(String.valueOf(product.getRetailPrice()));
            snatchListItem.setSort("0");

            int mpayVal = Optional.ofNullable(product.getPointRatio()).orElse(300);
            int point = Optional.ofNullable(business.getSystemType()).orElse(1) == 3 ? Optional.ofNullable(product.getMaximumPoints()).orElse(0) :
                    (Optional.ofNullable(product.getOnlyPoint()).orElse(0) == 1 ? (product.getPrice().multiply(BigDecimal.valueOf(mpayVal))).intValue() :
                            mpayVal / 2);
            snatchListItem.setPoint(point);
            snatchListItem.setBusinessName(business.getName());
            snatchListItem.setBuyEndTime(JodaTimeUtil.format(product.getBuyEndTime()));
            snatchListItem.setBuyStartTime(JodaTimeUtil.format(product.getBuyStartTime()));
            snatchListItem.setType(product.getType());
            snatchListItem.setHrefUrl(BusinessProductUtil.fillHrefUrlBy(product.getId(), product.getType(), product.getHrefUrl(), product.getGoodsId()));
            snatchListItemList.add(snatchListItem);
        }

        hotDealsResponse.setSnatchList(snatchListItemList);
        return hotDealsResponse;
    }
}
