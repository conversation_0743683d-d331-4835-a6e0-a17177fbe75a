package com.mcoin.mall.constant;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
public enum UrlTypeEnum {
    NONE(0, "無"),
    WELFARE(1, "福利"),
    STORE(2, "門店"),
    EXTERNAL_LINK(3, "外部鏈接"),
    PHYSICAL_LINK(4, "實物鏈路"),
    SPECIAL_AD(5, "專場廣告");

    private final int code;
    private final String description;

    UrlTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据整数值获取枚举常量
     *
     * @param code 整数值
     * @return 对应的枚举对象，如果没有匹配则返回 NONE
     */
    public static UrlTypeEnum fromCode(int code) {
        for (UrlTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }


    /**
     * 获取枚举类型的整数代码
     *
     * @return 整数代码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取枚举类型的描述信息
     *
     * @return 描述信息
     */
    public String getDescription() {
        return description;
    }
}

