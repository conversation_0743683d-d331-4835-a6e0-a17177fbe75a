<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingWeekDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookTradingWeek">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="product_title" jdbcType="VARCHAR" property="productTitle" />
    <result column="pruduct_num" jdbcType="INTEGER" property="pruductNum" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="trading_id" jdbcType="INTEGER" property="tradingId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, product_id, product_title, pruduct_num, order_amount, trading_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_trading_week
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_trading_week
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookTradingWeek">
    insert into fook_trading_week
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productTitle != null">
        product_title,
      </if>
      <if test="pruductNum != null">
        pruduct_num,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="tradingId != null">
        trading_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productTitle != null">
        #{productTitle,jdbcType=VARCHAR},
      </if>
      <if test="pruductNum != null">
        #{pruductNum,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="tradingId != null">
        #{tradingId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookTradingWeek">
    update fook_trading_week
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="productTitle != null">
        product_title = #{productTitle,jdbcType=VARCHAR},
      </if>
      <if test="pruductNum != null">
        pruduct_num = #{pruductNum,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="tradingId != null">
        trading_id = #{tradingId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookTradingWeek">
    update fook_trading_week
    set product_id = #{productId,jdbcType=INTEGER},
      product_title = #{productTitle,jdbcType=VARCHAR},
      pruduct_num = #{pruductNum,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      trading_id = #{tradingId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>