<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformSuggestDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformSuggest">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="label_type" jdbcType="INTEGER" property="labelType" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="user_source" jdbcType="INTEGER" property="userSource" />
    <result column="user_ip" jdbcType="VARCHAR" property="userIp" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="order" jdbcType="INTEGER" property="order" />
    <result column="setup_name" jdbcType="VARCHAR" property="setupName" />
    <result column="popularity" jdbcType="INTEGER" property="popularity" />
    <result column="product_count" jdbcType="INTEGER" property="productCount" />
    <result column="stores_count" jdbcType="INTEGER" property="storesCount" />
    <result column="browse_count" jdbcType="INTEGER" property="browseCount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, `label`, label_type, param_key, add_time, user_source, user_ip, `enable`,
    `order`, setup_name, popularity, product_count, stores_count, browse_count
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_suggest
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_suggest
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformSuggest">
    insert into fook_platform_suggest
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="labelType != null">
        label_type,
      </if>
      <if test="paramKey != null">
        param_key,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="userSource != null">
        user_source,
      </if>
      <if test="userIp != null">
        user_ip,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="order != null">
        `order`,
      </if>
      <if test="setupName != null">
        setup_name,
      </if>
      <if test="popularity != null">
        popularity,
      </if>
      <if test="productCount != null">
        product_count,
      </if>
      <if test="storesCount != null">
        stores_count,
      </if>
      <if test="browseCount != null">
        browse_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="labelType != null">
        #{labelType,jdbcType=INTEGER},
      </if>
      <if test="paramKey != null">
        #{paramKey,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userSource != null">
        #{userSource,jdbcType=INTEGER},
      </if>
      <if test="userIp != null">
        #{userIp,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
      <if test="order != null">
        #{order,jdbcType=INTEGER},
      </if>
      <if test="setupName != null">
        #{setupName,jdbcType=VARCHAR},
      </if>
      <if test="popularity != null">
        #{popularity,jdbcType=INTEGER},
      </if>
      <if test="productCount != null">
        #{productCount,jdbcType=INTEGER},
      </if>
      <if test="storesCount != null">
        #{storesCount,jdbcType=INTEGER},
      </if>
      <if test="browseCount != null">
        #{browseCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformSuggest">
    update fook_platform_suggest
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="labelType != null">
        label_type = #{labelType,jdbcType=INTEGER},
      </if>
      <if test="paramKey != null">
        param_key = #{paramKey,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userSource != null">
        user_source = #{userSource,jdbcType=INTEGER},
      </if>
      <if test="userIp != null">
        user_ip = #{userIp,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=INTEGER},
      </if>
      <if test="order != null">
        `order` = #{order,jdbcType=INTEGER},
      </if>
      <if test="setupName != null">
        setup_name = #{setupName,jdbcType=VARCHAR},
      </if>
      <if test="popularity != null">
        popularity = #{popularity,jdbcType=INTEGER},
      </if>
      <if test="productCount != null">
        product_count = #{productCount,jdbcType=INTEGER},
      </if>
      <if test="storesCount != null">
        stores_count = #{storesCount,jdbcType=INTEGER},
      </if>
      <if test="browseCount != null">
        browse_count = #{browseCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformSuggest">
    update fook_platform_suggest
    set user_id = #{userId,jdbcType=INTEGER},
      `label` = #{label,jdbcType=VARCHAR},
      label_type = #{labelType,jdbcType=INTEGER},
      param_key = #{paramKey,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      user_source = #{userSource,jdbcType=INTEGER},
      user_ip = #{userIp,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=INTEGER},
      `order` = #{order,jdbcType=INTEGER},
      setup_name = #{setupName,jdbcType=VARCHAR},
      popularity = #{popularity,jdbcType=INTEGER},
      product_count = #{productCount,jdbcType=INTEGER},
      stores_count = #{storesCount,jdbcType=INTEGER},
      browse_count = #{browseCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>