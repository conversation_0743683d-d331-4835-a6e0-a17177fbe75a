package com.mcoin.mall.service.business.impl;

import static com.mcoin.mall.service.common.CacheService.CACHE_ACTIVE_MERCHANT;
import static com.mcoin.mall.service.common.CacheService.CACHE_DAY_GAIN;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.IntStream;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mcoin.mall.bean.FookActiveMerchant;
import com.mcoin.mall.bean.FookActiveMerchantData;
import com.mcoin.mall.bean.FookDayDiscount;
import com.mcoin.mall.bean.FookDayGain;
import com.mcoin.mall.bean.FookDayItem;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookActiveMerchantDao;
import com.mcoin.mall.dao.FookActiveMerchantDataDao;
import com.mcoin.mall.dao.FookDayDiscountDao;
import com.mcoin.mall.dao.FookDayGainDao;
import com.mcoin.mall.dao.FookDayItemDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.mapping.FookActiveMerchantDataMapping;
import com.mcoin.mall.mapping.FookDayginInfoMapping;
import com.mcoin.mall.model.ActiveMerchantListRequest;
import com.mcoin.mall.model.ActiveMerchantListResponse;
import com.mcoin.mall.model.DaygainListRequest;
import com.mcoin.mall.model.DaygainListResponse;
import com.mcoin.mall.model.Page;
import com.mcoin.mall.model.RespMeta.Links;
import com.mcoin.mall.model.RespMeta.Meta;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.service.business.ActivityService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.util.PageUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ActivityServiceImpl implements ActivityService {
    @Resource
    private FookDayGainDao fookDayGainDao;
    @Resource
    private FookDayDiscountDao fookDayDiscountDao;
    @Resource
    private FookDayItemDao fookDayItemDao;
    @Resource
    private FookActiveMerchantDao fookActiveMerchantDao;
    @Resource
    private FookActiveMerchantDataDao fookActiveMerchantDataDao;
    @Resource
    private MessageSource messageSource;
    @Resource
	private ContextHolder contextHolder;

	@Cacheable(value = CACHE_DAY_GAIN, key = "@cacheService.getDayGainKey(#request)", unless = "#result == null || #result.getSnatchList().isEmpty()")
	@Override
	public DaygainListResponse dayGainList(DaygainListRequest request) {
		long startTime = System.currentTimeMillis();
		DaygainListResponse resp = new DaygainListResponse();
		int id = request.getId();
		Locale locale = contextHolder.getLocale();
		String language = locale.getLanguage();
		int page = request.getPage();
		FookDayGain fookDayGain = fookDayGainDao.selectOne(new LambdaQueryWrapper<FookDayGain>()
				.eq(FookDayGain::getId, id));
		if(fookDayGain==null) {
			throw new BusinessException(Response.Code.BAD_REQUEST,
					messageSource.getMessage("message.merchant.nodata", null, locale));
		}
		if(fookDayGain.getStatus()==0) {
			throw new BusinessException(Response.Code.BAD_REQUEST,
					messageSource.getMessage("message.merchant.business", null, locale));
		}
		Date date = new Date();
		if(McoinMall.LANG_EN.equals(language)) {
			fookDayGain.setName(fookDayGain.getNameEn());
			fookDayGain.setImg(fookDayGain.getImgEn());
		}
		fookDayGain.setImg(OssUtil.getProductImg(fookDayGain.getImg()));
		resp.setDaygain(FookDayginInfoMapping.INSTANCE.getDaygain(fookDayGain));
		//设置查询对象
		LambdaQueryWrapper<FookDayDiscount> querywrapper = new LambdaQueryWrapper<FookDayDiscount>();
		querywrapper.eq(FookDayDiscount::getGainId, id).eq(FookDayDiscount::getStatus, "1");
		//获取优惠信息
		IPage<FookDayDiscount> ipage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<FookDayDiscount>(page, McoinMall.DEFAULT_PAGE_SIZE);
		IPage<FookDayDiscount> fookDayDiscount = fookDayDiscountDao.selectPage(ipage,querywrapper);
		int discountCount = (int)fookDayDiscount.getTotal();
		log.info("请求id[{}]获取到discount数量为[{}]",id,discountCount);
		//设置返回的page信息
		Page respPage = new Page();
        respPage.setPerPage(McoinMall.DEFAULT_PAGE_SIZE);
        respPage.setTotal(discountCount);
        respPage.setLastPage(PageUtil.getPageNum(discountCount, McoinMall.DEFAULT_PAGE_SIZE));
        respPage.setCurrentPage(page);
		resp.setPage(respPage);
		List<FookDayDiscount> pageList = fookDayDiscount.getRecords();
		List<DaygainListResponse.SnatchListItem> list = new ArrayList<DaygainListResponse.SnatchListItem>();
		for (FookDayDiscount fdd : pageList) {
			String discount = fdd.getDiscount();
			if(McoinMall.LANG_EN.equals(language)) {
				discount = fdd.getDiscountEn();
			}
			DaygainListResponse.SnatchListItem slItem = new DaygainListResponse.SnatchListItem();
			slItem.setDiscount(discount);
			slItem.setId(fdd.getId());
			//获取items
			List<FookDayItem> itemList = fookDayItemDao.selectList(new LambdaQueryWrapper<FookDayItem>()
					.eq(FookDayItem::getDiscountId, fdd.getId()).eq(FookDayItem::getStatus, "1")
					.le(FookDayItem::getStartTime, date).ge(FookDayItem::getEndTime, date)
					.orderByAsc(FookDayItem::getSort).orderByDesc(FookDayItem::getCreatedAt));
			List<DaygainListResponse.Item> items = new ArrayList<DaygainListResponse.Item>();
			for (FookDayItem fookDayItem : itemList) {
				DaygainListResponse.Item item = FookDayginInfoMapping.INSTANCE.getDaygainItem(fookDayItem);
				if(McoinMall.LANG_EN.equals(language)) {
					item.setBusinessName(fookDayItem.getBusinessNameEn());
					item.setDiscountName(fookDayItem.getDiscountNameEn());
					item.setLogo(fookDayItem.getLogoEn());
				}
				item.setLogo(OssUtil.getProductImg(item.getLogo()));
				items.add(item);
			}
			slItem.setItem(items);
			list.add(slItem);
		}
		resp.setSnatchList(list);
		log.info("查询优惠&活动信息成功，耗时：{}",System.currentTimeMillis() - startTime);
		return resp;
	}

	@Cacheable(value = CACHE_ACTIVE_MERCHANT, key = "@cacheService.getActiveMerchantKey(#request)",
			condition = "@cacheService.isCacheActiveMerchant(#request)", unless = "#result == null")
	@Override
	public Response<ActiveMerchantListResponse> merchantList(ActiveMerchantListRequest request) {
		
		try {
			long startTime = System.currentTimeMillis();
			ActiveMerchantListResponse data = new ActiveMerchantListResponse();
			Locale locale = contextHolder.getLocale();
			String language = locale.getLanguage();
			int page = request.getPage();
			Integer activeId = request.getActive_id();
			String name = request.getName();
			String adress = request.getAddress();
			Integer luckyDraw = request.getLucky_draw();//抽奖机会 1是 0否
			Integer voucher = request.getVoucher();// 电子消费券 1是 0否
			String event = request.getEvent();//商戶活動內容
			//参数校验
			if(activeId==null) {
				throw new BusinessException(Response.Code.BAD_REQUEST,
						messageSource.getMessage("message.basic.failure", null, locale));
			}
			if ((luckyDraw != null && IntStream.of(0,1).noneMatch(x -> x == luckyDraw)) || 
					(voucher != null && IntStream.of(0,1).noneMatch(x -> x == voucher))) {
	             //参数错误
	             throw new BusinessException(Response.Code.BAD_REQUEST,
	                     messageSource.getMessage("message.basic.failure", null, locale));
	         }
			//获取活动商户数据
			FookActiveMerchant fookActiveMerchant = fookActiveMerchantDao.selectOne(new LambdaQueryWrapper<FookActiveMerchant>()
					.eq(FookActiveMerchant::getId, activeId));
			if(fookActiveMerchant==null) {
				throw new BusinessException(Response.Code.BAD_REQUEST,
						messageSource.getMessage("message.merchant.nodata", null, locale));
			}
			if(fookActiveMerchant.getStatus()==0) {
				throw new BusinessException(Response.Code.BAD_REQUEST,
						messageSource.getMessage("message.merchant.business", null, locale));
			}
			if(McoinMall.LANG_EN.equals(language)) {
				if(StringUtils.isNotEmpty(fookActiveMerchant.getTitleEn())) {
					fookActiveMerchant.setTitle(fookActiveMerchant.getTitleEn());
				}
				if(StringUtils.isNotEmpty(fookActiveMerchant.getEventEn())) {
					fookActiveMerchant.setEvent(fookActiveMerchant.getEventEn());
				}
			}
			data.setActive(FookActiveMerchantDataMapping.INSTANCE.getFookActiveMer(fookActiveMerchant));
			//设置查询条件
			LambdaQueryWrapper<FookActiveMerchantData> queryWrapper = new LambdaQueryWrapper<FookActiveMerchantData>();
			queryWrapper.eq(FookActiveMerchantData::getActiveId, activeId);
			if(StringUtils.isNotEmpty(name)) {
				if(McoinMall.LANG_EN.equals(language)) {
					queryWrapper.and(wq -> wq.like(FookActiveMerchantData::getNameEn, name)
							.or()
							.like(FookActiveMerchantData::getName, name));
				}else {
					queryWrapper.like(FookActiveMerchantData::getName, name);
				}
			}
			if(StringUtils.isNotEmpty(adress)) {
				if(McoinMall.LANG_EN.equals(language)) {
					queryWrapper.and(wq -> wq.like(FookActiveMerchantData::getAddressEn, adress)
							.or()
							.like(FookActiveMerchantData::getAddress, adress));
				}else {
					queryWrapper.eq(FookActiveMerchantData::getAddress, adress);
				}
			}
			if(luckyDraw != null) {
				queryWrapper.eq(FookActiveMerchantData::getLuckyDraw, luckyDraw);
			}
			if(voucher != null) {
				queryWrapper.eq(FookActiveMerchantData::getVoucher, voucher);
			}
			if(StringUtils.isNotEmpty(event) && !"all".equals(event)) {
				if(McoinMall.LANG_EN.equals(language)) {
					queryWrapper.apply(" event_en REGEXP {0}", event);
				}else {
					queryWrapper.apply(" event REGEXP {0}", event);
				}
			}
			//获取活动商户数据
			IPage<FookActiveMerchantData> ipage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<FookActiveMerchantData>(page, McoinMall.DEFAULT_PAGE_SIZE);
			IPage<FookActiveMerchantData> list = fookActiveMerchantDataDao.selectPage(ipage, queryWrapper);
			int totalCount = (int)list.getTotal();
			log.info("请求activeId[{}]获取到discount数量为[{}]",activeId,totalCount);
			//设置page信息
			Page respPage = new Page();
	        respPage.setPerPage(McoinMall.DEFAULT_PAGE_SIZE);
	        respPage.setTotal(totalCount);
	        respPage.setLastPage(PageUtil.getPageNum(totalCount, McoinMall.DEFAULT_PAGE_SIZE));
	        respPage.setCurrentPage(page);
	        data.setPage(respPage);
			List<ActiveMerchantListResponse.SnatchListItem> dataList = new ArrayList<ActiveMerchantListResponse.SnatchListItem>();
			for (FookActiveMerchantData famd : list.getRecords()) {
				ActiveMerchantListResponse.SnatchListItem item = FookActiveMerchantDataMapping.INSTANCE.getFookActiveMerData(famd);
				item.setLuckyDraw("1".equals(item.getLuckyDraw())?"Y":"N");
				item.setVoucher("1".equals(item.getVoucher())?"Y":"N");
				if(McoinMall.LANG_EN.equals(language)) {
					if (StringUtils.isNotBlank(famd.getNameEn())) {
						item.setName(famd.getNameEn());
					}
					if (StringUtils.isNotBlank(famd.getAddressEn())) {
						item.setAddress(famd.getAddressEn());
					}
					if (StringUtils.isNotBlank(famd.getEventEn())) {
						item.setEvent(famd.getEventEn());
					}
				}
				dataList.add(item);
			}
			//设置返回参数
			data.setSnatchList(dataList);
			String eventStr = fookActiveMerchant.getEvent();
			List<String> eventList = new ArrayList<String>();
			if(StringUtils.isNotEmpty(eventStr)) {
				eventList = Arrays.asList(eventStr.replace("，", ",").split(","));
			}
			data.setEvent(eventList);
			String api = "api/business/merchantlist";
			log.info("获取优惠活动商家列表成功，耗时：{}",System.currentTimeMillis() - startTime);
			return Responses.metaOk(data, Response.Status.SUCCESS.ordinal(), null, createLinks(page,totalCount, api), createMeta(page, totalCount, api));
		} catch (Exception e) {
			log.error("获取优惠活动商家列表出错：{}",e);
			return Responses.metaOk(new ActiveMerchantListResponse(), Response.Status.FAILED.ordinal(), e.getMessage(), new Links(), new Meta());
		}
	}
	/***
	 * 根据page和总数生成Links对象
	 * @param page
	 * @param totalCount
	 * @param url
	 * @return
	 */
	public static Links createLinks(int page, int totalCount,String url) {
		Links likns = new Links();
		String baseUrl = ConfigUtils.getProperty("API_URL") + url + "?page=";
		likns.setFirst(baseUrl + 1);
		likns.setLast(baseUrl + PageUtil.getPageNum(totalCount, McoinMall.DEFAULT_PAGE_SIZE));
		int nextPage = page + 1;
		likns.setNext(baseUrl + nextPage);
		String prev = "";//上一页
		if(page!=1) {
			prev = baseUrl + (page - 1);
		}
		likns.setPrev(prev);
		return likns;
	}
	/***
	 * 根据page和总数生成Meta对象
	 * @param page
	 * @param totalCount
	 * @param url
	 * @return
	 */
	public static Meta createMeta(int page, int totalCount, String url ) {
		Meta meta = new Meta();
		meta.setCurrentPage(page);
		meta.setFrom(page * McoinMall.DEFAULT_PAGE_SIZE);
		meta.setLastPage(PageUtil.getPageNum(totalCount, McoinMall.DEFAULT_PAGE_SIZE));
		meta.setPath(ConfigUtils.getProperty("API_URL") + url);
		meta.setPerPage(McoinMall.DEFAULT_PAGE_SIZE);
		meta.setTo(page * McoinMall.DEFAULT_PAGE_SIZE + McoinMall.DEFAULT_PAGE_SIZE);
		meta.setTotal(totalCount);
		return meta;
	}
}
