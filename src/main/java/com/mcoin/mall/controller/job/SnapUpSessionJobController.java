package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.CheckSessionTimeAndProductBuyTimeJobRequest;
import com.mcoin.mall.service.job.SnapUpSessionJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/snapUp/session")
@Slf4j
public class SnapUpSessionJobController {
    @Resource
    private SnapUpSessionJobService snapUpSessionJobService;

    /**
     * 场次活动时间是否和关联商品一致
     * @return
     */
    @PostMapping(value = "/checkSessionTimeAndProductBuyTime")
    public Response<String> checkSessionTimeAndProductBuyTime(@Valid CheckSessionTimeAndProductBuyTimeJobRequest request) {
        snapUpSessionJobService.checkSessionTimeAndProductBuyTime(request);
        return Responses.ok("success");
    }
}
