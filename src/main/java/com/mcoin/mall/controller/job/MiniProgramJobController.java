package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.MiniOrderSettlementRequest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.MiniOrderSyncRequest;
import com.mcoin.mall.model.job.SyncProductListRequest;
import com.mcoin.mall.mq.MiniOrderSyncListener;
import com.mcoin.mall.service.common.CsvHandleService;
import com.mcoin.mall.service.mini.MiniGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@Slf4j
@RestController
@RequestMapping("/api/job/mini")
public class MiniProgramJobController {

    @Resource
    private CsvHandleService csvHandleService;

    @Resource
    private MiniGoodsService miniGoodsService;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource
    private MiniOrderSyncListener miniOrderSyncListener;

    /**
     * 定时任务触发零售小程序结算信息同步
     *
     */
    @PostMapping(value = "/settlement")
    public Response<String> settlement(@Valid @RequestBody MiniOrderSettlementRequest request) {
        taskExecutor.execute(()->{
            try {
                csvHandleService.handleByMiniSettlement(request);
            } catch (Exception e){
                log.error("处理零售小程序结算信息失败", e);
            }
        });
        return Responses.ok("success");
    }

    /**
     * 同步销量和库存
     */
    @PostMapping(value = "/syncProducts")
    public Response<String> syncProducts(@Valid SyncProductListRequest request) {
        this.miniGoodsService.syncProducts(request);
        return Responses.ok("success");
    }

    /**
     * 同步订单
     */
    @PostMapping(value = "/syncOrder")
    public Response<String> syncOrder(@Valid @RequestBody MiniOrderSyncRequest request) {
        miniOrderSyncListener.onMessage(request.getMessage());
        return Responses.ok("success");
    }
}
