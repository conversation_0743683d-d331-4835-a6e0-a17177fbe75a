<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMacaupassUserDao">

    <select id="getMpayUserByUserId" resultType="com.mcoin.mall.bean.FookMacaupassUser">
        SELECT
        <include refid="Base_Column_List"/>
        from fook_macaupass_user
        where user_id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="getFirstMpayUserByUserId" resultType="com.mcoin.mall.bean.FookMacaupassUser">
        SELECT
        <include refid="Base_Column_List"/>
        from fook_macaupass_user
        where user_id = #{userId,jdbcType=BIGINT} limit 1
    </select>

    <select id="getFirstMpayUserByCustomId" resultType="com.mcoin.mall.bean.FookMacaupassUser">
        SELECT
        <include refid="Base_Column_List"/>
        from fook_macaupass_user
        where customid = #{customid,jdbcType=VARCHAR} limit 1
    </select>
</mapper>