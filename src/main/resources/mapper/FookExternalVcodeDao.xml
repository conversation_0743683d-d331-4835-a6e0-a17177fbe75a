<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookExternalVcodeDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookExternalVcode">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="vcode" jdbcType="VARCHAR" property="vcode" />
    <result column="isascription" jdbcType="BIT" property="isascription" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_a_open" jdbcType="BIT" property="isAOpen" />
    <result column="build_status" jdbcType="BIT" property="buildStatus" />
    <result column="is_build_order" jdbcType="BIT" property="isBuildOrder" />
  </resultMap>
  <sql id="Base_Column_List">
    id, product_id, vcode, isascription, title, `enable`, `status`, created_at, update_time,
    is_a_open, build_status, is_build_order
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_external_vcode
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_external_vcode
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookExternalVcode">
    insert into fook_external_vcode
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="vcode != null">
        vcode,
      </if>
      <if test="isascription != null">
        isascription,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isAOpen != null">
        is_a_open,
      </if>
      <if test="buildStatus != null">
        build_status,
      </if>
      <if test="isBuildOrder != null">
        is_build_order,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="vcode != null">
        #{vcode,jdbcType=VARCHAR},
      </if>
      <if test="isascription != null">
        #{isascription,jdbcType=BIT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isAOpen != null">
        #{isAOpen,jdbcType=BIT},
      </if>
      <if test="buildStatus != null">
        #{buildStatus,jdbcType=BIT},
      </if>
      <if test="isBuildOrder != null">
        #{isBuildOrder,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookExternalVcode">
    update fook_external_vcode
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="vcode != null">
        vcode = #{vcode,jdbcType=VARCHAR},
      </if>
      <if test="isascription != null">
        isascription = #{isascription,jdbcType=BIT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isAOpen != null">
        is_a_open = #{isAOpen,jdbcType=BIT},
      </if>
      <if test="buildStatus != null">
        build_status = #{buildStatus,jdbcType=BIT},
      </if>
      <if test="isBuildOrder != null">
        is_build_order = #{isBuildOrder,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookExternalVcode">
    update fook_external_vcode
    set product_id = #{productId,jdbcType=INTEGER},
      vcode = #{vcode,jdbcType=VARCHAR},
      isascription = #{isascription,jdbcType=BIT},
      title = #{title,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      `status` = #{status,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_a_open = #{isAOpen,jdbcType=BIT},
      build_status = #{buildStatus,jdbcType=BIT},
      is_build_order = #{isBuildOrder,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>