package com.mcoin.mall.service.job.impl;

import com.mcoin.mall.bean.FookProductManualRecommend;
import com.mcoin.mall.bo.RecommendProductBo;
import com.mcoin.mall.dao.FookProductManualRecommendDao;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import java.util.Date;
import static org.mockito.Mockito.*;
import org.mockito.Mock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import com.mcoin.mall.model.job.UpdateRecommendCacheRequest;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Arrays;
import static org.junit.jupiter.api.Assertions.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.exception.RetryException;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.redisson.api.RScoredSortedSet;
import java.util.concurrent.TimeUnit;

@ExtendWith(MockitoExtension.class)
public class FeedsJobServiceImplTest{

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private FookProductManualRecommendDao fookProductManualRecommendDao;

    @InjectMocks
    private FeedsJobServiceImpl feedsJobService;

    @Mock
    private RLock rLock;

    @Mock
    private RScoredSortedSet<Object> rScoredSortedSet;

    @BeforeEach
    public void setUp() {
        // Set the lockUpdateCacheSeconds to a specific value for testing
        feedsJobService.lockUpdateCacheSeconds = 10;
    }

    @Test
    public void testDoUpdateRecommendCache_Success() throws InterruptedException {
        // Mock the RLock and RScoredSortedSet
        when(redissonClient.getLock("FeedsJobServiceImpl.doUpdateRecommendCache")).thenReturn(rLock);
        when(redissonClient.getScoredSortedSet(anyString())).thenReturn(rScoredSortedSet);
    
        // Mock the data returned by the DAO
        RecommendProductBo bo1  = new RecommendProductBo();
        bo1.setProductId(1);
        bo1.setType(1);
        RecommendProductBo bo2  = new RecommendProductBo();
        bo2.setProductId(2);
        bo2.setType(1);
        when(fookProductManualRecommendDao.selectRecommendProductBos(any(Date.class)))
                .thenReturn(Arrays.asList(bo1, bo2));
    
        // Mock the behavior of the lock
        when(rLock.tryLock(anyLong(), any(TimeUnit.class))).thenReturn(true);
    
        // Call the method under test
        UpdateRecommendCacheRequest request = new UpdateRecommendCacheRequest();
        request.setCurrentTime(new Date());
        request.setClearSwitchCache(false);
        feedsJobService.doUpdateRecommendCache(request);
    
        // Verify the interactions and assertions
        verify(rLock, times(1)).tryLock(anyLong(), any(TimeUnit.class));
        verify(rLock, times(1)).unlock();
        verify(fookProductManualRecommendDao, times(1)).selectRecommendProductBos(any(Date.class));
        verify(rScoredSortedSet, times(4)).delete();
        verify(rScoredSortedSet, times(4)).add(anyDouble(), anyInt());
    }

    @Test
    public void testDoUpdateRecommendCache_LockFailure() throws InterruptedException {
        // Mock the RLock and RScoredSortedSet
        when(redissonClient.getLock("FeedsJobServiceImpl.doUpdateRecommendCache")).thenReturn(rLock);
    
        // Mock the behavior of the lock
        when(rLock.tryLock(anyLong(), any(TimeUnit.class))).thenReturn(false);
    
        // Call the method under test and assert an exception
        UpdateRecommendCacheRequest request = new UpdateRecommendCacheRequest();
        request.setCurrentTime(new Date());
    
        RetryException exception = assertThrows(RetryException.class, () -> feedsJobService.doUpdateRecommendCache(request));
        assertEquals("加锁失败", exception.getMessage());
    
        // Verify the interactions and assertions
        verify(rLock, times(1)).tryLock(anyLong(), any(TimeUnit.class));
        verify(rLock, times(1)).unlock();
    }

}