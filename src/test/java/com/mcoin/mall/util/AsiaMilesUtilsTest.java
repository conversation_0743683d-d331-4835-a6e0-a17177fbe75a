package com.mcoin.mall.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

public class AsiaMilesUtilsTest {

    @Test
    public void testVerification_withNull() {
        String nullString = null;
        assertFalse(AsiaMilesUtils.verification(nullString));
    }

    @Test
    public void testVerification_withNonNumericCharacters() {
        String nonNumericNumber = "123456789a";
        assertFalse(AsiaMilesUtils.verification(nonNumericNumber));
    }

    @Test
    public void testVerification_withInvalidChecksum() {
        String invalidChecksumNumber = "1234567899";
        assertFalse(AsiaMilesUtils.verification(invalidChecksumNumber));
    }

    @Test
    public void testVerification_withEmptyString() {
        String emptyString = "";
        assertFalse(AsiaMilesUtils.verification(emptyString));
    }

    @Test
    public void testVerification_withLeadingAndTrailingWhitespace() {
        String leadingAndTrailingWhitespace = " 1513894482 ";
        assertTrue(AsiaMilesUtils.verification(leadingAndTrailingWhitespace));
    }

    @Test
    public void testVerification_withValidNumber() {
        String validNumber = "1513894482";
        assertTrue(AsiaMilesUtils.verification(validNumber));
    }

    @Test
    public void testVerification_withFirstDigitLessThanOne() {
        String firstDigitLessThanOne = "0234567890";
        assertFalse(AsiaMilesUtils.verification(firstDigitLessThanOne));
    }

    @Test
    public void testVerification_withWhitespaceOnly() {
        String whitespaceOnly = "     ";
        assertFalse(AsiaMilesUtils.verification(whitespaceOnly));
    }

    @Test
    public void testVerification_withInvalidLength() {
        String invalidLengthNumber = "123456789";
        assertFalse(AsiaMilesUtils.verification(invalidLengthNumber));
    }
}
