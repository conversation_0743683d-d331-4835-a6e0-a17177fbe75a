

# åæ­¥å°Coupon
coupon_sync_exchange_name=mcoin.mall.coupon.sync.exchange.local
coupon_sync_routingkey_name=mcoin.mall.coupon.sync.routingkey.local
coupon_sync_delay_exchange_name=mcoin.mall.coupon.sync.delay.exchange.local
coupon_sync_delay_routingkey_name=mcoin.mall.coupon.sync.delay.routingkey.local
coupon_sync_queue_name=mcoin.mall.coupon.sync.queue.local
coupon_sync_max_concurrent_consumers=8
coupon_sync_delay_time=2000
coupon_sync_delay_count_max=30


# ééæ¶æ¯
ding_ding_message_exchange_name=mcoin.mall.dd.message.exchange.local
ding_ding_message_routingkey_name=mcoin.mall.dd.message.routingkey.local
ding_ding_message_delay_exchange_name=mcoin.mall.dd.message.delay.exchange.local
ding_ding_message_delay_routingkey_name=mcoin.mall.dd.message.delay.routingkey.local
ding_ding_message_queue_name=mcoin.mall.dd.message.queue.local
ding_ding_message_max_concurrent_consumers=1

# æ£æ¥æ¢è´­ä¸­çåºå­æ¯å¦çæè®¢å
check_snapping_stock_exchange_name=mcoin.mall.check.snapping.stock.exchange.local
check_snapping_stock_routingkey_name=mcoin.mall.check.snapping.stock.routingkey.local
check_snapping_stock_delay_exchange_name=mcoin.mall.check.snapping.stock.delay.exchange.local
check_snapping_stock_delay_routingkey_name=mcoin.mall.check.snapping.stock.delay.routingkey.local
check_snapping_stock_queue_name=mcoin.mall.check.snapping.stock.queue.local
check_snapping_stock_max_concurrent_consumers=8
#  ååé
check_snapping_stock_delay_time=600000
check_snapping_stock_delay_count_max=30


# æ¸ ééæ¬¾
refund_exchange_name=mcoin.mall.refund.exchange.local
refund_routingkey_name=mcoin.mall.refund.routingkey.local
refund_delay_exchange_name=mcoin.mall.refund.delay.exchange.local
refund_delay_routingkey_name=mcoin.mall.refund.delay.routingkey.local
refund_queue_name=mcoin.mall.refund.queue.local
refund_max_concurrent_consumers=8
#  2ç§
refund_delay_time=2000
refund_delay_count_max=30


# å³å
order_close_exchange_name=mcoin.mall.order.close.exchange.local
order_close_routingkey_name=mcoin.mall.order.close.routingkey.local
order_close_delay_exchange_name=mcoin.mall.order.close.delay.exchange.local
order_close_delay_routingkey_name=mcoin.mall.order.close.delay.routingkey.local
order_close_queue_name=mcoin.mall.order.close.queue.local
order_close_max_concurrent_consumers=8
#  2ç§
order_close_delay_time=2000
order_close_delay_count_max=30

# æ¥è¯¢ç¶æ
query_status_exchange_name=mcoin.mall.query.status.exchange.local
query_status_routingkey_name=mcoin.mall.query.status.routingkey.local
query_status_delay_exchange_name=mcoin.mall.query.status.delay.exchange.local
query_status_delay_routingkey_name=mcoin.mall.query.status.delay.routingkey.local
query_status_queue_name=mcoin.mall.query.status.queue.local
query_status_max_concurrent_consumers=8
#  2ç§
query_status_delay_time=2000
query_status_delay_count_max=30


# å¯¹è´¦æ£æ¥æ¯ä»åè¾¹è´¦
recon_check_pay_exchange_name=mcoin.mall.recon.check.pay.exchange.local
recon_check_pay_routingkey_name=mcoin.mall.recon.check.pay.routingkey.local
recon_check_pay_delay_exchange_name=mcoin.mall.recon.check.pay.delay.exchange.local
recon_check_pay_delay_routingkey_name=mcoin.mall.recon.check.pay.delay.routingkey.local
recon_check_pay_queue_name=mcoin.mall.recon.check.pay.queue.local
recon_check_pay_max_concurrent_consumers=8
#  2ç§
recon_check_pay_delay_time=2000
recon_check_pay_delay_count_max=30


# ç»ç®
settlement_exchange_name=mcoin.mall.settlement.exchange.local
settlement_routingkey_name=mcoin.mall.settlement.routingkey.local
settlement_delay_exchange_name=mcoin.mall.settlement.delay.exchange.local
settlement_delay_routingkey_name=mcoin.mall.settlement.delay.routingkey.local
settlement_queue_name=mcoin.mall.settlement.queue.local
settlement_max_concurrent_consumers=1
# 10åé
settlement_delay_time=600000
settlement_delay_count_max=30


# ç»ç®ä¸ä¼ æ¥è¡¨
settlement_report_upload_exchange_name=mcoin.mall.settlement.report.upload.exchange.local
settlement_report_upload_routingkey_name=mcoin.mall.settlement.report.upload.routingkey.local
settlement_report_upload_delay_exchange_name=mcoin.mall.settlement.report.upload.delay.exchange.local
settlement_report_upload_delay_routingkey_name=mcoin.mall.settlement.report.upload.delay.routingkey.local
settlement_report_upload_queue_name=mcoin.mall.settlement.report.upload.queue.local
settlement_report_upload_max_concurrent_consumers=1
#  2åé
settlement_report_upload_delay_time=120000
settlement_report_upload_delay_count_max=30


# ç»ç®åéæ¥è¡¨
settlement_report_send_exchange_name=mcoin.mall.settlement.report.send.exchange.local
settlement_report_send_routingkey_name=mcoin.mall.settlement.report.send.routingkey.local
settlement_report_send_delay_exchange_name=mcoin.mall.settlement.report.send.delay.exchange.local
settlement_report_send_delay_routingkey_name=mcoin.mall.settlement.report.send.delay.routingkey.local
settlement_report_send_queue_name=mcoin.mall.settlement.report.send.queue.local
settlement_report_send_max_concurrent_consumers=1
#  2åé
settlement_report_send_delay_time=120000
settlement_report_send_delay_count_max=30


# å©æ¶¦æ¥è¡¨
report_profit_exchange_name=mcoin.mall.report.profit.exchange.local
report_profit_routingkey_name=mcoin.mall.report.profit.routingkey.local
report_profit_delay_exchange_name=mcoin.mall.report.profit.delay.exchange.local
report_profit_delay_routingkey_name=mcoin.mall.report.profit.delay.routingkey.local
report_profit_queue_name=mcoin.mall.report.profit.queue.local
report_profit_max_concurrent_consumers=1
#  2åé
report_profit_delay_time=120000
report_profit_delay_count_max=30


# åé¨ç»ç®æ¥è¡¨
settlement_internal_exchange_name=mcoin.mall.settlement.internal.exchange.local
settlement_internal_routingkey_name=mcoin.mall.settlement.internal.routingkey.local
settlement_internal_delay_exchange_name=mcoin.mall.settlement.internal.delay.exchange.local
settlement_internal_delay_routingkey_name=mcoin.mall.settlement.internal.delay.routingkey.local
settlement_internal_queue_name=mcoin.mall.settlement.internal.queue.local
settlement_internal_max_concurrent_consumers=1
#  2åé
settlement_internal_delay_time=120000
settlement_internal_delay_count_max=30


# å¯éç»ç®æ¥è¡¨
settlement_optional_exchange_name=mcoin.mall.settlement.optional.exchange.local
settlement_optional_routingkey_name=mcoin.mall.settlement.optional.routingkey.local
settlement_optional_delay_exchange_name=mcoin.mall.settlement.optional.delay.exchange.local
settlement_optional_delay_routingkey_name=mcoin.mall.settlement.optional.delay.routingkey.local
settlement_optional_queue_name=mcoin.mall.settlement.optional.queue.local
settlement_optional_max_concurrent_consumers=1
#  2åé
settlement_optional_delay_time=120000
settlement_optional_delay_count_max=30


# å¯éç»ç®ä¸ä¼ æ¥è¡¨
settlement_optional_report_upload_exchange_name=mcoin.mall.settlement.optional.report.upload.exchange.local
settlement_optional_report_upload_routingkey_name=mcoin.mall.settlement.optional.report.upload.routingkey.local
settlement_optional_report_upload_delay_exchange_name=mcoin.mall.settlement.optional.report.upload.delay.exchange.local
settlement_optional_report_upload_delay_routingkey_name=mcoin.mall.settlement.optional.report.upload.delay.routingkey.local
settlement_optional_report_upload_queue_name=mcoin.mall.settlement.optional.report.upload.queue.local
settlement_optional_report_upload_max_concurrent_consumers=1
#  2åé
settlement_optional_report_upload_delay_time=120000
settlement_optional_report_upload_delay_count_max=30


# å¯éç»ç®åéæ¥è¡¨
settlement_optional_report_send_exchange_name=mcoin.mall.settlement.optional.report.send.exchange.local
settlement_optional_report_send_routingkey_name=mcoin.mall.settlement.optional.report.send.routingkey.local
settlement_optional_report_send_delay_exchange_name=mcoin.mall.settlement.optional.report.send.delay.exchange.local
settlement_optional_report_send_delay_routingkey_name=mcoin.mall.settlement.optional.report.send.delay.routingkey.local
settlement_optional_report_send_queue_name=mcoin.mall.settlement.optional.report.send.queue.local
settlement_optional_report_send_max_concurrent_consumers=1
#  2åé
settlement_optional_report_send_delay_time=120000
settlement_optional_report_send_delay_count_max=30

# äºå¨ä»»å¡ä¸­å¿
task_upload_exchange_name=mcoin.mall.task.upload.exchange.local
task_upload_routingkey_name=mcoin.mall.task.upload.routingkey.local
task_upload_delay_exchange_name=mcoin.mall.task.upload.delay.exchange.local
task_upload_delay_routingkey_name=mcoin.mall.task.upload.delay.routingkey.local
task_upload_queue_name=mcoin.mall.task.upload.queue.local
task_upload_max_concurrent_consumers=1


# å®ç©å°ç¨åºè®¢ååæ­¥
mini_order_sync_exchange_name=mcoin.mall.mini.order.sync.exchange.local
mini_order_sync_routingkey_name=mcoin.mall.mini.order.sync.routingkey.local
mini_order_sync_delay_exchange_name=mcoin.mall.mini.order.sync.delay.exchange.local
mini_order_sync_delay_routingkey_name=mcoin.mall.mini.order.sync.delay.routingkey.local
mini_order_sync_queue_name=mcoin.mall.mini.order.sync.queue.local
mini_order_sync_max_concurrent_consumers=1
#  1åé
mini_order_sync_delay_time=60000
mini_order_sync_delay_count_max=30


# è®¾ç½®åºæ¬¡æ¶é´ç»ååè´­ä¹°æ¶é´
session_time_to_product_buy_time_exchange_name=mcoin.mall.session.time.to.product.buy.time.exchange.local
session_time_to_product_buy_time_routingkey_name=mcoin.mall.session.time.to.product.buy.time.routingkey.local
session_time_to_product_buy_time_delay_exchange_name=mcoin.mall.session.time.to.product.buy.time.delay.exchange.local
session_time_to_product_buy_time_delay_routingkey_name=mcoin.mall.session.time.to.product.buy.time.delay.routingkey.local
session_time_to_product_buy_time_queue_name=mcoin.mall.session.time.to.product.buy.time.queue.local
session_time_to_product_buy_time_max_concurrent_consumers=1
#  1åé
session_time_to_product_buy_time_delay_time=60000
session_time_to_product_buy_time_delay_count_max=30


# è®¢åç¯å¢ä¿¡æ¯
order_env_exchange_name=mcoin.mall.order.env.exchange.local
order_env_routingkey_name=mcoin.mall.order.env.routingkey.local
order_env_delay_exchange_name=mcoin.mall.order.env.delay.exchange.local
order_env_delay_routingkey_name=mcoin.mall.order.env.delay.routingkey.local
order_env_queue_name=mcoin.mall.order.env.queue.local
order_env_max_concurrent_consumers=8
#  2ç§
order_env_delay_time=2000
order_env_delay_count_max=30