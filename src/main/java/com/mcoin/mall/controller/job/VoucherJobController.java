package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.VoucherAssignRequest;
import com.mcoin.mall.service.job.VoucherJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/voucher")
@Slf4j
public class VoucherJobController {
    @Resource
    private VoucherJobService voucherJobService;

    /**
     * 每十分鐘執行一次派券任務
     * PHP(voucher:query)
     * @return
     */
    @PostMapping(value = "/assign")
    public Response<String> assignVoucher(@Valid VoucherAssignRequest request) {
        voucherJobService.assignVoucher(request);
        return Responses.ok("success");
    }
}
