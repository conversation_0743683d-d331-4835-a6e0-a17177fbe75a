<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrderrefundDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformOrderrefund">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areaid" jdbcType="INTEGER" property="areaid" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="ordercode_id" jdbcType="VARCHAR" property="ordercodeId" />
    <result column="refund_orderno" jdbcType="VARCHAR" property="refundOrderno" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="application_time" jdbcType="TIMESTAMP" property="applicationTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="platform_deal_status" jdbcType="BIT" property="platformDealStatus" />
    <result column="mpayintegral_status" jdbcType="BIT" property="mpayintegralStatus" />
    <result column="refund_transacation" jdbcType="VARCHAR" property="refundTransacation" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="refund_resson_front" jdbcType="VARCHAR" property="refundRessonFront" />
    <result column="return_route" jdbcType="INTEGER" property="returnRoute" />
    <result column="third_refund_code" jdbcType="VARCHAR" property="thirdRefundCode" />
    <result column="refund_score" jdbcType="DECIMAL" property="refundScore" />
    <result column="actual_refund_amount" jdbcType="DECIMAL" property="actualRefundAmount" />
    <result column="orderinfo_id" jdbcType="INTEGER" property="orderinfoId" />
    <result column="user_remark" jdbcType="VARCHAR" property="userRemark" />
    <result column="customer_remark" jdbcType="VARCHAR" property="customerRemark" />
    <result column="score_id" jdbcType="INTEGER" property="scoreId" />
    <result column="pay_id" jdbcType="INTEGER" property="payId" />
    <result column="refund_coupons_status" jdbcType="INTEGER" property="refundCouponsStatus" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, areaid, businessid, userid, orderid, ordercode_id, refund_orderno, refund_amount, 
    mpayintegral, currency, application_time, `status`, platform_deal_status, mpayintegral_status, 
    refund_transacation, refund_time, refund_reason, refund_resson_front, return_route, 
    third_refund_code, refund_score, actual_refund_amount, orderinfo_id, user_remark, 
    customer_remark, score_id, pay_id, refund_coupons_status, subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_orderrefund
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_orderrefund
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformOrderrefund">
    insert into fook_platform_orderrefund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areaid != null">
        areaid,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="ordercodeId != null">
        ordercode_id,
      </if>
      <if test="refundOrderno != null">
        refund_orderno,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="applicationTime != null">
        application_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="platformDealStatus != null">
        platform_deal_status,
      </if>
      <if test="mpayintegralStatus != null">
        mpayintegral_status,
      </if>
      <if test="refundTransacation != null">
        refund_transacation,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="refundReason != null">
        refund_reason,
      </if>
      <if test="refundRessonFront != null">
        refund_resson_front,
      </if>
      <if test="returnRoute != null">
        return_route,
      </if>
      <if test="thirdRefundCode != null">
        third_refund_code,
      </if>
      <if test="refundScore != null">
        refund_score,
      </if>
      <if test="actualRefundAmount != null">
        actual_refund_amount,
      </if>
      <if test="orderinfoId != null">
        orderinfo_id,
      </if>
      <if test="userRemark != null">
        user_remark,
      </if>
      <if test="customerRemark != null">
        customer_remark,
      </if>
      <if test="scoreId != null">
        score_id,
      </if>
      <if test="payId != null">
        pay_id,
      </if>
      <if test="refundCouponsStatus != null">
        refund_coupons_status,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areaid != null">
        #{areaid,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeId != null">
        #{ordercodeId,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderno != null">
        #{refundOrderno,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="applicationTime != null">
        #{applicationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="platformDealStatus != null">
        #{platformDealStatus,jdbcType=BIT},
      </if>
      <if test="mpayintegralStatus != null">
        #{mpayintegralStatus,jdbcType=BIT},
      </if>
      <if test="refundTransacation != null">
        #{refundTransacation,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundReason != null">
        #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="refundRessonFront != null">
        #{refundRessonFront,jdbcType=VARCHAR},
      </if>
      <if test="returnRoute != null">
        #{returnRoute,jdbcType=INTEGER},
      </if>
      <if test="thirdRefundCode != null">
        #{thirdRefundCode,jdbcType=VARCHAR},
      </if>
      <if test="refundScore != null">
        #{refundScore,jdbcType=DECIMAL},
      </if>
      <if test="actualRefundAmount != null">
        #{actualRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderinfoId != null">
        #{orderinfoId,jdbcType=INTEGER},
      </if>
      <if test="userRemark != null">
        #{userRemark,jdbcType=VARCHAR},
      </if>
      <if test="customerRemark != null">
        #{customerRemark,jdbcType=VARCHAR},
      </if>
      <if test="scoreId != null">
        #{scoreId,jdbcType=INTEGER},
      </if>
      <if test="payId != null">
        #{payId,jdbcType=INTEGER},
      </if>
      <if test="refundCouponsStatus != null">
        #{refundCouponsStatus,jdbcType=INTEGER},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformOrderrefund">
    update fook_platform_orderrefund
    <set>
      <if test="areaid != null">
        areaid = #{areaid,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeId != null">
        ordercode_id = #{ordercodeId,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderno != null">
        refund_orderno = #{refundOrderno,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="applicationTime != null">
        application_time = #{applicationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="platformDealStatus != null">
        platform_deal_status = #{platformDealStatus,jdbcType=BIT},
      </if>
      <if test="mpayintegralStatus != null">
        mpayintegral_status = #{mpayintegralStatus,jdbcType=BIT},
      </if>
      <if test="refundTransacation != null">
        refund_transacation = #{refundTransacation,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundReason != null">
        refund_reason = #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="refundRessonFront != null">
        refund_resson_front = #{refundRessonFront,jdbcType=VARCHAR},
      </if>
      <if test="returnRoute != null">
        return_route = #{returnRoute,jdbcType=INTEGER},
      </if>
      <if test="thirdRefundCode != null">
        third_refund_code = #{thirdRefundCode,jdbcType=VARCHAR},
      </if>
      <if test="refundScore != null">
        refund_score = #{refundScore,jdbcType=DECIMAL},
      </if>
      <if test="actualRefundAmount != null">
        actual_refund_amount = #{actualRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderinfoId != null">
        orderinfo_id = #{orderinfoId,jdbcType=INTEGER},
      </if>
      <if test="userRemark != null">
        user_remark = #{userRemark,jdbcType=VARCHAR},
      </if>
      <if test="customerRemark != null">
        customer_remark = #{customerRemark,jdbcType=VARCHAR},
      </if>
      <if test="scoreId != null">
        score_id = #{scoreId,jdbcType=INTEGER},
      </if>
      <if test="payId != null">
        pay_id = #{payId,jdbcType=INTEGER},
      </if>
      <if test="refundCouponsStatus != null">
        refund_coupons_status = #{refundCouponsStatus,jdbcType=INTEGER},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformOrderrefund">
    update fook_platform_orderrefund
    set areaid = #{areaid,jdbcType=INTEGER},
      businessid = #{businessid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      ordercode_id = #{ordercodeId,jdbcType=VARCHAR},
      refund_orderno = #{refundOrderno,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      application_time = #{applicationTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      platform_deal_status = #{platformDealStatus,jdbcType=BIT},
      mpayintegral_status = #{mpayintegralStatus,jdbcType=BIT},
      refund_transacation = #{refundTransacation,jdbcType=VARCHAR},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      refund_reason = #{refundReason,jdbcType=VARCHAR},
      refund_resson_front = #{refundRessonFront,jdbcType=VARCHAR},
      return_route = #{returnRoute,jdbcType=INTEGER},
      third_refund_code = #{thirdRefundCode,jdbcType=VARCHAR},
      refund_score = #{refundScore,jdbcType=DECIMAL},
      actual_refund_amount = #{actualRefundAmount,jdbcType=DECIMAL},
      orderinfo_id = #{orderinfoId,jdbcType=INTEGER},
      user_remark = #{userRemark,jdbcType=VARCHAR},
      customer_remark = #{customerRemark,jdbcType=VARCHAR},
      score_id = #{scoreId,jdbcType=INTEGER},
      pay_id = #{payId,jdbcType=INTEGER},
      refund_coupons_status = #{refundCouponsStatus,jdbcType=INTEGER},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>