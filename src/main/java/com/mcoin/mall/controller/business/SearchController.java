package com.mcoin.mall.controller.business;

import cn.hutool.core.util.NumberUtil;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.github.houbb.opencc4j.util.ZhTwConverterUtil;
import com.mcoin.mall.bean.SearchProductQuery;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.BusinessProductTypeEnum;
import com.mcoin.mall.model.*;
import com.mcoin.mall.service.business.SearchService;
import com.mcoin.mall.util.*;
import com.vdurmont.emoji.EmojiParser;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class SearchController {

    @Resource
    private SearchService searchService;

    @Resource
    private ContextHolder contextHolder;


    /**
     * 福利搜索
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 搜索结果响应
     */
    @PostMapping(value = "/api/business/searchproduct", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "福利搜索", notes = "根据关键词和筛选条件搜索福利商品")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<SearchProductResponse> searchProduct(@Valid @RequestBody SearchProductRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        Locale locale = contextHolder.getLocale();
        String language = locale.getLanguage();
        SearchProductQuery query = new SearchProductQuery();
        request.convertWsgToGsj();
        // 去除表情符
        String searchTerm = StrUtil.dbUtf8Parser(EmojiParser.removeAllEmojis(request.getSearchTerm()));
        String searchTermSimple = ZhTwConverterUtil.toSimple(ZhConverterUtil.toSimple(searchTerm));
        String searchTermTraditional = ZhConverterUtil.toTraditional(searchTermSimple);
        String searchTermTwTraditional = ZhTwConverterUtil.toTraditional(searchTermSimple);
        query.setSearch(searchTerm);
        query.setSearchSimple(searchTermSimple);
        query.setSearchTraditional(searchTermTraditional);
        query.setSearchTwTraditional(searchTermTwTraditional);

        String lat = NumberUtil.isNumber(request.getLat()) ? request.getLat() : "";
        String lon = NumberUtil.isNumber(request.getLot()) ? request.getLot() : "";
        query.setLat(lat);
        query.setLot(lon);
        query.setLanguage(language);
        int sortType = 1;
        if (StringUtils.isNotBlank(request.getSort()) && "2".equals(request.getSort())) {
            sortType = 2;
        }
        query.setSortType(sortType);
        if (null != request.getInformation() && request.getInformation().length > 0) {
            List<Integer> informationIds = Arrays.stream(request.getInformation()).sequential().map(Integer::parseInt).collect(Collectors.toList());
            query.setInformationIds(informationIds);
        }
        if (null != request.getCategory() && request.getCategory().length > 0) {
            List<Integer> categoryIds = Arrays.stream(request.getCategory()).sequential().map(Integer::parseInt).collect(Collectors.toList());
            query.setStoresTypes(categoryIds);
        }
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
        query.setHarmonySwitch(harmonySwitch);
        query.setClientType(clientType);
        if (null != request.getType() && request.getType().length > 0) {
            List<Integer> typeIds = Arrays.stream(request.getType()).sequential().map(Integer::parseInt).collect(Collectors.toList());

            // 如果是HARMONY设备且开关开启，过滤掉type=12的商品
            if (Boolean.TRUE.equals(harmonySwitch)) {
                typeIds = typeIds.stream().filter(type -> type != BusinessProductTypeEnum.PHYSICAL_LINK.getTypeId()).collect(Collectors.toList());
            }

            query.setTypes(typeIds);
        }
        if (StringUtils.isNotBlank(request.getBusinessCategoriesIds())) {
            List<Integer> businessCatogoriesList = Arrays.stream(request.getBusinessCategoriesIds().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            query.setBusinessCategoriesIds(businessCatogoriesList);
        }
        query.setOrderType(request.getOrdertype());
        query.setUserId(contextHolder.getAuthUserInfo().getUserId());
        int page = request.getPage();
        int offset = PageUtil.getOffset(page, McoinMall.DEFAULT_PAGE_SIZE);
        query.setOffset(offset);
        query.setLimit(McoinMall.DEFAULT_PAGE_SIZE);
        boolean productTempHasData = this.searchService.isProductTempHasData();
        Integer dataCount = 0;
        SearchProductResponse response = new SearchProductResponse();
        if (productTempHasData) {
            dataCount = this.searchService.searchProductTmpCount(query);
            response = this.searchService.searchProductTmp(query);
        }
        //搜索不到数据也再查原表
        if (!productTempHasData || 0 == dataCount) {
            // 缓存失效查原表开关
            String queryTableSwitch = ConfigUtils.getProperty("cache.invalidation.original.table.search.switch", "true");
            if (Boolean.parseBoolean(queryTableSwitch)) {
                try {
                    dataCount = this.searchService.searchProductCount(query);
                } catch (Exception e) {
                    log.error("搜索未知异常", e);
                    dataCount = 0;
                }
                response = this.searchService.searchProduct(query);
            } else {
                log.info("缓存失效查原表开关:{}", queryTableSwitch);
                dataCount = 0;
                response = new SearchProductResponse();
                response.setSnatchList(Collections.emptyList());
            }
        }
        Page rPage = new Page();
        rPage.setPerPage(query.getLimit());
        rPage.setTotal(dataCount);
        rPage.setLastPage(PageUtil.getPageNum(dataCount, query.getLimit()));
        rPage.setCurrentPage(page);
        response.setPage(rPage);
        return Responses.ok(response);
    }

    /**
     * 店鋪搜索
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 搜索结果响应
     */
    @PostMapping(value = "/api/business/searchshop", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "店鋪搜索", notes = "根据关键词和筛选条件搜索店铺")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<SearchStoreResponse> searchStore(@Valid @RequestBody SearchStoreRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        String language = this.contextHolder.getLocale().getLanguage();
        SearchStoreQuery query = new SearchStoreQuery();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = dateFormat.format(new Date());
        query.setCurrentTime(currentTime);
        query.setLanguage(language);

        // 客户端类型将在service层处理

        // 去除表情符
        String searchTerm = StrUtil.dbUtf8Parser(EmojiParser.removeAllEmojis(request.getSearchTerm()));
        String searchTermSimple = ZhTwConverterUtil.toSimple(ZhConverterUtil.toSimple(searchTerm));
        String searchTermTraditional = ZhConverterUtil.toTraditional(searchTermSimple);
        String searchTermTwTraditional = ZhTwConverterUtil.toTraditional(searchTermSimple);
        query.setSearchTerm(searchTerm);
        query.setSearchTermSimple(searchTermSimple);
        query.setSearchTermTraditional(searchTermTraditional);
        query.setSearchTermTwTraditional(searchTermTwTraditional);
        if (StringUtils.isNotBlank(request.getBusinessCategoriesIds())) {
            List<Integer> businessCatogoriesList = Arrays.stream(request.getBusinessCategoriesIds().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            query.setBusinessCategoriesIds(businessCatogoriesList);
        }
        if (null != request.getCategory() && request.getCategory().length > 0) {
            List<Integer> categories = Arrays.stream(request.getCategory()).sequential().map(Integer::parseInt).collect(Collectors.toList());
            query.setCategory(categories);
        }
        query.setLot(request.getLot());
        query.setLat(request.getLat());
        if (null != request.getType() && request.getType().length > 0) {
            List<Integer> types = Arrays.stream(request.getType()).sequential().map(Integer::parseInt).collect(Collectors.toList());
            query.setType(types);
        }
        if (null != request.getInformation() && request.getInformation().length > 0) {
            List<Integer> informationIds = Arrays.stream(request.getInformation()).sequential().map(Integer::parseInt).collect(Collectors.toList());
            query.setBusinessInformationIds(informationIds);
        }
        int page = request.getPage();
        int offset = PageUtil.getOffset(page, McoinMall.DEFAULT_PAGE_SIZE);
        query.setOffset(offset);
        query.setLimit(McoinMall.DEFAULT_PAGE_SIZE);

        // 在调用service方法前设置harmonySwitch
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
        query.setHarmonySwitch(harmonySwitch);

        boolean b = searchService.buildSearchShopQuery(query);
        Integer dataCount = 0;
        SearchStoreResponse response = new SearchStoreResponse();
        if (b) {
            dataCount = searchService.searchShopCnt(query);
            response = searchService.searchShop(query);
        }
        Page rPage = new Page();
        rPage.setPerPage(query.getLimit());
        rPage.setTotal(dataCount);
        rPage.setLastPage(PageUtil.getPageNum(dataCount, query.getLimit()));
        rPage.setCurrentPage(page);
        response.setPage(rPage);
        return Responses.ok(response);
    }

    /**
     * 获取热门关键字
     *
     * @return
     */
    @GetMapping(value = "/api/business/popularsearches", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<PopularSearchesResponse> popularSearches() {
        PopularSearchesResponse response = searchService.getPopularSearches();
        return Responses.ok(response);
    }

    /**
     * 检查搜索关键字
     *
     * @return
     */
    @PostMapping(value = "/api/business/checksearchvalue", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "检查搜索关键字", notes = "检查搜索关键字的有效性")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<CheckSearchValueResponse> checkSearchValue(@Valid @RequestBody CheckSearchValueRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        CheckSearchValueResponse response = searchService.checkSearchValue(request, clientType);
        return Responses.ok(response);
    }
}
