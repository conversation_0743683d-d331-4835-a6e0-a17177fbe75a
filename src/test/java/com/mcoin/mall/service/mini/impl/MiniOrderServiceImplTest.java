package com.mcoin.mall.service.mini.impl;

import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.client.MiniOrderClient;
import com.mcoin.mall.client.MpMcoinMallManagementClient;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.model.MiniOrderIsAfterRequest;
import com.mcoin.mall.model.MiniOrderRequest;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JsonTestUtils;
import com.mcoin.mall.util.SentinelUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.context.MessageSource;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

class MiniOrderServiceImplTest {
    
    private MockedStatic<ConfigUtils> mockedStatic;
    private MockedStatic<SentinelUtils> sentinelMock;
    
    @Mock
    MiniOrderClient miniOrderClient;
    
    @Mock
    MpMcoinMallManagementClient mpMcoinMallManagementClient;
    
    @Mock
    MessageSource messageSource;
    
    @Mock
    ContextHolder contextHolder;
    
    @Mock
    FookMacaupassUserDao fookMacaupassUserDao;
    
    @Mock
    Logger log;
    
    @InjectMocks
    MiniOrderServiceImpl miniOrderServiceImpl;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        sentinelMock = mockStatic(SentinelUtils.class);
    }

    @AfterEach
    void cleanup() {
        if (mockedStatic != null) {
            mockedStatic.close();
        }
        if (sentinelMock != null) {
            sentinelMock.close();
        }
    }

    @Test
    void testGetMiniOrders() {
        // given
        sentinelMock.when(() -> SentinelUtils.handleBlockException(any(), any()))
            .thenReturn(JsonTestUtils.getStringFromJson("json/miniOrders.json", "miniOrder"));
        
        when(contextHolder.getLocale()).thenReturn(null);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(0);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        
        FookMacaupassUser macaupassUser = new FookMacaupassUser();
        macaupassUser.setId(0);
        macaupassUser.setUserId(0);
        macaupassUser.setOpenid("openid");
        macaupassUser.setAccessToken("accessToken");
        macaupassUser.setRefreshToken("refreshToken");
        macaupassUser.setStatus(0);
        macaupassUser.setPhone("phone");
        macaupassUser.setArea("area");
        macaupassUser.setPoint(0);
        macaupassUser.setCreateTime(new GregorianCalendar(2024, Calendar.APRIL, 17, 10, 18).getTime());
        macaupassUser.setUpdateTime(new GregorianCalendar(2024, Calendar.APRIL, 17, 10, 18).getTime());
        macaupassUser.setCode("code");
        macaupassUser.setCustomid("customid");
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(macaupassUser);
        
        mockedStatic = Mockito.mockStatic(ConfigUtils.class);
        mockedStatic.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("http://127.0.0.1/");
        
        MiniOrderRequest request = new MiniOrderRequest();
        
        // when & then
        assertEquals("productName_af2824563b42", miniOrderServiceImpl.getMiniOrders(request).getResultList().get(0).getProductName());
    }

    @Test
    void testGetOrderIsAfter() {
        // given
        sentinelMock.when(() -> SentinelUtils.handleBlockException(any(), any()))
            .thenReturn(JsonTestUtils.getStringFromJson("json/miniOrderIsAfter.json", "orderIsAfter"));
            
        when(contextHolder.getLocale()).thenReturn(null);
        
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(0);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        
        FookMacaupassUser macaupassUser = new FookMacaupassUser();
        macaupassUser.setId(0);
        macaupassUser.setUserId(0);
        macaupassUser.setOpenid("openid");
        macaupassUser.setAccessToken("accessToken");
        macaupassUser.setRefreshToken("refreshToken");
        macaupassUser.setStatus(0);
        macaupassUser.setPhone("phone");
        macaupassUser.setArea("area");
        macaupassUser.setPoint(0);
        macaupassUser.setCreateTime(new GregorianCalendar(2024, Calendar.APRIL, 17, 10, 18).getTime());
        macaupassUser.setUpdateTime(new GregorianCalendar(2024, Calendar.APRIL, 17, 10, 18).getTime());
        macaupassUser.setCode("code");
        macaupassUser.setCustomid("customid");
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(macaupassUser);
        
        mockedStatic = Mockito.mockStatic(ConfigUtils.class);
        mockedStatic.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("http://127.0.0.1/");
        
        MiniOrderIsAfterRequest request = new MiniOrderIsAfterRequest();
        
        // when & then
        assertEquals(0, miniOrderServiceImpl.getOrderIsAfter(request).getIsAfterBtn());
    }
} 