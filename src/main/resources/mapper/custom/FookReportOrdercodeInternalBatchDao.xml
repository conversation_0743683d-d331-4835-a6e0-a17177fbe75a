<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportOrdercodeInternalBatchDao">
    <select id="sumSettlementAmount" resultType="com.mcoin.mall.bo.SettlementAmountBo">
        select SUM(bill_amount) as totalAmount,
               SUM(settlement_amount) as totalSettlement,
               SUM(commission) as totalCommission,
               SUM(momecoins) as totalMomecoins,
               SUM(subsidy_amount) as totalSubsidyAmount
        from fook_report_ordercode_internal_batch
        where start_time = #{startTime,jdbcType=TIMESTAMP}
        and end_time = #{endTime,jdbcType=TIMESTAMP}
    </select>
</mapper>