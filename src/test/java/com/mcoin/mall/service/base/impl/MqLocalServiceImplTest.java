package com.mcoin.mall.service.base.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mcoin.mall.bean.FookMqLocal;
import com.mcoin.mall.dao.FookMqLocalDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.List;

public class MqLocalServiceImplTest{

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private RabbitTemplate rabbitTemplate;

    @Mock
    private FookMqLocalDao fookMqLocalDao;

    @InjectMocks
    private MqLocalServiceImpl mqLocalServiceImpl;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testUpdateMqLocalFinish() {
        Long id = 1L;
        FookMqLocal fookMqLocal = new FookMqLocal();
        fookMqLocal.setId(id);
        fookMqLocal.setStatus(1);
    
        when(fookMqLocalDao.updateByPrimaryKeySelective(any(FookMqLocal.class))).thenReturn(1);
    
        int result = mqLocalServiceImpl.updateMqLocalFinish(id);
    
        assertEquals(1, result);
    }

    @Test
    public void testSaveMqLocal() {
        FookMqLocal record = new FookMqLocal();
        record.setResourceId("resourceId");
        record.setResourceType("resourceType");
        record.setTemplateName("templateName");
        record.setMaxTryCount(3);
        JSONObject object = new JSONObject();
        object.put("mqLocalId", 1L);
        record.setMessageBody(JSON.toJSONString(object));
    
        when(fookMqLocalDao.insert(any(FookMqLocal.class))).thenAnswer(invocation -> {
            FookMqLocal arg = invocation.getArgument(0);
            arg.setId(1L);
            return 1;
        });
    
        when(fookMqLocalDao.updateByPrimaryKeySelective(any(FookMqLocal.class))).thenReturn(1);
    
        int result = mqLocalServiceImpl.saveMqLocal(record);
    
        assertEquals(1, result);
        assertEquals(1L, record.getId());
    }

    @Test
    public void testSaveMqLocalWithException() {
        FookMqLocal record = new FookMqLocal();
        record.setMessageBody(JSON.toJSONString(new JSONObject()));
    
        when(fookMqLocalDao.insert(any(FookMqLocal.class))).thenAnswer(invocation -> {
            FookMqLocal arg = invocation.getArgument(0);
            arg.setId(null);
            return 1;
        });
    
        BusinessException exception = assertThrows(BusinessException.class, () -> mqLocalServiceImpl.saveMqLocal(record));
        assertEquals(Response.Code.UNKNOWN_ERROR, exception.getRespCode());
        assertEquals("system error", exception.getMessage());
    }

    @Test
    public void testResendMq() {
        List<FookMqLocal> mqLocals = new ArrayList<>();
        FookMqLocal mqLocal = new FookMqLocal();
        mqLocal.setId(1L);
        mqLocal.setStatus(0);
        mqLocal.setMaxTryCount(3);
        mqLocal.setTryCount(2);
        mqLocal.setTemplateName("templateName");
        mqLocal.setMessageBody("messageBody");
        mqLocals.add(mqLocal);
    
        when(fookMqLocalDao.selectList(any())).thenReturn(mqLocals);
        when(applicationContext.getBean(any(String.class), any(Class.class))).thenReturn(rabbitTemplate);
    
        assertDoesNotThrow(() -> mqLocalServiceImpl.resendMq());
    
        verify(rabbitTemplate, times(1)).convertAndSend(mqLocal.getMessageBody());
        verify(fookMqLocalDao, times(1)).updateByPrimaryKeySelective(any(FookMqLocal.class));
    }

}