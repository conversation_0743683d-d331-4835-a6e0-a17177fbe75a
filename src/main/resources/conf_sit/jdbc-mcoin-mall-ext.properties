
# é»è®¤è¶æ¶æ¶é´
spring.transaction.default-timeout=10

# master
spring.datasource.dynamic.datasource.master.hikari.pool-name=masterHikariPool
spring.datasource.dynamic.datasource.master.hikari.maximum-pool-size=64
spring.datasource.dynamic.datasource.master.hikari.idle-timeout=240000
spring.datasource.dynamic.datasource.master.hikari.max-lifetime=1800000
spring.datasource.dynamic.datasource.master.hikari.keepalive-time=120000
spring.datasource.dynamic.datasource.master.hikari.is-auto-commit=false
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.cachePrepStmts=true
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.prepStmtCacheSize=250
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.prepStmtCacheSqlLimit=2048
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.useServerPrepStmts=false
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.useLocalSessionState=true
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.rewriteBatchedStatements=true
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.cacheResultSetMetadata=true
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.cacheServerConfiguration=true
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.elideSetAutoCommits=true
spring.datasource.dynamic.datasource.master.hikari.dataSourceProperties.maintainTimeStats=false

# slave
spring.datasource.dynamic.datasource.slave.hikari.pool-name=slaveHikariPool
spring.datasource.dynamic.datasource.slave.hikari.maximum-pool-size=128
spring.datasource.dynamic.datasource.slave.hikari.idle-timeout=240000
spring.datasource.dynamic.datasource.slave.hikari.max-lifetime=1800000
spring.datasource.dynamic.datasource.slave.hikari.keepalive-time=120000
spring.datasource.dynamic.datasource.slave.hikari.is-auto-commit=false
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.cachePrepStmts=true
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.prepStmtCacheSize=250
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.prepStmtCacheSqlLimit=2048
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.useServerPrepStmts=false
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.useLocalSessionState=true
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.rewriteBatchedStatements=true
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.cacheResultSetMetadata=true
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.cacheServerConfiguration=true
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.elideSetAutoCommits=true
spring.datasource.dynamic.datasource.slave.hikari.dataSourceProperties.maintainTimeStats=false