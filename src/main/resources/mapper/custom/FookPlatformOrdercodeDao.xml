<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrdercodeDao">
    <select id="getMyCouponOther" resultType="com.mcoin.mall.bean.MyCouponOther" timeout="5">
        select * from
        (select o.sellerid as sellerid,o.payment_time as paymentTime,i.prodcutid as prodcutid,
        IF(@tmp=o.sellerid,@rank:=@rank + 1,@rank:=1) as
        newRank,@tmp:=o.sellerid as tmp,c.*
        from fook_platform_ordercode c
        left join fook_platform_order o on o.id=c.orderid
        left join fook_platform_orderinfo i on i.id=c.orderinfo_id
        ,(SELECT @rank := 0, @tmp := NULL) AS init
        where o.userid = #{userId,jdbcType=INTEGER} and o.payment_time >= #{dateLastYear,jdbcType=VARCHAR}
        <if test="status == 2">
            and c.refund_status = 1 and c.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="status == 3 || status == 5">
            and c.refund_status in (2,3)
        </if>
        <if test="status == 4">
            and c.refund_status = 1 and c.status = 1 and i.vaild_end_time <![CDATA[ < ]]> #{validDate,jdbcType=VARCHAR}
        </if>
        <if test="sellerId != null">
            and o.sellerid = #{sellerId,jdbcType=INTEGER}
        </if>
        ORDER BY o.sellerid,o.payment_time desc) b
        where newRank > #{start,jdbcType=INTEGER} and newRank  <![CDATA[ <= ]]> #{end,jdbcType=INTEGER}
    </select>

    <select id="getMyCouponCount" resultType="com.mcoin.mall.bean.MyCouponOtherCnt" timeout="5">
        select o.sellerid,count(c.id) as count
        from fook_platform_ordercode c
        left join fook_platform_order o on o.id=c.orderid
        left join fook_platform_orderinfo i on i.id=c.orderinfo_id
        where o.userid = #{userId,jdbcType=INTEGER}
        and o.payment_time >= #{dateLastYear,jdbcType=VARCHAR}
        <if test="status == 2">
            and c.refund_status = 1 and c.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="status == 3 || status == 5">
            and c.refund_status in (2,3)
        </if>
        <if test="status == 4">
            and c.refund_status = 1 and c.status = 1 and i.vaild_end_time <![CDATA[ < ]]> #{validDate,jdbcType=VARCHAR}
        </if>
        <if test="sellerId != null">
            and o.sellerid = #{sellerId,jdbcType=INTEGER}
        </if>
        GROUP BY o.sellerid ORDER BY o.sellerid
    </select>

    <select id="getCountByUserId" resultType="int">
        select count(1) count from fook_platform_ordercode fpoc left join fook_platform_orderinfo fpoi on
        fpoc.orderinfo_id = fpoi.id
        where fpoc.userid = #{userId,jdbcType=INTEGER}
        and fpoc.refund_status = '1'
        and fpoc.status = '1'
        and fpoi.vaild_end_time >= date_format(now() ,'%Y-%m-%d %H:%i:%s')
    </select>

    <select id="getMyCoupon" resultType="com.mcoin.mall.bean.MyCouponOther" timeout="5">
        SELECT
        c.id, c.orderid, c.code, c.status, c.shopid, c.user_time, c.verification_mode, c.is_comment, c.is_settlement,
        c.settlement_time, c.settlement_id, c.merch_settle_amount, c.merch_settle_amount_currency,
        c.refund_status, c.apportion_bill_amount, c.apportion_bill_final_amount, c.apportion_mpayintegral,
        c.apportion_momecoins, c.apportion_commission, c.orderinfo_id, c.refundid, c.userid, c.apportion_memberintegral,
        c.is_exported, c.is_untie, c.is_voucher, c.preferentialAmount, c.requestOrderNo, c.requestOrderNoStatus,
        c.is_a_open,
        o.payment_time as paymentTime, o.sellerid as sellerid,i.prodcutid as prodcutid
        FROM fook_platform_ordercode AS c
        JOIN fook_platform_order AS o ON o.id = c.orderid
        JOIN fook_platform_orderinfo AS i ON i.id = c.orderinfo_id
        WHERE o.userid = #{userId,jdbcType=INTEGER}
        AND c.refund_status = '1'
        AND c.status = #{status,jdbcType=INTEGER}
        AND i.vaild_end_time > #{now,jdbcType=VARCHAR}
        ORDER BY o.payment_time DESC, o.sellerid;
    </select>

    <select id="countByOrderId" resultType="int">
        select count(id)
        from fook_platform_ordercode where orderid = #{orderId,jdbcType=INTEGER}
    </select>

    <select id="countByOrderInfoId" resultType="int">
        select count(id)
        from fook_platform_ordercode where orderinfo_id = #{orderInfoId,jdbcType=INTEGER}
    </select>

    <select id="getOrderInfoIdByCode" resultType="java.lang.Integer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_platform_ordercode WHERE code = #{ordercode,jdbcType=VARCHAR}
    </select>
    <select id="selectByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fook_platform_ordercode where orderid = #{orderId,jdbcType=INTEGER}
        and status = 1
    </select>

    <update id="updateCodeToUsed" parameterType="com.mcoin.mall.bean.FookPlatformOrdercode">
        update fook_platform_ordercode
        set
        `status` = 2,
        shopid = #{shopId,jdbcType=INTEGER},
        user_time = #{userTime,jdbcType=TIMESTAMP},
        <if test="trackingNo != null">
            tracking_no = #{trackingNo,jdbcType=VARCHAR},
        </if>
        <if test="businessRedeemTime != null">
            business_redeem_time = #{businessRedeemTime,jdbcType=TIMESTAMP},
        </if>
        verification_mode = #{verificationMode,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
        and `status` = 1
    </update>

    <select id="getOrderCode" resultType="com.mcoin.mall.bean.FookPlatformOrdercode">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_platform_ordercode
        where
        code = #{code,jdbcType=VARCHAR}
    </select>


    <select id="countByOrderIdAndStatus" resultType="int">
        select count(id)
        from fook_platform_ordercode where orderid = #{orderId,jdbcType=INTEGER}
        And status = #{status,jdbcType=INTEGER}
    </select>

    <select id="coupons" resultType="com.mcoin.mall.bean.Coupons">
        SELECT * FROM (
        SELECT *, IF(@tmp = storeid, @rank := @rank + 1, @rank := 1) AS newRank, @tmp := storeid AS tmp FROM (
        SELECT o.sellerid as sellerid,o.payment_time as paymentTime,i.prodcutid as prodcutid,c.*,IF(c.shopid > 0 ,
        c.shopid,(
        SELECT fbsp.storeid AS storeid
        FROM fook_business_store_product fbsp
        WHERE fbsp.productid = i.prodcutid
        ORDER BY fbsp.storeid
        LIMIT 1)) AS storeid
        FROM fook_platform_ordercode c
        LEFT JOIN fook_platform_order o ON
        o.id = c.orderid
        LEFT JOIN fook_platform_orderinfo i ON
        i.id = c.orderinfo_id
        where o.userid = #{userId,jdbcType=INTEGER} and o.payment_time >= #{dateLastYear,jdbcType=VARCHAR}
        <if test="status == 2">
            and c.refund_status = 1 and c.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="status == 3 || status == 5">
            and c.refund_status in (2,3)
        </if>
        <if test="status == 4">
            and c.refund_status = 1 and c.status = 1 and i.vaild_end_time <![CDATA[ < ]]> #{validDate,jdbcType=VARCHAR}
        </if>
        ) aa
        ,(SELECT @rank := 0, @tmp := NULL) AS init
        <where>
            <if test="storeId != null">
                and storeid = #{storeId,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY storeid,paymentTime DESC,id ASC
        ) bb
        where newRank > #{start,jdbcType=INTEGER} and newRank  <![CDATA[ <= ]]> #{end,jdbcType=INTEGER}
    </select>

    <select id="couponsCnt" resultType="com.mcoin.mall.bean.CouponsCnt">
        SELECT storeid,count(id) as count FROM (
        SELECT o.sellerid as sellerid,o.payment_time as paymentTime,i.prodcutid as prodcutid,c.*,IF(c.shopid > 0 , c.shopid,(
        SELECT fbsp.storeid AS storeid
        FROM fook_business_store_product fbsp
        WHERE fbsp.productid = i.prodcutid
        ORDER BY fbsp.storeid
        LIMIT 1)) AS storeid
        FROM fook_platform_ordercode c
        LEFT JOIN fook_platform_order o ON
        o.id = c.orderid
        LEFT JOIN fook_platform_orderinfo i ON
        i.id = c.orderinfo_id
        where o.userid = #{userId,jdbcType=INTEGER} and o.payment_time >= #{dateLastYear,jdbcType=VARCHAR}
        <if test="status == 2">
            and c.refund_status = 1 and c.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="status == 3 || status == 5">
            and c.refund_status in (2,3)
        </if>
        <if test="status == 4">
            and c.refund_status = 1 and c.status = 1 and i.vaild_end_time <![CDATA[ < ]]> #{validDate,jdbcType=VARCHAR}
        </if>
        ) aa
        <where>
            <if test="storeId != null">
                and storeid = #{storeId,jdbcType=INTEGER}
            </if>
        </where>
        GROUP BY storeid ORDER BY storeid
    </select>

    <update id="updateStatusByRefund">
        update fook_platform_ordercode
        set
        `refund_status` = #{refundStatus,jdbcType=VARCHAR}
        where refundid = #{refundId,jdbcType=INTEGER}
    </update>

    <update id="batchUpdateSettlementInfo">
        update fook_platform_ordercode
        set settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
            settlement_id = #{settlementId,jdbcType=INTEGER}
        where is_settlement = '1'
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="sumRequiredSettlementAmount" resultType="com.mcoin.mall.bo.OrderCodeAmountBo">
        select SUM(merch_settle_amount) as totalAmount,
               SUM(apportion_commission) as commission
        from fook_platform_ordercode
        where user_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and status = 2
        and is_settlement = 0
    </select>

    <select id="sumRequiredWriteOffAmount" resultType="com.mcoin.mall.bo.OrderCodeAmountBo">
        select SUM(merch_settle_amount) as totalAmount,
               SUM(apportion_commission) as commission
        from fook_platform_ordercode
        where status = 1
          and refund_status = 1
    </select>

    <select id="countUsedOrderCode" resultType="java.lang.Integer">
        select count(id)  from fook_platform_ordercode
        where
            `status` = 2
        and refund_status = 1
        and is_voucher = 1
        and user_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and user_time <![CDATA[ <  ]]> #{endTime,jdbcType=TIMESTAMP}
        and orderid is not NULL;
    </select>

    <select id="queryUsedOrderCode" resultType="com.mcoin.mall.bo.VoucherSettlementDataBo">
        select
        fpo.sellerid,
        fpoc.shopid,
        fpoc.id as orderCodeId,
        fpoi.prodcutid as productId,
        fpo.id as orderId,
        fb.`name` as businessName,
        fss.`name` as storeName,
        fb.`code` as businessCode ,
        fb.js_account as businessBankAccount,
        fb.js_name as businessBankName,
        fb.js_bank as businessBank,
        fpo.create_time as orderCreateTime,
        fpoc.user_time as useTime,
        fpoc.apportion_bill_amount as apportionBillAmount,
        fpoc.apportion_bill_final_amount as apportionBillFinalAmount,
        fpoc.apportion_momecoins as apportionMomecoins,
        fpoc.apportion_mpayintegral as apportionMpayintegral,
        fpo.point_ratio as orderPointRatio,
        fpoc.apportion_commission as apportionCommission,
        fpoc.merch_settle_amount as merchantSettlementAmount,
        fpoc.`code` as voucherCode,
        fpoi.title_snapshots as voucherName,
        fpo.order_transaction as orderTransactionNo,
        fpo.is_mpay as isMpay,
        fpo.is_member as isMemeber,
        fpoc.apportion_memberintegral as apportionMemberIntegral


        from fook_platform_ordercode fpoc
        left join fook_platform_order fpo on fpoc.orderid = fpo.id
        left join fook_business fb on fpo.sellerid = fb.id
        left join fook_platform_orderinfo fpoi on fpoc.orderid = fpoi.orderid
        left join fook_stores fss on fss.id=fpoc.shopid
        where
        fpoc.`status` = 2
        and fpoc.refund_status = 1
        and fpoc.is_voucher = 1
     and user_time <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
     and user_time <![CDATA[ <  ]]> #{endTime,jdbcType=TIMESTAMP}
        and fpoc.orderid is not NULL
    </select>
    <select id="queryBusinessOrderSettlementBo" resultType="com.mcoin.mall.bo.BusinessOrderSettlementBo">
        select
        o.sellerid,
        SUM( CASE oc.is_settlement WHEN 1 THEN merch_settle_amount ELSE 0 END ) AS settlement,
                    SUM( CASE oc.is_settlement WHEN 1 THEN 0 ELSE merch_settle_amount END ) AS waitsettlement,
                    SUM( oc.apportion_commission ) AS commission,
                    SUM( CASE oc.`status` WHEN 2 THEN apportion_bill_amount ELSE 0 END ) AS settlementAccounting,
                    SUM( CASE oc.`status` WHEN 1 THEN apportion_bill_amount ELSE 0 END ) AS pendingAccounting
        from fook_platform_ordercode oc
                 left join fook_platform_order o on oc.orderid = o.id
                 left JOIN fook_business AS b ON b.id = o.sellerid
        where  o.sellerid in
        <foreach collection="sellerids" item="sellerid" open="(" separator="," close=")">
            #{sellerid}
        </foreach> and o.`status` = 2
          AND o.refund_status = 0
          AND o.create_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        group by sellerid order by sellerid desc
    </select>

    <update id="updateCodeToUnUsed">
        update fook_platform_ordercode
        set
        `status` = 1
        where id = #{id,jdbcType=INTEGER}
        and `status` = 2
    </update>
</mapper>