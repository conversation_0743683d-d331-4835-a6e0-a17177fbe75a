package com.mcoin.mall.service.common.impl;

import com.mcoin.mall.service.common.AtomicSeqService;
import com.mcoin.mall.util.JodaTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AtomicSeqServiceImpl implements AtomicSeqService {

    @Resource(name = "redissonClient")
    private RedissonClient redissonClient;

    @Override
    public String getOrderNo() {
        StringBuilder buf = new StringBuilder();
        buf.append(JodaTimeUtil.getFullTime()).append("100").append(buildSeq("order_no"+ JodaTimeUtil.getDate(), 6));
        return buf.toString();
    }

    @Override
    public String getSnapUpNo() {
        StringBuilder buf = new StringBuilder();
        buf.append(JodaTimeUtil.getFullTime()).append("100").append(buildSeq("snap_up"+ JodaTimeUtil.getDate(), 6));
        return buf.toString();
    }

    @Override
    public String getOutTradeNo() {
        StringBuilder buf = new StringBuilder();
        buf.append(JodaTimeUtil.getFullTime()).append("100").append(buildSeq("out_trade"+ JodaTimeUtil.getDate(), 6));
        return buf.toString();
    }



    private String buildSeq(String seqName, int strNum){
        RAtomicLong ati = redissonClient.getAtomicLong("seq_" + seqName);
        long newNum = ati.incrementAndGet();
        if(newNum < 3){
            ati.expire(2, TimeUnit.DAYS);
        }
        //数字长度为8位，长度不够数字前面补0
        return String.format("%0"+strNum+"d", newNum);
    }

}
