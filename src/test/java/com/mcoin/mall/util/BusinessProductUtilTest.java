package com.mcoin.mall.util;

import com.mcoin.mall.constant.BusinessProductTypeEnum;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class BusinessProductUtilTest{

    @Test
    public void testFillHrefUrlBy_ValidInputs() {
        Integer productId = 1;
        Integer productType = BusinessProductTypeEnum.PHYSICAL_LINK.getTypeId();
        String hrefUrl = "http://example.com/";
        Integer goodsId = 123;
        String result = BusinessProductUtil.fillHrefUrlBy(productId, productType, hrefUrl, goodsId);
        assertEquals(hrefUrl + goodsId, result, "Expected hrefUrl to be appended with goodsId.");
    }

    @Test
    public void testFillHrefUrlBy_NullProductId() {
        Integer productId = null;
        Integer productType = 1;
        String hrefUrl = "http://example.com/";
        Integer goodsId = 123;
        String result = BusinessProductUtil.fillHrefUrlBy(productId, productType, hrefUrl, goodsId);
        assertEquals(hrefUrl, result, "Expected hrefUrl to be returned when productId is null.");
    }

    @Test
    public void testFillHrefUrlBy_NonPhysicalLinkProductType() {
        Integer productId = 1;
        Integer productType = 2;
        String hrefUrl = "http://example.com/";
        Integer goodsId = 123;
        String result = BusinessProductUtil.fillHrefUrlBy(productId, productType, hrefUrl, goodsId);
        assertEquals(hrefUrl, result, "Expected hrefUrl to be returned when productType is not PHYSICAL_LINK.");
    }

    @Test
    public void testFillHrefUrlBy_BlankHrefUrl() {
        Integer productId = 1;
        Integer productType = BusinessProductTypeEnum.PHYSICAL_LINK.getTypeId();
        String hrefUrl = "";
        Integer goodsId = 123;
        String result = BusinessProductUtil.fillHrefUrlBy(productId, productType, hrefUrl, goodsId);
        assertEquals(hrefUrl, result, "Expected hrefUrl to be returned when hrefUrl is blank.");
    }

    @Test
    public void testFillHrefUrlBy_NullGoodsId() {
        Integer productId = 1;
        Integer productType = BusinessProductTypeEnum.PHYSICAL_LINK.getTypeId();
        String hrefUrl = "http://example.com/";
        Integer goodsId = null;
        String result = BusinessProductUtil.fillHrefUrlBy(productId, productType, hrefUrl, goodsId);
        assertEquals(hrefUrl, result, "Expected hrefUrl to be returned when goodsId is null.");
    }

}