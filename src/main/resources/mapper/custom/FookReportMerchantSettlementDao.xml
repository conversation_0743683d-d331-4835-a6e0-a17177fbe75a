<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMerchantSettlementDao">

  <select id="selectSettlementReportDetail" resultType="com.mcoin.mall.bo.SettlementReportDetailBo">
    select os.storename            as storeName,
           os.vouchercode          as voucherCode,
           os.vouchername          as voucherName,
           os.storeMid,
           os.usetime              as useTime,
           os.billamount           as billAmount,
           os.commission,
           os.merchantsettleamount as merchantSettleAmount,
           oi.product_price + abs(os.subsidy_amount)        as productPrice,
           o.order_no              as orderNo,
           o.order_transaction     as orderTransaction,
           o.type,
           os.delivery_type        as deliveryType,
           os.tracking_no          as trackingNo,
           os.business_redeem_time as businessRedeemTime
    from fook_report_ordercode_settlement as os
    left join fook_platform_ordercode as oc on os.ordercodeid = oc.id
    left join fook_platform_order as o on oc.orderid = o.id
    left join fook_platform_orderinfo as oi on o.id = oi.orderid
    where os.settlementbusinessid = #{settlementId,jdbcType=INTEGER}
    order by os.usetime desc
  </select>

</mapper>