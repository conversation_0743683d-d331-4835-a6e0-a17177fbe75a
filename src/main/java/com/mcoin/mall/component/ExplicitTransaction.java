package com.mcoin.mall.component;

import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.concurrent.Callable;

import static org.springframework.transaction.TransactionDefinition.*;

/**
 * 显式事务
 * <AUTHOR>
 */
@Slf4j
public class ExplicitTransaction {

    private final PlatformTransactionManager transactionManager;
    private final int timeout;
    private final boolean readonly;

    public ExplicitTransaction(PlatformTransactionManager transactionManager, int timeout, boolean readonly) {
        this.transactionManager = transactionManager;
        this.timeout = timeout;
        this.readonly = readonly;
    }

    public <T> T invokeWithNewTransaction(Callable<T> callable)  throws BusinessException {
        return invokeTransaction(PROPAGATION_REQUIRES_NEW, ISOLATION_DEFAULT, callable);
    }

    public void invokeWithNewTransaction(ThrowableRunnable<BusinessException> runnable) throws BusinessException{
        invokeTransaction(PROPAGATION_REQUIRES_NEW, ISOLATION_DEFAULT, ()-> {
            runnable.run();
            return null;
        });
    }

    public <T> T invokeWithRequiredTransaction(Callable<T> callable) throws BusinessException{
        return invokeTransaction(PROPAGATION_REQUIRED, ISOLATION_DEFAULT, callable);
    }

    public void invokeWithRequiredTransaction(ThrowableRunnable<BusinessException> runnable) throws BusinessException{
        invokeTransaction(PROPAGATION_REQUIRED, ISOLATION_DEFAULT, ()-> {
            runnable.run();
            return null;
        });
    }

    public   <T> T invokeTransaction(int propagationBehavior, int isolationLevel, Callable<T> callable) throws BusinessException {
        TransactionStatus transactionStatus = null;
        try {
            transactionStatus = openTransaction(propagationBehavior, isolationLevel, readonly);
            T t = callable.call();
            transactionManager.commit(transactionStatus);
            return t;
        } catch (Exception e){
            if(transactionStatus != null){
                try {
                    transactionManager.rollback(transactionStatus);
                } catch (Exception e2){
                    log.warn("事務已經回滾，無需重複執行：{}", e2.getMessage());
                }
            }
            return throwException(e);
        }
    }
    private static <T> T throwException(Exception e) throws BusinessException {
        if (e instanceof BusinessException){
            throw (BusinessException)e;
        }
        throw new BusinessException(Response.Code.UNKNOWN_ERROR, e.getMessage(), e);
    }

    private TransactionStatus openTransaction(int propagationBehavior, int isolationLevel, boolean readonly){
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setTimeout(timeout);
        definition.setPropagationBehavior(propagationBehavior);
        definition.setIsolationLevel(isolationLevel);
        definition.setReadOnly(readonly);
        return transactionManager.getTransaction(definition);
    }


}
