<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessTranslationsDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessTranslations">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="t_name" jdbcType="VARCHAR" property="tName" />
    <result column="t_company_name" jdbcType="VARCHAR" property="tCompanyName" />
    <result column="t_address" jdbcType="VARCHAR" property="tAddress" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, business_id, `locale`, t_name, t_company_name, t_address, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_translations
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_translations
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessTranslations">
    insert into fook_business_translations
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="locale != null">
        `locale`,
      </if>
      <if test="tName != null">
        t_name,
      </if>
      <if test="tCompanyName != null">
        t_company_name,
      </if>
      <if test="tAddress != null">
        t_address,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="locale != null">
        #{locale,jdbcType=VARCHAR},
      </if>
      <if test="tName != null">
        #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tCompanyName != null">
        #{tCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="tAddress != null">
        #{tAddress,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessTranslations">
    update fook_business_translations
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="locale != null">
        `locale` = #{locale,jdbcType=VARCHAR},
      </if>
      <if test="tName != null">
        t_name = #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tCompanyName != null">
        t_company_name = #{tCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="tAddress != null">
        t_address = #{tAddress,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessTranslations">
    update fook_business_translations
    set business_id = #{businessId,jdbcType=INTEGER},
      `locale` = #{locale,jdbcType=VARCHAR},
      t_name = #{tName,jdbcType=VARCHAR},
      t_company_name = #{tCompanyName,jdbcType=VARCHAR},
      t_address = #{tAddress,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>