package com.mcoin.mall.constant;


import lombok.Getter;

/***
 * 兑换记录表的支付状态枚举
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date 2023-10-20
 */
@Getter
public enum RedeemcodeUsePayStatusEnum {

    /**
     * 换记录表的支付状态枚举
     */
    UNPAID(1, "未付款"),
    PAID(2, "已付款"),
    INVALID_ORDER(3, "失效订单"),
    ;

    /**
     * 支付状态
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;

    RedeemcodeUsePayStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
