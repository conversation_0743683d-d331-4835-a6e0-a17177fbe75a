<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresTranslationsDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookStoresTranslations">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="stores_id" jdbcType="INTEGER" property="storesId" />
    <result column="t_name" jdbcType="VARCHAR" property="tName" />
    <result column="t_address" jdbcType="VARCHAR" property="tAddress" />
    <result column="t_detail" jdbcType="VARCHAR" property="tDetail" />
    <result column="t_reservation_time" jdbcType="VARCHAR" property="tReservationTime" />
    <result column="t_expense" jdbcType="VARCHAR" property="tExpense" />
    <result column="t_business_time" jdbcType="VARCHAR" property="tBusinessTime" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, stores_id, t_name, t_address, t_detail, t_reservation_time, t_expense, t_business_time, 
    `locale`, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_stores_translations
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_stores_translations
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookStoresTranslations">
    insert into fook_stores_translations
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="storesId != null">
        stores_id,
      </if>
      <if test="tName != null">
        t_name,
      </if>
      <if test="tAddress != null">
        t_address,
      </if>
      <if test="tDetail != null">
        t_detail,
      </if>
      <if test="tReservationTime != null">
        t_reservation_time,
      </if>
      <if test="tExpense != null">
        t_expense,
      </if>
      <if test="tBusinessTime != null">
        t_business_time,
      </if>
      <if test="locale != null">
        `locale`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="storesId != null">
        #{storesId,jdbcType=INTEGER},
      </if>
      <if test="tName != null">
        #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tAddress != null">
        #{tAddress,jdbcType=VARCHAR},
      </if>
      <if test="tDetail != null">
        #{tDetail,jdbcType=VARCHAR},
      </if>
      <if test="tReservationTime != null">
        #{tReservationTime,jdbcType=VARCHAR},
      </if>
      <if test="tExpense != null">
        #{tExpense,jdbcType=VARCHAR},
      </if>
      <if test="tBusinessTime != null">
        #{tBusinessTime,jdbcType=VARCHAR},
      </if>
      <if test="locale != null">
        #{locale,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookStoresTranslations">
    update fook_stores_translations
    <set>
      <if test="storesId != null">
        stores_id = #{storesId,jdbcType=INTEGER},
      </if>
      <if test="tName != null">
        t_name = #{tName,jdbcType=VARCHAR},
      </if>
      <if test="tAddress != null">
        t_address = #{tAddress,jdbcType=VARCHAR},
      </if>
      <if test="tDetail != null">
        t_detail = #{tDetail,jdbcType=VARCHAR},
      </if>
      <if test="tReservationTime != null">
        t_reservation_time = #{tReservationTime,jdbcType=VARCHAR},
      </if>
      <if test="tExpense != null">
        t_expense = #{tExpense,jdbcType=VARCHAR},
      </if>
      <if test="tBusinessTime != null">
        t_business_time = #{tBusinessTime,jdbcType=VARCHAR},
      </if>
      <if test="locale != null">
        `locale` = #{locale,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookStoresTranslations">
    update fook_stores_translations
    set stores_id = #{storesId,jdbcType=INTEGER},
      t_name = #{tName,jdbcType=VARCHAR},
      t_address = #{tAddress,jdbcType=VARCHAR},
      t_detail = #{tDetail,jdbcType=VARCHAR},
      t_reservation_time = #{tReservationTime,jdbcType=VARCHAR},
      t_expense = #{tExpense,jdbcType=VARCHAR},
      t_business_time = #{tBusinessTime,jdbcType=VARCHAR},
      `locale` = #{locale,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>