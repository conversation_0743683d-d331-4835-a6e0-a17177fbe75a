package com.mcoin.mall.controller.gateway.token;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.client.model.GatewayAccessTokenResponse;
import com.mcoin.mall.client.model.GatewayAppTicketResponse;
import com.mcoin.mall.client.model.GatewayAppTokenResponse;
import com.mcoin.mall.controller.management.TokenManageController;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
public class GatewayTokenManageControllerTest extends BaseUnitTest {

    @Resource
    private TokenManageController gatewayTokenManageController;

    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;


    @Test
    public void testAccessTokenWhenUserExist() throws BlockException {
        String mockAccessTokenJson = "{\"access_token\":\"mockAccessToken\",\"refresh_token\":\"mockRefreshToken\",\"openid\":\"mockOpenId\"}";
        when(mPayClient.accessToken(anyString())).thenReturn(mockAccessTokenJson);
        gatewayTokenManageController.manageUserToken("testWithoutAccessToken");
        FookMacaupassUser userInfo = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getOpenid, "mockOpenId")
                .last("limit 1")
        );
        assertNotNull(userInfo);

        assertEquals(userInfo.getAccessToken(), "mockAccessToken");
    }

    @Test
    public void testAccessTokenWhenUserNotExist() throws BlockException {
        String mockAccessTokenJson = "{\"access_token\":\"mockAccessToken\",\"refresh_token\":\"mockRefreshToken\",\"openid\":\"mockOpenIdNotExist\"}";
        when(mPayClient.accessToken(anyString())).thenReturn(mockAccessTokenJson);

        gatewayTokenManageController.manageUserToken("testWithoutAccessToken");
        FookMacaupassUser userInfo = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getOpenid, "mockOpenIdNotExist")
                .last("limit 1")
        );
        assertNull(userInfo);
    }

    @Test
    public void testAccessTokenWhenUserExistAndCustomerIdNotExist() throws BlockException {
        String mockAccessTokenJson = "{\"access_token\":\"mockAccessToken\",\"refresh_token\":\"mockRefreshToken\",\"openid\":\"UserExistAndCustomerIdNotExist\"}";
        String mockCustJson = "{\"user_id\":\"mockCustomerId\"}";

        when(mPayClient.accessToken(anyString())).thenReturn(mockAccessTokenJson);
        when(mPayClient.getUserId(anyString())).thenReturn(mockCustJson);
        gatewayTokenManageController.manageUserToken("testWithoutAccessToken");
        FookMacaupassUser userInfo = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getOpenid, "UserExistAndCustomerIdNotExist")
                .last("limit 1")
        );
        assertNotNull(userInfo);
        assertEquals(userInfo.getCustomid(), "mockCustomerId");

    }

    @Test
    public void testAccessTokenWhenGetTokenNull() throws BlockException {
        when(mPayClient.accessToken(anyString())).thenReturn(null);
        GatewayAccessTokenResponse response = gatewayTokenManageController.manageUserToken("testWithoutAccessToken");
        assertNotNull(response);
        assertEquals(response.getErrcode(), "50");

    }

    @Test
    public void testAccessTokenWhenGetCustIdNull() throws BlockException {
        String mockAccessTokenJson = "{\"access_token\":\"mockAccessToken\",\"refresh_token\":\"mockRefreshToken\",\"openid\":\"UserExistAndCustomerIdNotExist\"}";
        when(mPayClient.accessToken(anyString())).thenReturn(mockAccessTokenJson);
        when(mPayClient.getUserId(anyString())).thenReturn(null);
        GatewayAccessTokenResponse response = gatewayTokenManageController.manageUserToken("testWithoutAccessToken");
        assertNotNull(response);
    }

    @Test
    public void testGetCustIdWhenCustIdNull() throws BlockException {
        String mockAccessTokenJson = "{\"access_token\":\"mockAccessToken\",\"refresh_token\":\"mockRefreshToken\",\"openid\":\"UserExistAndCustomerIdNotExist\"}";
        String mockCustJson = "{\"user_id\":\"\"}";
        when(mPayClient.accessToken(anyString())).thenReturn(mockAccessTokenJson);
        when(mPayClient.getUserId(anyString())).thenReturn(mockCustJson);
        GatewayAccessTokenResponse response = gatewayTokenManageController.manageUserToken("testWithoutAccessToken");
        assertNotNull(response);
    }


    @Test
    public void testGetAppToken() throws BlockException {
        GatewayAppTokenResponse gatewayAppTokenResponse = gatewayTokenManageController.manageAppToken();
        assertEquals(gatewayAppTokenResponse.getAppToken(),"d3a2569bcaf1e75452b5b4323572ccae");
    }


    @Test
    public void testGetTicket() throws BlockException {
        GatewayAppTicketResponse gatewayAppTicketResponse = gatewayTokenManageController.manageAppTicket();
        assertEquals(gatewayAppTicketResponse.getTicket(),"fa837df4bc81f30319780ffd91946665");
    }


}
