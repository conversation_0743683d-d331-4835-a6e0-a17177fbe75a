package com.mcoin.mall.mp;

import ch.vorburger.exec.ManagedProcessException;
import ch.vorburger.mariadb4j.DBConfigurationBuilder;
import ch.vorburger.mariadb4j.springframework.MariaDB4jSpringService;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import com.mcoin.mall.mp.client.MASLClient;
import com.mcoin.mall.mp.client.MPayClient;
import com.mcoin.mall.mp.component.ContextHolder;
import com.mcoin.mall.mp.mapper.LsSelffetchShopMapper;
import com.mcoin.mall.mp.service.chennel.McoinChannelService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;

@Slf4j
@SpringBootTest(classes = MpMcoinMallApplicationTest.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class BaseUnitTest {

    private static MariaDB4jSpringService DB;
    private static boolean setUpIsDone = false;
    private static File tempDir;
    private static final Object lock = new Object();

    @MockBean
    protected ContextHolder contextHolder;
    @MockBean
    protected MASLClient maslClient;
    @MockBean
    protected MPayClient mPayClient;
    @MockBean(name = "goodsDetailTemplate")
    protected RabbitTemplate goodsDetailTemplate;
    @MockBean(name = "orderSyncTemplate")
    protected RabbitTemplate orderSyncTemplate;
    @MockBean(name = "orderCloseTemplateDelay")
    protected RabbitTemplate orderCloseTemplateDelay;
    @MockBean
    protected LsSelffetchShopMapper lsSelffetchShopMapper;
    @MockBean
    protected McoinChannelService mcoinChannelService;

    @BeforeAll
    public static void setup() throws ManagedProcessException {
        log.info("setup");
        synchronized (lock) {
            if (!setUpIsDone) {
                try {
                    tempDir = Files.createTempDirectory("MariaDB4j").toFile();
                    tempDir.deleteOnExit();
                } catch (IOException e) {
                    e.printStackTrace();
                }

                startMariaDB4j();
                setUpIsDone = true;
            }
        }
    }

    @AfterAll
    public static void tearDown() {
        log.info("tearDown");
    }

    private static void startMariaDB4j() throws ManagedProcessException {
        log.info("执行startMariaDB4j方法");
        DB = new MariaDB4jSpringService() {
            @Override
            public DBConfigurationBuilder getConfiguration() {
                DBConfigurationBuilder builder = super.getConfiguration();
                builder.addArg("--character-set-server=utf8mb4");
                builder.addArg("--collation_server=utf8mb4_unicode_ci");
                builder.addArg("--innodb_lock_wait_timeout=500");
                builder.addArg("--sql_mode=NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION");
                if (!builder._getArgs().contains("--user=root")) {
                    builder.addArg("--user=root");
                }
                return builder;
            }
        };
        DB.setDefaultPort(18120);
        DB.setDefaultBaseDir(tempDir.getAbsolutePath()+"/base");
        DB.setDefaultDataDir(tempDir.getAbsolutePath()+"/data");
        DB.start();
        DB.getDB().createDB("lingshou");
        
        // Load schema files
        List<File> schemeFiles = FileUtil.loopFiles(new ClassPathResource("db/schema").getFile(), file -> file.getName().endsWith(".sql"));
        for (File sqlFile : schemeFiles) {
            DB.getDB().source("db/schema/" + sqlFile.getName(), "lingshou");
        }

        // Load test data files
        List<File> sqlFiles = FileUtil.loopFiles(new ClassPathResource("db/data").getFile(), file -> file.getName().endsWith(".sql"));
        for (File sqlFile : sqlFiles) {
            DB.getDB().source("db/data/" + sqlFile.getName(), "lingshou");
        }
    }

    private static void stopMariaDB4j() {
        log.info("执行stopMariaDB4j方法");
        if (DB != null) DB.stop();
    }
}