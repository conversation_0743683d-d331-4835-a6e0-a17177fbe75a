package com.mcoin.mall.controller.management;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.model.OrderRefundApprovalRequest;
import com.mcoin.mall.model.OrderRefundApprovalResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.annotation.Resource;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doReturn;

/**
 * 集成测试 OrderRefundController
 * 使用模拟服务，测试退款审批相关功能
 */
public class OrderRefundControllerTest extends BaseUnitTest {

    @Resource
    private OrderRefundController orderRefundController;

    @SpyBean
    private ContextHolder contextHolder;



    private static final int TEST_USER_ID = 99999;

    @BeforeEach
    void setUp() {
        // 模拟Locale
        doReturn(Locale.CHINESE).when(contextHolder).getLocale();

        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");

        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();
    }

    /**
     * 测试退款审批处理 - 成功场景
     * 验证退款审批处理返回成功状态码
     */
    @Test
    @DisplayName("测试退款审批处理 - 成功场景")
    void testRefundApprovalProcessSuccess() {
        // 创建请求对象
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(243629); // 使用测试数据库中的实际ID
        request.setType(1);
        request.setOperationType(1);
        request.setOperationId(0);
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());

        // 调用控制器方法
        Response<OrderRefundApprovalResponse> response = orderRefundController.refundApprovalProcess(request);

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(1, response.getStatus(), "Response status should be 1");
        // 在实际集成测试中，响应数据可能为空，所以不验证数据内容
    }

    /**
     * 测试退款审批处理 - 已同意退款场景
     * 验证当退款已被同意时返回正确的状态码
     */
    @Test
    @DisplayName("测试退款审批处理 - 已同意退款场景")
    void testRefundApprovalProcessAlreadyApproved() {
        // 创建请求对象
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(243628); // 使用测试数据库中已完成退款的ID
        request.setType(1);
        request.setOperationType(1);
        request.setOperationId(0);
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());

        // 调用控制器方法
        Response<OrderRefundApprovalResponse> response = orderRefundController.refundApprovalProcess(request);

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(1, response.getStatus(), "Response status should be 1");
        // 在实际集成测试中，响应数据可能为空，所以不验证数据内容
    }

    /**
     * 测试退款审批处理 - 已拒绝退款场景
     * 验证当退款已被拒绝时返回正确的状态码
     */
    @Test
    @DisplayName("测试退款审批处理 - 已拒绝退款场景")
    void testRefundApprovalProcessAlreadyRejected() {
        // 创建请求对象
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(243629); // 使用测试数据库中的ID
        request.setType(1);
        request.setOperationType(1);
        request.setOperationId(0);
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());

        // 调用控制器方法
        Response<OrderRefundApprovalResponse> response = orderRefundController.refundApprovalProcess(request);

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(1, response.getStatus(), "Response status should be 1");
        // 在实际集成测试中，响应数据可能为空，所以不验证数据内容
    }

    /**
     * 测试退款审批处理 - 退款信息不存在场景
     * 验证当退款信息不存在时返回正确的状态码
     */
    @Test
    @DisplayName("测试退款审批处理 - 退款信息不存在场景")
    void testRefundApprovalProcessNotFound() {
        // 创建请求对象
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(999999); // 使用不存在的ID
        request.setType(1);
        request.setOperationType(1);
        request.setOperationId(0);
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());

        // 调用控制器方法
        Response<OrderRefundApprovalResponse> response = orderRefundController.refundApprovalProcess(request);

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(1, response.getStatus(), "Response status should be 1");
        // 在实际集成测试中，响应数据可能为空，所以不验证数据内容
    }

    /**
     * 测试退款审批处理 - 自动审批场景
     * 验证自动审批退款流程
     */
    @Test
    @DisplayName("测试退款审批处理 - 自动审批场景")
    void testRefundApprovalProcessAutoApproval() {
        // 创建请求对象
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(243629); // 使用测试数据库中的ID
        request.setType(1);
        request.setOperationType(1);
        request.setOperationId(0);
        request.setRefundScene(RefundSceneEnum.AUTO_APPROVAL_REFUND.getCode());

        // 调用控制器方法
        Response<OrderRefundApprovalResponse> response = orderRefundController.refundApprovalProcess(request);

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(1, response.getStatus(), "Response status should be 1");
        // 在实际集成测试中，响应数据可能为空，所以不验证数据内容
    }

    /**
     * 测试退款审批处理 - 不同类型和操作类型
     * 验证不同类型和操作类型的退款审批处理
     */
    @Test
    @DisplayName("测试退款审批处理 - 不同类型和操作类型")
    void testRefundApprovalProcessDifferentTypes() {
        // 创建请求对象
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(243629); // 使用测试数据库中的ID
        request.setType(2); // 不同的类型
        request.setOperationType(2); // 不同的操作类型
        request.setOperationId(1);
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());

        // 调用控制器方法
        Response<OrderRefundApprovalResponse> response = orderRefundController.refundApprovalProcess(request);

        // 验证响应结构
        assertNotNull(response, "Response should not be null");
        assertEquals(1, response.getStatus(), "Response status should be 1");
        // 在实际集成测试中，响应数据可能为空，所以不验证数据内容
    }
}
