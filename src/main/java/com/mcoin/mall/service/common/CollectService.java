package com.mcoin.mall.service.common;

import com.mcoin.mall.bo.ProductsCollectBo;
import com.mcoin.mall.model.Collectable;

import java.util.List;

public interface CollectService {

    ProductsCollectBo getProductsCollect(List<Integer> productIds);

    default void setCollectNumber(ProductsCollectBo collectBo, Integer productId, Collectable collectable){
        setCollectNumber(collectBo, productId, collectable, false);
    };

    void setCollectNumber(ProductsCollectBo collectBo, Integer productId, Collectable collectable, boolean showText);

}
