package com.mcoin.mall.service.showzone;

import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessStoreProduct;
import com.mcoin.mall.bean.FookShow;
import com.mcoin.mall.bean.FookShowZone;
import com.mcoin.mall.bean.FookShowZoneProduct;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.bo.ProductsCollectBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.dao.FookPlatformUsercollectionDao;
import com.mcoin.mall.dao.FookShowDao;
import com.mcoin.mall.dao.FookShowZoneDao;
import com.mcoin.mall.dao.FookShowZoneProductDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.model.ShowResponse;
import com.mcoin.mall.model.ShowZoneProductRequest;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.service.common.StockService;
import com.mcoin.mall.service.show.impl.ShowZoneProductServiceImpl;
import com.mcoin.mall.util.AESUtil;
import com.mcoin.mall.util.ConfigUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Locale;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ShowZoneProductServiceTest {

    @Mock
    private FookShowDao fookShowDao;

    @Mock
    private FookShowZoneDao fookShowZoneDao;

    @Mock
    private FookBusinessProductDao fookBusinessProductDao;

    @Mock
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Mock
    private FookStoresDao fookStoresDao;

    @Mock
    private FookBusinessDao fookBusinessDao;
    @InjectMocks
    private ShowZoneProductServiceImpl showZoneProductService;
    @Mock
    private ContextHolder contextHolder;
    @Mock
    private FookShowZoneProductDao fookShowZoneProductDao;
    @Mock
    private FookPlatformUsercollectionDao fookPlatformUsercollectionDao;
    @Mock
    private CollectService collectService;
    @Mock private StockService stockService;

    // Mocked static instances
    MockedStatic<ConfigUtils> configUtilsStatic;
    MockedStatic<AESUtil> aesUtilStatic;

    @BeforeEach
    void setUp() {
        // Initialize static mocks
        configUtilsStatic = mockStatic(ConfigUtils.class);
        aesUtilStatic = mockStatic(AESUtil.class);
        // Initialize regular mocks
        MockitoAnnotations.openMocks(this);
        // Mock FookShow
        FookShow fookShow = new FookShow();
        fookShow.setId(1);
        fookShow.setBannerImg("[\"image/show/3twhuk7IMxw9DMTdIIxMKx2A9lAzRvqtua4S8mJl.jpeg\"]");
        fookShow.setCnName("test");
        fookShow.setEnName("test");
        when(fookShowDao.selectByPrimaryKey(1)).thenReturn(fookShow);
        // Mock FookShowZone
        FookShowZone fookShowZone = new FookShowZone();
        fookShowZone.setId(1);
        when(fookShowZoneDao.selectByShowId(fookShow.getId())).thenReturn(Collections.singletonList(fookShowZone));
        // Mock static methods
        when(ConfigUtils.getProperty("security.product.share.secret")).thenReturn("test_secret");
        when(AESUtil.encryptToHex(any(), any())).thenReturn("encrypted_value");
        // Mock context holder locale
        when(contextHolder.getLocale()).thenReturn(Locale.getDefault());
    }

    @AfterEach
    void tearDown() {
        // Ensure that static mocks are closed after each test
        configUtilsStatic.close();
        aesUtilStatic.close();
    }

    @Test
    public void testQueryShow_without_ShowZoneProduct() {
        String clientType = "";
        // Mock empty zone products
        when(fookShowZoneProductDao.selectByZoneId(1)).thenReturn(Collections.emptyList());
        // Prepare request
        ShowZoneProductRequest request = new ShowZoneProductRequest();
        request.setShowId(1);
        // Call service
        ShowResponse response = showZoneProductService.queryShowZoneProductByShowId(request, clientType);
        // Validate response
        assertThat(response, is(notNullValue()));
        assertThat(response.getShowZones(), is(notNullValue()));
        assertThat(response.getShowZones(), hasSize(1));
        assertThat(response.getShowZones().get(0).getZoneId(), is(1));
    }

    @Test
    public void testQueryShow_without_business() {
        String clientType = "";
        Boolean harmonySwitch = false;
        // Mock zone product
        FookShowZoneProduct fookShowZoneProduct = new FookShowZoneProduct();
        fookShowZoneProduct.setId(1);
        fookShowZoneProduct.setZoneId(1);
        fookShowZoneProduct.setProductId(1);
        fookShowZoneProduct.setSort(1);
        fookShowZoneProduct.setShowInZone(1);
        when(fookShowZoneProductDao.selectByZoneId(1)).thenReturn(Collections.singletonList(fookShowZoneProduct));
        // Mock business product
        FookBusinessProduct fookBusinessProduct = new FookBusinessProduct();
        fookBusinessProduct.setId(1);
        fookBusinessProduct.setType(12);
        fookBusinessProduct.setId(1);
        fookBusinessProduct.setBuyStartTime(new Date());
        fookBusinessProduct.setOnlyPoint(300);
        fookBusinessProduct.setPointRatio(150);
        fookBusinessProduct.setHrefUrl("150");
        fookBusinessProduct.setGoodsId(3);
        when(fookBusinessProductDao.getProductByIdsAndActive(Arrays.asList(1), harmonySwitch)).thenReturn(Collections.singletonList(fookBusinessProduct));
        // Prepare request
        ShowZoneProductRequest request = new ShowZoneProductRequest();
        request.setShowId(1);
        // Call service
        ShowResponse response = showZoneProductService.queryShowZoneProductByShowId(request, clientType);
        // Validate response
        assertThat(response, is(notNullValue()));
        assertThat(response.getShowZones(), is(notNullValue()));
        assertThat(response.getShowZones(), hasSize(1));
        assertThat(response.getShowZones().get(0).getZoneId(), is(1));
    }


    @Test
    public void testQueryShow_without_stores() {
        String clientType = "";
        Boolean harmonySwitch = false;
        // Mock zone product
        FookShowZoneProduct fookShowZoneProduct = new FookShowZoneProduct();
        fookShowZoneProduct.setId(1);
        fookShowZoneProduct.setZoneId(1);
        fookShowZoneProduct.setProductId(1);
        fookShowZoneProduct.setSort(1);
        fookShowZoneProduct.setShowInZone(1);
        when(fookShowZoneProductDao.selectByZoneId(1)).thenReturn(Collections.singletonList(fookShowZoneProduct));
        // Mock business product
        FookBusinessProduct fookBusinessProduct = new FookBusinessProduct();
        fookBusinessProduct.setId(1);
        fookBusinessProduct.setType(12);
        fookBusinessProduct.setId(1);
        fookBusinessProduct.setBuyStartTime(new Date());
        when(fookBusinessProductDao.getProductByIdsAndActive(Arrays.asList(1), harmonySwitch)).thenReturn(Collections.singletonList(fookBusinessProduct));
        // Mock business
        FookBusinessStoreProduct fookBusinessStoreProduct = new FookBusinessStoreProduct();
        fookBusinessStoreProduct.setId(1);
        when(fookBusinessStoreProductDao.getStoresIdByProductIds(Arrays.asList(1))).thenReturn(Collections.singletonList(fookBusinessStoreProduct));
        // Prepare request
        ShowZoneProductRequest request = new ShowZoneProductRequest();
        request.setShowId(1);
        // Call service
        ShowResponse response = showZoneProductService.queryShowZoneProductByShowId(request, clientType);
        // Validate response
        assertThat(response, is(notNullValue()));
        assertThat(response.getShowZones(), is(notNullValue()));
        assertThat(response.getShowZones(), hasSize(1));
        assertThat(response.getShowZones().get(0).getZoneId(), is(1));
    }

    @Test
    public void testQueryShow_Success() {
        String clientType = "";
        Boolean harmonySwitch = false;
        // Mock zone product
        FookShowZoneProduct fookShowZoneProduct = new FookShowZoneProduct();
        fookShowZoneProduct.setId(1);
        fookShowZoneProduct.setZoneId(1);
        fookShowZoneProduct.setProductId(1);
        fookShowZoneProduct.setSort(1);
        fookShowZoneProduct.setShowInZone(1);
        when(fookShowZoneProductDao.selectByZoneId(1)).thenReturn(Collections.singletonList(fookShowZoneProduct));
        // Mock business product
        FookBusinessProduct fookBusinessProduct = new FookBusinessProduct();
        fookBusinessProduct.setId(1);
        fookBusinessProduct.setType(12);
        fookBusinessProduct.setId(1);
        fookBusinessProduct.setBusinessid(1);
        fookBusinessProduct.setBuyStartTime(new Date());

        fookBusinessProduct.setOnlyPoint(300);
        fookBusinessProduct.setPointRatio(150);
        fookBusinessProduct.setHrefUrl("150");
        fookBusinessProduct.setGoodsId(3);
        fookBusinessProduct.setMaximumPoints(300);
        fookBusinessProduct.setMinPoint(150);
        fookBusinessProduct.setPrice(new BigDecimal(10));
        when(fookBusinessProductDao.getProductByIdsAndActive(Arrays.asList(1), harmonySwitch)).thenReturn(Collections.singletonList(fookBusinessProduct));
        // Mock business relation
        FookBusinessStoreProduct fookBusinessStoreProduct = new FookBusinessStoreProduct();
        fookBusinessStoreProduct.setProductid(1);
        fookBusinessStoreProduct.setId(1);
        fookBusinessStoreProduct.setStoreid(1);
        when(fookBusinessStoreProductDao.getStoresIdByProductIds(Arrays.asList(1))).thenReturn(Collections.singletonList(fookBusinessStoreProduct));
        // Mock store
        FookStores fookStores = new FookStores();
        fookStores.setId(1);
        fookStores.setBusinessId(1);
        when(fookStoresDao.getStoresByIdsAndEnable(Arrays.asList(1))).thenReturn(Collections.singletonList(fookStores));
        // Mock store
        FookBusiness fookBusiness = new FookBusiness();
        fookBusiness.setId(1);
        fookBusiness.setSystemType(1);
        when(fookBusinessDao.getByIds(Arrays.asList(1))).thenReturn(Collections.singletonList(fookBusiness));
        // Mock collection
        when(collectService.getProductsCollect(Arrays.asList(1))).thenReturn(new ProductsCollectBo());
        when(stockService.isSlodOut(any(FookBusinessProduct.class))).thenReturn(false);
        // Prepare request
        ShowZoneProductRequest request = new ShowZoneProductRequest();
        request.setShowId(1);
        // Call service
        ShowResponse response = showZoneProductService.queryShowZoneProductByShowId(request, clientType);
        // Validate response
        assertThat(response, is(notNullValue()));
        assertThat(response.getShowZones(), is(notNullValue()));
        assertThat(response.getShowZones(), hasSize(1));
        assertThat(response.getShowZones().get(0).getZoneId(), is(1));
        assertThat(response.getShowZoneProducts().get(0).getZoneId(), is(1));
        assertThat(response.getShowZoneProducts().get(0).isSoldOut(), is(false));
    }
}
