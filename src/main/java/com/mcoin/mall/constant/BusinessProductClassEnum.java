package com.mcoin.mall.constant;


import lombok.Getter;

/**
 * 福利类别枚举
 *
 * @author: la<PERSON><PERSON><PERSON>
 * @date: 2023/11/21
 */
@Getter
public enum BusinessProductClassEnum {

    /**
     * 福利类别枚举
     */
    LIQUOR("liquor", "酒类"),
    ;

    /**
     * 类型（类型需要唯一，不可修改）
     */
    private final String type;

    /**
     * 描述
     */
    private final String desc;

    BusinessProductClassEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;

    }
}
