package com.mcoin.mall.controller.business;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.mcoin.mall.model.BusinessConfigRequest;
import com.mcoin.mall.model.BusinessConfigResponse;
import com.mcoin.mall.model.BusinessInformationResponse;
import com.mcoin.mall.model.GetClauseResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.SignConfigResponse;
import com.mcoin.mall.model.SignRequest;
import com.mcoin.mall.service.business.ClauseService;
import com.mcoin.mall.service.business.SearchService;
import com.mcoin.mall.service.business.SignService;

@RestController
@RequestMapping("/api/business")
public class BusinessController {
    @Resource
    private SearchService searchService;

    @Resource
    private SignService signService;

    @Resource
    private ClauseService clauseService;

    /**
     * 获取商圈列表和券类型列表
     * @return
     */
    @GetMapping(value = "/information", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<BusinessInformationResponse> information() {
        BusinessInformationResponse response = searchService.getInformation();
        return Responses.ok(response);
    }

    /**
     * 新增的配置接口，用于获取业务配置信息
     * 
     * @return 响应结果
     */
    @PostMapping(value = "/config", consumes = MediaType.APPLICATION_JSON_VALUE, 
                produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<BusinessConfigResponse> config(@RequestBody  @Valid BusinessConfigRequest request) {
        SignRequest signRequest = new SignRequest();
        signRequest.setShowImg(request.getShowImg());
        SignConfigResponse signResponse = signService.getSignConfig(signRequest);
        GetClauseResponse clausResponse = clauseService.getClause();
        BusinessConfigResponse response = new BusinessConfigResponse();
        response.setSignConfig(signResponse);
        response.setClause(clausResponse);
        return Responses.ok(response);
    }
}
