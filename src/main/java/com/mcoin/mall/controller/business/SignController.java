package com.mcoin.mall.controller.business;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.SignConfigResponse;
import com.mcoin.mall.model.SignRequest;
import com.mcoin.mall.service.business.SignService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
public class SignController {

    @Resource
    private SignService signService;

    @GetMapping(value = "/api/business/indexsign", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<SignConfigResponse> signConfig(@Valid SignRequest request) {
        SignConfigResponse response = signService.getSignConfig(request);
        return Responses.ok(response);
    }
}
