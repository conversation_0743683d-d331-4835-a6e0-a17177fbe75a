package com.mcoin.mall.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import redis.embedded.RedisServer;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;

@Slf4j
@Configuration
@Profile("test")
public class EmbeddedRedisConfig {

    private static RedisServer redisServer;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    // 单例模式，确保所有测试共享同一个Redis实例
    @PostConstruct
    public void startRedis() {
        // 如果Redis服务器已经运行，则不需要再次启动
        if (redisServer != null && redisServer.isActive()) {
            return;
        }

        try {
            // 创建一个更小的Redis实例，设置较少的内存和更高的maxclients
            redisServer = RedisServer.newRedisServer()
                    .port(redisPort)
                    .setting("maxmemory 128M")  // 限制内存使用
                    .setting("maxclients 100")  // 提高最大客户端连接数
                    .setting("timeout 3")       // 设置较短的超时
                    .setting("tcp-keepalive 60")
                    .setting("databases 1")     // 只需要一个数据库
                    .build();
            redisServer.start();
            log.info("Embedded Redis server started on port {}", redisPort);
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("Address already in use")) {
                log.info("Redis server already running on port {}", redisPort);
            } else {
                log.error("Failed to start Redis server", e);
                throw new RuntimeException(e);
            }
        }
    }

    @PreDestroy
    public void stopRedis() throws IOException {
        if (redisServer != null && redisServer.isActive()) {
            redisServer.stop();
            log.info("Embedded Redis server stopped");
        }
    }
}