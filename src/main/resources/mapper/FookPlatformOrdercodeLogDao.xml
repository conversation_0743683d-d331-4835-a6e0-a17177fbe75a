<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrdercodeLogDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformOrdercodeLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="shopid" jdbcType="INTEGER" property="shopid" />
    <result column="user_time" jdbcType="TIMESTAMP" property="userTime" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="ordercodeid" jdbcType="INTEGER" property="ordercodeid" />
    <result column="callback_msg" jdbcType="VARCHAR" property="callbackMsg" />
    <result column="external_ref_no" jdbcType="VARCHAR" property="externalRefNo" />
    <result column="terminal_code" jdbcType="VARCHAR" property="terminalCode" />
    <result column="branch_code" jdbcType="VARCHAR" property="branchCode" />
    <result column="merchant_code" jdbcType="VARCHAR" property="merchantCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shopid, user_time, code, orderid, userid, ordercodeid, callback_msg, external_ref_no,
    terminal_code, branch_code, merchant_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_ordercode_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_ordercode_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformOrdercodeLog">
    insert into fook_platform_ordercode_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopid != null">
        shopid,
      </if>
      <if test="userTime != null">
        user_time,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="ordercodeid != null">
        ordercodeid,
      </if>
      <if test="callbackMsg != null">
        callback_msg,
      </if>
      <if test="externalRefNo != null">
        external_ref_no,
      </if>
      <if test="terminalCode != null">
        terminal_code,
      </if>
      <if test="branchCode != null">
        branch_code,
      </if>
      <if test="merchantCode != null">
        merchant_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="shopid != null">
        #{shopid,jdbcType=INTEGER},
      </if>
      <if test="userTime != null">
        #{userTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="callbackMsg != null">
        #{callbackMsg,jdbcType=VARCHAR},
      </if>
      <if test="externalRefNo != null">
        #{externalRefNo,jdbcType=VARCHAR},
      </if>
      <if test="terminalCode != null">
        #{terminalCode,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="merchantCode != null">
        #{merchantCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformOrdercodeLog">
    update fook_platform_ordercode_log
    <set>
      <if test="shopid != null">
        shopid = #{shopid,jdbcType=INTEGER},
      </if>
      <if test="userTime != null">
        user_time = #{userTime,jdbcType=TIMESTAMP},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="callbackMsg != null">
        callback_msg = #{callbackMsg,jdbcType=VARCHAR},
      </if>
      <if test="externalRefNo != null">
        external_ref_no = #{externalRefNo,jdbcType=VARCHAR},
      </if>
      <if test="terminalCode != null">
        terminal_code = #{terminalCode,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        branch_code = #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="merchantCode != null">
        merchant_code = #{merchantCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformOrdercodeLog">
    update fook_platform_ordercode_log
    set shopid = #{shopid,jdbcType=INTEGER},
      user_time = #{userTime,jdbcType=TIMESTAMP},
      code = #{code,jdbcType=VARCHAR},
      orderid = #{orderid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      callback_msg = #{callbackMsg,jdbcType=VARCHAR},
      external_ref_no = #{externalRefNo,jdbcType=VARCHAR},
      terminal_code = #{terminalCode,jdbcType=VARCHAR},
      branch_code = #{branchCode,jdbcType=VARCHAR},
      merchant_code = #{merchantCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>