package com.mcoin.mall.constant;

public enum OrderTokenStatus {
    INVALID(0, "已使用"),
    VALID(1, "有效"),
    EXPIRED(2, "过期");

    private final int code;
    private final String description;

    OrderTokenStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 可选：根据code获取枚举实例
    public static OrderTokenStatus fromCode(int code) {
        for (OrderTokenStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid token status code: " + code);
    }
}
