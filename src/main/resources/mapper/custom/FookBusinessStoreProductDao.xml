<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessStoreProductDao">

    <select id="getStoreDistanceListByProductId" resultType="com.mcoin.mall.bo.StoreDistanceBo">
        select s.id storeId, s.name storeName, s.longitude, s.dimension, s.img_zip imgZip, s.img
        from fook_business_store_product bsp
        LEFT JOIN fook_stores s on s.id = bsp.storeid
        where s.`enable` = 1
        and bsp.productid = #{productId,jdbcType=INTEGER}
    </select>

    <select id="countStoreDistanceListByProductId" resultType="integer">
        select count(1)
        from fook_business_store_product bsp
                 LEFT JOIN fook_stores s on s.id = bsp.storeid
        where s.`enable` = 1
          and bsp.productid = #{productId,jdbcType=INTEGER}
    </select>

    
    <select id="getStoresIdByProductIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM fook_business_store_product
        WHERE productid in
        <foreach item="productId" collection="productIds" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>
    <select id="selectProductIdsForSearchValue" resultType="java.lang.Integer">
        select distinct productid from fook_business_store_product where storeid in
        <foreach collection="storeIds" item="storeid" open="(" separator="," close=")">
            #{storeid}
        </foreach>
    </select>
    <select id="getStoreIdsByBusinessId" resultType="java.lang.Integer">
        SELECT
        storeid
        FROM fook_business_store_product
        WHERE businessid = #{businessid,jdbcType=INTEGER}
        GROUP BY storeid ORDER BY storeid
    </select>

</mapper>