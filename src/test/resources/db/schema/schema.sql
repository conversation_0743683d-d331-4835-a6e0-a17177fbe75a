-- lingshou.ls_account_log definition

CREATE TABLE `ls_account_log` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT,
                                  `sn` varchar(32) NOT NULL DEFAULT '' COMMENT '流水单号',
                                  `user_id` int NOT NULL COMMENT '会员id',
                                  `change_object` tinyint(1) NOT NULL DEFAULT '0' COMMENT '变动对象',
                                  `change_type` smallint NOT NULL COMMENT '变动类型',
                                  `action` tinyint(1) NOT NULL DEFAULT '0' COMMENT '动作',
                                  `change_amount` decimal(10,2) NOT NULL COMMENT '变动数量',
                                  `left_amount` decimal(10,2) NOT NULL DEFAULT '100.00' COMMENT '变动后数量',
                                  `association_sn` varchar(255) DEFAULT NULL COMMENT '关联单号',
                                  `remark` varchar(255) DEFAULT '' COMMENT '备注',
                                  `feature` text COMMENT '预留扩展字段',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  `create_time` int DEFAULT NULL COMMENT '创建时间',
                                  `update_time` int DEFAULT NULL COMMENT '更新时间',
                                  `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=179 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='账户流水表';


-- lingshou.ls_admin definition

CREATE TABLE `ls_admin` (
                            `id` int unsigned NOT NULL AUTO_INCREMENT,
                            `root` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否超级管理员 0-否 1-是',
                            `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
                            `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '用户头像',
                            `account` varchar(32) NOT NULL DEFAULT '' COMMENT '账号',
                            `mch_id` varchar(50) DEFAULT '' COMMENT 'OMS 商戶ID',
                            `password` varchar(32) NOT NULL COMMENT '密码',
                            `role_id` int NOT NULL DEFAULT '0' COMMENT '角色id',
                            `create_time` int NOT NULL COMMENT '创建时间',
                            `update_time` int DEFAULT NULL COMMENT '修改时间',
                            `delete_time` int DEFAULT NULL COMMENT '删除时间',
                            `login_time` int DEFAULT NULL COMMENT '最后登录时间',
                            `login_ip` varchar(15) DEFAULT '' COMMENT '最后登录ip',
                            `multipoint_login` tinyint unsigned DEFAULT '1' COMMENT '是否支持多处登录：1-是；0-否；',
                            `disable` tinyint unsigned DEFAULT '0' COMMENT '是否禁用：0-否；1-是；',
                            `sid` int NOT NULL COMMENT '商户id',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='后台管理员表';


-- lingshou.ls_admin_session definition

CREATE TABLE `ls_admin_session` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT,
                                    `admin_id` int unsigned NOT NULL COMMENT '用户id',
                                    `terminal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户端类型：1-pc管理后台 2-mobile手机管理后台',
                                    `token` varchar(32) NOT NULL COMMENT '令牌',
                                    `update_time` int DEFAULT NULL COMMENT '更新时间',
                                    `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                    `expire_time` int NOT NULL COMMENT '到期时间',
                                    `sid` int NOT NULL COMMENT ' 商户id',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    UNIQUE KEY `admin_id_client` (`admin_id`,`terminal`) USING BTREE COMMENT '一个用户在一个终端只有一个token',
                                    UNIQUE KEY `token` (`token`) USING BTREE COMMENT 'token是唯一的'
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会话表';


-- lingshou.ls_after_sale definition

CREATE TABLE `ls_after_sale` (
                                 `id` int unsigned NOT NULL AUTO_INCREMENT,
                                 `sn` varchar(60) NOT NULL DEFAULT '' COMMENT '退款单号',
                                 `user_id` int unsigned NOT NULL COMMENT '用户id',
                                 `order_id` int DEFAULT NULL COMMENT '主订单id',
                                 `order_goods_id` int DEFAULT NULL COMMENT '子订单id',
                                 `refund_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '退款原因',
                                 `refund_remark` varchar(255) DEFAULT '' COMMENT '退款说明',
                                 `refund_image` varchar(255) DEFAULT '' COMMENT '退款图片',
                                 `refund_type` tinyint(1) NOT NULL COMMENT '退款类型 1-整单退款 2-商品售后',
                                 `refund_method` tinyint(1) NOT NULL COMMENT '退款方式 1-仅退款 2-退货退款',
                                 `refund_total_amount` decimal(10,2) unsigned NOT NULL COMMENT '退款总金额',
                                 `refund_total_integral_amount` decimal(10,2) unsigned DEFAULT NULL COMMENT '退款积分金额',
                                 `refund_total_integral` int DEFAULT '0' COMMENT '售後退款的積分',
                                 `refund_way` tinyint(1) DEFAULT NULL COMMENT '退款路径 1-原路退回 2-余额',
                                 `refund_status` tinyint(1) DEFAULT NULL COMMENT '退款方式 1-未退款 2-部分退款 3-全部退款',
                                 `express_name` varchar(255) DEFAULT '' COMMENT '快递公司名称',
                                 `invoice_no` varchar(255) DEFAULT '' COMMENT '快递单号',
                                 `express_remark` varchar(255) DEFAULT '' COMMENT '物流备注说明',
                                 `express_image` varchar(255) DEFAULT '' COMMENT '物流凭证',
                                 `express_time` int unsigned DEFAULT NULL COMMENT '买家退货时间',
                                 `confirm_take_time` int unsigned DEFAULT NULL COMMENT '确认收货时间',
                                 `status` tinyint(1) NOT NULL COMMENT '售后主状态 1-售后中 2-售后成功 3-售后失败',
                                 `sub_status` tinyint unsigned NOT NULL COMMENT '售后子状态：11-售后中，等待卖家同意;12-售后中，卖家已同意，等待买家发货;13-售后中，买家已发货，等待卖家收货;14-售后中，卖家已收货，等待卖家处理;15-售后中，卖家已同意，等待退款;16-售后中，售后退款中;17-售后中，售后退款失败，等待卖家处理;21-售后成功，售后退款成功;31-售后失败，买家取消售后;32-售后失败，卖家拒绝售后;33-售后失败，卖家拒绝收货;34-售后失败，卖家拒绝退款;',
                                 `receipt_status` tinyint DEFAULT '1' COMMENT '收貨狀態（0-未收貨 1-已收貨）',
                                 `audit_time` int unsigned DEFAULT NULL COMMENT '审核时间',
                                 `admin_id` int DEFAULT NULL COMMENT '管理员id',
                                 `admin_remark` varchar(255) DEFAULT NULL COMMENT '管理员备注',
                                 `voucher` text COMMENT '凭证图片，多张逗号分隔',
                                 `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                 `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                 `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                 `last_agree_time` datetime DEFAULT NULL COMMENT '最后同意售后的时间',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=806 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='售后表';


-- lingshou.ls_after_sale_goods definition

CREATE TABLE `ls_after_sale_goods` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT,
                                       `after_sale_id` int unsigned NOT NULL COMMENT '售后主订单id',
                                       `order_goods_id` int NOT NULL COMMENT '子订单id',
                                       `goods_id` int NOT NULL COMMENT '商品id',
                                       `item_id` int NOT NULL COMMENT '规格id',
                                       `goods_num` int NOT NULL COMMENT '商品数量',
                                       `goods_price` decimal(10,2) DEFAULT NULL COMMENT '商品单价',
                                       `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
                                       `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                       `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                       `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `idx_after_sale_id` (`after_sale_id`)
) ENGINE=InnoDB AUTO_INCREMENT=816 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='售后商品表';


-- lingshou.ls_after_sale_log definition

CREATE TABLE `ls_after_sale_log` (
                                     `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `after_sale_id` int unsigned DEFAULT NULL COMMENT '售后订单id',
                                     `operator_role` tinyint(1) DEFAULT NULL COMMENT '操作人角色 1-系统  2-买家 3-卖家',
                                     `operator_id` int DEFAULT NULL COMMENT '操作人id',
                                     `content` varchar(255) DEFAULT NULL COMMENT '日志内容',
                                     `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                     `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                     `sid` int DEFAULT NULL COMMENT '商户id',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1949 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='售后日志表';


-- lingshou.ls_article definition

CREATE TABLE `ls_article` (
                              `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                              `cid` int unsigned NOT NULL COMMENT '分类',
                              `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
                              `synopsis` varchar(255) NOT NULL DEFAULT '' COMMENT '简介',
                              `image` varchar(128) NOT NULL DEFAULT '' COMMENT '封面图',
                              `content` text COMMENT '文章内容',
                              `visit` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览人数',
                              `sort` int unsigned NOT NULL DEFAULT '50' COMMENT '排序',
                              `is_show` tinyint unsigned DEFAULT '1' COMMENT '是否显示 0-隐藏 1-显示',
                              `create_time` int DEFAULT NULL COMMENT '创建时间',
                              `update_time` int DEFAULT NULL COMMENT '更新时间',
                              `delete_time` int DEFAULT NULL COMMENT '删除时间',
                              `sid` int NOT NULL COMMENT '商户id',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商城资讯';


-- lingshou.ls_article_category definition

CREATE TABLE `ls_article_category` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '文章分类id',
                                       `name` varchar(128) NOT NULL DEFAULT '' COMMENT '分类名称',
                                       `sort` int unsigned NOT NULL DEFAULT '50' COMMENT '排序',
                                       `is_show` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否显示 0-隐藏 1-显示',
                                       `create_time` int DEFAULT NULL COMMENT '创建时间',
                                       `update_time` int DEFAULT NULL COMMENT '更新时间',
                                       `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商城资讯分类';


-- lingshou.ls_bargain_activity definition

CREATE TABLE `ls_bargain_activity` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT,
                                       `sn` varchar(255) NOT NULL COMMENT '活动编号',
                                       `name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
                                       `start_time` int NOT NULL COMMENT '开始时间',
                                       `end_time` int NOT NULL COMMENT '结束时间',
                                       `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
                                       `is_distribution` tinyint(1) NOT NULL COMMENT '是否参与分销 0-不参与 1-参与',
                                       `buy_condition` tinyint(1) NOT NULL COMMENT '购买条件 1-砍到任意价格都可购买 2-砍到底价才能购买',
                                       `valid_period` int NOT NULL COMMENT '有效期，单位：分钟',
                                       `help_num` int NOT NULL COMMENT '帮砍人数',
                                       `knife_amount_type` tinyint(1) NOT NULL COMMENT '每刀金额 1-固定金额 2-任意金额',
                                       `self` tinyint(1) NOT NULL COMMENT '自己是否能参与砍价 0-不能 1-能',
                                       `count` int NOT NULL COMMENT '每个用户对同一活动商品可发起的砍价次数',
                                       `buy_limit` int NOT NULL COMMENT '起购限制 0-不限购 其他整数代表限制数量',
                                       `order_limit` int NOT NULL COMMENT '每单限制 0-不限购 其他整数代表限制数量',
                                       `use_coupon` tinyint(1) NOT NULL COMMENT ' 是否允许使用优惠券 0-不可以 1-可以',
                                       `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '活动状态 1-未开始 2-进行中 3-已结束',
                                       `visited` int DEFAULT '0' COMMENT '浏览量',
                                       `close_time` int DEFAULT NULL COMMENT '手动结束活动时间',
                                       `create_time` int DEFAULT NULL COMMENT '创建时间',
                                       `update_time` int DEFAULT NULL COMMENT '更新时间',
                                       `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='砍价活动表';


-- lingshou.ls_bargain_goods definition

CREATE TABLE `ls_bargain_goods` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `activity_id` int NOT NULL COMMENT '砍价活动id',
                                    `goods_id` int NOT NULL COMMENT '商品id',
                                    `item_id` int NOT NULL COMMENT '规格id',
                                    `visited` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
                                    `first_knife` decimal(10,2) NOT NULL COMMENT '首刀金额',
                                    `floor_price` decimal(10,2) NOT NULL COMMENT '底价',
                                    `create_time` int DEFAULT NULL COMMENT '创建时间',
                                    `update_time` int DEFAULT NULL COMMENT '更新时间',
                                    `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='砍价活动商品表';


-- lingshou.ls_bargain_help definition

CREATE TABLE `ls_bargain_help` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `initiate_id` int NOT NULL COMMENT '发起砍价id',
                                   `user_id` int NOT NULL COMMENT '帮砍用户id',
                                   `reduce_amount` decimal(10,2) NOT NULL COMMENT '帮砍金额',
                                   `create_time` int DEFAULT NULL COMMENT '创建时间',
                                   `update_time` int DEFAULT NULL COMMENT '更新时间',
                                   `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='砍价助力表';


-- lingshou.ls_bargain_initiate definition

CREATE TABLE `ls_bargain_initiate` (
                                       `id` int NOT NULL AUTO_INCREMENT,
                                       `sn` varchar(100) NOT NULL DEFAULT '' COMMENT '编号',
                                       `activity_id` int NOT NULL COMMENT '砍价活动id',
                                       `user_id` int NOT NULL COMMENT '发起用户id',
                                       `order_id` int DEFAULT NULL COMMENT '关联订单id',
                                       `goods_snapshot` text NOT NULL COMMENT '商品快照',
                                       `bargain_snapshot` text NOT NULL COMMENT '砍价活动快照',
                                       `help_num` int NOT NULL COMMENT '累计帮砍次数',
                                       `current_price` decimal(10,2) NOT NULL COMMENT '当前价格',
                                       `floor_price` decimal(10,2) NOT NULL COMMENT '底价',
                                       `first_knife` decimal(10,2) NOT NULL COMMENT '首刀金额',
                                       `start_time` int NOT NULL COMMENT '发起时间',
                                       `end_time` int DEFAULT NULL COMMENT '结束时间',
                                       `status` tinyint(1) NOT NULL COMMENT '状态 1-砍价中 2-砍价成功 3-砍价失败',
                                       `create_time` int DEFAULT NULL COMMENT '创建时间',
                                       `update_time` int DEFAULT NULL COMMENT '更新时间',
                                       `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='砍价活动表';


-- lingshou.ls_cart definition

CREATE TABLE `ls_cart` (
                           `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '购物车表',
                           `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
                           `goods_id` int unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
                           `goods_num` smallint unsigned NOT NULL DEFAULT '0' COMMENT '购买数量',
                           `item_id` int NOT NULL DEFAULT '0' COMMENT '规格ID',
                           `selected` tinyint(1) DEFAULT '1' COMMENT '选中状态;1-选中;0-未选中',
                           `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                           `update_time` int unsigned DEFAULT NULL COMMENT '修改时间',
                           `sid` int NOT NULL COMMENT '商户id',
                           `delete_time` int DEFAULT NULL,
                           PRIMARY KEY (`id`) USING BTREE,
                           KEY `user_id` (`user_id`) USING BTREE,
                           KEY `goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=266 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='购物车表';


-- lingshou.ls_chat_record definition

CREATE TABLE `ls_chat_record` (
                                  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `from_id` int NOT NULL COMMENT '发送人user _id',
                                  `from_type` varchar(10) NOT NULL COMMENT '发送人类型',
                                  `to_id` int NOT NULL COMMENT '接收人user_id',
                                  `to_type` varchar(10) NOT NULL COMMENT '接收人类型',
                                  `msg` text NOT NULL COMMENT '聊天消息',
                                  `msg_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '聊天类型,1-文本,2-图片,3-商品',
                                  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '已读状态,0-未读,1-已读',
                                  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '记录类型，1-普通记录，2-提醒记录',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  `create_time` int NOT NULL COMMENT '创建时间',
                                  `update_time` int DEFAULT NULL COMMENT '修改时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='聊天关系表';


-- lingshou.ls_chat_relation definition

CREATE TABLE `ls_chat_relation` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `user_id` int NOT NULL DEFAULT '0' COMMENT '发送人的uid',
                                    `kefu_id` int NOT NULL DEFAULT '0' COMMENT '送达人的uid',
                                    `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '用户昵称',
                                    `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '用户头像',
                                    `msg` text NOT NULL COMMENT '消息',
                                    `msg_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '消息内容',
                                    `terminal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户端类型：1-pc管理后台 2-mobile手机管理后台',
                                    `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '已读状态,0-未读,1-已读',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    `create_time` int NOT NULL DEFAULT '0' COMMENT '添加时间',
                                    `update_time` int DEFAULT '0' COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='聊天记录表';


-- lingshou.ls_config definition

CREATE TABLE `ls_config` (
                             `id` int NOT NULL AUTO_INCREMENT,
                             `type` varchar(24) DEFAULT NULL COMMENT '类型',
                             `name` varchar(60) NOT NULL DEFAULT '' COMMENT '名称',
                             `value` text COMMENT '值',
                             `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                             `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                             `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                             `sid` int NOT NULL COMMENT '商户id',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=401 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='配置表';


-- lingshou.ls_coupon definition

CREATE TABLE `ls_coupon` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             `sn` varchar(16) NOT NULL DEFAULT '' COMMENT '优惠券编号',
                             `name` varchar(32) NOT NULL DEFAULT '' COMMENT '优惠券名称',
                             `money` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '优惠券面额',
                             `condition_type` tinyint(1) NOT NULL COMMENT '使用条件类型：1=无门槛；2=订单满足金额',
                             `condition_money` decimal(10,2) unsigned NOT NULL COMMENT '使用条件(订单满多少可用)：condition_money=2时生效',
                             `send_total_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发放数量限制：1=无限数量；2=固定发放数量',
                             `send_total` int unsigned NOT NULL DEFAULT '0' COMMENT '发放数量, send_total_type=2时生效',
                             `use_time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '用券时间类型：1=固定时间；2=领券当天起；3=领券次日起',
                             `use_time_start` int unsigned NOT NULL DEFAULT '0' COMMENT '用券开始时间：use_time_type=1时生效',
                             `use_time_end` int unsigned NOT NULL DEFAULT '0' COMMENT '用券结束时间：use_time_type=1时生效',
                             `use_time` int unsigned NOT NULL DEFAULT '0' COMMENT '多少天内可用：use_time_type=2/3时生效',
                             `get_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '领取类型：1=用户领取, 2=商家赠送',
                             `get_num_type` tinyint(1) NOT NULL COMMENT '领取次数类型：1=不限制领取次数；2=限制次数；3=每天限制数量',
                             `get_num` int NOT NULL COMMENT '领取次数类型: get_type=2/3时生效',
                             `use_goods_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '适用商品类型：1=全部商品；2=指定商品；3=指定商品不可用',
                             `use_goods_ids` text COMMENT '使用商品ID',
                             `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发放状态:  1=未开始,  2=进行中, 3=已结束',
                             `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                             `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                             `delete_time` int DEFAULT NULL COMMENT '删除时间',
                             `sid` int NOT NULL COMMENT '商户id',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='优惠券表';


-- lingshou.ls_coupon_list definition

CREATE TABLE `ls_coupon_list` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `channel` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '获取途径: 1=用户领取, 2=商家发放',
                                  `coupon_code` varchar(32) NOT NULL COMMENT '优惠券编码',
                                  `user_id` int unsigned NOT NULL COMMENT '用户ID',
                                  `coupon_id` int unsigned NOT NULL COMMENT '优惠券ID',
                                  `order_id` int NOT NULL COMMENT '订单ID',
                                  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态[0=未使用，1=已使用，2=已过期，3=已作废]',
                                  `use_time` int unsigned NOT NULL DEFAULT '0' COMMENT '使用时间',
                                  `invalid_time` int unsigned NOT NULL DEFAULT '0' COMMENT '失效时间',
                                  `create_time` int NOT NULL COMMENT '领取时间',
                                  `update_time` int NOT NULL COMMENT '更新时间',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  `delete_time` int DEFAULT NULL,
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=331 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='优惠券领取表';


-- lingshou.ls_decorate_theme definition

CREATE TABLE `ls_decorate_theme` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `name` varchar(32) NOT NULL COMMENT '名称',
                                     `create_time` int NOT NULL COMMENT '创建时间',
                                     `update_time` int NOT NULL COMMENT '更新时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     `sid` int NOT NULL COMMENT '商户id',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='装饰主题';


-- lingshou.ls_decorate_theme_config definition

CREATE TABLE `ls_decorate_theme_config` (
                                            `id` int NOT NULL AUTO_INCREMENT,
                                            `theme_id` int NOT NULL DEFAULT '1' COMMENT '主题id',
                                            `type` tinyint(1) NOT NULL COMMENT '配置类型:1-主题色；2-底部导航；3-开屏广告',
                                            `content` text NOT NULL COMMENT '配置内容',
                                            `create_time` int NOT NULL COMMENT '创建时间',
                                            `update_time` int NOT NULL COMMENT '更新时间',
                                            `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                            `sid` int NOT NULL COMMENT '商户id',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='装饰配置';


-- lingshou.ls_decorate_theme_page definition

CREATE TABLE `ls_decorate_theme_page` (
                                          `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `theme_id` int NOT NULL COMMENT '主题id',
                                          `name` varchar(32) NOT NULL COMMENT '页面名称',
                                          `is_home` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否首页：1-是；0-否',
                                          `type` tinyint(1) NOT NULL COMMENT '类型：1-首页；2-商品分类；3-会员中心；4-购物车',
                                          `content` longtext NOT NULL COMMENT '内容',
                                          `common` text NOT NULL COMMENT '公共配置',
                                          `create_time` int NOT NULL COMMENT '创建时间',
                                          `update_time` int NOT NULL COMMENT '更新时间',
                                          `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                          `sid` int NOT NULL COMMENT '商户id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='装饰页面';


-- lingshou.ls_delivery definition

CREATE TABLE `ls_delivery` (
                               `id` int unsigned NOT NULL AUTO_INCREMENT,
                               `order_id` int unsigned NOT NULL COMMENT '订单ID',
                               `order_sn` varchar(64) NOT NULL DEFAULT '' COMMENT '订单编号',
                               `user_id` int unsigned NOT NULL COMMENT '用户ID',
                               `admin_id` int unsigned DEFAULT '0' COMMENT '管理员ID',
                               `contact` varchar(64) NOT NULL DEFAULT '' COMMENT '收货人',
                               `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '联系方式',
                               `province` int NOT NULL COMMENT '省',
                               `city` int DEFAULT NULL COMMENT '市',
                               `district` int DEFAULT NULL COMMENT '区',
                               `address` varchar(64) DEFAULT NULL COMMENT '详细地址',
                               `express_status` tinyint unsigned DEFAULT '0' COMMENT '发货状态;0-未发货;1-已发货',
                               `express_id` int DEFAULT NULL COMMENT '物流公司id',
                               `express_name` varchar(64) DEFAULT NULL COMMENT '快递名称',
                               `invoice_no` varchar(255) DEFAULT '' COMMENT '物流单号',
                               `send_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '配送方式:1-快递配送;2-无需快递',
                               `remark` varchar(255) DEFAULT NULL COMMENT '发货备注',
                               `create_time` int NOT NULL COMMENT '创建时间',
                               `update_time` int DEFAULT NULL COMMENT '更新时间',
                               `delete_time` int DEFAULT NULL COMMENT '删除时间',
                               `sid` int NOT NULL COMMENT '商户id',
                               `last_trace` text COMMENT '最后一次物流轨迹信息',
                               `last_trace_time` datetime DEFAULT NULL COMMENT '最后一次物流轨迹时间',
                               PRIMARY KEY (`id`) USING BTREE,
                               UNIQUE KEY `uk_delivery_order_sn` (`order_sn`,`invoice_no`,`sid`)
) ENGINE=InnoDB AUTO_INCREMENT=1541 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='发货单表';


-- lingshou.ls_dev_crontab definition

CREATE TABLE `ls_dev_crontab` (
                                  `id` int NOT NULL AUTO_INCREMENT,
                                  `name` varchar(32) NOT NULL COMMENT '定时任务名称',
                                  `type` tinyint(1) NOT NULL COMMENT '类型：1-定时任务',
                                  `system` tinyint DEFAULT '0' COMMENT '是否系统任务：0-否；1-是；',
                                  `remark` varchar(255) DEFAULT '' COMMENT '备注',
                                  `command` varchar(64) NOT NULL COMMENT '命令内容',
                                  `params` varchar(64) DEFAULT '' COMMENT '参数',
                                  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：1-运行；2-停止；3-错误；',
                                  `expression` varchar(64) NOT NULL COMMENT '运行规则',
                                  `error` varchar(256) DEFAULT NULL COMMENT '运行失败原因',
                                  `last_time` int DEFAULT NULL COMMENT '最后执行时间',
                                  `time` varchar(64) DEFAULT '0' COMMENT '实时执行时长',
                                  `max_time` varchar(64) DEFAULT '0' COMMENT '最大执行时长',
                                  `create_time` int DEFAULT NULL COMMENT '创建时间',
                                  `update_time` int DEFAULT NULL COMMENT '更新时间',
                                  `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='定时任务表';


-- lingshou.ls_dev_footprint definition

CREATE TABLE `ls_dev_footprint` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '提醒类型',
                                    `name` varchar(32) NOT NULL DEFAULT '' COMMENT '提醒名称',
                                    `template` varchar(200) NOT NULL DEFAULT '' COMMENT '提醒模板',
                                    `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '气泡状态[0=关闭,1=开启]',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    `delete_time` int DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='足迹气泡表';


-- lingshou.ls_dev_notice_setting definition

CREATE TABLE `ls_dev_notice_setting` (
                                         `id` int NOT NULL AUTO_INCREMENT,
                                         `scene_id` int NOT NULL COMMENT '场景id',
                                         `scene_name` varchar(255) NOT NULL DEFAULT '' COMMENT '场景名称',
                                         `scene_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '场景描述',
                                         `recipient` tinyint(1) NOT NULL DEFAULT '1' COMMENT '接收者 1-买家 2-卖家',
                                         `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '通知类型: 1-业务通知 2-验证码',
                                         `system_notice` text COMMENT '系统通知设置',
                                         `sms_notice` text COMMENT '短信通知设置',
                                         `oa_notice` text COMMENT '公众号通知设置',
                                         `mnp_notice` text COMMENT '小程序通知设置',
                                         `support` char(10) NOT NULL DEFAULT '' COMMENT '支持的发送类型 1-系统通知 2-短信通知 3-微信模板消息 4-小程序提醒',
                                         `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                         `sid` int NOT NULL COMMENT '商户id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=187 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知设置表';


-- lingshou.ls_dev_pay definition

CREATE TABLE `ls_dev_pay` (
                              `id` int unsigned NOT NULL AUTO_INCREMENT,
                              `name` varchar(32) NOT NULL DEFAULT '' COMMENT '模版名称',
                              `pay_way` tinyint(1) NOT NULL COMMENT '支付方式:1-余额支付;2-微信支付;3-支付宝支付;',
                              `config` text COMMENT '对应支付配置(json字符串)',
                              `icon` varchar(255) DEFAULT NULL COMMENT '图标',
                              `sort` int DEFAULT NULL COMMENT '排序',
                              `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                              `sid` int NOT NULL COMMENT '商户id',
                              `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='支付配置表';


-- lingshou.ls_dev_pay_way definition

CREATE TABLE `ls_dev_pay_way` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT,
                                  `scene` tinyint(1) NOT NULL COMMENT '场景:1-微信小程序;2-微信公众号;3-H5;4-PC商城;5-APP;',
                                  `dev_pay_id` int NOT NULL COMMENT '支付配置ID',
                                  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认支付:0-否;1-是;',
                                  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0-关闭;1-开启;',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='支付方式表';


-- lingshou.ls_dev_region definition

CREATE TABLE `ls_dev_region` (
                                 `id` int NOT NULL DEFAULT '0' COMMENT '地区编号',
                                 `parent_id` int NOT NULL DEFAULT '0' COMMENT '父级地区编码',
                                 `level` tinyint(1) NOT NULL DEFAULT '0' COMMENT '等级 0-国家；1-省份；2-地级市；3-县区',
                                 `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                                 `short` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简称',
                                 `city_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地区编码',
                                 `zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮政编码',
                                 `gcj02_lng` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '纬度',
                                 `gcj02_lat` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '经度',
                                 `db09_lng` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '纬度',
                                 `db09_lat` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '经度',
                                 `remark1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
                                 `remark2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='地区表';


-- lingshou.ls_dev_region_20240229 definition

CREATE TABLE `ls_dev_region_20240229` (
                                          `id` int NOT NULL DEFAULT '0' COMMENT '地区编号',
                                          `parent_id` int NOT NULL DEFAULT '0' COMMENT '父级地区编码',
                                          `level` tinyint(1) NOT NULL DEFAULT '0' COMMENT '等级 0-国家；1-省份；2-地级市；3-县区',
                                          `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                                          `short` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简称',
                                          `city_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地区编码',
                                          `zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮政编码',
                                          `gcj02_lng` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '纬度',
                                          `gcj02_lat` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '经度',
                                          `db09_lng` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '纬度',
                                          `db09_lat` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '经度',
                                          `remark1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
                                          `remark2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='地区表';


-- lingshou.ls_discount_goods definition

CREATE TABLE `ls_discount_goods` (
                                     `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `goods_id` int DEFAULT NULL COMMENT '商品id',
                                     `is_discount` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否参与折扣：1-参与，0-不参与',
                                     `discount_rule` tinyint(1) NOT NULL COMMENT '折扣规则：1-根据会员等级设置，2-单独设置',
                                     `sid` int NOT NULL COMMENT '商家id',
                                     `create_time` int unsigned NOT NULL COMMENT '创建时间',
                                     `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE KEY `goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品折扣';


-- lingshou.ls_discount_goods_item definition

CREATE TABLE `ls_discount_goods_item` (
                                          `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                          `discount_id` int NOT NULL COMMENT '折扣id',
                                          `goods_id` int NOT NULL COMMENT '商品id',
                                          `item_id` int NOT NULL DEFAULT '0' COMMENT '规格id',
                                          `level` int NOT NULL COMMENT '等级id',
                                          `discount_price` decimal(10,2) NOT NULL COMMENT '会员价',
                                          `sid` int NOT NULL COMMENT '商家id',
                                          `create_time` int unsigned NOT NULL COMMENT '创建时间',
                                          `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品规格折扣';


-- lingshou.ls_distribution definition

CREATE TABLE `ls_distribution` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT,
                                   `user_id` int unsigned NOT NULL COMMENT '用户id',
                                   `level_id` int NOT NULL COMMENT '分销会员等级id',
                                   `is_distribution` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否分销会员 0-否 1-是',
                                   `is_freeze` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否冻结 0-否 1-是',
                                   `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
                                   `distribution_time` int DEFAULT NULL COMMENT '成为分销会员时间',
                                   `mnp_qr_code` varchar(255) DEFAULT '' COMMENT '小程序分享二维码',
                                   `h5_qr_code` varchar(255) DEFAULT '' COMMENT 'h5分享二维码',
                                   `app_qr_code` varchar(255) DEFAULT '' COMMENT 'app分享二维码',
                                   `create_time` int DEFAULT NULL COMMENT '创建时间',
                                   `update_time` int DEFAULT NULL COMMENT '更新时间',
                                   `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=570 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分销基础信息表';


-- lingshou.ls_distribution_apply definition

CREATE TABLE `ls_distribution_apply` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT,
                                         `user_id` int unsigned NOT NULL COMMENT '用户id',
                                         `real_name` varchar(12) NOT NULL DEFAULT '' COMMENT '真实姓名',
                                         `mobile` varchar(30) NOT NULL COMMENT '手机号',
                                         `province` int unsigned NOT NULL DEFAULT '0' COMMENT '省份',
                                         `city` int unsigned NOT NULL DEFAULT '0' COMMENT '城市',
                                         `district` int unsigned NOT NULL DEFAULT '0' COMMENT '县区',
                                         `reason` varchar(255) NOT NULL DEFAULT '' COMMENT '申请原因',
                                         `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核原因',
                                         `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态：0-待审核；1-审核通过；2-审核不通过',
                                         `create_time` int DEFAULT NULL COMMENT '创建时间',
                                         `update_time` int DEFAULT NULL COMMENT '更新时间',
                                         `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                         `sid` int NOT NULL COMMENT '商户id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分销申请表';


-- lingshou.ls_distribution_config definition

CREATE TABLE `ls_distribution_config` (
                                          `id` int unsigned NOT NULL AUTO_INCREMENT,
                                          `key` varchar(255) NOT NULL COMMENT '配置项名称',
                                          `value` text COMMENT '配置项值',
                                          `create_time` int DEFAULT NULL COMMENT '创建时间',
                                          `update_time` int DEFAULT NULL COMMENT '更新时间',
                                          `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                          `sid` int NOT NULL COMMENT '商户id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分销配置表';


-- lingshou.ls_distribution_goods definition

CREATE TABLE `ls_distribution_goods` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT,
                                         `goods_id` int unsigned NOT NULL COMMENT '商品id',
                                         `item_id` int unsigned NOT NULL DEFAULT '0' COMMENT '商品规格id',
                                         `level_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分销会员等级id',
                                         `self_ratio` decimal(10,0) DEFAULT NULL COMMENT '自购佣金比例',
                                         `first_ratio` decimal(10,0) DEFAULT NULL COMMENT '一级佣金比例',
                                         `second_ratio` decimal(10,0) DEFAULT NULL COMMENT '二级佣金比例',
                                         `third_ratio` decimal(10,2) DEFAULT NULL COMMENT '三级佣金比例',
                                         `is_distribution` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否参与分销 0-不参与 1-参与',
                                         `rule` tinyint(1) NOT NULL COMMENT '佣金规则 1-按分销等级比例分佣 2-单独设置分佣比例',
                                         `create_time` int DEFAULT NULL COMMENT '创建时间',
                                         `update_time` int DEFAULT NULL COMMENT '更新时间',
                                         `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                         `sid` int NOT NULL COMMENT '商户id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分销商品表';


-- lingshou.ls_distribution_level definition

CREATE TABLE `ls_distribution_level` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT,
                                         `name` varchar(60) NOT NULL COMMENT '等级名称',
                                         `weights` tinyint NOT NULL COMMENT '等级权重',
                                         `first_ratio` float unsigned NOT NULL DEFAULT '0' COMMENT '一级佣金比例',
                                         `second_ratio` float unsigned NOT NULL DEFAULT '0' COMMENT '二级佣金比例',
                                         `third_ratio` float unsigned NOT NULL DEFAULT '0' COMMENT '三级佣金比例',
                                         `self_ratio` float unsigned NOT NULL DEFAULT '0' COMMENT '自购佣金比例',
                                         `is_default` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否默认等级 0-否 1-是',
                                         `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '等级描述',
                                         `update_relation` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '升级关系，1-OR关系 2-AND关系',
                                         `create_time` int DEFAULT NULL COMMENT '创建时间',
                                         `update_time` int DEFAULT NULL COMMENT '更新时间',
                                         `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                         `sid` int NOT NULL COMMENT '商户id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分销会员等级表';


-- lingshou.ls_distribution_level_update definition

CREATE TABLE `ls_distribution_level_update` (
                                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT ' ',
                                                `level_id` int unsigned NOT NULL COMMENT '分销会员等级id',
                                                `key` varchar(255) NOT NULL COMMENT '条件名称',
                                                `value_int` int DEFAULT NULL COMMENT '整型条件值',
                                                `value_decimal` decimal(10,2) DEFAULT NULL COMMENT '浮点型条件值',
                                                `value_text` text COMMENT '文本型条件值,主要用于存储json格式字符串',
                                                `create_time` int DEFAULT NULL COMMENT '创建时间',
                                                `update_time` int DEFAULT NULL COMMENT '更新时间',
                                                `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                                `sid` int NOT NULL COMMENT '商户id',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分销会员等级升级条件表';


-- lingshou.ls_distribution_order_goods definition

CREATE TABLE `ls_distribution_order_goods` (
                                               `id` int unsigned NOT NULL AUTO_INCREMENT,
                                               `user_id` int unsigned NOT NULL COMMENT '用户id',
                                               `order_goods_id` int unsigned NOT NULL COMMENT '子订单id',
                                               `goods_id` int NOT NULL COMMENT '商品id',
                                               `item_id` int NOT NULL COMMENT '商品SKU',
                                               `earnings` decimal(10,2) unsigned NOT NULL COMMENT '预估收入',
                                               `level_id` int NOT NULL COMMENT '分销会员等级',
                                               `level` int NOT NULL COMMENT '分销层级',
                                               `ratio` decimal(10,2) NOT NULL COMMENT '分佣比例',
                                               `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-未返佣金 2-已返佣金 3-佣金失效',
                                               `create_time` int DEFAULT NULL COMMENT '创建时间',
                                               `update_time` int DEFAULT NULL COMMENT '更新时间',
                                               `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                               `settlement_time` int DEFAULT NULL COMMENT '结算时间',
                                               `sid` int NOT NULL COMMENT '商户id',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分销订单表';


-- lingshou.ls_express definition

CREATE TABLE `ls_express` (
                              `id` int NOT NULL AUTO_INCREMENT,
                              `name` varchar(32) NOT NULL COMMENT '快递公司名称',
                              `icon` varchar(128) DEFAULT NULL COMMENT '快递图标',
                              `code` varchar(128) DEFAULT NULL COMMENT '快递公司编码',
                              `code100` varchar(128) DEFAULT NULL COMMENT '快递100编码',
                              `codebird` varchar(128) DEFAULT NULL COMMENT '快递鸟编码',
                              `codexc` varchar(128) DEFAULT NULL COMMENT '想送物流',
                              `sort` int unsigned DEFAULT NULL COMMENT '排序',
                              `create_time` int DEFAULT NULL COMMENT '创建时间',
                              `update_time` int DEFAULT NULL COMMENT '修改时间',
                              `delete_time` int DEFAULT NULL COMMENT '删除时间',
                              `sid` int NOT NULL COMMENT '商户id',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='快递公司表';


-- lingshou.ls_face_sheet_sender definition

CREATE TABLE `ls_face_sheet_sender` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `name` varchar(64) NOT NULL COMMENT '发件人名称',
                                        `mobile` varchar(64) NOT NULL COMMENT '发件人电话',
                                        `province_id` int unsigned NOT NULL COMMENT '省ID',
                                        `city_id` int unsigned NOT NULL COMMENT '市ID',
                                        `district_id` int unsigned NOT NULL COMMENT '区ID',
                                        `address` varchar(255) NOT NULL COMMENT '详细地址',
                                        `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                        `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                        `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                        `sid` int NOT NULL COMMENT '商户id',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='电子面单模板表';


-- lingshou.ls_face_sheet_template definition

CREATE TABLE `ls_face_sheet_template` (
                                          `id` int unsigned NOT NULL AUTO_INCREMENT,
                                          `express_id` int unsigned NOT NULL COMMENT '快递公司ID',
                                          `name` varchar(120) NOT NULL DEFAULT '' COMMENT '模板名称',
                                          `template_id` varchar(200) NOT NULL DEFAULT '' COMMENT '模板ID',
                                          `partner_id` varchar(120) NOT NULL DEFAULT '' COMMENT '电子面单账号',
                                          `partner_key` varchar(120) NOT NULL DEFAULT '' COMMENT '电子面单密码',
                                          `net` varchar(120) NOT NULL DEFAULT '' COMMENT '网点标识',
                                          `pay_type` varchar(120) NOT NULL DEFAULT '' COMMENT '运费支付方式',
                                          `create_time` int DEFAULT NULL COMMENT '创建时间',
                                          `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                          `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                          `sid` int NOT NULL COMMENT '商户id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='电子面单发件人表';


-- lingshou.ls_file definition

CREATE TABLE `ls_file` (
                           `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                           `cid` int unsigned NOT NULL DEFAULT '0' COMMENT '类目ID',
                           `source` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：0-后台（平台）；1-用户；2-客服',
                           `source_id` int NOT NULL COMMENT '来源id',
                           `type` tinyint unsigned NOT NULL DEFAULT '10' COMMENT '类型[10=图片, 20=视频]',
                           `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件名称',
                           `uri` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件路径',
                           `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                           `update_time` int DEFAULT NULL COMMENT '更新时间',
                           `delete_time` int DEFAULT NULL COMMENT '删除时间',
                           `sid` int NOT NULL COMMENT '商户id',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=519 DEFAULT CHARSET=utf8mb3 COMMENT='文件表';


-- lingshou.ls_file_cate definition

CREATE TABLE `ls_file_cate` (
                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                `pid` int unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
                                `type` tinyint unsigned NOT NULL DEFAULT '10' COMMENT '类型[10=图片，20=视频，30=文件]',
                                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
                                `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                `sid` int NOT NULL COMMENT '商户id',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb3 COMMENT='文件分类表';


-- lingshou.ls_footprint_record definition

CREATE TABLE `ls_footprint_record` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '提醒类型',
                                       `user_id` int unsigned NOT NULL COMMENT '用户ID',
                                       `foreign_id` int unsigned NOT NULL DEFAULT '0' COMMENT '外键ID(取决于type字段, 有可能是商品ID, 也可能是其它表的ID)',
                                       `template` varchar(255) NOT NULL DEFAULT '' COMMENT '模板信息',
                                       `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       `delete_time` int DEFAULT NULL,
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `create_time_idx` (`create_time`),
                                       KEY `sid_idx` (`sid`)
) ENGINE=InnoDB AUTO_INCREMENT=59765 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='足迹记录表';


-- lingshou.ls_free_shipping definition

CREATE TABLE `ls_free_shipping` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT,
                                    `name` varchar(255) NOT NULL COMMENT '活动名称',
                                    `start_time` int NOT NULL COMMENT '开始时间',
                                    `end_time` int NOT NULL COMMENT '结束时间',
                                    `status` tinyint(1) NOT NULL COMMENT '活动状态 0-未开始 1-进行中 2-已结束',
                                    `target_user_type` tinyint(1) NOT NULL COMMENT '目标用户 1-全部 2-指定用户 3-排除指定用户',
                                    `target_goods_type` tinyint(1) NOT NULL COMMENT '目标商品 1-全部 2-指定商品 3-排除指定商品',
                                    `condition_type` tinyint(1) NOT NULL COMMENT '活动规则 1-按订单实付金额包邮 2-按购买件数包邮',
                                    `region` json NOT NULL COMMENT '包邮区域',
                                    `create_time` int DEFAULT NULL COMMENT '创建时间',
                                    `update_time` int DEFAULT NULL COMMENT '更新时间',
                                    `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='包邮活动';


-- lingshou.ls_free_shipping_order definition

CREATE TABLE `ls_free_shipping_order` (
                                          `id` int unsigned NOT NULL AUTO_INCREMENT,
                                          `free_shpping_id` int NOT NULL COMMENT '包邮活动id',
                                          `order_id` int NOT NULL COMMENT '订单id',
                                          `amount` decimal(10,2) DEFAULT NULL COMMENT '订单实付金额',
                                          `create_time` int DEFAULT NULL,
                                          `update_time` int DEFAULT NULL,
                                          `delete_time` int DEFAULT NULL,
                                          `sid` int NOT NULL COMMENT '商户id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='包邮活动订单';


-- lingshou.ls_freight definition

CREATE TABLE `ls_freight` (
                              `id` int unsigned NOT NULL AUTO_INCREMENT,
                              `name` varchar(255) NOT NULL COMMENT '模板名称',
                              `charge_way` tinyint(1) NOT NULL COMMENT '计费方式:1-件数计费;2-重量计费;3-体积计费',
                              `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                              `create_time` int unsigned DEFAULT '0' COMMENT '添加时间',
                              `update_time` int unsigned DEFAULT '0' COMMENT '更新时间',
                              `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                              `sid` int NOT NULL COMMENT '商户id',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='运费模板表';


-- lingshou.ls_freight_config definition

CREATE TABLE `ls_freight_config` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT,
                                     `freight_id` int DEFAULT NULL COMMENT '模板id',
                                     `region_id` text COMMENT '地区id',
                                     `first_unit` int DEFAULT NULL COMMENT '首重/件',
                                     `first_money` decimal(10,0) DEFAULT NULL COMMENT '首重/件价格',
                                     `continue_unit` int DEFAULT NULL COMMENT '续重/件',
                                     `continue_money` decimal(10,0) DEFAULT NULL COMMENT '续重/件价格',
                                     `sid` int NOT NULL COMMENT '商户id',
                                     `delete_time` int unsigned DEFAULT NULL,
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='运费模板配置表';


-- lingshou.ls_goods definition

CREATE TABLE `ls_goods` (
                            `id` int NOT NULL AUTO_INCREMENT,
                            `uuid` varchar(100) DEFAULT '' COMMENT 'mcoin 商城商品唯一標',
                            `name` varchar(255) NOT NULL COMMENT '商品名称',
                            `pay_type` int DEFAULT '2' COMMENT '支付類型 0：混合支付 1：純積分 2：純金額',
                            `min_integral` int DEFAULT NULL COMMENT '最小積分',
                            `max_integral` int DEFAULT NULL COMMENT '最大積分',
                            `integral_ratio` int DEFAULT '300' COMMENT '積分金額比例',
                            `is_mcoin` int DEFAULT '2' COMMENT '是否mcoin福利 1：mcoin+零售 2:零售',
                            `fee_rate` int DEFAULT '0' COMMENT '福利佣金',
                            `code` varchar(100) NOT NULL COMMENT '商品编码',
                            `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型：1-实物商品，2-虚拟商品',
                            `snap_up` int DEFAULT '0' COMMENT '是否mCoin商城的秒殺商品',
                            `user_limit` int DEFAULT '0' COMMENT '用戶限購數',
                            `brand_id` int DEFAULT NULL COMMENT '品牌id',
                            `supplier_id` int DEFAULT NULL COMMENT '供应商id',
                            `unit_id` int DEFAULT NULL COMMENT '单位id',
                            `video` varchar(128) DEFAULT NULL COMMENT '商品视频',
                            `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品状态：1-上架；0-下架；',
                            `video_source` tinyint(1) DEFAULT NULL COMMENT '视频来源：1-视频素材库；2-视频链接',
                            `image` varchar(128) NOT NULL COMMENT '商品主图',
                            `video_cover` varchar(128) DEFAULT NULL COMMENT '商品视频封面',
                            `poster` varchar(128) DEFAULT NULL COMMENT '商品自定义海报',
                            `is_express` tinyint(1) DEFAULT '1' COMMENT '是否开启快递配送:1-是;0-否;',
                            `is_selffetch` tinyint(1) DEFAULT '0' COMMENT '是否开启上门自提:1-是;0-否;',
                            `is_virtualdelivery` tinyint(1) DEFAULT '0' COMMENT '是否虚拟发货：1-是,0-否',
                            `express_type` tinyint(1) NOT NULL COMMENT '运费类型：1-包邮；2-统一运费；3-运费模板',
                            `express_money` decimal(10,2) DEFAULT NULL COMMENT '统一运费',
                            `express_template_id` int DEFAULT NULL COMMENT '运费模板',
                            `after_pay` tinyint(1) DEFAULT '0' COMMENT '买家付款后: 1-自动发货,2-手动发货',
                            `after_delivery` tinyint(1) DEFAULT '0' COMMENT '发货后:1-自动完成订单,2-需要买家确认收货',
                            `virtual_sales_num` int DEFAULT '0' COMMENT '虚拟销量',
                            `stock_warning` int DEFAULT NULL COMMENT '库存预警（空或0表示不做预警）',
                            `spec_type` tinyint(1) NOT NULL COMMENT '规格类型：1-单规格；2-多规格',
                            `sort` int NOT NULL COMMENT '排序',
                            `min_price` decimal(10,2) NOT NULL COMMENT '最小价',
                            `max_price` decimal(10,2) NOT NULL COMMENT '最大价',
                            `min_lineation_price` decimal(10,2) NOT NULL COMMENT '最小划线价',
                            `max_lineation_price` decimal(10,2) NOT NULL COMMENT '最大划线价',
                            `subsidy_amount` decimal(10,2) DEFAULT '0.00' COMMENT '平台優惠金',
                            `total_stock` int NOT NULL COMMENT '总库存',
                            `click_num` int DEFAULT '0' COMMENT '点击量',
                            `virtual_click_num` int DEFAULT '0' COMMENT '虚拟点击量',
                            `sales_num` int DEFAULT '0' COMMENT '销量',
                            `sales_money` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售金额',
                            `content` text COMMENT '商品详情',
                            `delivery_content` text COMMENT '发货内容',
                            `create_time` int unsigned NOT NULL COMMENT '创建时间',
                            `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                            `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                            `sid` int NOT NULL COMMENT '商户id',
                            `is_address` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启收货地址(用于虚拟商品)：1-开启；0-关闭；',
                            `buy_start_time` datetime DEFAULT NULL COMMENT '可購買開始时间',
                            `buy_end_time` datetime DEFAULT NULL COMMENT '可購買結束时间',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `update_time_index` (`update_time`),
                            KEY `idx_uuid` (`uuid`)
) ENGINE=InnoDB AUTO_INCREMENT=170 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品表';


-- lingshou.ls_goods_activity definition

CREATE TABLE `ls_goods_activity` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT,
                                     `activity_type` tinyint(1) NOT NULL COMMENT '营销活动类型',
                                     `activity_id` int NOT NULL COMMENT '营销活动id',
                                     `goods_id` int NOT NULL COMMENT '商品id',
                                     `item_id` int NOT NULL COMMENT '规格id',
                                     `create_time` int DEFAULT NULL COMMENT '创建时间',
                                     `update_time` int DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     `sid` int NOT NULL COMMENT '商户id',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=393 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品活动信息表';


-- lingshou.ls_goods_brand definition

CREATE TABLE `ls_goods_brand` (
                                  `id` int NOT NULL AUTO_INCREMENT,
                                  `name` varchar(32) NOT NULL COMMENT '品牌名称',
                                  `image` varchar(255) DEFAULT NULL COMMENT '品牌图片',
                                  `is_show` tinyint(1) DEFAULT '1' COMMENT '是否显示:1-是.0-否',
                                  `sort` int unsigned DEFAULT NULL COMMENT '排序',
                                  `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                  `update_time` int unsigned DEFAULT NULL COMMENT '修改时间',
                                  `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品品牌表';


-- lingshou.ls_goods_category definition

CREATE TABLE `ls_goods_category` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `name` varchar(90) NOT NULL COMMENT '分类名称',
                                     `pid` int unsigned NOT NULL COMMENT '父级id',
                                     `level` tinyint unsigned DEFAULT NULL COMMENT '等级',
                                     `image` varchar(255) DEFAULT '' COMMENT '分类图标',
                                     `sort` int unsigned DEFAULT '255' COMMENT '排序',
                                     `is_show` tinyint unsigned DEFAULT '1' COMMENT '是否显示:1-是;0-否',
                                     `is_recommend` tinyint unsigned DEFAULT NULL COMMENT '是否首页推荐:1-是;0-否',
                                     `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                     `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                     `sid` int NOT NULL COMMENT '商户id',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9004 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品分类表';


-- lingshou.ls_goods_category_index definition

CREATE TABLE `ls_goods_category_index` (
                                           `id` int NOT NULL AUTO_INCREMENT,
                                           `goods_id` int NOT NULL COMMENT '商品id',
                                           `category_id` int NOT NULL COMMENT '分类id',
                                           `sid` int NOT NULL COMMENT '商户id',
                                           `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=536 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品分类关联表';


-- lingshou.ls_goods_collect definition

CREATE TABLE `ls_goods_collect` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `user_id` int NOT NULL COMMENT '用户id',
                                    `goods_id` int NOT NULL COMMENT '商品id',
                                    `create_time` int NOT NULL COMMENT '收藏时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    `delete_time` int DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品收藏表';


-- lingshou.ls_goods_comment definition

CREATE TABLE `ls_goods_comment` (
                                    `id` int NOT NULL AUTO_INCREMENT COMMENT '商品评论id',
                                    `goods_id` int NOT NULL COMMENT '商品id',
                                    `item_id` int NOT NULL COMMENT '规格id',
                                    `spec_value_str` varchar(32) NOT NULL COMMENT '规格名称',
                                    `user_id` int NOT NULL COMMENT '用户id',
                                    `order_goods_id` int NOT NULL COMMENT '订单商品表id',
                                    `goods_comment` tinyint(1) DEFAULT NULL COMMENT '商品评论星级 1 一星 2 二星 3三星 4四星 5五星',
                                    `service_comment` tinyint(1) DEFAULT NULL COMMENT '服务评论星级 1 一星 2 二星 3三星 4四星 5五星',
                                    `description_comment` tinyint(1) DEFAULT NULL COMMENT '描述相符星级1 一星 2 二星 3三星 4四星 5五星',
                                    `express_comment` tinyint(1) DEFAULT NULL COMMENT '物流评论星级 1 一星 2 二星 3三星 4四星 5五星',
                                    `comment` varchar(255) DEFAULT NULL COMMENT '商品评论',
                                    `reply` varchar(255) DEFAULT NULL COMMENT '商家回复',
                                    `status` tinyint(1) DEFAULT '0' COMMENT '审核状态 0-待审核 1-审核通过 2-审核拒绝 ',
                                    `virtual` text COMMENT '虚拟评论',
                                    `create_time` int NOT NULL COMMENT '创建时间',
                                    `update_time` int DEFAULT NULL COMMENT '更新时间',
                                    `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品评论表';


-- lingshou.ls_goods_comment_copy1 definition

CREATE TABLE `ls_goods_comment_copy1` (
                                          `id` int NOT NULL AUTO_INCREMENT COMMENT '商品评论id',
                                          `goods_id` int NOT NULL COMMENT '商品id',
                                          `item_id` int NOT NULL COMMENT '规格id',
                                          `spec_value_str` varchar(32) NOT NULL COMMENT '规格名称',
                                          `user_id` int NOT NULL COMMENT '用户id',
                                          `order_goods_id` int NOT NULL COMMENT '订单商品表id',
                                          `goods_comment` tinyint(1) DEFAULT NULL COMMENT '商品评论星级 1 一星 2 二星 3三星 4四星 5五星',
                                          `service_comment` tinyint(1) DEFAULT NULL COMMENT '服务评论星级 1 一星 2 二星 3三星 4四星 5五星',
                                          `description_comment` tinyint(1) DEFAULT NULL COMMENT '描述相符星级1 一星 2 二星 3三星 4四星 5五星',
                                          `express_comment` tinyint(1) DEFAULT NULL COMMENT '物流评论星级 1 一星 2 二星 3三星 4四星 5五星',
                                          `comment` varchar(255) DEFAULT NULL COMMENT '商品评论',
                                          `reply` varchar(255) DEFAULT NULL COMMENT '商家回复',
                                          `status` tinyint(1) DEFAULT '0' COMMENT '审核状态 0-待审核 1-审核通过 2-审核拒绝 ',
                                          `virtual` text COMMENT '虚拟评论',
                                          `create_time` int NOT NULL COMMENT '创建时间',
                                          `update_time` int DEFAULT NULL COMMENT '更新时间',
                                          `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                          `sid` int NOT NULL COMMENT '商户id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品评论表';


-- lingshou.ls_goods_comment_image definition

CREATE TABLE `ls_goods_comment_image` (
                                          `id` int NOT NULL AUTO_INCREMENT,
                                          `comment_id` int NOT NULL COMMENT '商品评价id',
                                          `uri` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '图片',
                                          `sid` int NOT NULL COMMENT '商户id',
                                          `delete_time` int DEFAULT NULL,
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='评论图片表';


-- lingshou.ls_goods_gather definition

CREATE TABLE `ls_goods_gather` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT,
                                   `log_id` int NOT NULL COMMENT '采集记录ID',
                                   `gather_url` text NOT NULL COMMENT '采集链接',
                                   `gather_info` longtext COMMENT '采集商品信息',
                                   `gather_status` tinyint(1) NOT NULL COMMENT '采集状态：0-失败；1-成功；',
                                   `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态：0-待处理；1-已处理；',
                                   `channel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '渠道：0-未知；1-淘宝；2-天猫；3-京东；4-阿里巴巴；',
                                   `create_time` int DEFAULT NULL COMMENT '创建时间',
                                   `update_time` int DEFAULT NULL COMMENT '更新时间',
                                   `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品采集表';


-- lingshou.ls_goods_gather_goods definition

CREATE TABLE `ls_goods_gather_goods` (
                                         `id` int NOT NULL AUTO_INCREMENT,
                                         `gather_id` int NOT NULL COMMENT '采集ID',
                                         `goods_id` int DEFAULT NULL COMMENT '同步过去的商品ID',
                                         `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型：1-实物商品，2-虚拟商品',
                                         `code` varchar(32) NOT NULL COMMENT '商品编码',
                                         `name` varchar(255) NOT NULL COMMENT '商品名称',
                                         `category_id` text NOT NULL COMMENT '分类ID',
                                         `goods_image` text NOT NULL COMMENT '商品轮播图',
                                         `video_source` tinyint(1) DEFAULT NULL COMMENT '视频来源：1-视频素材库；2-视频链接',
                                         `video_cover` varchar(128) DEFAULT NULL COMMENT '商品视频封面',
                                         `video` varchar(128) DEFAULT NULL COMMENT '商品视频',
                                         `brand_id` int DEFAULT NULL COMMENT '品牌id',
                                         `unit_id` int DEFAULT NULL COMMENT '单位id',
                                         `supplier_id` int DEFAULT NULL COMMENT '供应商id',
                                         `poster` varchar(128) DEFAULT NULL COMMENT '商品自定义海报',
                                         `is_express` tinyint(1) DEFAULT '1' COMMENT '是否开启快递配送:1-是;0-否;',
                                         `is_selffetch` tinyint(1) DEFAULT '0' COMMENT '是否开启上门自提:1-是;0-否;',
                                         `express_type` tinyint(1) DEFAULT '1' COMMENT '运费类型：1-包邮；2-统一运费；3-运费模板',
                                         `express_money` decimal(10,2) DEFAULT NULL COMMENT '统一运费',
                                         `express_template_id` int DEFAULT NULL COMMENT '运费模板',
                                         `is_virtualdelivery` tinyint(1) DEFAULT '1' COMMENT '是否虚拟发货：1-是,0-否',
                                         `after_pay` tinyint(1) DEFAULT '1' COMMENT '买家付款后: 1-自动发货,2-手动发货',
                                         `after_delivery` tinyint(1) DEFAULT '1' COMMENT '发货后:1-自动完成订单,2-需要买家确认收货',
                                         `delivery_content` text COMMENT '发货内容',
                                         `content` text COMMENT '商品详情',
                                         `stock_warning` int DEFAULT NULL COMMENT '库存预警（空或0表示不做预警）',
                                         `virtual_sales_num` int DEFAULT '0' COMMENT '虚拟销量',
                                         `virtual_click_num` int DEFAULT '0' COMMENT '虚拟点击量',
                                         `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品状态：1-上架；0-下架；',
                                         `spec_type` tinyint(1) NOT NULL COMMENT '规格类型：1-单规格；2-多规格',
                                         `spec_value` text COMMENT '规格项',
                                         `spec_value_list` text NOT NULL COMMENT '规格项信息',
                                         `is_address` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启收货地址(用于虚拟商品)：1-开启；0-关闭；',
                                         `create_time` int DEFAULT NULL COMMENT '创建时间',
                                         `update_time` int DEFAULT NULL COMMENT '更新时间',
                                         `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                         `sid` int NOT NULL COMMENT '商户id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品采集商品信息表';


-- lingshou.ls_goods_gather_log definition

CREATE TABLE `ls_goods_gather_log` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT,
                                       `goods_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '商品类型：1-实物商品，2-虚拟商品',
                                       `goods_category` int NOT NULL COMMENT '商品分类',
                                       `delivery_content` text COMMENT '发货内容',
                                       `create_time` int DEFAULT NULL COMMENT '创建时间',
                                       `update_time` int DEFAULT NULL COMMENT '更新时间',
                                       `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品采集记录表';


-- lingshou.ls_goods_image definition

CREATE TABLE `ls_goods_image` (
                                  `id` int NOT NULL AUTO_INCREMENT,
                                  `goods_id` int NOT NULL COMMENT '商品id',
                                  `uri` varchar(128) NOT NULL COMMENT '图片链接',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  `delete_time` int DEFAULT NULL,
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=215 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品轮播图表';


-- lingshou.ls_goods_item definition

CREATE TABLE `ls_goods_item` (
                                 `id` int unsigned NOT NULL AUTO_INCREMENT,
                                 `image` varchar(255) NOT NULL DEFAULT '' COMMENT '规格图片',
                                 `goods_id` int unsigned NOT NULL COMMENT '商品id',
                                 `spec_value_ids` varchar(64) NOT NULL DEFAULT '' COMMENT '多个规格id，隔开',
                                 `spec_value_str` varchar(255) NOT NULL DEFAULT '' COMMENT '多个规格名称，隔开',
                                 `sell_price` decimal(10,2) unsigned NOT NULL COMMENT '价格',
                                 `lineation_price` decimal(10,2) DEFAULT NULL COMMENT '划线价',
                                 `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
                                 `stock` int unsigned NOT NULL DEFAULT '0' COMMENT '库存',
                                 `volume` decimal(10,2) unsigned DEFAULT NULL COMMENT '体积',
                                 `weight` decimal(10,2) unsigned DEFAULT NULL COMMENT '重量',
                                 `bar_code` varchar(100) NOT NULL DEFAULT '' COMMENT '条码',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=347 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品规格信息表';


-- lingshou.ls_goods_spec definition

CREATE TABLE `ls_goods_spec` (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `goods_id` int NOT NULL COMMENT '商品id',
                                 `name` varchar(16) NOT NULL COMMENT '规格名称',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=187 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品规格';


-- lingshou.ls_goods_spec_value definition

CREATE TABLE `ls_goods_spec_value` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT,
                                       `goods_id` int unsigned NOT NULL COMMENT '商品id',
                                       `spec_id` int unsigned NOT NULL COMMENT '规格id',
                                       `value` varchar(255) NOT NULL DEFAULT '' COMMENT '规格值',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=265 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品规格项表';


-- lingshou.ls_goods_supplier definition

CREATE TABLE `ls_goods_supplier` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `name` varchar(64) NOT NULL COMMENT '名称',
                                     `code` varchar(32) NOT NULL COMMENT '供应商编码',
                                     `supplier_category_id` int NOT NULL COMMENT '供应商分类id',
                                     `contact` varchar(255) DEFAULT NULL COMMENT '联系人',
                                     `mobile` varchar(15) DEFAULT NULL COMMENT '联系电话',
                                     `landline` varchar(20) DEFAULT NULL COMMENT '座机号码',
                                     `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
                                     `province_id` int DEFAULT NULL COMMENT '省',
                                     `city_id` int DEFAULT NULL COMMENT '市',
                                     `district_id` int DEFAULT NULL COMMENT '区',
                                     `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
                                     `bank_account` varchar(255) DEFAULT NULL COMMENT '银行账号',
                                     `bank` varchar(255) DEFAULT NULL COMMENT '开户银行',
                                     `cardholder_name` varchar(255) DEFAULT NULL COMMENT '持卡人姓名',
                                     `tax_id` varchar(20) DEFAULT NULL COMMENT '税务登记号',
                                     `sort` int DEFAULT NULL COMMENT '排序',
                                     `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                     `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     `sid` int NOT NULL COMMENT '商户id',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品供应商';


-- lingshou.ls_goods_supplier_category definition

CREATE TABLE `ls_goods_supplier_category` (
                                              `id` int NOT NULL AUTO_INCREMENT,
                                              `name` varchar(255) NOT NULL COMMENT '供应商分类名称',
                                              `sort` int unsigned NOT NULL COMMENT '排序',
                                              `create_time` int NOT NULL COMMENT '创建时间',
                                              `update_time` int DEFAULT NULL COMMENT '更新时间',
                                              `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                              `sid` int NOT NULL COMMENT '商户id',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品供应商分类';


-- lingshou.ls_goods_unit definition

CREATE TABLE `ls_goods_unit` (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `name` varchar(255) NOT NULL COMMENT '商品单位名称',
                                 `sort` int DEFAULT NULL COMMENT '排序',
                                 `create_time` int DEFAULT NULL COMMENT '创建时间',
                                 `update_time` int DEFAULT NULL COMMENT '更新时间',
                                 `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品单位表';


-- lingshou.ls_goods_visit definition

CREATE TABLE `ls_goods_visit` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT,
                                  `goods_id` int NOT NULL COMMENT '商品id',
                                  `ip` varchar(255) NOT NULL COMMENT 'ip地址',
                                  `user_id` int DEFAULT NULL COMMENT '用户id',
                                  `visit` int NOT NULL COMMENT '浏览量',
                                  `create_time` int DEFAULT NULL COMMENT '创建时间',
                                  `update_time` int DEFAULT NULL COMMENT '更新时间',
                                  `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `create_time_idx` (`create_time`),
                                  KEY `goods_id_idx` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=365503 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品浏览记录表';


-- lingshou.ls_index_visit definition

CREATE TABLE `ls_index_visit` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT,
                                  `ip` varchar(255) NOT NULL COMMENT '访客ip地址',
                                  `terminal` tinyint(1) NOT NULL COMMENT '访问终端',
                                  `visit` int NOT NULL COMMENT '浏览量',
                                  `create_time` int DEFAULT NULL COMMENT '访问时间',
                                  `update_time` int DEFAULT NULL,
                                  `delete_time` int DEFAULT NULL,
                                  `sid` int NOT NULL COMMENT '商户id',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='首页浏览记录表';


-- lingshou.ls_integral_delivery definition

CREATE TABLE `ls_integral_delivery` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '发货单ID',
                                        `order_id` int unsigned NOT NULL COMMENT '订单ID',
                                        `order_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
                                        `user_id` int unsigned NOT NULL COMMENT '用户ID',
                                        `admin_id` int unsigned DEFAULT '0' COMMENT '管理员ID',
                                        `consignee` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收货人',
                                        `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '联系手机',
                                        `province` int unsigned DEFAULT NULL COMMENT '省ID',
                                        `city` int unsigned DEFAULT NULL COMMENT '市ID',
                                        `district` int unsigned DEFAULT NULL COMMENT '区ID',
                                        `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '地址',
                                        `express_status` tinyint unsigned DEFAULT '0' COMMENT '发货状态 0-未发货 1-已发货',
                                        `express_id` int DEFAULT NULL COMMENT '物流公司id',
                                        `express_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '快递名称',
                                        `invoice_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '物流单号',
                                        `send_type` tinyint(1) DEFAULT '0' COMMENT '配送方式 1-快递配送 2-无需快递',
                                        `sid` int NOT NULL COMMENT '商家id',
                                        `create_time` int NOT NULL COMMENT '创建时间',
                                        `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COMMENT='积分发货单';


-- lingshou.ls_integral_goods definition

CREATE TABLE `ls_integral_goods` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
                                     `code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品编码',
                                     `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品主图',
                                     `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '类型：1商品 2-红包',
                                     `market_price` decimal(10,2) unsigned DEFAULT NULL COMMENT '市场价(最低价格对应商品吊牌价/划线价)',
                                     `stock` int unsigned NOT NULL DEFAULT '0' COMMENT '总库存',
                                     `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '商品状态: 0-下架,1-上架中',
                                     `exchange_way` tinyint(1) NOT NULL DEFAULT '1' COMMENT '兑换方式 1-积分 2-积分加余额',
                                     `need_integral` int NOT NULL DEFAULT '0' COMMENT '积分',
                                     `need_money` decimal(10,2) DEFAULT '0.00' COMMENT '金额',
                                     `delivery_way` tinyint(1) NOT NULL DEFAULT '1' COMMENT '配送方式  0-无需物流 1-快递',
                                     `balance` decimal(10,2) DEFAULT '0.00' COMMENT '红包面值-待定',
                                     `express_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '运费类型：1-包邮,2-统一运费',
                                     `express_money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '统一运费金额',
                                     `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '商品详细描述',
                                     `sales` int unsigned NOT NULL DEFAULT '0' COMMENT '销量',
                                     `sort` smallint unsigned DEFAULT '0' COMMENT '排序',
                                     `sid` int NOT NULL COMMENT '商家id',
                                     `create_time` int unsigned NOT NULL COMMENT '商品创建时间',
                                     `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '商品更新时间',
                                     `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COMMENT='积分商品表';


-- lingshou.ls_integral_order definition

CREATE TABLE `ls_integral_order` (
                                     `id` int NOT NULL AUTO_INCREMENT COMMENT '商家id',
                                     `sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单编号',
                                     `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
                                     `exchange_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '兑换类型 1-商品 2-红包',
                                     `exchange_way` tinyint(1) NOT NULL DEFAULT '1' COMMENT '兑换方式 1-积分 2-积分加余额',
                                     `order_source` tinyint(1) NOT NULL DEFAULT '1' COMMENT '订单来源 1-小程序 2-h5 3-ios 4-安卓',
                                     `order_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '订单状态 0-待付款 1-待发货 2-待收货 3-已发货 4-已取消',
                                     `pay_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '支付状态 0-待支付 1-已支付 2-已退款 3-拒绝退款',
                                     `pay_way` tinyint DEFAULT '0' COMMENT '1-微信支付  2-支付宝支付 3-余额支付  4-线下支付',
                                     `pay_time` int unsigned DEFAULT '0' COMMENT '支付时间',
                                     `delivery_way` tinyint(1) NOT NULL DEFAULT '0' COMMENT '配送方式 0-无需快递 1-快递配送',
                                     `express_status` tinyint unsigned DEFAULT '0' COMMENT '发货状态',
                                     `express_price` decimal(10,2) DEFAULT NULL COMMENT '运费',
                                     `express_time` int DEFAULT '0' COMMENT '最后新发货时间',
                                     `order_amount` decimal(10,2) NOT NULL COMMENT '应支付金额（商品金额+运费）',
                                     `order_integral` int NOT NULL COMMENT '应支付积分（商品应付积分）',
                                     `total_num` int NOT NULL DEFAULT '0' COMMENT '订单商品数量',
                                     `goods_price` decimal(10,2) NOT NULL COMMENT '商品应付金额',
                                     `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '地址',
                                     `transaction_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台交易流水号',
                                     `goods_snap` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品信息快照',
                                     `user_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户备注',
                                     `cancel_time` int DEFAULT NULL COMMENT '订单取消时间',
                                     `refund_status` tinyint(1) DEFAULT '0' COMMENT '退款状态 0-未退款 1-已退款',
                                     `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
                                     `refund_integral` int DEFAULT '0' COMMENT '退回积分',
                                     `confirm_time` int DEFAULT NULL COMMENT '确认收货时间',
                                     `sid` int NOT NULL COMMENT '商家id',
                                     `create_time` int unsigned NOT NULL COMMENT '下单时间',
                                     `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COMMENT='积分订单';


-- lingshou.ls_integral_order_refund definition

CREATE TABLE `ls_integral_order_refund` (
                                            `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款单号，一个订单分多次退款则有多个退款单号',
                                            `order_id` int unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                            `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '下单用户id，冗余字段',
                                            `order_amount` decimal(10,2) unsigned NOT NULL COMMENT '订单总的应付款金额，冗余字段',
                                            `refund_amount` decimal(10,2) unsigned NOT NULL COMMENT '本次退款金额',
                                            `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台交易流水号',
                                            `refund_status` tinyint unsigned DEFAULT '0' COMMENT '退款状态，0退款中，1完成退款，2退款失败',
                                            `refund_way` tinyint unsigned DEFAULT '0' COMMENT '退款方式，0原路退',
                                            `refund_time` int unsigned DEFAULT '0' COMMENT '退款时间',
                                            `wechat_refund_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信返回退款id',
                                            `refund_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '第三方返回信息',
                                            `sid` int NOT NULL COMMENT '商家id',
                                            `create_time` int unsigned DEFAULT '0' COMMENT '创建时间',
                                            `update_time` int DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='积分订单退款记录';


-- lingshou.ls_kefu definition

CREATE TABLE `ls_kefu` (
                           `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                           `admin_id` int NOT NULL COMMENT '管理员_id',
                           `nickname` varchar(32) NOT NULL DEFAULT '' COMMENT '客服昵称',
                           `avatar` varchar(256) NOT NULL DEFAULT '' COMMENT '客服头像',
                           `disable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用,0-启用,1-禁用',
                           `sort` smallint unsigned DEFAULT '1' COMMENT '排序',
                           `sid` int NOT NULL COMMENT '商户id',
                           `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
                           `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
                           `delete_time` int DEFAULT NULL COMMENT '删除时间',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客服表';


-- lingshou.ls_kefu_lang definition

CREATE TABLE `ls_kefu_lang` (
                                `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                `title` varchar(100) DEFAULT '' COMMENT '标题',
                                `content` varchar(255) DEFAULT '' COMMENT '内容',
                                `sort` smallint unsigned DEFAULT '1' COMMENT '排序',
                                `sid` int NOT NULL COMMENT '商户id',
                                `create_time` int DEFAULT NULL COMMENT '创建时间',
                                `update_time` int DEFAULT NULL COMMENT '修改时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客服话术表';


-- lingshou.ls_kefu_session definition

CREATE TABLE `ls_kefu_session` (
                                   `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `kefu_id` int NOT NULL COMMENT '管理员id',
                                   `token` varchar(32) NOT NULL COMMENT '令牌',
                                   `terminal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户端类型：1-pc管理后台 2-mobile手机管理后台',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   `update_time` int DEFAULT NULL COMMENT '更新时间',
                                   `expire_time` int NOT NULL COMMENT '到期时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE KEY `kefu_id_terminal` (`kefu_id`,`terminal`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客服token表';


-- lingshou.ls_lucky_draw definition

CREATE TABLE `ls_lucky_draw` (
                                 `id` int unsigned NOT NULL AUTO_INCREMENT,
                                 `sn` varchar(64) NOT NULL COMMENT '活动编号',
                                 `name` varchar(120) NOT NULL DEFAULT '' COMMENT '活动名称',
                                 `start_time` int NOT NULL COMMENT '开始时间',
                                 `end_time` int NOT NULL COMMENT '结束时间',
                                 `remark` text NOT NULL COMMENT '活动说明',
                                 `need_integral` int unsigned NOT NULL DEFAULT '0' COMMENT '参与一次需要多少积分',
                                 `frequency_type` tinyint(1) NOT NULL COMMENT '频率类型 0-不限次数 1-每人每天不能超过的次数',
                                 `frequency` int NOT NULL COMMENT '频率也可称为次数',
                                 `rule` text COMMENT '抽奖规则',
                                 `show_winning_list` tinyint(1) NOT NULL COMMENT '是否显示中奖名单',
                                 `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态 0-未开始 1-进行中 2-已结束',
                                 `create_time` int DEFAULT NULL COMMENT '创建时间',
                                 `update_time` int DEFAULT NULL COMMENT '更新时间',
                                 `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='幸运转盘活动表';


-- lingshou.ls_lucky_draw_prize definition

CREATE TABLE `ls_lucky_draw_prize` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT,
                                       `activity_id` int unsigned NOT NULL COMMENT '抽奖活动id',
                                       `name` varchar(120) NOT NULL DEFAULT '' COMMENT '奖品名称',
                                       `image` varchar(255) NOT NULL DEFAULT '' COMMENT '奖品图片',
                                       `type` tinyint(1) NOT NULL COMMENT '奖品类型 1-未中奖 2-积分 3-优惠券 4-余额',
                                       `type_value` int NOT NULL COMMENT '奖品类型对应的值',
                                       `num` int unsigned NOT NULL DEFAULT '0' COMMENT '数量',
                                       `probability` int NOT NULL COMMENT '概率',
                                       `tips` varchar(255) NOT NULL COMMENT '中奖提示语',
                                       `location` tinyint NOT NULL COMMENT '位置',
                                       `create_time` int DEFAULT NULL,
                                       `update_time` int DEFAULT NULL,
                                       `delete_time` int DEFAULT NULL,
                                       `sid` int NOT NULL COMMENT '商户id',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='幸运转盘奖品表';


-- lingshou.ls_lucky_draw_record definition

CREATE TABLE `ls_lucky_draw_record` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT,
                                        `user_id` int NOT NULL COMMENT '用户id',
                                        `activity_id` int NOT NULL COMMENT '活动id',
                                        `prize_id` int NOT NULL COMMENT '奖品id',
                                        `prize_type` tinyint(1) NOT NULL COMMENT '奖品类型',
                                        `create_time` int DEFAULT NULL COMMENT '创建时间',
                                        `is_send` tinyint(1) NOT NULL COMMENT '是否已奖',
                                        `remark` varchar(255) DEFAULT '' COMMENT '备注',
                                        `sid` int NOT NULL COMMENT '商户id',
                                        `delete_time` int DEFAULT NULL,
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='幸运转盘记录表';


-- lingshou.ls_notice definition

CREATE TABLE `ls_notice` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
                             `user_id` int unsigned NOT NULL COMMENT '用户id',
                             `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
                             `content` text NOT NULL COMMENT '内容',
                             `scene_id` int unsigned DEFAULT '0' COMMENT '场景',
                             `read` tinyint(1) DEFAULT '0' COMMENT '已读状态;0-未读,1-已读',
                             `recipient` tinyint(1) DEFAULT '0' COMMENT '通知接收对象类型;1-会员;2-商家;3-平台;4-游客(未注册用户)',
                             `send_type` tinyint(1) DEFAULT '0' COMMENT '通知发送类型 1-系统通知 2-短信通知 3-微信模板 4-微信小程序',
                             `notice_type` tinyint(1) DEFAULT NULL COMMENT '通知类型 1-业务通知 2-验证码',
                             `extra` varchar(255) DEFAULT '' COMMENT '其他',
                             `create_time` int DEFAULT NULL COMMENT '创建时间',
                             `update_time` int DEFAULT NULL COMMENT '更新时间',
                             `delete_time` int DEFAULT NULL COMMENT '删除时间',
                             `sid` int NOT NULL COMMENT '商户id',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=440 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知记录表';


-- lingshou.ls_official_account_reply definition

CREATE TABLE `ls_official_account_reply` (
                                             `id` int unsigned NOT NULL AUTO_INCREMENT,
                                             `name` varchar(64) NOT NULL DEFAULT '' COMMENT '规则名称',
                                             `keyword` varchar(64) NOT NULL DEFAULT '' COMMENT '关键词',
                                             `reply_type` tinyint(1) NOT NULL COMMENT '回复类型 1-关注回复 2-关键字回复 3-默认回复',
                                             `matching_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '匹配方式：1-全匹配；2-模糊匹配',
                                             `content_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '内容类型：1-文本',
                                             `content` text NOT NULL COMMENT '回复内容',
                                             `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '启动状态：1-启动；0-关闭',
                                             `sort` int unsigned NOT NULL DEFAULT '50' COMMENT '排序',
                                             `create_time` int DEFAULT NULL COMMENT '创建时间',
                                             `update_time` int DEFAULT NULL COMMENT '更新时间',
                                             `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                             `sid` int NOT NULL COMMENT '商户id',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='微信公众号自动回复表';


-- lingshou.ls_operation_log definition

CREATE TABLE `ls_operation_log` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `admin_id` int NOT NULL COMMENT '管理员ID',
                                    `admin_name` varchar(16) NOT NULL DEFAULT '' COMMENT '管理员名称',
                                    `account` varchar(16) NOT NULL DEFAULT '' COMMENT '管理员账号',
                                    `action` varchar(64) DEFAULT '' COMMENT '操作名称',
                                    `type` varchar(8) NOT NULL COMMENT '请求方式',
                                    `url` varchar(255) NOT NULL COMMENT '访问链接',
                                    `params` text COMMENT '请求数据',
                                    `result` text COMMENT '请求结果',
                                    `ip` varchar(15) NOT NULL DEFAULT '' COMMENT 'ip地址',
                                    `create_time` int DEFAULT NULL COMMENT '创建时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    `delete_time` int DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=477922 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='后台操作日志表';


-- lingshou.ls_order definition
-- lingshou_dev.ls_order definition

CREATE TABLE `ls_order`
(
    `id`                   int                                                           NOT NULL AUTO_INCREMENT COMMENT '订单id',
    `sn`                   varchar(20)  NOT NULL DEFAULT '' COMMENT '订单编号',
    `mer_no`               varchar(30)           DEFAULT NULL COMMENT '支付商戶單號',
    `pickup_code`          varchar(20)           DEFAULT NULL COMMENT '提货码',
    `user_id`              int unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `trade_no`             varchar(40)           DEFAULT NULL COMMENT 'mpay 交易单号',
    `trans_id`             varchar(40)           DEFAULT NULL COMMENT 'mpay 交易单号',
    `order_type`           tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '订单类型：0-普通订单,1-拼团订单,2-秒杀订单,3-砍价订单,4-虚拟订单',
    `order_terminal`       tinyint unsigned                                              NOT NULL DEFAULT '1' COMMENT '订单来源;1-微信小程序;2-微信公众号;3-手机H5;4-PC;5-苹果app;6-安卓app;',
    `order_status`         tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '订单状态;0-待付款;1-待发货;2-待收货;3-已完成;4-已关闭',
    `pay_status`           tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '支付状态;0-待支付;1-已支付;',
    `pay_way`              tinyint                                                       NOT NULL DEFAULT '0' COMMENT '支付方式:1-余额支付;2-微信支付;3-支付宝支付;',
    `pay_time`             int unsigned                                                           DEFAULT '0' COMMENT '支付时间',
    `address`              text COMMENT '地址',
    `delivery_type`        tinyint(1)                                                             DEFAULT '1' COMMENT '配送方式：1-快递发货,2-上门自提,3-同城配送,4-虚拟发货',
    `goods_price`          decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '订单商品总价',
    `order_amount`         decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '应付款金额',
    `discount_amount`      decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '优惠券金额',
    `member_amount`        decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '会员价优惠金额',
    `pay_integral`         int                                                                    DEFAULT '0' COMMENT '消耗積分',
    `is_mcoin`             int                                                                    DEFAULT NULL COMMENT '是否mcoin福利',
    `integral_ratio`       int                                                                    DEFAULT '0' COMMENT '比例',
    `integral_amount`      decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '积分抵扣金额',
    `change_price`         decimal(10, 2)                                                         DEFAULT NULL COMMENT '商品改价',
    `total_amount`         decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '订单总价',
    `total_num`            int                                                                    DEFAULT '0' COMMENT '订单商品数量',
    `express_status`       tinyint unsigned                                                       DEFAULT '0' COMMENT '发货状态',
    `express_price`        decimal(10, 2)                                                         DEFAULT '0.00' COMMENT '运费',
    `express_time`         int unsigned                                                           DEFAULT '0' COMMENT '最后新发货时间',
    `transaction_id`       varchar(40)         DEFAULT NULL COMMENT '第三方平台交易流水号',
    `user_remark`          varchar(255)        DEFAULT '' COMMENT '用户备注',
    `confirm_take_time`    int                                                                    DEFAULT NULL COMMENT '确认收货时间',
    `after_sale_deadline`  int                                                                    DEFAULT NULL COMMENT '售后截止时间',
    `cancel_time`          int                                                                    DEFAULT NULL COMMENT '订单取消时间',
    `order_remarks`        varchar(500)  NOT NULL DEFAULT '' COMMENT '订单备注',
    `coupon_list_id`       int                                                                    DEFAULT '0' COMMENT '用户领取的优惠券id-coupon_list表',
    `delivery_id`          int                                                                    DEFAULT '0' COMMENT '发货单ID',
    `seckill_id`           int unsigned                                                           DEFAULT '0' COMMENT '秒杀活动ID',
    `team_found_id`        int                                                                    DEFAULT NULL COMMENT '拼团活动的开团ID',
    `is_team_success`      tinyint unsigned                                                       DEFAULT '0' COMMENT '拼的团是否成功[0=未成功, 1=已成功, 2=已失败]',
    `verification_status`  tinyint(1)                                                             DEFAULT '0' COMMENT '核销状态:0-待核销;1-已核销;',
    `selffetch_shop_id`    int                                                                    DEFAULT '0' COMMENT '自提门店ID',
    `delivery_content`     text  COMMENT '发货内容',
    `create_time`          int unsigned                                                           DEFAULT NULL COMMENT '下单时间',
    `update_time`          int unsigned                                                           DEFAULT NULL COMMENT '更新时间',
    `delete_time`          int unsigned                                                           DEFAULT NULL COMMENT '软删除',
    `sid`                  int                                                           NOT NULL COMMENT '商户id',
    `award_integral_event` tinyint(1)                                                             DEFAULT '0' COMMENT '消费赠送积分场景:0-不赠送积分；1-订单付款时赠送积分;2-订单发货时赠送积分;3-订单完成时赠送积分;4-订单完成并且超过售后期赠送积分;',
    `award_integral`       int                                                                    DEFAULT '0' COMMENT '消费赠送积分数量',
    `is_award_integral`    tinyint(1)                                                             DEFAULT '0' COMMENT '积分是否已结算:0-否;1-是;',
    `order_contact`        varchar(128)                                                           DEFAULT '' COMMENT '订单联系人',
    `order_mobile`         varchar(128)                                                           DEFAULT '' COMMENT '订单联系号码',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `order_sn` (`sn`) USING BTREE,
    KEY `user_id` (`user_id`) USING BTREE,
    KEY `mer_no_idx` (`mer_no`) USING BTREE,
    KEY `idx_update_time` (`update_time`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_seckill_id` (`seckill_id`),
    KEY `idx_create_time_sid_pay_status` (`sid`, `create_time`, `pay_status`),
    KEY `idx_expresstime_orderstatus_paystatus` (`express_time`, `order_status`, `pay_status`),
    KEY `idx_order_contact` (`order_contact`),
    KEY `idx_order_mobile` (`order_mobile`)
) ENGINE = InnoDB COMMENT ='订单表';


-- lingshou.ls_order_goods definition

CREATE TABLE `ls_order_goods` (
                                  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `order_id` int NOT NULL DEFAULT '0' COMMENT '订单id',
                                  `goods_id` int NOT NULL DEFAULT '0' COMMENT '商品id',
                                  `uuid` varchar(100) DEFAULT NULL COMMENT '商品唯一標識',
                                  `item_id` int NOT NULL DEFAULT '0' COMMENT '规格id',
                                  `goods_name` varchar(64) DEFAULT '' COMMENT '商品名称',
                                  `goods_num` int DEFAULT '0' COMMENT '商品数量',
                                  `goods_price` decimal(10,2) DEFAULT '0.00' COMMENT '商品价格',
                                  `member_price` decimal(10,2) DEFAULT '0.00' COMMENT '会员价格',
                                  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '商品原价',
                                  `total_price` decimal(10,2) DEFAULT '0.00' COMMENT '商品总价',
                                  `goods_unit_price` decimal(10,2) DEFAULT '0.00' COMMENT '单价',
                                  `goods_unit_integral` int DEFAULT '0' COMMENT '积分单价',
                                  `pay_integral` int DEFAULT NULL COMMENT '商品消耗積分',
                                  `pay_integral_ratio` int DEFAULT NULL COMMENT '消耗積分的兑换比例',
                                  `pay_goods_type` int DEFAULT '2' COMMENT '福利类型',
                                  `is_mcoin` int DEFAULT '3' COMMENT '是否mcoin福利',
                                  `fee_rate` int DEFAULT '0' COMMENT '福利佣金',
                                  `total_pay_price` decimal(10,2) DEFAULT '0.00' COMMENT '实际支付商品金额',
                                  `subsidy_amount` decimal(10,2) DEFAULT '0.00' COMMENT '平台優惠金',
                                  `discount_price` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券金额',
                                  `integral_price` decimal(10,2) DEFAULT '0.00' COMMENT '积分抵扣的金额',
                                  `change_price` decimal(10,2) DEFAULT '0.00' COMMENT '商品改价',
                                  `spec_value_ids` varchar(40) DEFAULT NULL COMMENT '商品规格id',
                                  `is_comment` tinyint(1) DEFAULT '0' COMMENT '是否已评论；0-否；1-是',
                                  `goods_snap` text COMMENT '商品信息',
                                  `create_time` int unsigned DEFAULT '0' COMMENT '创建时间',
                                  `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                  `delete_time` int unsigned DEFAULT NULL COMMENT '软删除',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `ls_order_goods_order_id_IDX` (`order_id`) USING BTREE,
                                  KEY `ls_order_goods_create_time_IDX` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1490629 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单商品表';


-- lingshou.ls_order_log definition

CREATE TABLE `ls_order_log` (
                                `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型 1-系统 2-卖家 3-买家',
                                `channel` smallint unsigned NOT NULL DEFAULT '0' COMMENT '渠道编号。变动方式。',
                                `order_id` int NOT NULL DEFAULT '0' COMMENT '订单id',
                                `operator_id` int NOT NULL DEFAULT '0' COMMENT '操作人id',
                                `content` varchar(255) DEFAULT '' COMMENT '日志内容',
                                `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                `sid` int NOT NULL COMMENT '商户id',
                                `delete_time` int DEFAULT NULL,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2950081 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单日志表';


-- lingshou.ls_pc_decorate_theme definition

CREATE TABLE `ls_pc_decorate_theme` (
                                        `id` int NOT NULL AUTO_INCREMENT,
                                        `name` varchar(32) NOT NULL COMMENT '名称',
                                        `create_time` int NOT NULL COMMENT '创建时间',
                                        `update_time` int NOT NULL COMMENT '更新时间',
                                        `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                        `sid` int NOT NULL COMMENT '商户id',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='pc装饰主题';


-- lingshou.ls_pc_decorate_theme_page definition

CREATE TABLE `ls_pc_decorate_theme_page` (
                                             `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `theme_id` int NOT NULL COMMENT '主题id',
                                             `name` varchar(32) NOT NULL COMMENT '页面名称',
                                             `type` tinyint(1) NOT NULL COMMENT '类型：1-首页；2-登录页；3-限时秒杀；4-领券中心；5-商城资讯；6-帮助中心',
                                             `content` text NOT NULL COMMENT '内容',
                                             `common` text COMMENT '公告配置',
                                             `create_time` int NOT NULL COMMENT '创建时间',
                                             `update_time` int NOT NULL COMMENT '更新时间',
                                             `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                             `sid` int NOT NULL COMMENT '商户id',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='pc装饰页面';


-- lingshou.ls_platform_admin definition

CREATE TABLE `ls_platform_admin` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT,
                                     `root` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否超级管理员 0-否 1-是',
                                     `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
                                     `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '用户头像',
                                     `account` varchar(32) NOT NULL DEFAULT '' COMMENT '账号',
                                     `platform_id` varchar(50) DEFAULT '' COMMENT '平台ID',
                                     `password` varchar(32) NOT NULL COMMENT '密码',
                                     `create_time` int NOT NULL COMMENT '创建时间',
                                     `update_time` int DEFAULT NULL COMMENT '修改时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     `login_time` int DEFAULT NULL COMMENT '最后登录时间',
                                     `login_ip` varchar(15) DEFAULT '' COMMENT '最后登录ip',
                                     `multipoint_login` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否允许多处登录 0-不允许 1-允许',
                                     `disable` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否禁用 0-未禁用 1-已禁用',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='平台管理员表';


-- lingshou.ls_platform_admin_session definition

CREATE TABLE `ls_platform_admin_session` (
                                             `id` int unsigned NOT NULL AUTO_INCREMENT,
                                             `platform_admin_id` int unsigned NOT NULL COMMENT '用户id',
                                             `terminal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户端类型：1-PC平台后台',
                                             `token` varchar(32) NOT NULL COMMENT '令牌',
                                             `update_time` int DEFAULT NULL COMMENT '更新时间',
                                             `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                             `expire_time` int NOT NULL COMMENT '到期时间',
                                             PRIMARY KEY (`id`) USING BTREE,
                                             UNIQUE KEY `admin_id_client` (`platform_admin_id`,`terminal`) USING BTREE COMMENT '一个用户在一个终端只有一个token',
                                             UNIQUE KEY `token` (`token`) USING BTREE COMMENT 'token是唯一的'
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会话表';


-- lingshou.ls_platform_shop definition

CREATE TABLE `ls_platform_shop` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT,
                                    `sn` varchar(32) NOT NULL COMMENT '商城编号',
                                    `name` varchar(128) NOT NULL COMMENT '商城名称',
                                    `contact` varchar(64) DEFAULT NULL COMMENT '联系人',
                                    `contact_mobile` varchar(32) DEFAULT NULL COMMENT '联系人手机号',
                                    `status` tinyint(1) NOT NULL COMMENT '状态 0-停止服务 1-开启服务',
                                    `domain_alias` varchar(255) NOT NULL COMMENT '域名别名',
                                    `java_domain_alias` varchar(255) DEFAULT NULL COMMENT 'JAVA - 域名别名',
                                    `set_meal_id` int NOT NULL COMMENT '套餐id',
                                    `mch_id` varchar(50) DEFAULT '' COMMENT '商户ID',
                                    `expires_time` int unsigned NOT NULL COMMENT '到期时间',
                                    `remark` text COMMENT '备注',
                                    `create_time` int DEFAULT NULL,
                                    `update_time` int DEFAULT NULL,
                                    `delete_time` int DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商家表';


-- lingshou.ls_printer definition

CREATE TABLE `ls_printer` (
                              `id` int unsigned NOT NULL AUTO_INCREMENT,
                              `type` tinyint unsigned NOT NULL COMMENT '类型',
                              `name` varchar(120) NOT NULL COMMENT '打印机名称',
                              `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用ID',
                              `client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用密钥',
                              `template_id` int unsigned NOT NULL COMMENT '模板id',
                              `machine_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '终端号',
                              `private_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '秘钥',
                              `print_number` tinyint(1) NOT NULL DEFAULT '1' COMMENT '打印联数：默认1张',
                              `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '打印机状态：1开启；0-关闭',
                              `auto_print` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否自动打印：1-是；0-否',
                              `create_time` int DEFAULT NULL COMMENT '创建时间',
                              `update_time` int DEFAULT NULL COMMENT '更新时间',
                              `delete_time` int DEFAULT NULL COMMENT '删除时间',
                              `sid` int NOT NULL COMMENT '商户id',
                              `del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除状态：1-是；0-否',
                              `act_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '小程序打印播報設置，0不打印不播報，1只打印，2只播報，3打印並播報',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小票打印配置表';


-- lingshou.ls_printer_template definition

CREATE TABLE `ls_printer_template` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT,
                                       `template_name` varchar(120) NOT NULL COMMENT '模板名称',
                                       `ticket_name` varchar(120) NOT NULL COMMENT '小票名称',
                                       `show_shop_name` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否显示商城名称 0-隐藏 1-显示',
                                       `selffetch_shop` text COMMENT '自提门店配置',
                                       `verification_info` text COMMENT '提货人配置',
                                       `show_buyer_message` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否显示买家留言 0-隐藏 1-显示',
                                       `consignee_info` text COMMENT '收货人信息',
                                       `show_qrcode` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否显示二维码 0-隐藏 1-显示',
                                       `qrcode_name` varchar(120) DEFAULT '' COMMENT '二级码名称',
                                       `qrcode_content` text COMMENT '二维码内容',
                                       `bottom` varchar(255) NOT NULL DEFAULT '' COMMENT '底部信息',
                                       `create_time` int DEFAULT NULL,
                                       `update_time` int DEFAULT NULL,
                                       `delete_time` int DEFAULT NULL,
                                       `sid` int NOT NULL COMMENT '商户id',
                                       `template_type` smallint DEFAULT NULL COMMENT '模版类型：1-下单模版、2-取消模版',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小票打印配置表';


-- lingshou.ls_recharge_order definition

CREATE TABLE `ls_recharge_order` (
                                     `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                     `sn` varchar(64) NOT NULL COMMENT '订单编号',
                                     `user_id` int NOT NULL COMMENT '用户id',
                                     `transaction_id` varchar(128) DEFAULT NULL COMMENT '第三方平台交易流水号',
                                     `terminal` tinyint(1) DEFAULT '1' COMMENT '终端',
                                     `pay_way` tinyint NOT NULL DEFAULT '1' COMMENT '支付方式',
                                     `pay_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付状态：0-待支付；1-已支付',
                                     `pay_time` int DEFAULT NULL COMMENT '支付时间',
                                     `template_id` int DEFAULT NULL COMMENT '模板id',
                                     `order_amount` decimal(10,2) NOT NULL COMMENT '充值金额',
                                     `award` text COMMENT '充值奖励',
                                     `create_time` int DEFAULT NULL COMMENT '创建时间',
                                     `update_time` int DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     `sid` int NOT NULL COMMENT '商户id',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='充值订单表';


-- lingshou.ls_recharge_template definition

CREATE TABLE `ls_recharge_template` (
                                        `id` int NOT NULL AUTO_INCREMENT,
                                        `money` decimal(10,2) NOT NULL COMMENT '充值金额',
                                        `award` text COMMENT '充值奖励',
                                        `sort` int DEFAULT NULL COMMENT '排序',
                                        `is_recommend` tinyint unsigned DEFAULT '0' COMMENT '是否推荐：1-是；0-否',
                                        `create_time` int DEFAULT NULL COMMENT '创建时间',
                                        `update_time` int DEFAULT NULL COMMENT '更新时间',
                                        `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                        `sid` int NOT NULL COMMENT '商户id',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='充值模板';


-- lingshou.ls_refund definition

CREATE TABLE `ls_refund` (
                             `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                             `sn` varchar(32) NOT NULL DEFAULT '' COMMENT '退款单号，一个订单分多次退款则有多个退款单号',
                             `order_id` int unsigned DEFAULT '0' COMMENT '订单id',
                             `after_sale_id` int unsigned DEFAULT '0' COMMENT '售后订单id',
                             `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '下单用户id，冗余字段',
                             `order_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '订单总的应付款金额，冗余字段',
                             `refund_integral` int DEFAULT '0' COMMENT '退款積分',
                             `refund_integral_amount` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '退款積分金額',
                             `refund_amount` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '本次退款金额',
                             `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方平台交易流水号',
                             `refund_status` tinyint unsigned DEFAULT '0' COMMENT '退款申请状态:0-未申请;1-申请成功;2申请失败;',
                             `refund_time` int unsigned DEFAULT NULL COMMENT '退款时间',
                             `wechat_refund_id` varchar(30) DEFAULT NULL COMMENT '微信返回退款id',
                             `refund_msg` text COMMENT '微信返回信息',
                             `create_time` int unsigned DEFAULT '0' COMMENT '创建时间',
                             `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                             `sid` int NOT NULL COMMENT '商户id',
                             `delete_time` int DEFAULT NULL,
                             PRIMARY KEY (`id`) USING BTREE,
                             KEY `idx_refund_time` (`refund_time`)
) ENGINE=InnoDB AUTO_INCREMENT=637 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='退款记录表';


-- lingshou.ls_role definition

CREATE TABLE `ls_role` (
                           `id` int unsigned NOT NULL AUTO_INCREMENT,
                           `name` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                           `desc` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '描述',
                           `create_time` int DEFAULT NULL COMMENT '创建时间',
                           `update_time` int DEFAULT NULL COMMENT '更新时间',
                           `delete_time` int DEFAULT NULL COMMENT '删除时间',
                           `sid` int NOT NULL COMMENT '商户id',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色表';


-- lingshou.ls_role_auth_index definition

CREATE TABLE `ls_role_auth_index` (
                                      `role_id` int NOT NULL COMMENT '菜单权限ID',
                                      `auth_key` varchar(64) NOT NULL COMMENT '权限key',
                                      `sid` int NOT NULL COMMENT '商户id',
                                      `delete_time` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='菜单权限与角色关系表';


-- lingshou.ls_search_record definition

CREATE TABLE `ls_search_record` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `user_id` int NOT NULL DEFAULT '0' COMMENT '用户id',
                                    `keyword` varchar(64) NOT NULL DEFAULT '' COMMENT '关键字',
                                    `count` int DEFAULT '1' COMMENT '次数',
                                    `create_time` int NOT NULL COMMENT '创建时间',
                                    `update_time` int DEFAULT '0' COMMENT '更新时间',
                                    `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='热门搜索记录表';


-- lingshou.ls_seckill_activity definition

CREATE TABLE `ls_seckill_activity` (
                                       `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `sn` varchar(20) NOT NULL COMMENT '活动编号',
                                       `name` varchar(100) NOT NULL DEFAULT '' COMMENT '秒杀活动名称',
                                       `min_buy` int unsigned NOT NULL DEFAULT '0' COMMENT '单笔最少购买的商品件数',
                                       `max_buy` int unsigned NOT NULL DEFAULT '0' COMMENT '单笔最多购买的商品件数',
                                       `explain` varchar(255) NOT NULL DEFAULT '' COMMENT '活动说明',
                                       `start_time` int unsigned NOT NULL DEFAULT '0' COMMENT '活动开始时间',
                                       `end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
                                       `is_coupon` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否允许使用优惠券[0=否, 1=是]',
                                       `is_distribution` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否参与分销[0=否, 1=是]',
                                       `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '活动状态[1=未开始，2=进行中, 3=已结束]',
                                       `create_time` int DEFAULT NULL COMMENT '创建时间',
                                       `update_time` int DEFAULT NULL COMMENT '更新时间',
                                       `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       `session_id` int DEFAULT NULL COMMENT '商城场次ID',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀活动表';


-- lingshou.ls_seckill_goods definition

CREATE TABLE `ls_seckill_goods` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `seckill_id` int NOT NULL COMMENT '秒杀活动ID',
                                    `goods_id` int NOT NULL COMMENT '商品ID',
                                    `min_seckill_price` decimal(8,2) unsigned NOT NULL COMMENT '最低秒杀价格',
                                    `max_seckill_price` decimal(8,2) unsigned NOT NULL COMMENT '最高秒杀价格',
                                    `browse_volume` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
                                    `goods_snap` text COMMENT '商品快照',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    `delete_time` int DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE,
                                    KEY `idx_seckill_id` (`seckill_id`)
) ENGINE=InnoDB AUTO_INCREMENT=319 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀活动商品表';


-- lingshou.ls_seckill_goods_item definition

CREATE TABLE `ls_seckill_goods_item` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `seckill_gid` int unsigned NOT NULL COMMENT '秒杀商品表ID',
                                         `seckill_id` int unsigned NOT NULL COMMENT '秒杀活动ID',
                                         `goods_id` int unsigned NOT NULL COMMENT '商品ID',
                                         `item_id` int unsigned NOT NULL COMMENT '商品规格ID',
                                         `spec_value_str` varchar(100) NOT NULL DEFAULT '' COMMENT '规格名称',
                                         `sell_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '销售价格',
                                         `seckill_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '秒杀价格',
                                         `sales_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '销售金额',
                                         `sales_volume` int unsigned NOT NULL DEFAULT '0' COMMENT '销量',
                                         `closing_order` int unsigned NOT NULL DEFAULT '0' COMMENT '成交订单数',
                                         `item_snap` text COMMENT '商品规格快照',
                                         `sid` int NOT NULL COMMENT '商户id',
                                         `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         KEY `idx_seckill_id` (`seckill_id`)
) ENGINE=InnoDB AUTO_INCREMENT=364 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀活动商品规格表';


-- lingshou.ls_selffetch_shop definition

CREATE TABLE `ls_selffetch_shop` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT,
                                     `name` varchar(255) NOT NULL COMMENT '门店名称',
                                     `image` varchar(255) NOT NULL COMMENT '门店LOGO',
                                     `contact` varchar(64) NOT NULL COMMENT '联系人',
                                     `mobile` varchar(20) NOT NULL COMMENT '联系电话',
                                     `province` int NOT NULL COMMENT '省',
                                     `city` int DEFAULT NULL COMMENT '市',
                                     `district` int DEFAULT NULL COMMENT '区',
                                     `address` varchar(64) DEFAULT NULL COMMENT '详细地址',
                                     `longitude` varchar(16) DEFAULT '0' COMMENT '经度',
                                     `latitude` varchar(16) DEFAULT '0' COMMENT '纬度',
                                     `business_start_time` varchar(10) NOT NULL COMMENT '营业开始时间',
                                     `business_end_time` varchar(10) NOT NULL COMMENT '营业结束时间',
                                     `weekdays` varchar(32) NOT NULL COMMENT '营业周天,逗号隔开如 1,2,3,4,5,6,7',
                                     `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '门店状态:1-启用;0-停用;',
                                     `remark` varchar(255) DEFAULT NULL COMMENT '门店简介',
                                     `create_time` int NOT NULL COMMENT '创建时间',
                                     `update_time` int DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     `stock` int DEFAULT NULL COMMENT '门店库存（null代表不限制库存）',
                                     `sid` int NOT NULL COMMENT '商户id',
                                       `pickup_days` int NOT NULL DEFAULT '0' COMMENT '预期?日提货',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='自提门店表';


-- lingshou.ls_selffetch_verifier definition

CREATE TABLE `ls_selffetch_verifier` (
                                         `id` int unsigned NOT NULL AUTO_INCREMENT,
                                         `selffetch_shop_id` int NOT NULL COMMENT '自取门店ID',
                                         `user_id` int NOT NULL COMMENT '用户ID',
                                         `sn` varchar(8) NOT NULL COMMENT '核销员编号',
                                         `name` varchar(255) NOT NULL COMMENT '核销员名称',
                                         `mobile` varchar(20) NOT NULL COMMENT '联系电话',
                                         `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '核销员状态:1-启用;0-停用;',
                                         `create_time` int NOT NULL COMMENT '创建时间',
                                         `update_time` int DEFAULT NULL COMMENT '更新时间',
                                         `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                         `sid` int NOT NULL COMMENT '商户id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='核销员表';


-- lingshou.ls_set_meal definition

CREATE TABLE `ls_set_meal` (
                               `id` int NOT NULL AUTO_INCREMENT,
                               `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '套餐名称',
                               `explain` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '套餐说明',
                               `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '套餐状态:1-开启；0-关闭',
                               `sort` int DEFAULT '50' COMMENT '排序',
                               `create_time` int DEFAULT NULL COMMENT '创建时间',
                               `update_time` int DEFAULT NULL COMMENT '更新时间',
                               `delete_time` int DEFAULT NULL COMMENT '删除时间',
                               `func` text COMMENT '关联的营销应用',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='套餐表';


-- lingshou.ls_set_meal_log definition

CREATE TABLE `ls_set_meal_log` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `sid` int NOT NULL COMMENT '商户id',
                                   `type` tinyint(1) NOT NULL DEFAULT '2' COMMENT '类型 1-系统 2-平台 3-商户',
                                   `operator_id` int NOT NULL DEFAULT '0' COMMENT '操作人id',
                                   `set_meal_id` int NOT NULL COMMENT '现套餐id',
                                   `set_meal_order_id` int DEFAULT NULL COMMENT '续费订单id',
                                   `origin_set_meal_name` varchar(255) NOT NULL COMMENT '原套餐名称',
                                   `origin_set_meal_id` int NOT NULL COMMENT '原套餐id',
                                   `set_meal_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '套餐名称',
                                   `content` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '日志',
                                   `channel` smallint NOT NULL COMMENT '渠道编号',
                                   `origin_expires_time` int unsigned NOT NULL COMMENT '原到期时间',
                                   `expires_time` int unsigned NOT NULL COMMENT '到期时间',
                                   `create_time` int NOT NULL COMMENT '操作时间',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='套餐使用日志';


-- lingshou.ls_set_meal_order definition

CREATE TABLE `ls_set_meal_order` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT,
                                     `sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
                                     `set_meal_id` int DEFAULT NULL COMMENT '套餐id',
                                     `set_meal_price_snapshot` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '子套餐快照',
                                     `pay_way` tinyint(1) NOT NULL COMMENT '支付方式',
                                     `order_status` tinyint(1) DEFAULT '0' COMMENT '支付状态 0 未支付 1 已支付',
                                     `pay_status` tinyint(1) DEFAULT '0' COMMENT '订单状态 0 未付款 1 已完成 2 已关闭',
                                     `pay_time` int DEFAULT NULL,
                                     `prepay_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
                                     `pay_postback_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
                                     `transaction_id` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
                                     `voucher` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '支付凭证',
                                     `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '备注',
                                     `operator_id` int DEFAULT NULL,
                                     `sid` int NOT NULL COMMENT '商家id',
                                     `create_time` int DEFAULT NULL,
                                     `update_time` int DEFAULT NULL,
                                     `delete_time` int DEFAULT NULL,
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


-- lingshou.ls_set_meal_price definition

CREATE TABLE `ls_set_meal_price` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `set_meal_id` int NOT NULL COMMENT '套餐id',
                                     `time` int NOT NULL COMMENT '时长',
                                     `time_type` tinyint(1) NOT NULL COMMENT '时长类型:1-月；2-年',
                                     `price` decimal(10,2) NOT NULL COMMENT '价格',
                                     `create_time` int DEFAULT NULL COMMENT '创建时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='套餐价格表';


-- lingshou.ls_shop_notice definition

CREATE TABLE `ls_shop_notice` (
                                  `id` int unsigned NOT NULL AUTO_INCREMENT,
                                  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '公告标题',
                                  `synopsis` varchar(255) DEFAULT '' COMMENT '公告简介',
                                  `image` varchar(128) DEFAULT '' COMMENT '公告封面图',
                                  `content` text NOT NULL COMMENT '公告内容',
                                  `sort` int unsigned NOT NULL DEFAULT '50' COMMENT '公告排序',
                                  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '公告状态:0-隐藏;1-显示',
                                  `views` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
                                  `likes` int unsigned NOT NULL DEFAULT '0' COMMENT '点赞量',
                                  `create_time` int DEFAULT NULL COMMENT '创建时间',
                                  `update_time` int DEFAULT NULL COMMENT '更新时间',
                                  `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                  `publish_time` int DEFAULT NULL COMMENT '发布时间',
                                  `sid` int NOT NULL COMMENT '商户id',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商城公告表';


-- lingshou.ls_sign_daily definition

CREATE TABLE `ls_sign_daily` (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `type` tinyint(1) NOT NULL DEFAULT '2' COMMENT '类型 1-每日签至 2-连续签到',
                                 `integral` int NOT NULL DEFAULT '0' COMMENT '赠送积分',
                                 `integral_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否赠送积分 0-否 1-是',
                                 `growth` int DEFAULT '0' COMMENT '成长值',
                                 `growth_status` tinyint(1) DEFAULT '0' COMMENT '是否赠送成长值 0-否 1-是',
                                 `days` int NOT NULL DEFAULT '2' COMMENT '连续签到天数',
                                 `create_time` int DEFAULT NULL COMMENT '创建时间',
                                 `update_time` int DEFAULT NULL COMMENT '更新时间',
                                 `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签到规则表';


-- lingshou.ls_sign_log definition

CREATE TABLE `ls_sign_log` (
                               `id` int NOT NULL AUTO_INCREMENT,
                               `days` int DEFAULT '0' COMMENT '连续签到天数',
                               `integral` int DEFAULT '0' COMMENT '每日签到奖励积分',
                               `continuous_integral` int DEFAULT '0' COMMENT '连续奖励积分',
                               `user_id` int DEFAULT NULL COMMENT '用户id',
                               `growth` int DEFAULT '0' COMMENT '每日签到奖励成长值',
                               `continuous_growth` int DEFAULT '0' COMMENT '连续签到奖励成长值',
                               `create_time` int DEFAULT NULL COMMENT '创建时间',
                               `update_time` int DEFAULT NULL COMMENT '更新时间',
                               `delete_time` int DEFAULT NULL COMMENT '删除时间',
                               `sid` int NOT NULL COMMENT '商户id',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签到记录表';


-- lingshou.ls_sms_log definition

CREATE TABLE `ls_sms_log` (
                              `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                              `scene_id` int NOT NULL COMMENT '场景id',
                              `mobile` varchar(11) NOT NULL COMMENT '手机号码',
                              `content` varchar(255) NOT NULL COMMENT '发送内容',
                              `code` varchar(32) DEFAULT NULL COMMENT '发送关键字（注册、找回密码）',
                              `is_verify` tinyint(1) DEFAULT '0' COMMENT '是否已验证；0-否；1-是',
                              `check_num` int DEFAULT '0' COMMENT '验证次数',
                              `send_status` tinyint(1) NOT NULL COMMENT '发送状态：0-发送中；1-发送成功；2-发送失败',
                              `send_time` int NOT NULL COMMENT '发送时间',
                              `results` text COMMENT '短信结果',
                              `create_time` int DEFAULT NULL COMMENT '创建时间',
                              `update_time` int DEFAULT NULL COMMENT '更新时间',
                              `delete_time` int DEFAULT NULL COMMENT '删除时间',
                              `sid` int NOT NULL COMMENT '商户id',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=388 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='短信发送记录表';


-- lingshou.ls_system_theme definition

CREATE TABLE `ls_system_theme` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `name` varchar(32) NOT NULL COMMENT '名称',
                                   `image` varchar(128) NOT NULL COMMENT '主题缩略图',
                                   `create_time` int NOT NULL COMMENT '创建时间',
                                   `update_time` int NOT NULL COMMENT '更新时间',
                                   `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统主题';


-- lingshou.ls_system_theme_config definition

CREATE TABLE `ls_system_theme_config` (
                                          `id` int NOT NULL AUTO_INCREMENT,
                                          `theme_id` int NOT NULL DEFAULT '1' COMMENT '主题id',
                                          `type` tinyint(1) NOT NULL COMMENT '配置类型:1-主题色；2-底部导航；3-开屏广告',
                                          `content` text NOT NULL COMMENT '配置内容',
                                          `create_time` int NOT NULL COMMENT '创建时间',
                                          `update_time` int NOT NULL COMMENT '更新时间',
                                          `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                          `sid` int NOT NULL COMMENT '商户id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统主题配置';


-- lingshou.ls_system_theme_page definition

CREATE TABLE `ls_system_theme_page` (
                                        `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `theme_id` int NOT NULL COMMENT '主题id',
                                        `name` varchar(32) NOT NULL COMMENT '页面名称',
                                        `is_home` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否首页：1-是；0-否',
                                        `type` tinyint(1) NOT NULL COMMENT '类型：1-首页；2-商品分类；3-会员中心',
                                        `content` text NOT NULL COMMENT '内容',
                                        `common` text NOT NULL COMMENT '公共配置',
                                        `create_time` int NOT NULL COMMENT '创建时间',
                                        `update_time` int NOT NULL COMMENT '更新时间',
                                        `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                        `sid` int NOT NULL COMMENT '商户id',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统主题页面';


-- lingshou.ls_team_activity definition

CREATE TABLE `ls_team_activity` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `sn` varchar(10) NOT NULL DEFAULT '' COMMENT '拼团活动编号',
                                    `name` varchar(64) NOT NULL DEFAULT '' COMMENT '活动名称',
                                    `start_time` int unsigned DEFAULT NULL COMMENT '活动开始时间',
                                    `end_time` int unsigned DEFAULT NULL COMMENT '活动结束时间',
                                    `people_num` int unsigned NOT NULL DEFAULT '0' COMMENT '成团所需人数',
                                    `explain` varchar(255) NOT NULL DEFAULT '' COMMENT '活动说明',
                                    `min_buy` int unsigned NOT NULL DEFAULT '0' COMMENT '单笔最少购买的商品件数',
                                    `max_buy` int unsigned NOT NULL DEFAULT '0' COMMENT '单笔最多购买的商品件数',
                                    `partake_number` int unsigned NOT NULL DEFAULT '0' COMMENT '累计参与拼团的人数(开团/或参团就记录)',
                                    `is_coupon` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否允许使用优惠券[0=否, 1=是]',
                                    `is_distribution` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否参与分销[0=否, 1=是]',
                                    `is_automatic` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否虚拟自动成团[0=否，1=是]',
                                    `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '活动状态[1=未开始，2=进行中, 3=已结束]',
                                    `effective_time` int NOT NULL COMMENT '成团有效期, 单位: 分钟',
                                    `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                    `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                    `delete_time` int unsigned DEFAULT NULL COMMENT '删除时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='拼团活动表';


-- lingshou.ls_team_found definition

CREATE TABLE `ls_team_found` (
                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `team_id` int unsigned NOT NULL COMMENT '拼团活动ID',
                                 `found_sn` varchar(64) NOT NULL DEFAULT '' COMMENT '开团编号',
                                 `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
                                 `order_id` int unsigned NOT NULL COMMENT '团长的订单ID',
                                 `people` int unsigned NOT NULL DEFAULT '0' COMMENT '成团所需人数',
                                 `join` int unsigned NOT NULL DEFAULT '0' COMMENT '当前已参团人数',
                                 `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态[0-待成团, 1-拼团成功, 2-拼团失败]',
                                 `goods_snap` text COMMENT '活动商品快照',
                                 `kaituan_time` int unsigned NOT NULL DEFAULT '0' COMMENT '开团时间',
                                 `invalid_time` int unsigned NOT NULL DEFAULT '0' COMMENT '团失效时间',
                                 `team_end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '拼团成功 / 失败的时间',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 `delete_time` int DEFAULT NULL,
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='开团表';


-- lingshou.ls_team_goods definition

CREATE TABLE `ls_team_goods` (
                                 `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `team_id` int unsigned NOT NULL COMMENT '团活动ID',
                                 `goods_id` int NOT NULL COMMENT '商品ID',
                                 `min_team_price` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '最低拼团价',
                                 `max_team_price` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '最高拼团价',
                                 `browse_volume` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
                                 `goods_snap` text COMMENT '商品快照',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 `delete_time` int DEFAULT NULL,
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='拼团活动商品表';


-- lingshou.ls_team_goods_item definition

CREATE TABLE `ls_team_goods_item` (
                                      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `team_gid` int unsigned NOT NULL COMMENT '拼团商品表ID, 外键关联',
                                      `team_id` int NOT NULL COMMENT '团活动ID',
                                      `goods_id` int unsigned NOT NULL COMMENT '商品ID',
                                      `item_id` int unsigned NOT NULL COMMENT '规格ID',
                                      `spec_value_str` varchar(100) NOT NULL DEFAULT '' COMMENT '规格名称',
                                      `sell_price` decimal(8,2) unsigned NOT NULL COMMENT '原来售价',
                                      `team_price` decimal(8,2) unsigned NOT NULL COMMENT '拼团价',
                                      `sales_amount` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '销售金额',
                                      `sales_volume` int unsigned NOT NULL DEFAULT '0' COMMENT '销售数量',
                                      `closing_order` int unsigned NOT NULL DEFAULT '0' COMMENT '成交订单数',
                                      `item_snap` text COMMENT '规格快照信息',
                                      `sid` int NOT NULL COMMENT '商户id',
                                      `delete_time` int DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='拼团活动商品规格表';


-- lingshou.ls_team_join definition

CREATE TABLE `ls_team_join` (
                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                `join_sn` varchar(64) NOT NULL DEFAULT '' COMMENT '参团编号',
                                `team_id` int unsigned NOT NULL COMMENT '拼团活动ID',
                                `found_id` int unsigned NOT NULL COMMENT '团ID',
                                `user_id` int unsigned NOT NULL COMMENT '用户ID',
                                `order_id` int unsigned NOT NULL COMMENT '订单ID',
                                `identity` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '身份[1=团长， 2=团员]',
                                `team_snap` text COMMENT '拼团快照',
                                `goods_snap` text COMMENT '拼团商品快照信息',
                                `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态[0-待成团; 1-成团成功; 2-成团失败]',
                                `invalid_time` int unsigned NOT NULL DEFAULT '0' COMMENT '团失效时间(冗余字段)',
                                `create_time` int DEFAULT NULL COMMENT '创建时间',
                                `update_time` int unsigned DEFAULT NULL COMMENT '更新时间',
                                `team_end_time` int DEFAULT NULL COMMENT '团的最后 成功时间 / 失败时间',
                                `sid` int NOT NULL COMMENT '商户id',
                                `delete_time` int DEFAULT NULL,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='参团表';


-- lingshou.ls_user definition

CREATE TABLE `ls_user` (
                           `id` int NOT NULL AUTO_INCREMENT,
                           `sn` varchar(32) DEFAULT NULL COMMENT '会员码',
                           `nickname` varchar(32) NOT NULL COMMENT '用户昵称',
                           `avatar` varchar(256) NOT NULL COMMENT '用户头像',
                           `real_name` varchar(16) DEFAULT NULL COMMENT '真实姓名',
                           `mobile` varchar(15) DEFAULT '' COMMENT '手机号码',
                           `level` tinyint unsigned DEFAULT '0' COMMENT '等级',
                           `group_id` int DEFAULT NULL COMMENT '所属分组id',
                           `sex` tinyint(1) DEFAULT '0' COMMENT '性别:0-未知；1-男；2-女',
                           `birthday` int DEFAULT NULL COMMENT '生日',
                           `user_money` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '用户余额',
                           `user_integral` int unsigned DEFAULT '0' COMMENT '用户积分',
                           `total_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '消费累计额度',
                           `total_order_num` int DEFAULT '0' COMMENT '累计消费次数',
                           `total_recharge_amount` decimal(10,2) DEFAULT '0.00' COMMENT '累计充值金额',
                           `account` varchar(16) DEFAULT '' COMMENT '账号',
                           `password` varchar(32) DEFAULT NULL COMMENT '密码',
                           `pay_password` varchar(32) DEFAULT NULL COMMENT '支付密码',
                           `login_time` int DEFAULT NULL COMMENT '最后登录时间',
                           `login_ip` varchar(15) DEFAULT '' COMMENT '最后登录ip',
                           `disable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用：0-否；1-是；',
                           `user_growth` int DEFAULT '0' COMMENT '用户成长值',
                           `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
                           `first_leader` int unsigned NOT NULL DEFAULT '0' COMMENT '第一个上级',
                           `second_leader` int unsigned NOT NULL DEFAULT '0' COMMENT '第二个上级',
                           `third_leader` int NOT NULL DEFAULT '0' COMMENT '第三个上级',
                           `ancestor_relation` text COMMENT '关系链',
                           `code` varchar(30) NOT NULL COMMENT '邀请码',
                           `user_earnings` decimal(10,2) DEFAULT '0.00' COMMENT '用户收益(可提现)',
                           `register_source` tinyint(1) NOT NULL COMMENT '用户注册来源',
                           `inviter_id` int NOT NULL DEFAULT '0' COMMENT '邀请人id',
                           `create_time` int DEFAULT NULL COMMENT '创建时间',
                           `update_time` int DEFAULT NULL COMMENT '修改时间',
                           `delete_time` int DEFAULT NULL COMMENT '删除时间',
                           `sid` int NOT NULL COMMENT '商户id',
                           `is_new_user` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是新注册用户:1-是;0-否;',
                           PRIMARY KEY (`id`) USING BTREE,
                           UNIQUE KEY `sn` (`sn`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=770 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户表';


-- lingshou.ls_user_address definition

CREATE TABLE `ls_user_address` (
                                   `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
                                   `user_id` int unsigned NOT NULL COMMENT '用户id',
                                   `cust_id` varchar(100) DEFAULT '0' COMMENT 'mpay 用戶ID',
                                   `contact` varchar(20) NOT NULL DEFAULT '' COMMENT '收货人',
                                   `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '联系方式',
                                   `province_id` int NOT NULL COMMENT '省',
                                   `city_id` int DEFAULT NULL COMMENT '市',
                                   `district_id` int DEFAULT NULL COMMENT '区',
                                   `address` varchar(64) DEFAULT NULL COMMENT '详细地址',
                                   `longitude` varchar(16) DEFAULT '0' COMMENT '经度',
                                   `latitude` varchar(16) DEFAULT '0' COMMENT '纬度',
                                   `is_default` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否默认(1为默认)',
                                   `create_time` int unsigned DEFAULT NULL COMMENT '创建时间',
                                   `update_time` int unsigned DEFAULT NULL COMMENT '修改时间',
                                   `delete_time` int DEFAULT NULL COMMENT '软删除',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=697 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户地址表';


-- lingshou.ls_user_auth definition

CREATE TABLE `ls_user_auth` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `user_id` int NOT NULL COMMENT '用户id',
                                `openid` varchar(128) NOT NULL COMMENT '微信openid',
                                `cust_id` varchar(100) DEFAULT NULL COMMENT 'mpay cust id',
                                `unionid` varchar(128) DEFAULT '' COMMENT '微信unionid',
                                `terminal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户端类型：1-微信小程序；2-微信公众号；3-手机H5；4-电脑PC；5-苹果APP；6-安卓APP',
                                `create_time` int DEFAULT NULL COMMENT '创建时间',
                                `update_time` int DEFAULT NULL COMMENT '更新时间',
                                `sid` int NOT NULL COMMENT '商户id',
                                `delete_time` int DEFAULT NULL,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=769 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户授权表';


-- lingshou.ls_user_label definition

CREATE TABLE `ls_user_label` (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `name` varchar(32) NOT NULL COMMENT '名称',
                                 `remark` varchar(255) DEFAULT NULL COMMENT '描述',
                                 `label_type` tinyint(1) NOT NULL COMMENT '标签类型：0-手动标签；1-自动标签',
                                 `create_time` int NOT NULL COMMENT '创建时间',
                                 `update_time` int DEFAULT NULL COMMENT '更新时间',
                                 `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户标签表';


-- lingshou.ls_user_label_index definition

CREATE TABLE `ls_user_label_index` (
                                       `id` int NOT NULL AUTO_INCREMENT,
                                       `user_id` int NOT NULL COMMENT '用户id',
                                       `label_id` int NOT NULL COMMENT '标签id',
                                       `sid` int NOT NULL COMMENT '商户id',
                                       `create_time` int DEFAULT NULL,
                                       `update_time` int DEFAULT NULL,
                                       `delete_time` int DEFAULT NULL,
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户标签关联表';


-- lingshou.ls_user_level definition

CREATE TABLE `ls_user_level` (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `name` varchar(32) NOT NULL COMMENT '等级名称',
                                 `rank` int NOT NULL COMMENT '等级权重（1-为系统默认等级）',
                                 `image` varchar(128) DEFAULT NULL COMMENT '图标',
                                 `background_image` varchar(128) DEFAULT NULL COMMENT '背景图',
                                 `remark` varchar(255) DEFAULT NULL COMMENT '等级描述',
                                 `discount` decimal(4,2) DEFAULT NULL COMMENT '等级折扣（空表示无折扣）',
                                 `condition` text COMMENT '条件(保存json格式)',
                                 `create_time` int NOT NULL COMMENT '创建时间',
                                 `update_time` int DEFAULT NULL COMMENT '更新时间',
                                 `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                 `sid` int NOT NULL COMMENT '商户id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户等级表';


-- lingshou.ls_user_session definition

CREATE TABLE `ls_user_session` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `user_id` int NOT NULL COMMENT '用户id',
                                   `terminal` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户端类型：1-微信小程序；2-微信公众号；3-手机H5；4-电脑PC；5-苹果APP；6-安卓APP',
                                   `token` varchar(32) NOT NULL COMMENT '令牌',
                                   `update_time` int DEFAULT NULL COMMENT '更新时间',
                                   `expire_time` int NOT NULL COMMENT '到期时间',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   `delete_time` int DEFAULT NULL,
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE KEY `admin_id_client` (`user_id`,`terminal`) USING BTREE COMMENT '一个用户在一个终端只有一个token',
                                   UNIQUE KEY `token` (`token`) USING BTREE COMMENT 'token是唯一的'
) ENGINE=InnoDB AUTO_INCREMENT=791 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会话表';


-- lingshou.ls_user_transfer definition

CREATE TABLE `ls_user_transfer` (
                                    `id` int unsigned NOT NULL AUTO_INCREMENT,
                                    `transfer_out` int NOT NULL COMMENT '转出用户id',
                                    `transfer_in` int NOT NULL COMMENT '转入用户id',
                                    `money` decimal(10,2) NOT NULL COMMENT '转账金额',
                                    `create_time` int DEFAULT NULL COMMENT '创建时间',
                                    `update_time` int DEFAULT NULL COMMENT '更新时间',
                                    `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                    `sid` int NOT NULL COMMENT '商户id',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户转账表';


-- lingshou.ls_verification definition

CREATE TABLE `ls_verification` (
                                   `id` int unsigned NOT NULL AUTO_INCREMENT,
                                   `order_id` int NOT NULL COMMENT '订单ID',
                                   `selffetch_shop_id` int NOT NULL COMMENT '自提门店ID',
                                   `handle_id` int NOT NULL DEFAULT '0' COMMENT '操作人ID(0为系统核销)',
                                   `verification_scene` tinyint(1) NOT NULL DEFAULT '0' COMMENT '核销场景:0-系统;1-管理员;2-会员;',
                                   `snapshot` text COMMENT '核销员数据快照',
                                   `create_time` int NOT NULL COMMENT '创建时间',
                                   `sid` int NOT NULL COMMENT '商户id',
                                   `delete_time` int DEFAULT NULL,
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='核销表';


-- lingshou.ls_withdraw_apply definition

CREATE TABLE `ls_withdraw_apply` (
                                     `id` int unsigned NOT NULL AUTO_INCREMENT,
                                     `sn` varchar(25) NOT NULL COMMENT '提现单号',
                                     `batch_no` varchar(25) DEFAULT NULL COMMENT '商家批次单号',
                                     `user_id` int NOT NULL COMMENT '用户id',
                                     `real_name` varchar(255) DEFAULT '' COMMENT '真实姓名',
                                     `account` varchar(32) DEFAULT '' COMMENT '账号',
                                     `type` tinyint(1) NOT NULL COMMENT '类型：1-钱包余额；2-微信零钱；3-银行卡;4-微信收款码;5-支付宝收款码',
                                     `money` decimal(10,2) unsigned NOT NULL COMMENT '提现金额',
                                     `left_money` decimal(10,2) DEFAULT NULL COMMENT '用户可得的金额(扣除手续费后)',
                                     `money_qr_code` varchar(128) DEFAULT '' COMMENT '收款二维码',
                                     `handling_fee` decimal(10,2) DEFAULT NULL COMMENT '手续费',
                                     `apply_remark` varchar(255) DEFAULT '' COMMENT '申请备注',
                                     `status` tinyint(1) NOT NULL COMMENT '状态：\r\n1-待提现\r\n2-提现中\r\n3-提现成功\r\n4-提现失败',
                                     `pay_desc` text COMMENT '微信零钱支付信息',
                                     `pay_search_result` text COMMENT '微信零钱支付查询结果',
                                     `payment_no` varchar(255) DEFAULT '' COMMENT '支付单号',
                                     `payment_time` int DEFAULT NULL COMMENT '支付时间',
                                     `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
                                     `transfer_voucher` varchar(255) DEFAULT '' COMMENT '转账凭证',
                                     `transfer_time` int DEFAULT NULL COMMENT '转账时间',
                                     `transfer_remark` varchar(255) DEFAULT '' COMMENT '转账备注',
                                     `bank` varchar(255) DEFAULT '' COMMENT '提现银行',
                                     `subbank` varchar(255) DEFAULT '' COMMENT '提现银行支行',
                                     `create_time` int DEFAULT NULL COMMENT '创建时间',
                                     `update_time` int DEFAULT NULL COMMENT '更新时间',
                                     `delete_time` int DEFAULT NULL COMMENT '删除时间',
                                     `sid` int NOT NULL COMMENT '商户id',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='提现申请表';



CREATE TABLE `ls_logistics_token` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `express_code` varchar(64)  NOT NULL COMMENT '物流平台代码',
  `app_key` varchar(255) NOT NULL DEFAULT '' COMMENT '密钥',
  `app_secret` varchar(256) NOT NULL DEFAULT '' COMMENT '密钥',
  `access_token` varchar(512) NOT NULL DEFAULT '' COMMENT '访问令牌',
  `refresh_token` varchar(512)  NOT NULL DEFAULT '' COMMENT '刷新令牌',
  `access_token_expire` datetime NOT NULL COMMENT '访问令牌过期时间',
  `refresh_token_expire` datetime DEFAULT NULL COMMENT '刷新令牌过期时间',
  `last_refresh_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上次刷新时间',
  `sid` int NOT NULL COMMENT '商户id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  `ext_info` varchar(512) NOT NULL DEFAULT '' COMMENT '其他信息',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '0-未删除；1-删除',
  PRIMARY KEY (`id`),
  KEY `idx_expire_time` (`access_token_expire`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='物流令牌表';


create unique index uidx_sid_express_code on ls_logistics_token(sid, express_code, del_flag);
