package com.mcoin.mall.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import com.mcoin.mall.bo.StoreTypeProductsBo;
import java.math.BigDecimal;

public class StoreTypeUtilsTest{

    @Test
    public void testGetPoint_OnlyPointIsOne() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(1));
        item.setMinPoint("0");
    
        int result = StoreTypeUtils.getPoint(item);
        assertEquals(200, result);
    }

    @Test
    public void testGetPoint_OnlyPointIsTwo() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(2));
        item.setMinPoint("0");
    
        int result = StoreTypeUtils.getPoint(item);
        assertEquals(0, result);
    }

    @Test
    public void testGetPoint_OnlyPointIsZero() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(0));
        item.setMinPoint("1");
    
        int result = StoreTypeUtils.getPoint(item);
        assertEquals(1, result);
    }

    @Test
    public void testGetPoint_MinPointIsGreaterThanZero() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(0));
        item.setMinPoint("50");
    
        int result = StoreTypeUtils.getPoint(item);
        assertEquals(50, result);
    }

    @Test
    public void testGetPoint_MinPointIsZero() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(0));
        item.setMinPoint("0");
    
        int result = StoreTypeUtils.getPoint(item);
        assertEquals(1, result);
    }

    @Test
    public void testGetPreferential_OnlyPointIsTwo() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(2));
        item.setMinPoint("0");
    
        BigDecimal result = StoreTypeUtils.getPreferential(item);
        assertEquals(new BigDecimal("100"), result);
    }

    @Test
    public void testGetPreferential_OnlyPointIsZero() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(0));
        item.setMinPoint("0");
    
        BigDecimal result = StoreTypeUtils.getPreferential(item);
        assertEquals(new BigDecimal("99.50"), result);
    }

    @Test
    public void testGetPreferential_OnlyPointIsNegative() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(-1));
        item.setMinPoint("0");
    
        BigDecimal result = StoreTypeUtils.getPreferential(item);
        assertEquals(new BigDecimal("99.50"), result);
    }

    @Test
    public void testGetPreferential_MinPointIsZero() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(0));
        item.setMinPoint(String.valueOf(0));
    
        BigDecimal result = StoreTypeUtils.getPreferential(item);
        assertEquals(new BigDecimal("99.50"), result);
    }

    @Test
    public void testGetPreferential_MinPointIsPositive() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(0));
        item.setMinPoint(String.valueOf(50));
    
        BigDecimal result = StoreTypeUtils.getPreferential(item);
        assertEquals(new BigDecimal("75.00"), result);
    }

    @Test
    public void testGetPreferential_OnlyPointIsOne() {
        StoreTypeProductsBo item = new StoreTypeProductsBo();
        item.setPrice("100");
        item.setPointRatio(2);
        item.setOnlyPoint(String.valueOf(1));
        item.setMinPoint("0");
    
        BigDecimal result = StoreTypeUtils.getPreferential(item);
        assertEquals(BigDecimal.ZERO, result);
    }

}