package com.mcoin.mall.service.auth.impl;

import static com.mcoin.mall.util.McoinMall.NOT_LOGIN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformUserinfo;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.model.MPayRefreshTokenResponse;
import com.mcoin.mall.client.model.MPayUserInfoResponse;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.dao.FookMacaupassUserLoginLog2Dao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformUserinfoDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.model.ConfigMPayJSApiResponse;
import com.mcoin.mall.model.CouponLoginRequest;
import com.mcoin.mall.model.CouponLoginResponse;
import com.mcoin.mall.model.GetTokenRequest;
import com.mcoin.mall.model.GetTokenResponse;
import com.mcoin.mall.model.GetUserInfoResponse;
import com.mcoin.mall.model.RefreshTokenResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.UserProfileResponse;
import com.mcoin.mall.security.TokenManager;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.chennel.McoinChannelService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.GrayUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.StrUtil;

import feign.FeignException;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class MPayAuthServiceImplTest{

    @Mock
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Mock
    private McoinChannelService mcoinChannelService;

    @Mock
    private SettingsDao settingsDao;

    @Mock
    private MPayClient mPayClient;

    @Mock
    private TokenManager tokenManager;

    @Mock
    private FookMacaupassUserDao fookMacaupassUserDao;

    @Mock
    private FookPlatformUserinfoDao fookPlatformUserinfoDao;

    @InjectMocks
    private MPayAuthServiceImpl mPayAuthServiceImpl;

    @Mock
    private AuthCacheServiceImpl authCacheServiceImpl;

    @Mock
    private ContextHolder contextHolder;
    @Mock
    private FookMacaupassUserLoginLog2Dao fookMacaupassUserLoginLog2Dao;

    private MockedStatic<ConfigUtils> configUtilsMockedStatic;

    @Mock
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Captor
    private ArgumentCaptor<FookPlatformUserinfo> platformUserinfoCaptor;
    
    @Captor
    private ArgumentCaptor<FookMacaupassUser> userCaptor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        configUtilsMockedStatic = Mockito.mockStatic(ConfigUtils.class);
    }

    @AfterEach
    public void tearDown() {
        configUtilsMockedStatic.close();
    }

    @Test
    public void testGetRedirectUrlWithGraySwitchOnAndGv1True() {
        String scope = "testScope";
        String state = "testState";
        String source = "testSource";
        String oauthParams = "testOauthParams";
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("g_v1", "true");
    
        when(ConfigUtils.getProperty("mcoin.gray.switch", GrayUtils.CLOSED)).thenReturn(GrayUtils.GRAYING);
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI")).thenReturn("http://redirect.uri");
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI_V2")).thenReturn("http://redirect.uri.v2");
        when(ConfigUtils.getProperty("macaupass.URL")).thenReturn("http://macaupass.url");
        when(ConfigUtils.getProperty("macaupass.APPID")).thenReturn("testAppId");
    
        String result = mPayAuthServiceImpl.getRedirectUrl(scope, state, source, oauthParams, paramsMap);
    
        assertEquals("http://macaupass.url/oauth2/authorize?appid=testAppId&redirect_uri=http%3A%2F%2Fredirect.uri.v2%3FoauthParams%3DtestOauthParams%26source%3DtestSource&response_type=code&scope=testScope&state=testState", result);
    }

    @Test
    public void testGetRedirectUrlWithGraySwitchOnAndGv1False() {
        String scope = "testScope";
        String state = "testState";
        String source = "testSource";
        String oauthParams = "testOauthParams";
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("g_v1", "false");
    
        when(ConfigUtils.getProperty("mcoin.gray.switch", GrayUtils.CLOSED)).thenReturn(GrayUtils.GRAYING);
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI")).thenReturn("http://redirect.uri");
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI_V2")).thenReturn("http://redirect.uri.v2");
        when(ConfigUtils.getProperty("macaupass.URL")).thenReturn("http://macaupass.url");
        when(ConfigUtils.getProperty("macaupass.APPID")).thenReturn("testAppId");
    
        String result = mPayAuthServiceImpl.getRedirectUrl(scope, state, source, oauthParams, paramsMap);
    
        assertEquals("http://macaupass.url/oauth2/authorize?appid=testAppId&redirect_uri=http%3A%2F%2Fredirect.uri%3FoauthParams%3DtestOauthParams%26source%3DtestSource&response_type=code&scope=testScope&state=testState", result);
    }

    @Test
    public void testGetRedirectUrlWithGraySwitchClosed() {
        String scope = "testScope";
        String state = "testState";
        String source = "testSource";
        String oauthParams = "testOauthParams";
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("g_v1", "true");
    
        when(ConfigUtils.getProperty("mcoin.gray.switch", GrayUtils.CLOSED)).thenReturn(GrayUtils.CLOSED);
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI")).thenReturn("http://redirect.uri");
        when(ConfigUtils.getProperty("macaupass.URL")).thenReturn("http://macaupass.url");
        when(ConfigUtils.getProperty("macaupass.APPID")).thenReturn("testAppId");
    
        String result = mPayAuthServiceImpl.getRedirectUrl(scope, state, source, oauthParams, paramsMap);
    
        assertEquals("http://macaupass.url/oauth2/authorize?appid=testAppId&redirect_uri=http%3A%2F%2Fredirect.uri%3FoauthParams%3DtestOauthParams%26source%3DtestSource&response_type=code&scope=testScope&state=testState", result);
    }

    @Test
    public void testGetRedirectUrlWithEmptySourceAndOauthParams() {
        String scope = "testScope";
        String state = "testState";
        String source = "";
        String oauthParams = "";
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("g_v1", "true");
    
        when(ConfigUtils.getProperty("mcoin.gray.switch", GrayUtils.CLOSED)).thenReturn(GrayUtils.GRAYING);
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI")).thenReturn("http://redirect.uri");
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI_V2")).thenReturn("http://redirect.uri.v2");
        when(ConfigUtils.getProperty("macaupass.URL")).thenReturn("http://macaupass.url");
        when(ConfigUtils.getProperty("macaupass.APPID")).thenReturn("testAppId");
    
        String result = mPayAuthServiceImpl.getRedirectUrl(scope, state, source, oauthParams, paramsMap);
    
        assertEquals("http://macaupass.url/oauth2/authorize?appid=testAppId&redirect_uri=http%3A%2F%2Fredirect.uri.v2&response_type=code&scope=testScope&state=testState", result);
    }

    @Test
    public void testGetRedirectUrlWithNullSourceAndOauthParams() {
        String scope = "testScope";
        String state = "testState";
        String source = null;
        String oauthParams = null;
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("g_v1", "true");
    
        when(ConfigUtils.getProperty("mcoin.gray.switch", GrayUtils.CLOSED)).thenReturn(GrayUtils.GRAYING);
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI")).thenReturn("http://redirect.uri");
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI_V2")).thenReturn("http://redirect.uri.v2");
        when(ConfigUtils.getProperty("macaupass.URL")).thenReturn("http://macaupass.url");
        when(ConfigUtils.getProperty("macaupass.APPID")).thenReturn("testAppId");
    
        String result = mPayAuthServiceImpl.getRedirectUrl(scope, state, source, oauthParams, paramsMap);
    
        assertEquals("http://macaupass.url/oauth2/authorize?appid=testAppId&redirect_uri=http%3A%2F%2Fredirect.uri.v2&response_type=code&scope=testScope&state=testState", result);
    }

    @Test
    public void testSaveAndGetJSApi() {
        String url = "http://test.url";
    
        when(ConfigUtils.getProperty("macaupass.APPID")).thenReturn("testAppId");
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI")).thenReturn("http://redirect.uri");
        when(ConfigUtils.getProperty("macaupass.REDIRECT_URI_V2")).thenReturn("http://redirect.uri.v2");
        when(ConfigUtils.getProperty("macaupass.URL")).thenReturn("http://macaupass.url");
        when(authCacheServiceImpl.saveAndGetMacaupassTicketPhp()).thenReturn("testTicket");
    
        ConfigMPayJSApiResponse response = mPayAuthServiceImpl.saveAndGetJSApi(url);
    
        long timestamp = System.currentTimeMillis();
        String str = StrUtil.generateCode(32, -1);
        String nonceStr = "jsapi_ticket=testTicket&nonceStr=" + str + "&timestamp=" + timestamp + "&url=" + url;
        String signature = DigestUtils.sha1Hex(nonceStr);
    
        ConfigMPayJSApiResponse expectedResponse = new ConfigMPayJSApiResponse();
        expectedResponse.setDebug(1);
        expectedResponse.setAppId("testAppId");
        expectedResponse.setTimestamp(timestamp);
        expectedResponse.setNonceStr(str);
        expectedResponse.setSignature(signature);
        expectedResponse.setJsApiList(Arrays.asList("checkJsApi", "getLocation",
                "openLocation", "getNetworkType", "ctrlNavBar", "closeWindow",
                "scanQRCode", "payCode", "choosePayMent", "clearCache", "callPhone",
                "goBack", "getTakePhoto", "modifyImage", "brightnessSettings",
                "mpCardAutoChargeActive", "thirdShare", "setupStatusBarBgColor",
                "openNativePage", "pushWindow", "openTinyApp", "getSystemInfo"));
    
        assertEquals(expectedResponse.getAppId(), response.getAppId());
        assertEquals(expectedResponse.getDebug(), response.getDebug());
        assertEquals(expectedResponse.getJsApiList(), response.getJsApiList());
    }

    @Test
    public void testSaveAndGetTokenFailureCodeEmpty() {
    
        // Mocking the request with empty code
        GetTokenRequest request = new GetTokenRequest();
    
        // Expect a BusinessException with NOT_LOGIN
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.saveAndGetToken(request);
        });
    
        assertEquals(Response.Code.UNAUTHORIZED, exception.getRespCode());
        assertEquals(NOT_LOGIN, exception.getMessage());
    }

    @Test
    public void testSaveAndGetTokenFailureAccessTokenEmpty() throws BlockException {
        MockitoAnnotations.openMocks(this);
    
        // Mocking the request
        GetTokenRequest request = new GetTokenRequest();
        request.setCode("testCode");
        request.setState("testState");
    
        // Mocking the responses from MPayClient
        String accessTokenResponseJson = "{\"access_token\":\"\",\"refresh_token\":\"testRefreshToken\",\"openid\":\"testOpenid\"}";
    
        when(mPayClient.accessToken(anyString())).thenReturn(accessTokenResponseJson);
    
        // Expect a BusinessException with NOT_LOGIN
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.saveAndGetToken(request);
        });
    
        assertEquals(Response.Code.UNAUTHORIZED, exception.getRespCode());
        assertEquals(NOT_LOGIN, exception.getMessage());
    }

    @Test
    public void testSaveAndGetTokenFailureUserInfoEmpty() throws BlockException {
        MockitoAnnotations.openMocks(this);
    
        // Mocking the request
        GetTokenRequest request = new GetTokenRequest();
        request.setCode("testCode");
        request.setState("testState");
    
        // Mocking the responses from MPayClient
        String accessTokenResponseJson = "{\"access_token\":\"testAccessToken\",\"refresh_token\":\"testRefreshToken\",\"openid\":\"testOpenid\"}";
        String userInfoResponseJson = "";
    
        when(mPayClient.accessToken(anyString())).thenReturn(accessTokenResponseJson);
//        when(mPayClient.getUserInfo(anyString(), anyString())).thenReturn(userInfoResponseJson);
    
        // Expect a BusinessException with NOT_LOGIN
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.saveAndGetToken(request);
        });
    
        assertEquals(Response.Code.UNAUTHORIZED, exception.getRespCode());
        assertEquals(NOT_LOGIN, exception.getMessage());
    }

    @Test
    public void testSaveAndGetTokenFailureUserIdEmpty() throws BlockException {
        MockitoAnnotations.openMocks(this);
    
        // Mocking the request
        GetTokenRequest request = new GetTokenRequest();
        request.setCode("testCode");
        request.setState("testState");
    
        // Mocking the responses from MPayClient
        String accessTokenResponseJson = "{\"access_token\":\"testAccessToken\",\"refresh_token\":\"testRefreshToken\",\"openid\":\"testOpenid\"}";
        String userInfoResponseJson = "{\"openid\":\"testOpenid\",\"nickname\":\"testNickname\",\"headimgurl\":\"testHeadimgurl\",\"point\":100}";
        String userIdResponseJson = "";
    
        when(mPayClient.accessToken(anyString())).thenReturn(accessTokenResponseJson);
//        when(mPayClient.getUserInfo(anyString(), anyString())).thenReturn(userInfoResponseJson);
        when(mPayClient.getUserId(anyString())).thenReturn(userIdResponseJson);
    
        // Expect a BusinessException with NOT_LOGIN
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.saveAndGetToken(request);
        });
    
        assertEquals(Response.Code.UNAUTHORIZED, exception.getRespCode());
        assertEquals(NOT_LOGIN, exception.getMessage());
    }

    @Test
    public void testSaveAndGetTokenSuccess() throws Exception {

        // 模拟配置返回值
        when(ConfigUtils.getProperty("macaupassave.userinfo.get", "1")).thenReturn("1");

        // Mocking the request
        GetTokenRequest request = new GetTokenRequest();
        request.setCode("testCode");
        request.setState("testState");
    
        // Mocking the responses from MPayClient
        String accessTokenResponseJson = "{\"access_token\":\"testAccessToken\",\"refresh_token\":\"testRefreshToken\",\"openid\":\"testOpenid\"}";
        String userInfoResponseJson = "{\"openid\":\"testOpenid\",\"nickname\":\"testNickname\",\"headimgurl\":\"testHeadimgurl\",\"point\":100}";
        String userIdResponseJson = "{\"userId\":\"testUserId\"}";
    
        when(mPayClient.accessToken(anyString())).thenReturn(accessTokenResponseJson);
        when(mPayClient.getUserInfo(anyString(), anyString())).thenReturn(userInfoResponseJson);
        when(mPayClient.getUserId(anyString())).thenReturn(userIdResponseJson);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(tokenManager.createJwtToken(any(), any())).thenReturn("testJwtToken");
    
        // Call the method
        GetTokenResponse response = mPayAuthServiceImpl.saveAndGetToken(request);
    
        // Verify the results
        assertNotNull(response);
        assertEquals("testJwtToken", response.getToken());
        assertEquals("testOpenid", response.getOpenId());
        assertEquals(0, response.getRead());
        assertEquals("testState", response.getState());

        // Verify interactions
        verify(mPayClient, times(1)).accessToken(anyString());
        verify(mPayClient, times(1)).getUserInfo(anyString(), anyString());
        verify(mPayClient, times(1)).getUserId(anyString());
        verify(fookMacaupassUserDao, times(1)).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdAndGetUserInfoExceptionThrown() throws Exception {

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setAccessToken("oldToken");
        fookMacaupassUser.setRefreshToken("newRefreshToken");
        fookMacaupassUser.setUpdateTime(JodaTimeUtil.plusDayToDate(new Date(), -1));
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(settingsDao.getValue("site.mpay")).thenReturn(null);
        when(ConfigUtils.getProperty("macaupass.POINT_RATIO")).thenReturn("300");
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        when(mcoinChannelService.getPoint(any())).thenThrow(new RetryException("Retry Exception"));

        MPayRefreshTokenResponse mPayRefreshTokenResponse = new MPayRefreshTokenResponse();
        mPayRefreshTokenResponse.setAccessToken("newToken");
        mPayRefreshTokenResponse.setRefreshToken("newRefreshToken");
        when(mPayClient.refreshToken(anyString()))
                .thenReturn(JSON.toJSONString(mPayRefreshTokenResponse));

        // Act & Assert
        Exception exception = assertThrows(RetryException.class, () -> {
            mPayAuthServiceImpl.updAndGetUserInfo();
        });
    
        assertEquals("Retry Exception", exception.getMessage());
    }

    @Test
    public void testUpdAndGetUserInfoUserNotFound() {

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(settingsDao.getValue("site.mpay")).thenReturn(null);
        when(ConfigUtils.getProperty("macaupass.POINT_RATIO")).thenReturn("300");
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
    
        // Act
        GetUserInfoResponse response = mPayAuthServiceImpl.updAndGetUserInfo();
    
        // Assert
        assertEquals(-10, response.getPoint());
        assertEquals(BigDecimal.valueOf(Math.floor((double) -10 / 300)), response.getAmount());
    }

    @Test
    public void testUpdAndGetUserInfoUserTokenExpired() throws Exception {

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setAccessToken("oldToken");
        fookMacaupassUser.setRefreshToken("newRefreshToken");
        fookMacaupassUser.setUpdateTime(JodaTimeUtil.plusDayToDate(new Date(), -3));
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(settingsDao.getValue("site.mpay")).thenReturn(null);
        when(ConfigUtils.getProperty("macaupass.POINT_RATIO")).thenReturn("300");
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        MPayRefreshTokenResponse mPayRefreshTokenResponse = new MPayRefreshTokenResponse();
        mPayRefreshTokenResponse.setAccessToken("newToken");
        mPayRefreshTokenResponse.setRefreshToken("newRefreshToken");
        when(mPayClient.refreshToken(anyString()))
                .thenReturn(JSON.toJSONString(mPayRefreshTokenResponse));
        when(mcoinChannelService.getPoint(any())).thenReturn(null);
        MPayUserInfoResponse mPayUserInfoResponse = new MPayUserInfoResponse();
        mPayUserInfoResponse.setOpenid("testOpenId");
        mPayUserInfoResponse.setPoint(100);
        when(mPayClient.getUserInfo(any(), any())).thenReturn(JSON.toJSONString(mPayUserInfoResponse));
    
        // Act
        GetUserInfoResponse response = mPayAuthServiceImpl.updAndGetUserInfo();
    
        // Assert
        assertEquals(100, response.getPoint());
        assertEquals(BigDecimal.valueOf(Math.floor((double) 100 / 300)), response.getAmount());
    }

    @Test
    public void testUpdAndGetUserInfoUserTokenNotExpired() throws Exception {
        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setAccessToken("oldToken");
        fookMacaupassUser.setRefreshToken("newRefreshToken");
        fookMacaupassUser.setUpdateTime(JodaTimeUtil.plusDayToDate(new Date(), -1));
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(settingsDao.getValue("site.mpay")).thenReturn(null);
        when(ConfigUtils.getProperty("macaupass.POINT_RATIO")).thenReturn("300");
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        when(mcoinChannelService.getPoint(any())).thenReturn(100);
        MPayRefreshTokenResponse mPayRefreshTokenResponse = new MPayRefreshTokenResponse();
        mPayRefreshTokenResponse.setAccessToken("newToken");
        mPayRefreshTokenResponse.setRefreshToken("newRefreshToken");
        when(mPayClient.refreshToken(anyString()))
                .thenReturn(JSON.toJSONString(mPayRefreshTokenResponse));

        // Act
        GetUserInfoResponse response = mPayAuthServiceImpl.updAndGetUserInfo();
    
        // Assert
        assertEquals(100, response.getPoint());
        assertEquals(BigDecimal.valueOf(Math.floor((double) 100 / 300)), response.getAmount());
    }

    @Test
    public void testRefreshToken() {
        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        userInfo.setUsername("testUser");
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(ConfigUtils.getProperty("security.jwt.expire", "7200")).thenReturn("7200");
        String expectedJwtToken = "mockedJwtToken";
        when(tokenManager.createJwtToken(userInfo.getUserId(), userInfo.getUsername())).thenReturn(expectedJwtToken);
    
        // Act
        RefreshTokenResponse response = mPayAuthServiceImpl.refreshToken();
    
        // Assert
        assertEquals(expectedJwtToken, response.getAccessToken());
        assertEquals("bearer", response.getTokenType());
        assertEquals(7200L, response.getExpiresIn());
        assertEquals(1, response.getStatus());
    
        verify(contextHolder, times(1)).getAuthUserInfo();
        verify(tokenManager, times(1)).createJwtToken(userInfo.getUserId(), userInfo.getUsername());
    }

    @Test
    public void testCouponLoginUserNotExist() {
        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid("testCustomId");
        request.setOrder_no("testOrderNo");
    
        when(fookMacaupassUserDao.selectList(any())).thenReturn(new ArrayList<>());
    
        assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.couponLogin(request);
        });
    }

    @Test
    public void testCouponLoginUserinfoNotExist() {

        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid("testCustomId");
        request.setOrder_no("testOrderNo");
    
        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setCustomid("testCustomId");
        fookMacaupassUser.setUserId(1);
    
        List<FookMacaupassUser> fookMacaupassUserList = new ArrayList<>();
        fookMacaupassUserList.add(fookMacaupassUser);
    
        when(fookMacaupassUserDao.selectList(any())).thenReturn(fookMacaupassUserList);
        when(fookPlatformUserinfoDao.selectOne(any())).thenReturn(null);
    
        assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.couponLogin(request);
        });
    }

    @Test
    public void testCouponLoginOrderNotExist() {

        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid("testCustomId");
        request.setOrder_no("testOrderNo");
    
        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setCustomid("testCustomId");
        fookMacaupassUser.setUserId(1);
    
        FookPlatformUserinfo fookPlatformUserinfo = new FookPlatformUserinfo();
        fookPlatformUserinfo.setId(1);
        fookPlatformUserinfo.setNickName("testNickName");
    
        when(fookMacaupassUserDao.selectList(any())).thenReturn(new ArrayList<>());

        assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.couponLogin(request);
        });
    }

    @Test
    public void testCouponLoginSuccess() {

        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid("testCustomId");
        request.setOrder_no("testOrderNo");
    
        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setCustomid("testCustomId");
        fookMacaupassUser.setUserId(1);
    
        FookPlatformUserinfo fookPlatformUserinfo = new FookPlatformUserinfo();
        fookPlatformUserinfo.setId(1);
        fookPlatformUserinfo.setNickName("testNickName");
    
        FookPlatformOrder fookPlatformOrder = new FookPlatformOrder();
        fookPlatformOrder.setUserid(1);
        fookPlatformOrder.setOrderNo("testOrderNo");
    
        List<FookMacaupassUser> fookMacaupassUserList = new ArrayList<>();
        fookMacaupassUserList.add(fookMacaupassUser);
    
        when(fookMacaupassUserDao.selectList(any())).thenReturn(fookMacaupassUserList);
        when(fookPlatformUserinfoDao.selectOne(any())).thenReturn(fookPlatformUserinfo);
        when(fookPlatformOrderDao.selectOne(any())).thenReturn(fookPlatformOrder);
        when(tokenManager.createJwtToken(any(), any())).thenReturn("testJwtToken");
    
        CouponLoginResponse response = mPayAuthServiceImpl.couponLogin(request);
    
        assertEquals("testJwtToken", response.getToken());
    }

    @Test
    public void testCouponLoginCustomIdEmpty() {
        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid("");
        request.setOrder_no("testOrderNo");
    
        assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.couponLogin(request);
        });
    }

    @Test
    public void testCouponLoginOrderNoEmpty() {
        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid("testCustomId");
        request.setOrder_no("");
    
        assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.couponLogin(request);
        });
    }

    @Test
    public void testUpdAndGetUserProfile_Normal() throws BlockException {
        // 正常场景：用户存在，token未过期，获取信息正常

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        userInfo.setUsername("testUser");

        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setId(100);
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setOpenid("testOpenId");
        fookMacaupassUser.setAccessToken("testAccessToken");
        fookMacaupassUser.setRefreshToken("testRefreshToken");
        fookMacaupassUser.setUpdateTime(new Date()); // 最近更新，不会过期

        MPayUserInfoResponse mPayUserInfoResponse = new MPayUserInfoResponse();
        mPayUserInfoResponse.setOpenid("testOpenId");
        mPayUserInfoResponse.setNickname("testNickname");
        mPayUserInfoResponse.setHeadimgurl("http://test.url/avatar.jpg");
        mPayUserInfoResponse.setPoint(100);
        mPayUserInfoResponse.setPhone("12345678");
        mPayUserInfoResponse.setArea("+853");

        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        when(mPayClient.getUserInfo(anyString(), anyString())).thenReturn(JSON.toJSONString(mPayUserInfoResponse));

        // Act
        UserProfileResponse response = mPayAuthServiceImpl.updAndGetUserProfile();

        // Assert
        assertNotNull(response);
        assertEquals("testNickname", response.getNickname());
        assertEquals("http://test.url/avatar.jpg", response.getHeadimgurl());

        // 验证更新用户信息的调用
        verify(fookPlatformUserinfoDao).update(platformUserinfoCaptor.capture(), any(LambdaQueryWrapper.class));
        assertEquals("http://test.url/avatar.jpg", platformUserinfoCaptor.getValue().getAvatar());
        assertEquals("85312345678", platformUserinfoCaptor.getValue().getRegisteredPhone());

        verify(fookMacaupassUserDao).update(userCaptor.capture(), any(LambdaQueryWrapper.class));
        assertEquals("12345678", userCaptor.getValue().getPhone());
        assertEquals("+853", userCaptor.getValue().getArea());
        assertEquals(100, userCaptor.getValue().getPoint());
        assertNotNull(userCaptor.getValue().getUpdateTime());
    }

    @Test
    public void testUpdAndGetUserProfile_TokenExpired() throws BlockException {
        // token过期场景：用户存在，token已过期，需要刷新token

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        userInfo.setUsername("testUser");

        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setId(100);
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setOpenid("testOpenId");
        fookMacaupassUser.setAccessToken("oldAccessToken");
        fookMacaupassUser.setRefreshToken("oldRefreshToken");
        // 设置两小时前的更新时间，使token过期
        fookMacaupassUser.setUpdateTime(JodaTimeUtil.parse(JodaTimeUtil.addDateHours(-3, JodaTimeUtil.DEFAULT_PATTERN)));

        MPayRefreshTokenResponse refreshTokenResponse = new MPayRefreshTokenResponse();
        refreshTokenResponse.setAccessToken("newAccessToken");
        refreshTokenResponse.setRefreshToken("newRefreshToken");

        MPayUserInfoResponse mPayUserInfoResponse = new MPayUserInfoResponse();
        mPayUserInfoResponse.setOpenid("testOpenId");
        mPayUserInfoResponse.setNickname("testNickname");
        mPayUserInfoResponse.setHeadimgurl("http://test.url/avatar.jpg");
        mPayUserInfoResponse.setPoint(100);
        mPayUserInfoResponse.setPhone("12345678");
        mPayUserInfoResponse.setArea("+853");

        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        when(mPayClient.refreshToken(anyString())).thenReturn(JSON.toJSONString(refreshTokenResponse));
        when(mPayClient.getUserInfo(anyString(), anyString())).thenReturn(JSON.toJSONString(mPayUserInfoResponse));

        // Act
        UserProfileResponse response = mPayAuthServiceImpl.updAndGetUserProfile();

        // Assert
        assertNotNull(response);
        assertEquals("testNickname", response.getNickname());
        assertEquals("http://test.url/avatar.jpg", response.getHeadimgurl());

        // 验证刷新token的调用
        verify(mPayClient).refreshToken("oldRefreshToken");

        // 验证更新用户信息的调用
        verify(fookMacaupassUserDao).update(userCaptor.capture(), any(LambdaQueryWrapper.class));
        assertEquals("newAccessToken", userCaptor.getValue().getAccessToken());
        assertEquals("newRefreshToken", userCaptor.getValue().getRefreshToken());
        assertEquals("12345678", userCaptor.getValue().getPhone());
        assertEquals("+853", userCaptor.getValue().getArea());
        assertEquals(100, userCaptor.getValue().getPoint());
        assertNotNull(userCaptor.getValue().getUpdateTime());
    }

    @Test
    public void testUpdAndGetUserProfile_UserNotFound() throws BlockException {
        // 用户不存在场景：应该抛出BusinessException

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        userInfo.setUsername("testUser");

        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.updAndGetUserProfile();
        });

        assertEquals(Response.Code.UNAUTHORIZED, exception.getRespCode());
        assertEquals(NOT_LOGIN, exception.getMessage());
    }

    @Test
    public void testUpdAndGetUserProfile_RefreshTokenFailed() throws BlockException {
        // 刷新token失败场景：token已过期，但刷新失败

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        userInfo.setUsername("testUser");

        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setId(100);
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setOpenid("testOpenId");
        fookMacaupassUser.setAccessToken("oldAccessToken");
        fookMacaupassUser.setRefreshToken("oldRefreshToken");
        // 设置两小时前的更新时间，使token过期
        fookMacaupassUser.setUpdateTime(JodaTimeUtil.parse(JodaTimeUtil.addDateHours(-3, JodaTimeUtil.DEFAULT_PATTERN)));

        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        when(mPayClient.refreshToken(anyString())).thenReturn("");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.updAndGetUserProfile();
        });

        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("request refreshToken error", exception.getMessage());
    }

    @Test
    public void testUpdAndGetUserProfile_GetUserInfoFailed() throws BlockException {
        // 获取用户信息失败场景：调用API获取用户信息失败

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        userInfo.setUsername("testUser");

        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setId(100);
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setOpenid("testOpenId");
        fookMacaupassUser.setAccessToken("testAccessToken");
        fookMacaupassUser.setRefreshToken("testRefreshToken");
        fookMacaupassUser.setUpdateTime(new Date()); // 最近更新，不会过期

        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        when(mPayClient.getUserInfo(anyString(), anyString())).thenReturn("");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.updAndGetUserProfile();
        });

        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("request userinfo error", exception.getMessage());
    }

    @Test
    public void testUpdAndGetUserProfile_GetUserInfoReturns401() throws BlockException {
        // 获取用户信息返回401错误场景

        // Arrange
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(1);
        userInfo.setUsername("testUser");

        FookMacaupassUser fookMacaupassUser = new FookMacaupassUser();
        fookMacaupassUser.setId(100);
        fookMacaupassUser.setUserId(1);
        fookMacaupassUser.setOpenid("testOpenId");
        fookMacaupassUser.setAccessToken("testAccessToken");
        fookMacaupassUser.setRefreshToken("testRefreshToken");
        fookMacaupassUser.setUpdateTime(new Date()); // 最近更新，不会过期

        // 使用lenient模式避免UnnecessaryStubbingException
        FeignException feignException = Mockito.mock(FeignException.class);
        Mockito.lenient().when(feignException.status()).thenReturn(401);
        Mockito.lenient().when(feignException.getMessage()).thenReturn("request userinfo error");

        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassUser);
        when(mPayClient.getUserInfo(anyString(), anyString())).thenThrow(feignException);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            mPayAuthServiceImpl.updAndGetUserProfile();
        });

        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("request userinfo error", exception.getMessage());
    }
}