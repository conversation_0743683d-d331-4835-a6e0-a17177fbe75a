package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 MqJobController
 * 使用真实服务和数据库，测试MQ任务相关功能
 */
class MqJobControllerTest extends BaseUnitTest {

    @Resource
    private MqJobController mqJobController;

    @Test
    void testResendMq() {
        // 直接调用控制器方法
        Response<String> response = mqJobController.resendMq();
        
        // 验证响应结构
        assertNotNull(response);
    }
} 