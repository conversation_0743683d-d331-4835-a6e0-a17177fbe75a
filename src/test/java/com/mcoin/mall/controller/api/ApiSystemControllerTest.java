package com.mcoin.mall.controller.api;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.api.system.SystemTimeResponse;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Integration test for the ApiSystemController
 * Tests the system time functionality
 */
class ApiSystemControllerTest extends BaseUnitTest {

    @Resource
    private ApiSystemController apiSystemController;

    @Test
    void testTime() {
        // Call the controller method directly
        Response<SystemTimeResponse> result = apiSystemController.time();

        // Verify response
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
        assertNotNull(result.getData().getSysTime());
    }
} 