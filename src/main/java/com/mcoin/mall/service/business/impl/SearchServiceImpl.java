package com.mcoin.mall.service.business.impl;

import cn.hutool.core.util.BooleanUtil;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.github.houbb.opencc4j.util.ZhTwConverterUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mcoin.mall.bean.*;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.BusinessProductTypeEnum;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.mapping.BusinessProductMapping;
import com.mcoin.mall.mapping.FookStoreMapping;
import com.mcoin.mall.model.*;
import com.mcoin.mall.service.business.SearchService;
import com.mcoin.mall.util.*;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.mcoin.mall.service.common.CacheService.CACHE_BUSINESS_INFORMATION;

/**
 *
 */
@Slf4j
@Service
public class SearchServiceImpl implements SearchService {

    @Resource
    private FookBusinessInformationDao fookBusinessInformationDao;

    @Resource
    private FookStoresDao fookStoresDao;

    @Resource
    private FookPlatformSuggestDao fookPlatformSuggestDao;

    @Resource
    private FookBusinessProductcategoryDao fookBusinessProductcategoryDao;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    @Resource
    private FookTemporaryProductDao fookTemporaryProductDao;

    @Resource
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Resource
    private FookPlatformUsercollectionDao fookPlatformUsercollectionDao;

    @Resource
    private FookBusinessDao fookBusinessDao;

    @Resource
    private FookStoresKeywordDao fookStoresKeywordDao;

    @Resource
    private MessageSource messageSource;

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;


    @Override
    public boolean buildSearchShopQuery(SearchStoreQuery query) {
        Map<String, List<Integer>> maps = Maps.newHashMap();
        //商圈
        if (CollectionUtils.isNotEmpty(query.getBusinessInformationIds())) {
            List<Integer> ids = this.fookBusinessInformationDao.getBusinessInformationByIds(query.getBusinessInformationIds());
            if (CollectionUtils.isNotEmpty(ids)) {
                query.setBusinessInformationIds(ids);
            } else {
                return false;
            }
        }
        //以下三个条件是And
        boolean categoryCondition = CollectionUtils.isNotEmpty(query.getCategory());
        boolean typeCondition = CollectionUtils.isNotEmpty(query.getType());
        boolean searchCondition = StringUtils.isNotBlank(query.getSearchTerm());

        //门店分类
        if (categoryCondition) {
            List<Integer> typeStoreIds = this.fookStoresDao.getStoresByCategory(query.getCategory());
            if (CollectionUtils.isEmpty(typeStoreIds)) {
                return false;
            }
            maps.put("categories", typeStoreIds);
        }

        if (CollectionUtils.isNotEmpty(query.getBusinessCategoriesIds())) {
            Date currentDate = new Date();
            // harmonySwitch已经在controller中设置
            Boolean harmonySwitch = query.getHarmonySwitch();

            // 在SQL中过滤type=12的商品
            List<Integer> businessCategoriesIds = query.getBusinessCategoriesIds();
            List<Integer> storeIds = this.fookStoresDao.getProductsByBusinessCategoriesIds(businessCategoriesIds, currentDate);

            // 再过滤掉只有type=12福利的门店
            if (Boolean.TRUE.equals(harmonySwitch) && CollectionUtils.isNotEmpty(storeIds)) {
                storeIds = this.fookStoresDao.filterStoresWithOnlyType12Products(storeIds, currentDate);
            }

            if (CollectionUtils.isEmpty(storeIds)) {
                return false;
            }
            maps.put("businessCategoriesIds", storeIds);
        }
        //福利分类
        if (typeCondition) {
            Date currentDate = new Date();
            // harmonySwitch已经在controller中设置
            Boolean harmonySwitch = query.getHarmonySwitch();

            List<Integer> types = query.getType();
            //过滤掉types中的12
            if (Boolean.TRUE.equals(harmonySwitch) && CollectionUtils.isNotEmpty(types)) {
                types = types.stream().filter(type -> type != BusinessProductTypeEnum.PHYSICAL_LINK.getTypeId()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(types)) {
                    return false;
                }
            }

            List<Integer> bpStoreIds = this.fookStoresDao.getProductsByType(types, currentDate);
            if (CollectionUtils.isEmpty(bpStoreIds)) {
                return false;
            }
            maps.put("types", bpStoreIds);
        }
        //关键字
        if (searchCondition) {
            Date currentDate = new Date();

            List<Integer> searchStoreIds = this.fookStoresDao.searchStoresByKeywords(query);
            // harmonySwitch已经在controller中设置
            Boolean harmonySwitch = query.getHarmonySwitch();
            // 再过滤掉只有type=12福利的门店
            if (Boolean.TRUE.equals(harmonySwitch) && CollectionUtils.isNotEmpty(searchStoreIds)) {
                searchStoreIds = this.fookStoresDao.filterStoresWithOnlyType12Products(searchStoreIds, currentDate);
            }

            if (CollectionUtils.isEmpty(searchStoreIds)) {
                return false;
            }
            maps.put("keywords", searchStoreIds);
        }

        Set<Integer> storeIds = Sets.newHashSet();
        //merge以上查询结果，然后去重取交集
        maps.forEach((key, value) -> {
            if (CollectionUtils.isEmpty(storeIds)) {
                storeIds.addAll(value);
            } else {
                storeIds.retainAll(value);
            }
        });
        if (CollectionUtils.isNotEmpty(storeIds)) {
            query.setStoreIds(Lists.newArrayList(storeIds));
        } else if (BooleanUtil.or(categoryCondition, typeCondition, searchCondition)) {
            //如果有任何查询条件不为空，但交集为空
            return false;
        }
        return true;
    }

    @Override
    public Integer searchShopCnt(SearchStoreQuery query) {
        return this.fookStoresDao.searchStoresCnt(query);
    }

    @Override
    public SearchStoreResponse searchShop(SearchStoreQuery query) {

        SearchStoreResponse result = new SearchStoreResponse();
        List<SearchStoreResponse.SnatchListItem> snatchList = Lists.newArrayList();
        result.setSnatchList(snatchList);

        // 获取店铺列表
        List<FookStores> fookStores = this.fookStoresDao.searchStores(query);

        // 注意：不需要过滤店铺，只需要在SQL中过滤type=12的商品
        if (CollectionUtils.isNotEmpty(fookStores)) {
            List<Integer> storesIds = fookStores.stream().map(FookStores::getId).collect(Collectors.toList());
            List<StoresKeywordWithStoreId> keywords = this.fookStoresKeywordDao.getStoreKeyWordsByStoreIds(storesIds);
            List<StoreBusinessProductCnt> storeBusinessProductCnt = this.fookBusinessProductDao.getBusinessProductCntByStoreIds(storesIds, query.getCurrentTime(), query.getHarmonySwitch());
            for (FookStores fookStore : fookStores) {
                SearchStoreResponse.SnatchListItem item = new SearchStoreResponse.SnatchListItem();
                item.setId(fookStore.getId());
                item.setName(fookStore.getName());
                double distanceKm = DistanceUtil.getDistance(query.getLot(), query.getLat(), fookStore.getLongitude(), fookStore.getDimension());
                String distance = DistanceUtil.getDistanceStr(distanceKm);
                item.setDistance(distance);
                //join with ,
                String keywordName = keywords.stream().filter(e -> e.getStoresId().equals(fookStore.getId())).map(StoresKeywordWithStoreId::getName).collect(Collectors.joining(","));
                item.setStoresKeywordName(keywordName);
                int productCount = storeBusinessProductCnt.stream().filter(e -> e.getStoresId().equals(fookStore.getId())).mapToInt(StoreBusinessProductCnt::getCnt).findFirst().orElse(0);
                String prdCnt = messageSource.getMessage("message.business.product_count", new Object[]{productCount}, contextHolder.getLocale());
                item.setProductCount(prdCnt);
                String img = FookStoreMapping.INSTANCE.extractImg(fookStore.getImg());
                item.setCover(img);
                snatchList.add(item);
            }
        }
        return result;
    }

    @Override
    public PopularSearchesResponse getPopularSearches() {
        List<FookPlatformSuggest> list = fookPlatformSuggestDao.getPopularSearches();
        PopularSearchesResponse popularSearchesResponse = new PopularSearchesResponse();
        List<PopularSearchesResponse.PopularSearchItem> itemList = new ArrayList<>();
        if (list.isEmpty()) {
            return popularSearchesResponse;
        }
        for (FookPlatformSuggest fookPlatformSuggest : list) {
            PopularSearchesResponse.PopularSearchItem item = new PopularSearchesResponse.PopularSearchItem();
            item.setLabel(fookPlatformSuggest.getLabel());
            item.setPopularity(fookPlatformSuggest.getPopularity());
            item.setLabelType(fookPlatformSuggest.getLabelType());
            itemList.add(item);
        }
        popularSearchesResponse.setSnatchList(itemList);
        return popularSearchesResponse;
    }

    @Cacheable(value = CACHE_BUSINESS_INFORMATION, key = "@cacheService.getLanguage()",
            unless = "#result.getSnatchList() == null || #result.getSnatchList().isEmpty()")
    @Override
    public BusinessInformationResponse getInformation() {
        List<FookBusinessInformation> informationList = fookBusinessInformationDao.selectInformation();
        BusinessInformationResponse businessInformationResponse = new BusinessInformationResponse();
        List<BusinessInformationResponse.SnatchListItem> snatchList = new ArrayList<>();
        List<BusinessInformationResponse.TypeListItem> typeList = new ArrayList<>();
        if (informationList.isEmpty()) {
            return businessInformationResponse;
        }
        String lang = LocaleContextHolder.getLocale().getLanguage();
        for (FookBusinessInformation fookBusinessInformation : informationList) {
            BusinessInformationResponse.SnatchListItem snatchListItem = new BusinessInformationResponse.SnatchListItem();
            snatchListItem.setId(fookBusinessInformation.getId());
            snatchListItem.setName(McoinMall.LANG_EN.equals(lang) && StringUtils.isNotBlank(fookBusinessInformation.getNameEn()) ? fookBusinessInformation.getNameEn() : fookBusinessInformation.getName());
            snatchList.add(snatchListItem);
        }
        businessInformationResponse.setSnatchList(snatchList);

        List<FookBusinessProductcategory> productcategoryList = fookBusinessProductcategoryDao.selectBusinessProductcategorys(1);
        for (FookBusinessProductcategory productcategory : productcategoryList) {
            BusinessInformationResponse.TypeListItem typeListItem = new BusinessInformationResponse.TypeListItem();
            typeListItem.setId(productcategory.getId());
            typeListItem.setParentId(productcategory.getParentId());
            typeListItem.setName(McoinMall.LANG_EN.equals(lang) ? productcategory.getEnglishName() : productcategory.getName());
            typeListItem.setEnglishName(productcategory.getEnglishName());
            typeListItem.setSort(productcategory.getSort());
            typeListItem.setIcon(productcategory.getIcon());
            typeListItem.setEnable(productcategory.getEnable());
            typeListItem.setType(productcategory.getType());
            typeListItem.setMcoinPlatform(productcategory.getMcoinPlatform());
            typeListItem.setMcardPlatform(productcategory.getMcardPlatform());
            typeList.add(typeListItem);
        }
        businessInformationResponse.setTypeList(typeList);
        return businessInformationResponse;
    }

    @Override
    public CheckSearchValueResponse checkSearchValue(CheckSearchValueRequest request, String clientType) {
        String searchTerm = request.getSearchTerm();
        CheckSearchValueResponse checkSearchValueResponse = new CheckSearchValueResponse();
        if (StringUtils.isBlank(searchTerm)) {
            return checkSearchValueResponse;
        }

        // 检查是否为HARMONY设备且开关开启
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
        // 去除表情符
        searchTerm = StrUtil.dbUtf8Parser(EmojiParser.removeAllEmojis(searchTerm));
        String searchTermSimple = ZhTwConverterUtil.toSimple(ZhConverterUtil.toSimple(searchTerm));
        String searchTermTraditional = ZhConverterUtil.toTraditional(searchTermSimple);
        String searchTermTwTraditional = ZhTwConverterUtil.toTraditional(searchTermSimple);

        int productCount = 0;
        int storeCount = 0;
        int temporaryProductCount = fookTemporaryProductDao.selectTotol();
        //可能定時任務沒有生成db福利視圖
        if (temporaryProductCount > 0) {
            //根據福利信息得到產品分組
            List<Integer> productIds = fookTemporaryProductDao.selectSearchTerm(searchTerm, searchTermSimple,
                    searchTermTraditional, searchTermTwTraditional, harmonySwitch);
            if (CollectionUtils.isNotEmpty(productIds)) {
                productCount = productIds.size();
                //通過產品信息查詢門店信息
                List<FookBusinessStoreProduct> businessStoreProductList = fookBusinessStoreProductDao.getStoresIdByProductIds(productIds);
                if (CollectionUtils.isNotEmpty(businessStoreProductList)) {
                    List<Integer> storeIds = Lists.newArrayList();
                    for (FookBusinessStoreProduct storeProduct : businessStoreProductList) {
                        storeIds.add(storeProduct.getStoreid());
                    }
                    //門店去重
                    storeIds = storeIds.stream().distinct().collect(Collectors.toList());
                    List<FookStores> stores = this.fookStoresDao.getStores(storeIds);
                    if (CollectionUtils.isNotEmpty(stores)) {
                        stores = stores.stream().filter(e -> e.getEnable() == 1).collect(Collectors.toList());
                        storeCount = stores.size();
                    }
                }
            } else {
                log.info("福利視圖沒有對應的關鍵字,查找原始数据");
            }
        }
        // 缓存失效查原表开关
        String queryTableSwitch = ConfigUtils.getProperty("cache.invalidation.original.table.search.switch", "true");
        if (productCount == 0 && Boolean.parseBoolean(queryTableSwitch)) {
            //福利視圖没有对应数据，需要查原表
            List<Map<String, Integer>> temporaryList;
            try {
                temporaryList = fookTemporaryProductDao.selectOriginalTemporary(searchTerm, searchTermSimple,
                        searchTermTraditional, searchTermTwTraditional, harmonySwitch);
            } catch (Exception e) {
                log.error("查询异常", e);
                temporaryList = Collections.emptyList();
            }

            if (CollectionUtils.isNotEmpty(temporaryList)) {
                Set<Integer> productIdSet = new HashSet<>();
                Set<Integer> storeIdSet = new HashSet<>();
                for (Map<String, Integer> map : temporaryList) {
                    productIdSet.add(map.get("productId"));
                    storeIdSet.add(map.get("storeId"));
                }
                productCount = productIdSet.size();
                storeCount = storeIdSet.size();
            }
        }
        checkSearchValueResponse.setProductCount(productCount);
        checkSearchValueResponse.setStoresCount(storeCount);
        return checkSearchValueResponse;
    }

    @Override
    public SearchProductResponse searchProduct(SearchProductQuery searchProductQuery) {
        return this.searchProduct(searchProductQuery, false);
    }

    @Override
    public Integer searchProductCount(SearchProductQuery searchProductQuery) {
        return this.fookBusinessProductDao.searchProductCount(searchProductQuery);
    }

    @Override
    public boolean isProductTempHasData() {
        int cnt = this.fookTemporaryProductDao.isProductTempHasData();
        return cnt > 0;
    }

    @Override
    public Integer searchProductTmpCount(SearchProductQuery query) {
        return this.fookTemporaryProductDao.searchProductCount(query);
    }

    @Override
    public SearchProductResponse searchProductTmp(SearchProductQuery query) {
        return this.searchProduct(query, true);
    }


    private SearchProductResponse searchProduct(SearchProductQuery searchProductQuery, boolean isUseTmp) {
        SearchProductResponse result = new SearchProductResponse();
        List<SearchProductResponse.SnatchListItem> items = Lists.newArrayList();
        List<FookTemporaryProduct> products = Lists.newArrayList();
        if (isUseTmp) {
            products = this.fookTemporaryProductDao.searchProduct(searchProductQuery);
        } else {
            try {
                List<FookSearchProduct> searchProducts = this.fookBusinessProductDao.searchProduct(searchProductQuery);
                String language = searchProductQuery.getLanguage();
                List<Integer> searchProductIds = searchProducts.stream().map(FookSearchProduct::getId).collect(Collectors.toList());
                List<FookBusinessProductTranslations> fookBusinessProductTranslations = Lists.newArrayList();
                if (McoinMall.LANG_EN.equals(language)) {
                    //再查翻译表
                    fookBusinessProductTranslations = this.fookBusinessProductTranslationsDao.getTranslationsByIds(searchProductIds, language);
                }
                //取出SearchProduct的的productId和不为空的storeId，变成map
                Map<Integer, Integer> productIdToStoreIdsMap = searchProducts.stream().filter(s -> null != s.getStoreId()).collect(Collectors.toMap(FookSearchProduct::getId, FookSearchProduct::getStoreId));
                List<FookStores> fookStores = Lists.newArrayList();
                if (MapUtils.isNotEmpty(productIdToStoreIdsMap)) {
                    //fookStoresDao查询
                    fookStores = this.fookStoresDao.getStores(new ArrayList<>(productIdToStoreIdsMap.values()));
                }
                for (FookSearchProduct searchProduct : searchProducts) {
                    if (CollectionUtils.isNotEmpty(fookBusinessProductTranslations)) {
                        FookBusinessProductTranslations fookBusinessProductTranslations1 = fookBusinessProductTranslations.stream().filter(t1 -> t1.getBusinessProductId().equals(searchProduct.getId())).findFirst().orElse(null);
                        if (null != fookBusinessProductTranslations1) {
                            searchProduct.setTProductName(fookBusinessProductTranslations1.getTTitle());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(fookStores)) {
                        FookStores fookStore = fookStores.stream().filter(s -> s.getId().equals(searchProduct.getStoreId())).findFirst().orElse(null);
                        if (null != fookStore) {
                            searchProduct.setSName(fookStore.getName());
                            searchProduct.setLongitude(fookStore.getLongitude());
                            searchProduct.setDimension(fookStore.getDimension());
                        }
                    }
                }
                products = BusinessProductMapping.INSTANCE.toTempList(searchProducts);
            } catch (Exception e) {
                log.error("搜索未知异常", e);
            }
        }
        if (CollectionUtils.isNotEmpty(products)) {
            List<Integer> productIds = products.stream().map(FookTemporaryProduct::getId).collect(Collectors.toList());
            List<FookBusinessProduct> bps = this.fookBusinessProductDao.getBusinessProductByIds(productIds);
            List<FookPlatformUsercollection> usercollections = this.fookPlatformUsercollectionDao.getUserEnabledByIds(productIds, searchProductQuery.getUserId());
            List<UsercollectionCnt> countByIds = this.fookPlatformUsercollectionDao.getCountByIds(productIds);
            List<Integer> businessIds = products.stream().map(FookTemporaryProduct::getBusinessid).collect(Collectors.toList());
            List<FookBusiness> businesses = this.fookBusinessDao.getByIds(businessIds);

            items = BusinessProductMapping.INSTANCE.getSearchProductResponse(products, bps, usercollections, countByIds, businesses, searchProductQuery.getLat(), searchProductQuery.getLot(), searchProductQuery.getLanguage(), messageSource);
        }
        result.setSnatchList(items);
        return result;
    }
}
