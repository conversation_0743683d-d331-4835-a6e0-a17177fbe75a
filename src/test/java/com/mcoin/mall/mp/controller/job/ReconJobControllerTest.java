package com.mcoin.mall.mp.controller.job;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.job.ReconCheckPayRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Transactional
class ReconJobControllerTest extends BaseUnitTest {

    @Autowired
    private ReconJobController reconJobController;

    @Test
    void checkPay_ShouldReturnSuccess() {
        // Arrange
        ReconCheckPayRequest request = new ReconCheckPayRequest();
        request.setCurrentTime(new Date());

        // Act
        Response<String> response = reconJobController.checkPay(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertEquals("success", response.getData());
    }
} 