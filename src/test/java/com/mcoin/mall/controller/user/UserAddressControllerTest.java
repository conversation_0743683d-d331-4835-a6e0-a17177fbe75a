package com.mcoin.mall.controller.user;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.client.model.MiniOrdersHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressDetailHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressEditHttpRequest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.UserAddressAddRequest;
import com.mcoin.mall.model.UserAddressListResponse;
import com.mcoin.mall.model.UserAddressLocationRequest;
import com.mcoin.mall.model.UserAddressRequest;
import com.mcoin.mall.model.UserAddressResponse;
import com.mcoin.mall.model.UserAddressSetRequest;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;

/**
 * Integration test for the UserAddressController
 * This test uses the real database from src/test/resources/db/data 
 * and only mocks FeignClient services
 */
class UserAddressControllerTest extends BaseUnitTest {

    @Resource
    private UserAddressController userAddressController;
    


    @BeforeEach
    void setUp() throws BlockException {
        // Create mock user for authentication
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        Mockito.when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
        
        // Mock AmapClient response
        Mockito.when(amapClient.getLocation(any(), any()))
               .thenReturn("{\"status\":\"1\",\"regeocode\":{\"formatted_address\":\"Test Address\"}}");
               
        // Mock MpMcoinMallManagementClient responses
        Mockito.when(managementClient.add(any(MiniUserAddressEditHttpRequest.class)))
               .thenReturn("{\"code\":1,\"data\":{}}");
        Mockito.when(managementClient.edit(any(MiniUserAddressEditHttpRequest.class)))
               .thenReturn("{\"code\":1,\"data\":{}}");
        Mockito.when(managementClient.del(any(MiniUserAddressDetailHttpRequest.class)))
               .thenReturn("{\"code\":1,\"data\":{}}");
        Mockito.when(managementClient.setDefault(any(MiniUserAddressDetailHttpRequest.class)))
               .thenReturn("{\"code\":1,\"data\":{}}");
        Mockito.when(managementClient.detail(any(MiniUserAddressDetailHttpRequest.class)))
               .thenReturn("{\"code\":1,\"data\":{\"id\":\"1\"}}");
        Mockito.when(managementClient.lists(any(MiniOrdersHttpRequest.class)))
               .thenReturn("{\"code\":1,\"data\":{\"list\":[]}}");
    }

    @Test
    void testDetail() {
        UserAddressRequest request = new UserAddressRequest();
        request.setId(1);

        Response<UserAddressResponse> result = userAddressController.detail(request);

        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }

    @Test
    void testAdd() {
        UserAddressAddRequest request = new UserAddressAddRequest();
        request.setId(0);
        request.setContact("Test User");
        request.setMobile("12345678");
        request.setAddress("Test Address");
        request.setProvince("Test Province");
        request.setCity("Test City");
        request.setDistrict("Test District");
        request.setIsDefault(1);

        Response<Object> result = userAddressController.add(request);

        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }

    @Test
    void testEdit() {
        UserAddressAddRequest request = new UserAddressAddRequest();
        request.setId(1);
        request.setContact("Updated User");
        request.setMobile("87654321");
        request.setAddress("Updated Address");
        request.setProvince("Updated Province");
        request.setCity("Updated City");
        request.setDistrict("Updated District");
        request.setIsDefault(1);

        Response<Object> result = userAddressController.add(request);

        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }

    @Test
    void testList() {
        Response<UserAddressListResponse> result = userAddressController.list();

        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }

    @Test
    void testSetDefault() {
        UserAddressSetRequest request = new UserAddressSetRequest();
        request.setId(1);

        Response<Object> result = userAddressController.setDefault(request);

        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }

    @Test
    void testDel() {
        UserAddressSetRequest request = new UserAddressSetRequest();
        request.setId(1);

        Response<Object> result = userAddressController.del(request);

        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }

    @Test
    void testGeocoderCoordinate() {
        UserAddressLocationRequest request = new UserAddressLocationRequest();
        request.setLocation("113.543873,22.188119");

        Response<Map<String, Object>> result = userAddressController.geocoderCoordinate(request);

        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
    }
} 