package com.mcoin.mall.component;

import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

@Component
public class ClasspathResourceLoader {

    private final ResourceLoader resourceLoader;

    public ClasspathResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    public Resource getResource(String resourceName) {
        return resourceLoader.getResource("classpath:" + resourceName);
    }
}
