<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformSuggestDao">
    <select id="getPopularSearches" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM fook_platform_suggest
        WHERE
            enable = 1
            order by `order` asc,popularity desc,add_time desc
        limit 10
    </select>

    <select id="getIndexPopularSearches" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM fook_platform_suggest
        WHERE
        enable = 1
    </select>

</mapper>