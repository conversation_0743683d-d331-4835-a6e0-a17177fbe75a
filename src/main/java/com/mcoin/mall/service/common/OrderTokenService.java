package com.mcoin.mall.service.common;

import com.mcoin.mall.constant.OrderTokenStatus;

public interface OrderTokenService {

    String createOrderToken(Integer userId);

    boolean removeOrderToken(Integer userId);

    /**
     * 验证orderToken
     * @param userId 用户ID
     * @param token
     * @return OrderTokenStatus
     */
    OrderTokenStatus checkOrderToken(Integer userId, String token);
}
