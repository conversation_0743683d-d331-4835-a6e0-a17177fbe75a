<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportBusinessDataDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportBusinessData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="report_business_id" jdbcType="INTEGER" property="reportBusinessId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="settlement" jdbcType="DECIMAL" property="settlement" />
    <result column="waitsettlement" jdbcType="DECIMAL" property="waitsettlement" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="settlement_accounting" jdbcType="DECIMAL" property="settlementAccounting" />
    <result column="pending_accounting" jdbcType="DECIMAL" property="pendingAccounting" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="settlementtime" jdbcType="TIMESTAMP" property="settlementtime" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, report_business_id, business_id, order_amount, settlement, waitsettlement, commission, 
    settlement_accounting, pending_accounting, score, mpayintegral, total_amount, settlementtime, 
    subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_business_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_business_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportBusinessData">
    insert into fook_report_business_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportBusinessId != null">
        report_business_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="settlement != null">
        settlement,
      </if>
      <if test="waitsettlement != null">
        waitsettlement,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="settlementAccounting != null">
        settlement_accounting,
      </if>
      <if test="pendingAccounting != null">
        pending_accounting,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="settlementtime != null">
        settlementtime,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="reportBusinessId != null">
        #{reportBusinessId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlement != null">
        #{settlement,jdbcType=DECIMAL},
      </if>
      <if test="waitsettlement != null">
        #{waitsettlement,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="settlementAccounting != null">
        #{settlementAccounting,jdbcType=DECIMAL},
      </if>
      <if test="pendingAccounting != null">
        #{pendingAccounting,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementtime != null">
        #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportBusinessData">
    update fook_report_business_data
    <set>
      <if test="reportBusinessId != null">
        report_business_id = #{reportBusinessId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlement != null">
        settlement = #{settlement,jdbcType=DECIMAL},
      </if>
      <if test="waitsettlement != null">
        waitsettlement = #{waitsettlement,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="settlementAccounting != null">
        settlement_accounting = #{settlementAccounting,jdbcType=DECIMAL},
      </if>
      <if test="pendingAccounting != null">
        pending_accounting = #{pendingAccounting,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementtime != null">
        settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportBusinessData">
    update fook_report_business_data
    set report_business_id = #{reportBusinessId,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      settlement = #{settlement,jdbcType=DECIMAL},
      waitsettlement = #{waitsettlement,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      settlement_accounting = #{settlementAccounting,jdbcType=DECIMAL},
      pending_accounting = #{pendingAccounting,jdbcType=DECIMAL},
      score = #{score,jdbcType=DECIMAL},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>