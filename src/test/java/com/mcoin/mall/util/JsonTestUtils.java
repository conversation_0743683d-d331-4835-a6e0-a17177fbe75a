package com.mcoin.mall.util;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

public class JsonTestUtils {
    
    public static <T> T getObjectFromJson(String fileName, String caseName, Class<T> clazz) {
        String resource = ResourceUtil.readUtf8Str(fileName);
        JSONObject jsonObject = JSONUtil.parseObj(resource);
        JSONObject allPassObject = jsonObject.getJSONObject(caseName);
        return allPassObject.toBean(clazz);
    }

    public static <T> T getObjectFromJson(String fileName, String caseName, TypeReference<T> typeReference) {
        String resource = ResourceUtil.readUtf8Str(fileName);
        JSONObject jsonObject = JSONUtil.parseObj(resource);
        Object allPassObject = jsonObject.get(caseName);
        return JSONUtil.toBean(allPassObject.toString(), typeReference, true);
    }

    public static String getStringFromJson(String fileName, String caseName) {
        String resource = ResourceUtil.readUtf8Str(fileName);
        JSONObject jsonObject = JSONUtil.parseObj(resource);
        JSONObject allPassObject = jsonObject.getJSONObject(caseName);
        return JSONUtil.toJsonStr(allPassObject);
    }
}