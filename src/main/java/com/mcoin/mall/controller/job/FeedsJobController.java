package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.UpdateRecommendCacheRequest;
import com.mcoin.mall.service.job.FeedsJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/feeds")
@Slf4j
public class FeedsJobController {

    @Resource
    private FeedsJobService feedsJobService;
    @Resource
    private RetryTemplate retryTemplate;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 更新“推荐”tab的缓存数据
     */
    @PostMapping(value = "/updateRecommendCache")
    public Response<String> updateRecommendCache(@Valid UpdateRecommendCacheRequest request) {
        // 异步执行，解决响应时间过长问题
        taskExecutor.execute(()->{
            try {
                retryTemplate.execute((e)-> {
                    feedsJobService.doUpdateRecommendCache(request);
                    return null;
                });
            } catch (Exception e) {
                log.error("更新推荐tab缓存失败", e);
            }
        });
        return Responses.ok("success");
    }
}
