package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 MpayAuthJobController
 * 使用真实服务和数据库，测试支付认证相关功能
 */
class MpayAuthJobControllerTest extends BaseUnitTest {

    @Resource
    private MpayAuthJobController mpayAuthJobController;

    @Test
    void testRefreshToken() {
        // 直接调用控制器方法
        Response<String> response = mpayAuthJobController.refreshToken();
        
        // 验证响应结构
        assertNotNull(response);
    }
} 