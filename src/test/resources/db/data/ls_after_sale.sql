INSERT INTO `ls_after_sale` (`id`, `sn`, `user_id`, `order_id`, `order_goods_id`, `refund_reason`, `refund_remark`, `refund_image`, `refund_type`, `refund_method`, `refund_total_amount`, `refund_total_integral_amount`, `refund_total_integral`, `refund_way`, `refund_status`, `express_name`, `invoice_no`, `express_remark`, `express_image`, `express_time`, `confirm_take_time`, `status`, `sub_status`, `receipt_status`, `audit_time`, `admin_id`, `admin_remark`, `voucher`, `create_time`, `update_time`, `delete_time`, `sid`, `last_agree_time`) VALUES (1, '202502181642551511', 1, 1534, 1562, '做工粗糙/有瑕疵', '123', '', 2, 2, 19.50, 0.50, 150, NULL, 1, '还是恐惧的回复', '121323', '', '', 1739868300, NULL, 1, 17, 0, NULL, 1, '', '', 1739868175, 1739868300, NULL, 65, '2025-02-18 16:43:04');
-- 插入售后记录
INSERT INTO `ls_after_sale` (
    `id`, `sn`, `user_id`, `order_id`, `refund_type`, `refund_method`,
    `refund_total_amount`, `refund_total_integral`, `refund_total_integral_amount`,
    `status`, `sub_status`, `refund_status`, `sid`, `create_time`, `update_time`
) VALUES (
             11, 'AS001', 1, 11, 2, 1,
             100.00, 1000, 10.00,
             1, 15, 1, 65, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
         );
