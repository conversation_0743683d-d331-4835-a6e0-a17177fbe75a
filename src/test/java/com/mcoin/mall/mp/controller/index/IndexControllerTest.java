package com.mcoin.mall.mp.controller.index;

import com.alibaba.fastjson.JSONObject;
import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.service.address.UserAddressService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@Transactional
public class IndexControllerTest extends BaseUnitTest {

    @Autowired
    private IndexController indexController;

    @Autowired
    private UserAddressService userAddressService;

    @BeforeEach
    public void setUp() {
        // 不需要特殊的设置
    }

    @Test
    public void geocoderCoordinate_ShouldReturnValidCoordinate_WhenLocationIsValid() {
        // 准备测试数据
        String location = "北京市朝阳区";

        // 执行请求
        Response<JSONObject> response = indexController.geocoderCoordinate(location);

        // 验证响应
        assertNotNull(response);
        assertEquals(1, response.getCode());
        assertNotNull(response.getData());

        JSONObject data = response.getData();
        assertEquals(data.get("status"),"1");
    }

    @Test
    public void geocoderCoordinate_ShouldThrowException_WhenLocationIsNull() {
        // 执行请求并验证异常
        Response<JSONObject> jsonObjectResponse = indexController.geocoderCoordinate(null);

        assertEquals(jsonObjectResponse.getCode(),1);
    }

    @Test
    public void geocoderCoordinate_ShouldHandleInvalidLocation() {
        // 准备测试数据
        String location = "Invalid Location 123456789";

        // 执行请求
        Response<JSONObject> response = indexController.geocoderCoordinate(location);

        // 验证响应
        assertNotNull(response);
        assertEquals(1, response.getCode());
        assertNotNull(response.getData());

        JSONObject data = response.getData();
        assertNull(data.get("latitude"));
        assertNull(data.get("longitude"));
        assertNull(data.get("address"));
    }
} 