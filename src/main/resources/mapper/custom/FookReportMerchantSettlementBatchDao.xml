<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMerchantSettlementBatchDao">


  <sql id="Settlement_Where">
    where start_time = #{startTime,jdbcType=TIMESTAMP}
    and end_time = #{endTime,jdbcType=TIMESTAMP}
    and seller_id = #{businessId,jdbcType=INTEGER}
    and is_a = #{isAlipayPlus,jdbcType=INTEGER}
  </sql>

  <select id="sumSettlementAmount" resultType="com.mcoin.mall.bo.SettlementAmountBo">
    select  SUM(bill_amount) as totalAmount,
            SUM(settlement_amount) as totalSettlement,
            SUM(commission) as totalCommission,
            SUM(momecoins) as totalMomecoins
    from fook_report_merchant_settlement_batch
    <include refid="Settlement_Where"/>
  </select>

  <select id="sumSettlementAmountGroupByStore" resultType="com.mcoin.mall.bo.StoreSettlementAmountBo">
    select  store_id as storeId,
            SUM(bill_amount) as totalAmount,
            SUM(settlement_amount) as totalSettlement,
            SUM(commission) as totalCommission,
            SUM(momecoins) as totalMomecoins
    from fook_report_merchant_settlement_batch
    <include refid="Settlement_Where"/>
    group by store_id
  </select>
</mapper>