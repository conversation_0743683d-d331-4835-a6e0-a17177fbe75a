<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMiniOrderSyncLogDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMiniOrderSyncLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mini_sn" jdbcType="VARCHAR" property="miniSn" />
    <result column="mini_update_time" jdbcType="TIMESTAMP" property="miniUpdateTime" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mini_sn, mini_update_time, order_id, created_at, update_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_mini_order_sync_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_mini_order_sync_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMiniOrderSyncLog">
    insert into fook_mini_order_sync_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="miniSn != null">
        mini_sn,
      </if>
      <if test="miniUpdateTime != null">
        mini_update_time,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="miniSn != null">
        #{miniSn,jdbcType=VARCHAR},
      </if>
      <if test="miniUpdateTime != null">
        #{miniUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMiniOrderSyncLog">
    update fook_mini_order_sync_log
    <set>
      <if test="miniSn != null">
        mini_sn = #{miniSn,jdbcType=VARCHAR},
      </if>
      <if test="miniUpdateTime != null">
        mini_update_time = #{miniUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMiniOrderSyncLog">
    update fook_mini_order_sync_log
    set mini_sn = #{miniSn,jdbcType=VARCHAR},
      mini_update_time = #{miniUpdateTime,jdbcType=TIMESTAMP},
      order_id = #{orderId,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      update_at = #{updateAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>