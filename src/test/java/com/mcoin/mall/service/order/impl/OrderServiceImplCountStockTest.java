package com.mcoin.mall.service.order.impl;

import cn.hutool.core.lang.TypeReference;
import com.mcoin.mall.bean.FookProductStockLog;
import com.mcoin.mall.dao.FookProductStockLogDao;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class OrderServiceImplCountStockTest {

    @Mock
    FookProductStockLogDao fookProductStockLogDao;
    
    @InjectMocks
    OrderServiceImpl orderServiceImpl;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCountStockWithMixedStockLogs() {
        // given
        int productId = 3838;

        when(fookProductStockLogDao.selectList(any())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookProductStockLog.json", "array", 
                        new TypeReference<List<FookProductStockLog>>() {})
        );

        // when
        int result = orderServiceImpl.countStock(productId);

        // then
        assertEquals(1101, result);
    }

    @Test
    void testCountStockWithSingleStockLogs() {
        // given
        int productId = 3838;

        when(fookProductStockLogDao.selectList(any())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/fookProductStockLog.json", 
                        "singleCode", FookProductStockLog.class))
        );

        // when
        int result = orderServiceImpl.countStock(productId);

        // then
        assertEquals(101, result);
    }
} 