package com.mcoin.mall.mp.controller.management;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.request.ProductUpdateRequest;

/**
 * <AUTHOR>
 * @date 2025/3/31
 */
public class McoinProductUpdateControllerTest extends BaseUnitTest {

    @Autowired
    private McoinProductUpdateController mcoinProductUpdateController;


    @Test
    void productUpdate() {
        // Arrange
        ProductUpdateRequest request = new ProductUpdateRequest();
        List<String> uuids = new ArrayList<>();
        uuids.add("testDelete");
        request.setStatus(1);
        request.setUuids(uuids);

        // Act
        Response<Integer> response = mcoinProductUpdateController.updateProductStatus(
                request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
    }

}
