-- Clear existing test data if any
DELETE FROM fook_platform_usercollection WHERE userid = 99999;
DELETE FROM fook_business_product WHERE id BETWEEN 30001 AND 30005;
DELETE FROM fook_business_store_product WHERE productid BETWEEN 30001 AND 30005;
DELETE FROM fook_stores WHERE id BETWEEN 30001 AND 30005;
DELETE FROM fook_business WHERE id BETWEEN 30001 AND 30005;

-- Insert test businesses
INSERT INTO fook_business (id, name, system_type, enbale, created_at, updated_at)
VALUES
(30001, 'Collection Test Business 1', 1, 1, NOW(), NOW()),
(30002, 'Collection Test Business 2', 1, 1, NOW(), NOW());

-- Insert test stores
INSERT INTO fook_stores (id, business_id, name, enable, created_at, updated_at)
VALUES
(30001, 30001, 'Collection Test Store 1', 1, NOW(), NOW()),
(30002, 30001, 'Collection Test Store 2', 1, NOW(), NOW()),
(30003, 30002, 'Collection Test Store 3', 1, NOW(), NOW());

-- Insert test products in fook_business_product
INSERT INTO fook_business_product (id, areaid, `type`, businessid, img, zip_img, imgs, zip_imgs, title, `desc`, price, retail_price,
                                  point_ratio, stock, history_stock, sales, actual_sales, limited_number, is_allow_refund, details, tnc, score,
                                  comment_number, shelf_status, view_number, buy_start_time, buy_end_time, vaild_mode, vaild_start_time, vaild_end_time,
                                  day_number, is_weekend, no_useday, use_start_time, use_end_time, is_vacation, fee_rate, created_at, updated_at,
                                  `enable`, is_hot, momecoins_option, integral_option, threshord, is_redeem, is_momecoin, is_great, collect_times,
                                  isexport, receive_method, is_mpay_exchange, is_mcoin_open, is_mpay_open, user_limited_number, orderby, limited_offer,
                                  snap_up, snap_up_index, platform_type, member_integral, location, coupon_id, shop_id, coupon_type, maximum_points,
                                  istemporary, only_point, is_settlement, img_en, mpay_coupons_code_id, only_money, relation_sales, product_ids,
                                  trans_coupon_type, meet_money, dec_money, discount, max_discount_money, stock_a, is_a_open, third_party_settlement_price,
                                  a_fee_type, min_point, href_url, goods_id, category_path, subsidy_amount, seckill_img, lock_stock)
VALUES
-- Products for collection test
(30001, 1, 2, 30001, 'image/test/collection1.jpg', 'image/test/zip_collection1.jpg', NULL, NULL, 'Collection Test Product 1', 'Test product description 1', 10.00, 20.00,
 300, 100, 100, 0, 0, 10, 1, NULL, 'Terms and conditions for product 1', 5.0,
 0, 1, 0, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 NULL, 1, NULL, NULL, NULL, 1, 0.00, '2023-01-01 00:00:00', '2023-01-01 00:00:00',
 1, 1, '0', '0', NULL, 0, 0, 1, 5,
 1, NULL, NULL, 1, 1, NULL, 0, 0,
 0, 0, 1, 0.00, NULL, NULL, NULL, 1, NULL,
 NULL, 0, 1, NULL, NULL, 0, 0, NULL,
 0, 0.0, 0.0, 0.00, 0.0, 0, 0, 0.00,
 0, 0, NULL, NULL, NULL, 0.00, NULL, 0),
(30002, 1, 2, 30001, 'image/test/collection2.jpg', 'image/test/zip_collection2.jpg', NULL, NULL, 'Collection Test Product 2', 'Test product description 2', 15.00, 25.00,
 300, 100, 100, 0, 0, 10, 1, NULL, 'Terms and conditions for product 2', 5.0,
 0, 1, 0, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 NULL, 1, NULL, NULL, NULL, 1, 0.00, '2023-01-01 00:00:00', '2023-01-01 00:00:00',
 1, 1, '0', '0', NULL, 0, 0, 0, 3,
 1, NULL, NULL, 1, 1, NULL, 0, 0,
 0, 0, 1, 0.00, NULL, NULL, NULL, 1, NULL,
 NULL, 0, 1, NULL, NULL, 0, 0, NULL,
 0, 0.0, 0.0, 0.00, 0.0, 0, 0, 0.00,
 0, 0, NULL, NULL, NULL, 0.00, NULL, 0),
(30003, 1, 2, 30002, 'image/test/collection3.jpg', 'image/test/zip_collection3.jpg', NULL, NULL, 'Collection Test Product 3', 'Test product description 3', 20.00, 30.00,
 300, 100, 100, 0, 0, 10, 1, NULL, 'Terms and conditions for product 3', 5.0,
 0, 1, 0, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 NULL, 1, NULL, NULL, NULL, 1, 0.00, '2023-01-01 00:00:00', '2023-01-01 00:00:00',
 1, 0, '0', '0', NULL, 0, 0, 1, 2,
 1, NULL, NULL, 1, 1, NULL, 0, 0,
 0, 0, 1, 0.00, NULL, NULL, NULL, 1, NULL,
 NULL, 0, 1, NULL, NULL, 0, 0, NULL,
 0, 0.0, 0.0, 0.00, 0.0, 0, 0, 0.00,
 0, 0, NULL, NULL, NULL, 0.00, NULL, 0);

-- Associate products with stores
INSERT INTO fook_business_store_product (productid, storeid, type, businessid)
VALUES
(30001, 30001, 1, 30001),
(30002, 30002, 1, 30001),
(30003, 30003, 1, 30002);

-- Insert user collections for test user
INSERT INTO fook_platform_usercollection (userid, `type`, collectionid, create_time, enable)
VALUES
(99999, 2, 30001, NOW(), 1),
(99999, 2, 30002, NOW(), 1);
