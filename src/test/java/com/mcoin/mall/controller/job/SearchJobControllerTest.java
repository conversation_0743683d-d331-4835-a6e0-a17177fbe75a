package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookTemporaryProductDao;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

/**
 * 集成测试 SearchJobController
 * 使用真实服务和数据库，测试搜索任务相关功能
 */
class SearchJobControllerTest extends BaseUnitTest {

    @Resource
    private SearchJobController searchJobController;

    @SpyBean
    private ContextHolder contextHolder;

    @Resource
    private FookTemporaryProductDao fookTemporaryProductDao;

    private static final int TEST_USER_ID = 99999;

    @BeforeEach
    void setUp() {
        // 模拟Locale
        doReturn(Locale.CHINESE).when(contextHolder).getLocale();

        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");

        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();

        // 加载测试数据
        try {
            // 加载SQL文件，包含清理和初始化测试数据
            DB.getDB().source("db/tempData/test_search_job_data.sql", "fooku");
        } catch (Exception e) {
            throw new RuntimeException("Failed to load test data for SearchJobController tests", e);
        }
    }

    @Test
    void testCreateViewProduct() {
        // 直接调用控制器方法
        Response<String> response = searchJobController.createViewProduct();

        // 验证响应结构
        assertNotNull(response);

        // 验证数据是否成功插入到临时表中
        // 搜索包含"Search Job Test"的产品
        List<Integer> searchResults = fookTemporaryProductDao.selectSearchTerm(
            "Search Job Test", "Search Job Test", "Search Job Test", "Search Job Test", false);

        // 验证是否找到了测试数据
        assertNotNull(searchResults, "Search results should not be null");
        assertTrue(searchResults.size() > 0, "Should find at least one product with 'Search Job Test' in the title");

        // 验证是否找到了我们插入的特定产品ID
        boolean foundTestProduct = false;
        for (Integer productId : searchResults) {
            if (productId >= 20001 && productId <= 20005) {
                foundTestProduct = true;
                break;
            }
        }

        assertTrue(foundTestProduct, "Should find at least one of our test products (ID 20001-20005)");
    }
}