<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.SettingsDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.Settings">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="key" jdbcType="VARCHAR" property="key" />
    <result column="display_name" jdbcType="VARCHAR" property="displayName" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="order" jdbcType="INTEGER" property="order" />
    <result column="group" jdbcType="VARCHAR" property="group" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.Settings">
    <result column="value" jdbcType="LONGVARCHAR" property="value" />
    <result column="details" jdbcType="LONGVARCHAR" property="details" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `key`, display_name, `type`, `order`, `group`
  </sql>
  <sql id="Blob_Column_List">
    `value`, details
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from settings
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settings
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.Settings">
    insert into settings
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="key != null">
        `key`,
      </if>
      <if test="displayName != null">
        display_name,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="order != null">
        `order`,
      </if>
      <if test="group != null">
        `group`,
      </if>
      <if test="value != null">
        `value`,
      </if>
      <if test="details != null">
        details,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null">
        #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="order != null">
        #{order,jdbcType=INTEGER},
      </if>
      <if test="group != null">
        #{group,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=LONGVARCHAR},
      </if>
      <if test="details != null">
        #{details,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.Settings">
    update settings
    <set>
      <if test="key != null">
        `key` = #{key,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null">
        display_name = #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="order != null">
        `order` = #{order,jdbcType=INTEGER},
      </if>
      <if test="group != null">
        `group` = #{group,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=LONGVARCHAR},
      </if>
      <if test="details != null">
        details = #{details,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.Settings">
    update settings
    set `key` = #{key,jdbcType=VARCHAR},
      display_name = #{displayName,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `order` = #{order,jdbcType=INTEGER},
      `group` = #{group,jdbcType=VARCHAR},
      `value` = #{value,jdbcType=LONGVARCHAR},
      details = #{details,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.Settings">
    update settings
    set `key` = #{key,jdbcType=VARCHAR},
      display_name = #{displayName,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `order` = #{order,jdbcType=INTEGER},
      `group` = #{group,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>