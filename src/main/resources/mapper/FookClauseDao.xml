<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookClauseDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookClause">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="adminid" jdbcType="INTEGER" property="adminid" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="systemid" jdbcType="TINYINT" property="systemid" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.FookClause">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="content_en" jdbcType="LONGVARCHAR" property="contentEn" />
  </resultMap>
  <sql id="Base_Column_List">
    id, adminid, `enable`, created_at, updated_at, systemid
  </sql>
  <sql id="Blob_Column_List">
    content, content_en
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fook_clause
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_clause
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookClause">
    insert into fook_clause
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adminid != null">
        adminid,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="systemid != null">
        systemid,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="contentEn != null">
        content_en,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="adminid != null">
        #{adminid,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="systemid != null">
        #{systemid,jdbcType=TINYINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="contentEn != null">
        #{contentEn,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookClause">
    update fook_clause
    <set>
      <if test="adminid != null">
        adminid = #{adminid,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="systemid != null">
        systemid = #{systemid,jdbcType=TINYINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="contentEn != null">
        content_en = #{contentEn,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.FookClause">
    update fook_clause
    set adminid = #{adminid,jdbcType=INTEGER},
      `enable` = #{enable,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      systemid = #{systemid,jdbcType=TINYINT},
      content = #{content,jdbcType=LONGVARCHAR},
      content_en = #{contentEn,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookClause">
    update fook_clause
    set adminid = #{adminid,jdbcType=INTEGER},
      `enable` = #{enable,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      systemid = #{systemid,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>