package com.mcoin.mall.service.common.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.dao.FookBusinessProductDao;

@ExtendWith(MockitoExtension.class)
class StockServiceImplTest {

    @Mock
    private FookBusinessProductDao productDao;

    @InjectMocks
    private StockServiceImpl stockService;

    private FookBusinessProduct product;

    @BeforeEach
    void setUp() {
        product = new FookBusinessProduct();
        product.setStock(10);
        product.setLockStock(5);
    }

    @Test
    void testIsSlodOut_WhenProductIsNull() {
        assertTrue(stockService.isSlodOut((FookBusinessProduct) null));
    }

    @Test
    void testIsSlodOut_WhenStockIsNull() {
        product.setStock(null);
        assertTrue(stockService.isSlodOut(product));
    }

    @Test
    void testIsSlodOut_WhenLockStockIsNull() {
        product.setLockStock(null);
        assertFalse(stockService.isSlodOut(product));
    }

    @Test
    void testIsSlodOut_WhenBothStocksAreNull() {
        product.setStock(null);
        product.setLockStock(null);
        assertTrue(stockService.isSlodOut(product));
    }

    @Test
    void testIsSlodOut_WhenHasSufficientStock() {
        assertFalse(stockService.isSlodOut(product));
    }

    @Test
    void testIsSlodOut_WhenStockIsZero() {
        product.setStock(0);
        assertTrue(stockService.isSlodOut(product));
    }

    @Test
    void testIsSlodOut_WhenOutOfStock() {
        product.setStock(-5);
        assertTrue(stockService.isSlodOut(product));
    }

    @Test
    void testIsSlodOut_ByProductId_WhenProductIdIsNull() {
        assertTrue(stockService.isSlodOut((Integer) null));
    }

    @Test
    void testIsSlodOut_ByProductId_WhenProductNotFound() {
        when(productDao.selectByPrimaryKey(1)).thenReturn(null);
        assertTrue(stockService.isSlodOut(1));
    }

    @Test
    void testIsSlodOut_ByProductId_WhenProductExists() {
        when(productDao.selectByPrimaryKey(1)).thenReturn(product);
        assertFalse(stockService.isSlodOut(1));
        verify(productDao).selectByPrimaryKey(1);
    }

    @Test
    void testIsSlodOut_ByProductId_WithDifferentStockScenarios() {
        // 测试库存充足
        product.setStock(10);
        when(productDao.selectByPrimaryKey(1)).thenReturn(product);
        assertFalse(stockService.isSlodOut(1));

        // 测试库存不足
        product.setStock(0);
        product.setLockStock(0);
        when(productDao.selectByPrimaryKey(2)).thenReturn(product);
        assertTrue(stockService.isSlodOut(2));
    }
}