package com.mcoin.mall.security;

import com.alibaba.fastjson.JSON;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

import static com.mcoin.mall.util.McoinMall.NOT_LOGIN;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtAuthenticationEntryPoint  implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {

        log.info("认证异常:{}", authException.getMessage());
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        try (PrintWriter out = response.getWriter()){
            BusinessException exception = new BusinessException(Response.Code.UNAUTHORIZED, NOT_LOGIN);
            String data = JSON.toJSONString(Responses.error(exception));
            log.info("uri:{}, 认证异常返回报文：{}", request.getRequestURI(), data);
            out.print(data);
            out.flush();
        }
    }
}
