<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookOrderSettlementDataDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookOrderSettlementData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areaid" jdbcType="INTEGER" property="areaid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="sellerid" jdbcType="INTEGER" property="sellerid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="payment_type" jdbcType="INTEGER" property="paymentType" />
    <result column="payment_transaction" jdbcType="VARCHAR" property="paymentTransaction" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="order_transaction" jdbcType="VARCHAR" property="orderTransaction" />
    <result column="mpayintegral" jdbcType="INTEGER" property="mpayintegral" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="total_amount_currency" jdbcType="VARCHAR" property="totalAmountCurrency" />
    <result column="transaction_amount" jdbcType="DECIMAL" property="transactionAmount" />
    <result column="transaction_amount_currency" jdbcType="VARCHAR" property="transactionAmountCurrency" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="payment_amount_currency" jdbcType="VARCHAR" property="paymentAmountCurrency" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="bank_charges" jdbcType="DECIMAL" property="bankCharges" />
    <result column="bank_settlement_amount" jdbcType="DECIMAL" property="bankSettlementAmount" />
    <result column="refundid" jdbcType="INTEGER" property="refundid" />
    <result column="ordercode_status" jdbcType="INTEGER" property="ordercodeStatus" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="is_mpay" jdbcType="VARCHAR" property="isMpay" />
    <result column="pay_tradeno" jdbcType="VARCHAR" property="payTradeno" />
    <result column="score_tradeno" jdbcType="VARCHAR" property="scoreTradeno" />
    <result column="order_settlement_id" jdbcType="INTEGER" property="orderSettlementId" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="pending_amount" jdbcType="DECIMAL" property="pendingAmount" />
    <result column="settlement_accounting" jdbcType="DECIMAL" property="settlementAccounting" />
    <result column="pending_accounting" jdbcType="DECIMAL" property="pendingAccounting" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="approval_amount" jdbcType="DECIMAL" property="approvalAmount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="point_ratio" jdbcType="INTEGER" property="pointRatio" />
    <result column="total_merch_settle_amount" jdbcType="DECIMAL" property="totalMerchSettleAmount" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, areaid, userid, sellerid, `type`, order_no, create_time, payment_type, payment_transaction, 
    `status`, complete_time, order_transaction, mpayintegral, score, platform, order_amount, 
    currency, total_amount, total_amount_currency, transaction_amount, transaction_amount_currency, 
    payment_amount, payment_amount_currency, payment_time, bank_charges, bank_settlement_amount, 
    refundid, ordercode_status, refund_status, is_mpay, pay_tradeno, score_tradeno, order_settlement_id, 
    order_id, settlement_amount, pending_amount, settlement_accounting, pending_accounting, 
    refund_amount, approval_amount, commission, point_ratio, total_merch_settle_amount, 
    subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_order_settlement_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_order_settlement_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookOrderSettlementData">
    insert into fook_order_settlement_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areaid != null">
        areaid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="sellerid != null">
        sellerid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="paymentTransaction != null">
        payment_transaction,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="orderTransaction != null">
        order_transaction,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="totalAmountCurrency != null">
        total_amount_currency,
      </if>
      <if test="transactionAmount != null">
        transaction_amount,
      </if>
      <if test="transactionAmountCurrency != null">
        transaction_amount_currency,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="paymentAmountCurrency != null">
        payment_amount_currency,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="bankCharges != null">
        bank_charges,
      </if>
      <if test="bankSettlementAmount != null">
        bank_settlement_amount,
      </if>
      <if test="refundid != null">
        refundid,
      </if>
      <if test="ordercodeStatus != null">
        ordercode_status,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="isMpay != null">
        is_mpay,
      </if>
      <if test="payTradeno != null">
        pay_tradeno,
      </if>
      <if test="scoreTradeno != null">
        score_tradeno,
      </if>
      <if test="orderSettlementId != null">
        order_settlement_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="pendingAmount != null">
        pending_amount,
      </if>
      <if test="settlementAccounting != null">
        settlement_accounting,
      </if>
      <if test="pendingAccounting != null">
        pending_accounting,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="approvalAmount != null">
        approval_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="pointRatio != null">
        point_ratio,
      </if>
      <if test="totalMerchSettleAmount != null">
        total_merch_settle_amount,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areaid != null">
        #{areaid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="sellerid != null">
        #{sellerid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentTransaction != null">
        #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderTransaction != null">
        #{orderTransaction,jdbcType=VARCHAR},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountCurrency != null">
        #{totalAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="transactionAmount != null">
        #{transactionAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionAmountCurrency != null">
        #{transactionAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmountCurrency != null">
        #{paymentAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankCharges != null">
        #{bankCharges,jdbcType=DECIMAL},
      </if>
      <if test="bankSettlementAmount != null">
        #{bankSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundid != null">
        #{refundid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeStatus != null">
        #{ordercodeStatus,jdbcType=INTEGER},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="isMpay != null">
        #{isMpay,jdbcType=VARCHAR},
      </if>
      <if test="payTradeno != null">
        #{payTradeno,jdbcType=VARCHAR},
      </if>
      <if test="scoreTradeno != null">
        #{scoreTradeno,jdbcType=VARCHAR},
      </if>
      <if test="orderSettlementId != null">
        #{orderSettlementId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="pendingAmount != null">
        #{pendingAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAccounting != null">
        #{settlementAccounting,jdbcType=DECIMAL},
      </if>
      <if test="pendingAccounting != null">
        #{pendingAccounting,jdbcType=DECIMAL},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="approvalAmount != null">
        #{approvalAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="totalMerchSettleAmount != null">
        #{totalMerchSettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookOrderSettlementData">
    update fook_order_settlement_data
    <set>
      <if test="areaid != null">
        areaid = #{areaid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="sellerid != null">
        sellerid = #{sellerid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentTransaction != null">
        payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderTransaction != null">
        order_transaction = #{orderTransaction,jdbcType=VARCHAR},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountCurrency != null">
        total_amount_currency = #{totalAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="transactionAmount != null">
        transaction_amount = #{transactionAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionAmountCurrency != null">
        transaction_amount_currency = #{transactionAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmountCurrency != null">
        payment_amount_currency = #{paymentAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankCharges != null">
        bank_charges = #{bankCharges,jdbcType=DECIMAL},
      </if>
      <if test="bankSettlementAmount != null">
        bank_settlement_amount = #{bankSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundid != null">
        refundid = #{refundid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeStatus != null">
        ordercode_status = #{ordercodeStatus,jdbcType=INTEGER},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="isMpay != null">
        is_mpay = #{isMpay,jdbcType=VARCHAR},
      </if>
      <if test="payTradeno != null">
        pay_tradeno = #{payTradeno,jdbcType=VARCHAR},
      </if>
      <if test="scoreTradeno != null">
        score_tradeno = #{scoreTradeno,jdbcType=VARCHAR},
      </if>
      <if test="orderSettlementId != null">
        order_settlement_id = #{orderSettlementId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="pendingAmount != null">
        pending_amount = #{pendingAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAccounting != null">
        settlement_accounting = #{settlementAccounting,jdbcType=DECIMAL},
      </if>
      <if test="pendingAccounting != null">
        pending_accounting = #{pendingAccounting,jdbcType=DECIMAL},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="approvalAmount != null">
        approval_amount = #{approvalAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        point_ratio = #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="totalMerchSettleAmount != null">
        total_merch_settle_amount = #{totalMerchSettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookOrderSettlementData">
    update fook_order_settlement_data
    set areaid = #{areaid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      sellerid = #{sellerid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      payment_type = #{paymentType,jdbcType=INTEGER},
      payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      order_transaction = #{orderTransaction,jdbcType=VARCHAR},
      mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      score = #{score,jdbcType=DECIMAL},
      platform = #{platform,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      total_amount_currency = #{totalAmountCurrency,jdbcType=VARCHAR},
      transaction_amount = #{transactionAmount,jdbcType=DECIMAL},
      transaction_amount_currency = #{transactionAmountCurrency,jdbcType=VARCHAR},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      payment_amount_currency = #{paymentAmountCurrency,jdbcType=VARCHAR},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      bank_charges = #{bankCharges,jdbcType=DECIMAL},
      bank_settlement_amount = #{bankSettlementAmount,jdbcType=DECIMAL},
      refundid = #{refundid,jdbcType=INTEGER},
      ordercode_status = #{ordercodeStatus,jdbcType=INTEGER},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      is_mpay = #{isMpay,jdbcType=VARCHAR},
      pay_tradeno = #{payTradeno,jdbcType=VARCHAR},
      score_tradeno = #{scoreTradeno,jdbcType=VARCHAR},
      order_settlement_id = #{orderSettlementId,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=INTEGER},
      settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      pending_amount = #{pendingAmount,jdbcType=DECIMAL},
      settlement_accounting = #{settlementAccounting,jdbcType=DECIMAL},
      pending_accounting = #{pendingAccounting,jdbcType=DECIMAL},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      approval_amount = #{approvalAmount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      point_ratio = #{pointRatio,jdbcType=INTEGER},
      total_merch_settle_amount = #{totalMerchSettleAmount,jdbcType=DECIMAL},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>