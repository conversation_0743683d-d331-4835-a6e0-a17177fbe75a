<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingWeekDao">
    <select id="selectByTradingId" resultType="com.mcoin.mall.bean.FookTradingWeek">
      select
      IFNULL(c.name, '') AS "businessName",
       a.id, a.product_id, a.product_title, a.pruduct_num, a.order_amount, a.trading_id
      from fook_trading_week  a
      left join fook_business_product b on a.product_id = b.id
      left join fook_business c on b.businessid = c.id
      where trading_id = #{tradingId,jdbcType=INTEGER}
      order by pruduct_num desc
    </select>

</mapper>