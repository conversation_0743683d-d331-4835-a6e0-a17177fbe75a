package com.mcoin.mall.util;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class CipherUtilsTest{

    @Test
    public void testFilterWithNormalMap() {
        Map<String, String> param = new HashMap<>();
        param.put("key1", "value1");
        param.put("key2", "value2");
        param.put("sign", "signValue");
        param.put("sign_type", "RSA");
        param.put("key3", "");
        param.put("key4", null);
    
        Map<String, String> result = CipherUtils.filter(param);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
        assertEquals("null", result.get("key4"));
    }

    @Test
    public void testFilterWithOnlySignKey() {
        Map<String, String> param = new HashMap<>();
        param.put("sign", "signValue");
    
        Map<String, String> result = CipherUtils.filter(param);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testFilterWithOnlySignTypeKey() {
        Map<String, String> param = new HashMap<>();
        param.put("sign_type", "RSA");
    
        Map<String, String> result = CipherUtils.filter(param);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testFilterWithAllKeys() {
        Map<String, String> param = new HashMap<>();
        param.put("key1", "value1");
        param.put("key2", "value2");
        param.put("sign", "signValue");
        param.put("sign_type", "RSA");
        param.put("key3", "");
        param.put("key4", null);
    
        Map<String, String> result = CipherUtils.filter(param);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
        assertEquals("null", result.get("key4"));
    }

    @Test
    public void testFilterWithNullMap() {
        Map<String, ?> param = null;
        Map<String, String> result = CipherUtils.filter(param);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testFilterWithEmptyMap() {
        Map<String, ?> param = new HashMap<>();
        Map<String, String> result = CipherUtils.filter(param);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testCreateLinkStringWithMixedValues() {
        Map<String, String> param = new HashMap<>();
        param.put("key1", "value1");
        param.put("key2", null);
        param.put("key3", "");
    
        String result = CipherUtils.createLinkString(param, false);
        assertNotNull(result);
        assertEquals("key1=value1&key3=", result);
    }

    @Test
    public void testCreateLinkStringWithValueIfWithQuote() {
        Map<String, String> param = new HashMap<>();
        param.put("key1", "value1");
        param.put("key2", "value2");
    
        String result = CipherUtils.createLinkString(param, true);
        assertNotNull(result);
        assertEquals("key1=\"value1\"&key2=\"value2\"", result);
    }

    @Test
    public void testCreateLinkStringWithNormalMap() {
        Map<String, String> param = new HashMap<>();
        param.put("key1", "value1");
        param.put("key2", "value2");
        param.put("sign", "signValue");
        param.put("sign_type", "RSA");
        param.put("key3", "");
        param.put("key4", null);
    
        String result = CipherUtils.createLinkString(param, false);
        assertNotNull(result);
        assertEquals("key1=value1&key2=value2&key3=&sign=signValue&sign_type=RSA", result);
    }

    @Test
    public void testCreateLinkStringWithEmptyMap() {
        Map<String, String> param = new HashMap<>();
    
        String result = CipherUtils.createLinkString(param, false);
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    public void testCreateLinkStringWithNullValues() {
        Map<String, String> param = new HashMap<>();
        param.put("key1", null);
        param.put("key2", null);
    
        String result = CipherUtils.createLinkString(param, false);
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    public void testVerifyMD5_NullCharset() {
        String sign = "536788f4dbdffeecfbb8f350a941eea3";
        String signSource = "testString";
        String charset = null;
    
        boolean result = CipherUtils.verifyMD5(sign, signSource, charset);
        assertFalse(result, "The MD5 signature verification should fail for a null charset.");
    }

    @Test
    public void testVerifyMD5_ValidSignature() {
        String sign = "536788f4dbdffeecfbb8f350a941eea3";
        String signSource = "testString";
        String charset = "UTF-8";
    
        boolean result = CipherUtils.verifyMD5(sign, signSource, charset);
        assertTrue(result, "The MD5 signature verification should pass for a valid signature.");
    }

    @Test
    public void testVerifyMD5_InvalidSignature() {
        String sign = "536788f4dbdffeecfbb8f350a941eea3";
        String signSource = "wrongString";
        String charset = "UTF-8";
    
        boolean result = CipherUtils.verifyMD5(sign, signSource, charset);
        assertFalse(result, "The MD5 signature verification should fail for an invalid signature.");
    }

    @Test
    public void testVerifyMD5_EmptySignSource() {
        String sign = "d41d8cd98f00b204e9800998ecf8427e";
        String signSource = "";
        String charset = "UTF-8";
    
        boolean result = CipherUtils.verifyMD5(sign, signSource, charset);
        assertTrue(result, "The MD5 signature verification should pass for an empty sign source.");
    }

    @Test
    public void testVerifyMD5_NullSignSource() {
        String sign = "d41d8cd98f00b204e9800998ecf8427e";
        String signSource = null;
        String charset = "UTF-8";
    
        boolean result = CipherUtils.verifyMD5(sign, signSource, charset);
        assertFalse(result, "The MD5 signature verification should fail for a null sign source.");
    }

    @Test
    public void testVerifyMD5_DifferentCharset() {
        String sign = "536788f4dbdffeecfbb8f350a941eea3";
        String signSource = "testString";
        String charset = "UTF-8";
    
        boolean result = CipherUtils.verifyMD5(sign, signSource, charset);
        assertTrue(result, "The MD5 signature verification should pass for a different charset.");
    }

    @Test
    public void testVerifyMD5_EmptyCharset() {
        String sign = "536788f4dbdffeecfbb8f350a941eea3";
        String signSource = "testString";
        String charset = "";
    
        boolean result = CipherUtils.verifyMD5(sign, signSource, charset);
        assertFalse(result, "The MD5 signature verification should fail for an empty charset.");
    }

    @Test
    public void testMd5ToByteWithDifferentCharset() {
        String source = "testString";
        String charset = "UTF-16";
        byte[] result = CipherUtils.md5ToByte(source, charset);
        assertNotNull(result);
        assertEquals(16, result.length);
    }

    @Test
    public void testMd5ToByteWithUnsupportedCharset() {
        String source = "testString";
        String charset = "UNSUPPORTED";
        assertThrows(RuntimeException.class, () -> {
            CipherUtils.md5ToByte(source, charset);
        });
    }

    @Test
    public void testMd5ToByteWithValidString() {
        String source = "testString";
        String charset = "UTF-8";
        byte[] result = CipherUtils.md5ToByte(source, charset);
        assertNotNull(result);
        assertEquals(16, result.length);
    }

    @Test
    public void testMd5ToByteWithEmptyString() {
        String source = "";
        String charset = "UTF-8";
        byte[] result = CipherUtils.md5ToByte(source, charset);
        assertNotNull(result);
        assertEquals(16, result.length);
    }

    @Test
    public void testMd5ToByteWithNullString() {
        String source = null;
        String charset = "UTF-8";
        assertThrows(RuntimeException.class, () -> {
            CipherUtils.md5ToByte(source, charset);
        });
    }

}