package com.mcoin.mall.service.business.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookActiveMerchant;
import com.mcoin.mall.bean.FookActiveMerchantData;
import com.mcoin.mall.bean.FookDayDiscount;
import com.mcoin.mall.bean.FookDayGain;
import com.mcoin.mall.bean.FookDayItem;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookActiveMerchantDao;
import com.mcoin.mall.dao.FookActiveMerchantDataDao;
import com.mcoin.mall.dao.FookDayDiscountDao;
import com.mcoin.mall.dao.FookDayGainDao;
import com.mcoin.mall.dao.FookDayItemDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.ActiveMerchantListRequest;
import com.mcoin.mall.model.ActiveMerchantListResponse;
import com.mcoin.mall.model.DaygainListRequest;
import com.mcoin.mall.model.DaygainListResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.ContextUtils;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.OssUtil;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.ArrayList;
import java.util.Locale;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class ActivityServiceImplTest{

    @Mock
    private FookActiveMerchantDataDao fookActiveMerchantDataDao;

    private FookActiveMerchant fookActiveMerchant;

    private IPage<FookActiveMerchantData> ipage;

    @Mock
    private FookActiveMerchantDao fookActiveMerchantDao;

    private IPage<FookDayDiscount> fookDayDiscountPage;

    @Mock
    private ContextHolder contextHolder;

    @Mock
    private FookDayGainDao fookDayGainDao;

    @Mock
    private FookDayItemDao fookDayItemDao;

    @InjectMocks
    private ActivityServiceImpl activityService;

    private DaygainListRequest request;

    private FookDayDiscount fookDayDiscount;

    @Mock
    private MessageSource messageSource;

    @Mock
    private FookDayDiscountDao fookDayDiscountDao;

    private FookDayGain fookDayGain;

    private FookDayItem fookDayItem;

    private MockedStatic<OssUtil> ossUtilMock;

    private MockedStatic<ContextUtils> contextUtilsMock;

    private MockedStatic<ConfigUtils> configUtilsMock;

    @BeforeEach
    public void setUp() {
        ossUtilMock = mockStatic(OssUtil.class);
        contextUtilsMock = mockStatic(ContextUtils.class);
        configUtilsMock = mockStatic(ConfigUtils.class);
        request = new DaygainListRequest();
        request.setId(1);
        request.setPage(1);
    
        fookDayGain = new FookDayGain();
        fookDayGain.setId(1);
        fookDayGain.setStatus(1);
        fookDayGain.setNameEn("Test Name");
        fookDayGain.setImgEn("test.jpg");
    
        fookDayDiscount = new FookDayDiscount();
        fookDayDiscount.setId(1);
        fookDayDiscount.setGainId(1);
        fookDayDiscount.setStatus(1);
        fookDayDiscount.setDiscountEn("10% Off");
    
        fookDayItem = new FookDayItem();
        fookDayItem.setId(1);
        fookDayItem.setDiscountId(1);
        fookDayItem.setStatus(1);
        fookDayItem.setBusinessNameEn("Test Business");
        fookDayItem.setDiscountNameEn("Test Discount");
        fookDayItem.setLogoEn("test.jpg");
    
        fookDayDiscountPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 10);
        fookDayDiscountPage.setTotal(1);
        fookDayDiscountPage.setRecords(new ArrayList<>(Lists.newArrayList(fookDayDiscount)));
    }

    @AfterEach
    public void tearDown() {
        ossUtilMock.close();
        contextUtilsMock.close();
        configUtilsMock.close();
    }

    @Test
    public void testDayGainListSuccess() {
        Locale locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);
        when(fookDayGainDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookDayGain);
        when(fookDayDiscountDao.selectPage(any(IPage.class), any(LambdaQueryWrapper.class))).thenReturn(fookDayDiscountPage);
        when(fookDayItemDao.selectList(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>(Lists.newArrayList(fookDayItem)));
        when(OssUtil.getProductImg(anyString())).thenReturn("test.jpg");
    
        DaygainListResponse response = activityService.dayGainList(request);
    
        assertNotNull(response);
        assertEquals(1, response.getPage().getCurrentPage());
        assertEquals(10, response.getPage().getPerPage());
        assertEquals(1, response.getPage().getTotal());
        assertEquals(1, response.getPage().getLastPage());
        assertNotNull(response.getSnatchList());
        assertEquals(1, response.getSnatchList().size());
    }

    @Test
    public void testDayGainListFookDayGainNotFound() {
        Locale locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);
        when(messageSource.getMessage(eq("message.merchant.nodata"), any(), eq(locale))).thenReturn("No data found");
        when(fookDayGainDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
    
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            activityService.dayGainList(request);
        });
    
        assertEquals("No data found", exception.getMessage());
    }

    @Test
    public void testDayGainListBusinessNotOpen() {
        Locale locale = new Locale("en");
        fookDayGain.setStatus(0);
        when(contextHolder.getLocale()).thenReturn(locale);
        when(messageSource.getMessage(eq("message.merchant.business"), any(), eq(locale))).thenReturn("Business is not open");
        when(fookDayGainDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookDayGain);
    
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            activityService.dayGainList(request);
        });
    
        assertEquals("Business is not open", exception.getMessage());
    }

    @Test
    public void testMerchantListSuccess() {

        fookActiveMerchant = new FookActiveMerchant();
        fookActiveMerchant.setId(1);
        fookActiveMerchant.setStatus(1);
        fookActiveMerchant.setTitleEn("Test Title");
        fookActiveMerchant.setEventEn("Test Event");
    
        ipage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, McoinMall.DEFAULT_PAGE_SIZE);
        ipage.setTotal(1);
        ipage.setRecords(new ArrayList<>());
    
        Locale locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);
        when(fookActiveMerchantDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookActiveMerchant);
        when(fookActiveMerchantDataDao.selectPage(any(IPage.class), any(LambdaQueryWrapper.class))).thenReturn(ipage);
        when(ContextUtils.getBean(ContextHolder.class)).thenReturn(contextHolder);
        when(ConfigUtils.getProperty("API_URL")).thenReturn("http://api.example.com");
        when(messageSource.getMessage(anyString(), any(), any(Locale.class))).thenReturn("Success");

        when(ContextUtils.getBean("messageSource", MessageSource.class)).thenReturn(messageSource);

        ActiveMerchantListRequest request = new ActiveMerchantListRequest();
        request.setPage(1);
        request.setActive_id(1);
        request.setName("Test");
        request.setAddress("Test Address");
        request.setLucky_draw(1);
        request.setVoucher(1);
        request.setEvent("Test Event");
    
        Response<ActiveMerchantListResponse> response = activityService.merchantList(request);
    
        assertNotNull(response);
        assertEquals(Response.Status.SUCCESS.ordinal(), response.getStatus());
        assertEquals("Success", response.getMessage());
    }

    @Test
    public void testMerchantListBusinessException() {

        fookActiveMerchant = new FookActiveMerchant();
        fookActiveMerchant.setId(1);
        fookActiveMerchant.setStatus(1);
        fookActiveMerchant.setTitleEn("Test Title");
        fookActiveMerchant.setEventEn("Test Event");
    
        ipage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, McoinMall.DEFAULT_PAGE_SIZE);
        ipage.setTotal(1);
        ipage.setRecords(new ArrayList<>());
    
        Locale locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);
        when(fookActiveMerchantDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(messageSource.getMessage(anyString(), any(), any(Locale.class))).thenReturn("Merchant not found");
    
        ActiveMerchantListRequest request = new ActiveMerchantListRequest();
        request.setPage(1);
        request.setActive_id(1);
        request.setName("Test");
        request.setAddress("Test Address");
        request.setLucky_draw(1);
        request.setVoucher(0);
        request.setEvent("Test Event");

        when(ContextUtils.getBean(ContextHolder.class)).thenReturn(contextHolder);

        Response<ActiveMerchantListResponse> response = activityService.merchantList(request);

        assertEquals("Merchant not found", response.getMessage());
        assertEquals( Response.Status.FAILED.ordinal(), response.getStatus());
    }

    @Test
    public void testMerchantListBusinessExceptionInvalidParams() {
    
        fookActiveMerchant = new FookActiveMerchant();
        fookActiveMerchant.setId(1);
        fookActiveMerchant.setStatus(1);
        fookActiveMerchant.setTitleEn("Test Title");
        fookActiveMerchant.setEventEn("Test Event");
    
        ipage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, McoinMall.DEFAULT_PAGE_SIZE);
        ipage.setTotal(1);
        ipage.setRecords(new ArrayList<>());
    
        Locale locale = new Locale("en");
        when(contextHolder.getLocale()).thenReturn(locale);
        when(messageSource.getMessage(anyString(), any(), any(Locale.class))).thenReturn("Invalid parameters");
    
        ActiveMerchantListRequest request = new ActiveMerchantListRequest();
        request.setPage(1);
        request.setActive_id(1);
        request.setName("Test");
        request.setAddress("Test Address");
        request.setLucky_draw(2);
        request.setVoucher(0);
        request.setEvent("Test Event");

        Response<ActiveMerchantListResponse> response = activityService.merchantList(request);

        assertEquals("Invalid parameters", response.getMessage());
        assertEquals( Response.Status.FAILED.ordinal(), response.getStatus());
    }

}