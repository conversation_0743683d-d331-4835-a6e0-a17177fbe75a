package com.mcoin.mall.service.pay.impl;

import static com.mcoin.mall.constant.SnapUpEnum.SNAP_UP;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Locale;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;

import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessProductstock;
import com.mcoin.mall.bean.FookMacaupassOrder;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookShowSnapupSession;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.BusinessProductTypeEnum;
import com.mcoin.mall.constant.ShelfStatus;
import com.mcoin.mall.constant.SnapUpEnum;
import com.mcoin.mall.constant.YesNo;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookBusinessProductstockDao;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.dao.FookExternalVcodeDao;
import com.mcoin.mall.dao.FookMacaupassOrderDao;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundDao;
import com.mcoin.mall.dao.FookProductSnappingRecordDao;
import com.mcoin.mall.dao.FookProductStockLogDao;
import com.mcoin.mall.dao.FookShowSnapupSessionDao;
import com.mcoin.mall.dao.FookStoresTranslationsDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.ApplyBuyRequest;
import com.mcoin.mall.model.ApplyBuyResponse;
import com.mcoin.mall.model.CreateOrderRequest;
import com.mcoin.mall.model.CreateOrderResponse;
import com.mcoin.mall.model.GetUserInfoResponse;
import com.mcoin.mall.model.ObtainPayRequest;
import com.mcoin.mall.model.ObtainPayResponse;
import com.mcoin.mall.model.OrderEnvInfo;
import com.mcoin.mall.model.QueryPayResponse;
import com.mcoin.mall.mq.model.OrderCloseMessage;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.auth.MPayAuthService;
import com.mcoin.mall.service.common.AtomicSeqService;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.service.common.OrderTokenService;
import com.mcoin.mall.service.common.StockService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PayServiceImplTest {

    @InjectMocks private PayServiceImpl payService;

    @Mock private MessageSource messageSource;

    @Mock private SettingsDao settingsDao;

    @Mock private FookBusinessDao fookBusinessDao;

    @Mock private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Mock private FookBusinessProductDao fookBusinessProductDao;

    @Mock private ContextHolder contextHolder;
    MockedStatic<ConfigUtils> configUtilsStatic;

    @Mock
    private FookShowSnapupSessionDao fookShowSnapupSessionDao;


    @Mock private FookPlatformOrderDao fookPlatformOrderDao;
    @Mock private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Mock private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;
    @Mock private FookMacaupassOrderDao fookMacaupassOrderDao;
    @Mock private FookBusinessProductstockDao fookBusinessProductstockDao;
    @Mock private FookMacaupassUserDao fookMacaupassUserDao;
    @Mock private FookProductStockLogDao fookProductStockLogDao;
    @Mock private FookExternalVcodeDao fookExternalVcodeDao;
    @Mock private FookProductSnappingRecordDao fookProductSnappingRecordDao;
    @Mock private FookStoresTranslationsDao fookStoresTranslationsDao;
    @Mock private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;
    @Mock private MPayAuthService mPayAuthService;
    @Mock private AtomicSeqService atomicSeqService;
    @Mock private ApplicationContext applicationContext;
    @Mock private CollectService collectService;
    @Mock private OrderTokenService orderTokenService;

    @Mock private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;
    @Mock private StockService stockService;

    private static final int PRODUCT_ID = 1;

    private static final int BUSINESS_ID = 1;

    private static final int USER_ID = 1;

    private static final String TITLE = "Product Title";

    private static final String TNC = "Terms and Conditions";

    private static final BigDecimal PRICE = new BigDecimal("100");

    private static final int POINT_RATIO = 200;

    private static final int MAXIMUM_POINTS = 10000;

    private static final int DAY_MULTIPLE = 1;

    private static final int MIN_POINT = 500;

    private static final int DAY_MILEAGE = 100;

    private static final int MONTH_MILEAGE = 200;

    private static final int MONTH_MULTIPLE = 200;

    private static final int ASIA_MILES = 25;

    private static final int ONLY_POINT = 0;

    private static final int SYSTEM_TYPE = 3;

    private static final int STOCK = 100;

    private static final String IMG = "https://product_img.jpg";

    private static final String IMGS = "https://product_imgs.jpg,https://product_imgs2.jpg";

    private static final String RETAIL_PRICE = "200";

    private static final String BUY_START_TIME = "2023-06-20 00:00:00";

    private static final String BUY_END_TIME = "9923-06-30 23:59:59";

    private static final Date NOW = new Date();

    private static final BigDecimal SUBSIDY_AMOUNT = new BigDecimal(0);

    @BeforeEach
    public void setUp() {
        configUtilsStatic = mockStatic(ConfigUtils.class);
        MockitoAnnotations.openMocks(this);
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(USER_ID);
        userInfo.setUsername("user");
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(contextHolder.getLocale()).thenReturn(Locale.ENGLISH);
    }
    @AfterEach
    public void tearDown() {
        configUtilsStatic.close();
    }


    @Test
    public void testGetObtainPay() {
        // Arrange
        FookBusinessProduct product = createBusinessProduct();

        FookBusiness fookBusiness = new FookBusiness();
        fookBusiness.setId(BUSINESS_ID);
        fookBusiness.setSystemType(SYSTEM_TYPE);

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);
        when(fookBusinessDao.selectByPrimaryKey(BUSINESS_ID)).thenReturn(fookBusiness);
        when(settingsDao.getValue(eq("site.maximum_points"))).thenReturn(String.valueOf(MAXIMUM_POINTS));
        when(settingsDao.getValue(eq("site.day_mileage"))).thenReturn(String.valueOf(DAY_MILEAGE));
        when(settingsDao.getValue(eq("site.asia_miles_day_multiple"))).thenReturn(String.valueOf(DAY_MULTIPLE));
        when(settingsDao.getValue(eq("site.month_mileage"))).thenReturn(String.valueOf(MONTH_MILEAGE));
        when(settingsDao.getValue(eq("site.asia_miles_month_multiple"))).thenReturn(String.valueOf(MONTH_MULTIPLE));
        when(settingsDao.getValue(eq("site.asia_miles"))).thenReturn(String.valueOf(ASIA_MILES));
        when(settingsDao.getValue(eq("site.not_show_stock_sales"))).thenReturn("0");
        when(collectService.getProductsCollect(any())).thenReturn(null);
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("mockOrderToken");
        doNothing().when(collectService).setCollectNumber(any(), anyInt(), any());

        // 亚洲万里通
//        when(fookPlatformOrderDao.sumMileageNumber(any(), any(), any(), any())).thenReturn(0);

        when(messageSource.getMessage(anyString(), any(), any(Locale.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        when(ConfigUtils.getProperty("security.product.share.secret"))
                .thenReturn("UNYmcFBSz30Sibfk");
        when(stockService.isSlodOut(anyInt())).thenReturn(false);

        // Act
        ObtainPayRequest request = new ObtainPayRequest();
        request.setBusinessId(BUSINESS_ID);
        ObtainPayResponse response = payService.getObtainPay(request);

        // Assert
        Assertions.assertNotNull(response);
        Assertions.assertEquals(PRODUCT_ID, response.getId().longValue());
        Assertions.assertEquals(SYSTEM_TYPE, response.getSystemType());
        Assertions.assertEquals(POINT_RATIO, response.getMpayintegralProportion());
        Assertions.assertEquals(PRICE.toString(), response.getPrice());
        Assertions.assertEquals(TITLE, response.getTitle());
        Assertions.assertEquals(IMG, response.getImg());
        Assertions.assertEquals(Arrays.asList(IMGS.split(",")), response.getImgs());
        Assertions.assertEquals(RETAIL_PRICE, response.getRetailPrice());
        Assertions.assertEquals(MAXIMUM_POINTS, response.getMaximumPoints());
        Assertions.assertEquals(100, response.getMinPoint());
        Assertions.assertEquals(DAY_MILEAGE, response.getDayMileage());
        Assertions.assertEquals(40000, response.getMonthMileage());
        Assertions.assertEquals(ONLY_POINT, response.getPayType());
        Assertions.assertEquals(TNC, response.getTnc());
        Assertions.assertEquals(STOCK, response.getStock().intValue());
        Assertions.assertEquals(null, response.getClassCode());
        Assertions.assertEquals("mockOrderToken", response.getOrderToken());
    }

    private FookBusinessProduct createBusinessProduct(){
        // Arrange
        FookBusinessProduct product = new FookBusinessProduct();
        product.setBusinessid(BUSINESS_ID);
        product.setId(PRODUCT_ID);
        product.setPrice(PRICE);
        product.setPointRatio(POINT_RATIO);
        product.setTitle(TITLE);
        product.setTnc(TNC);
        product.setStock(STOCK);
        product.setImg(IMG);
        product.setImgs(IMGS);
        product.setRetailPrice(new BigDecimal(RETAIL_PRICE));
        product.setBuyStartTime(JodaTimeUtil.parse(BUY_START_TIME));
        product.setBuyEndTime(JodaTimeUtil.parse(BUY_END_TIME));
        product.setSubsidyAmount(SUBSIDY_AMOUNT);
        product.setType(BusinessProductTypeEnum.SIGNATURE_PRODUCT.getTypeId());
        product.setVaildMode(1);
        product.setIsWeekend(1);
        product.setIsVacation(1);
        product.setOnlyPoint(ONLY_POINT);
        product.setShelfStatus(ShelfStatus.SHELVED.getValue());
        product.setSnapUp(0);
        return product;
    }

    @Test
    public void testGetObtainPay_snapUp_withRequestSessionId() {
        FookBusinessProduct product = createBusinessProduct();
        product.setSnapUp(SNAP_UP.getValue());


        FookBusiness fookBusiness = new FookBusiness();
        fookBusiness.setId(BUSINESS_ID);
        fookBusiness.setSystemType(SYSTEM_TYPE);

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);
        when(fookBusinessDao.selectByPrimaryKey(BUSINESS_ID)).thenReturn(fookBusiness);
        when(settingsDao.getValue(eq("site.maximum_points"))).thenReturn(String.valueOf(MAXIMUM_POINTS));
        when(settingsDao.getValue(eq("site.day_mileage"))).thenReturn(String.valueOf(DAY_MILEAGE));
        when(settingsDao.getValue(eq("site.asia_miles_day_multiple"))).thenReturn(String.valueOf(DAY_MULTIPLE));
        when(settingsDao.getValue(eq("site.month_mileage"))).thenReturn(String.valueOf(MONTH_MILEAGE));
        when(settingsDao.getValue(eq("site.asia_miles_month_multiple"))).thenReturn(String.valueOf(MONTH_MULTIPLE));
        when(settingsDao.getValue(eq("site.asia_miles"))).thenReturn(String.valueOf(ASIA_MILES));
        when(settingsDao.getValue(eq("site.not_show_stock_sales"))).thenReturn("0");
        FookShowSnapupSession session = new FookShowSnapupSession();
        session.setId(1);
        session.setStartTime(JodaTimeUtil.parse("2024-06-20 00:00:00"));
        session.setEndTime(JodaTimeUtil.parse("2024-06-20 23:00:00"));
        session.setValid(YesNo.YES.getValue());
        when(fookShowSnapupSessionDao.selectSessionByProductIdAndSessionId(anyInt(), any())).thenReturn(session);

        when(stockService.isSlodOut(anyInt())).thenReturn(false);
        // 亚洲万里通
//        when(fookPlatformOrderDao.sumMileageNumber(any(), any(), any(), any())).thenReturn(0);

        when(messageSource.getMessage(anyString(), any(), any(Locale.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        when(ConfigUtils.getProperty("security.product.share.secret"))
                .thenReturn("UNYmcFBSz30Sibfk");

        when(collectService.getProductsCollect(any())).thenReturn(null);
        doNothing().when(collectService).setCollectNumber(any(), anyInt(), any());
        // Act
        ObtainPayRequest request = new ObtainPayRequest();
        request.setBusinessId(BUSINESS_ID);
        request.setSessionId(1);
        ObtainPayResponse response = payService.getObtainPay(request);

        // Assert
        Assertions.assertNotNull(response);
        Assertions.assertEquals(PRODUCT_ID, response.getId().longValue());
        Assertions.assertEquals(SYSTEM_TYPE, response.getSystemType());
        Assertions.assertEquals(POINT_RATIO, response.getMpayintegralProportion());
        Assertions.assertEquals(PRICE.toString(), response.getPrice());
        Assertions.assertEquals(TITLE, response.getTitle());
        Assertions.assertEquals(IMG, response.getImg());
        Assertions.assertEquals(Arrays.asList(IMGS.split(",")), response.getImgs());
        Assertions.assertEquals(RETAIL_PRICE, response.getRetailPrice());
        Assertions.assertEquals(MAXIMUM_POINTS, response.getMaximumPoints());
        Assertions.assertEquals(100, response.getMinPoint());
        Assertions.assertEquals(DAY_MILEAGE, response.getDayMileage());
        Assertions.assertEquals(40000, response.getMonthMileage());
        Assertions.assertEquals(ONLY_POINT, response.getPayType());
        Assertions.assertEquals(TNC, response.getTnc());
        Assertions.assertEquals(STOCK, response.getStock().intValue());
        Assertions.assertEquals(null, response.getClassCode());

        Assertions.assertEquals(1, response.getSessionId());
        Assertions.assertEquals(session.getStartTime(), response.getBuyStartTime());
        Assertions.assertEquals(session.getEndTime(), response.getBuyEndTime());
    }

    @Test
    public void testCheckBusinessProduct_ValidProduct() {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(USER_ID);
        userInfo.setUsername("user");
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        when(contextHolder.getLocale()).thenReturn(Locale.ENGLISH);

        FookBusinessProduct product = createBusinessProduct();
        int number = 1;
        boolean isCheckStock = true;
        Integer sessionId = 1;

        when(fookBusinessStoreProductDao.countStoreDistanceListByProductId(PRODUCT_ID)).thenReturn(1);
        when(fookShowSnapupSessionDao.selectSessionByProductIdAndSessionId(anyInt(), any())).thenReturn(createFookShowSnapupSession());
        when(ConfigUtils.getProperty("security.product.share.secret"))
                .thenReturn("UNYmcFBSz30Sibfk");

        assertDoesNotThrow(() -> payService.checkBusinessProduct(product, number, isCheckStock, sessionId));
    }

    private FookShowSnapupSession createFookShowSnapupSession() {
        FookShowSnapupSession session = new FookShowSnapupSession();
        session.setId(1);
        session.setStartTime(JodaTimeUtil.plusMinuteToDate(new Date(), -1));
        session.setEndTime(JodaTimeUtil.plusMinuteToDate(new Date(), 1));
        session.setValid(YesNo.YES.getValue());
        return session;
    }

    private FookBusiness createFookBusiness() {
        FookBusiness fookBusiness = new FookBusiness();
        fookBusiness.setId(BUSINESS_ID);
        fookBusiness.setSystemType(SYSTEM_TYPE);
        return fookBusiness;
    }

    @Test
    public void testCreateOrder() {
        // Arrange
        FookBusinessProduct product = createBusinessProduct();
        FookPlatformOrder platformOrder = new FookPlatformOrder();
        platformOrder.setId(1);
        platformOrder.setOrderNo("123456");
        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);
        when(fookBusinessDao.selectByPrimaryKey(BUSINESS_ID)).thenReturn(new FookBusiness());
        when(settingsDao.getValue(eq("site.maximum_points"))).thenReturn(String.valueOf(MAXIMUM_POINTS));
        when(settingsDao.getValue(eq("site.day_mileage"))).thenReturn(String.valueOf(DAY_MILEAGE));
        when(settingsDao.getValue(eq("site.asia_miles_day_multiple"))).thenReturn(String.valueOf(DAY_MULTIPLE));
        when(settingsDao.getValue(eq("site.month_mileage"))).thenReturn(String.valueOf(MONTH_MILEAGE));
        when(settingsDao.getValue(eq("site.asia_miles_month_multiple"))).thenReturn(String.valueOf(MONTH_MULTIPLE));
        when(settingsDao.getValue(eq("site.asia_miles"))).thenReturn(String.valueOf(ASIA_MILES));
        when(settingsDao.getValue(eq("site.not_show_stock_sales"))).thenReturn("0");
        when(messageSource.getMessage(anyString(), any(), any(Locale.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));
        when(ConfigUtils.getProperty("security.product.share.secret")).thenReturn("UNYmcFBSz30Sibfk");
        when(fookPlatformOrderDao.insert(any(FookPlatformOrder.class))).thenReturn(1);
        when(fookPlatformOrderinfoDao.insertSelective(any(FookPlatformOrderinfo.class))).thenReturn(1);
        when(fookMacaupassOrderDao.insert(any(FookMacaupassOrder.class))).thenReturn(1);
        when(fookBusinessProductstockDao.update(any(FookBusinessProductstock.class), any())).thenReturn(1);
        when(fookBusinessProductstockDao.insert(any(FookBusinessProductstock.class))).thenReturn(1);
        when(fookPlatformOrderDao.sumMileageNumber(any(Date.class), any(Date.class), anyInt(), anyInt())).thenReturn(0);
        when(fookProductSnappingRecordDao.selectOne(any())).thenReturn(null);
        when(fookProductSnappingRecordDao.sumSnappingRecordNumber(anyInt(), anyInt(), any(Integer.class))).thenReturn(0);
        when(fookPlatformOrderDao.sumUserBuyNumber(anyInt(), anyInt(), any(Date.class), any(Integer.class))).thenReturn(0);
//        when(fookPlatformOrderDao.sumMileageNumber(any(), any(), any(), any())).thenReturn(0);
        when(fookExternalVcodeDao.selectOne(any())).thenReturn(null);
        when(fookBusinessStoreProductDao.countStoreDistanceListByProductId(PRODUCT_ID)).thenReturn(1);
        GetUserInfoResponse userInfoResponse = new GetUserInfoResponse();
        userInfoResponse.setPoint(1000000);
        when(mPayAuthService.updAndGetUserInfo()).thenReturn(userInfoResponse);
        when(atomicSeqService.getOrderNo()).thenReturn("123456");
        when(fookBusinessProductDao.updateLockStock(anyInt(), anyInt())).thenReturn(1);
        doNothing().when(applicationContext).publishEvent(any(OrderCloseMessage.class));
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl", "")).thenReturn("https//mcoin-mall.macaupass.com");
        when(ConfigUtils.getProperty("order.payment.validity.period", "600000")).thenReturn("600000");

        // Act
        CreateOrderRequest request = new CreateOrderRequest();
        request.setId(PRODUCT_ID);
        request.setNumber(1);
        request.Mcurrency = 100;

        OrderEnvInfo orderEnvInfo = new OrderEnvInfo();
        // 赋值
        orderEnvInfo.setSource("source_value");
        orderEnvInfo.setTerminalType("H5"); // 或者 "小程序"
        orderEnvInfo.setDeviceBrand("苹果"); // 或者 "华为", "联想"
        orderEnvInfo.setDeviceModel("iPhone 13");
        orderEnvInfo.setAppVersion("1.0.0");
        orderEnvInfo.setOsVersion("iOS 15.0");
        orderEnvInfo.setSdkVersion("2.0.0");
        orderEnvInfo.setImsi("123456789012345");
        orderEnvInfo.setImei("012345678901234");
        orderEnvInfo.setMac("00-14-22-01-23-45");
        orderEnvInfo.setBrowser("Safari");
        orderEnvInfo.setScreenSize("1920x1080");
        orderEnvInfo.setApp("MyApp");
        orderEnvInfo.setOs("iOS"); // 或者 "Android"
        orderEnvInfo.setIp("***********");
        orderEnvInfo.setNetworkType("WIFI"); // 或者 "2G", "3G", "4G", "5G"
        orderEnvInfo.setLng("116.407396");
        orderEnvInfo.setLat("39.904200");
        request.setOrderEnvInfo(orderEnvInfo);



        CreateOrderResponse response = payService.createOrder(request);

        // Assert
        Assertions.assertNotNull(response);
//        verify(applicationContext).publishEvent(any(OrderCloseMessage.class));
//        verify(applicationContext).publishEvent(any(OrderEnvMessage.class));
    }

    @Test
    public void testApplyBuy_Success() {
        // Arrange
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(PRODUCT_ID);
        request.setNumber(1);
        request.setSessionId(1);

        FookBusinessProduct product = createBusinessProduct();
        product.setSnapUp(SNAP_UP.getValue());

        FookShowSnapupSession session = new FookShowSnapupSession();
        session.setId(1);
        session.setValid(YesNo.YES.getValue());
        session.setStartTime(JodaTimeUtil.plusMinuteToDate(new Date(), -10));
        session.setEndTime(JodaTimeUtil.plusMinuteToDate(new Date(), 10));

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);
        when(fookShowSnapupSessionDao.selectSessionByProductIdAndSessionId(PRODUCT_ID, 1))
            .thenReturn(session);
        when(atomicSeqService.getSnapUpNo()).thenReturn("123");
        when(fookShowSnapupSessionDao.selectSessionByProductIdAndSessionId(
            product.getId(), request.getSessionId())).thenReturn(session);
        when(fookBusinessStoreProductDao.countStoreDistanceListByProductId(product.getId())).thenReturn(1);

        // Act
        ApplyBuyResponse response = payService.applyBuy(request);

        // Assert
        assertNotNull(response);
        assertEquals(PRODUCT_ID, response.getProductId());
        assertEquals(1, response.getNumber());
        assertEquals(1, response.getStatus());
    }

    @Test
    public void testApplyBuy_NotSnapUpProduct() {
        // Arrange
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(PRODUCT_ID);
        request.setNumber(1);

        FookBusinessProduct product = createBusinessProduct();
        product.setSnapUp(SnapUpEnum.NOT_SNAP_UP.getValue());

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);
        when(messageSource.getMessage("message.business.no_apply_welfare", null, Locale.ENGLISH))
        .thenReturn("message.business.no_apply_welfare");
        when(fookBusinessStoreProductDao.countStoreDistanceListByProductId(product.getId())).thenReturn(1);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> payService.applyBuy(request));
        assertEquals("message.business.no_apply_welfare", exception.getMessage());
    }

    @Test
    public void testApplyBuy_ProductNotExist() {
        // Arrange
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(PRODUCT_ID);
        request.setNumber(1);

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(null);
        when(messageSource.getMessage("message.business.platform", null, Locale.ENGLISH))
        .thenReturn("message.business.platform");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> payService.applyBuy(request));
        assertEquals("message.business.platform", exception.getMessage());
    }

    @Test
    public void testApplyBuy_ProductOffShelf() {
        // Arrange
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(PRODUCT_ID);
        request.setNumber(1);

        FookBusinessProduct product = createBusinessProduct();
        product.setShelfStatus(ShelfStatus.UNSHELVED.getValue());

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);

        when(messageSource.getMessage("message.ordersaveinfo.product_the_shelves", null, Locale.ENGLISH))
        .thenReturn("message.ordersaveinfo.product_the_shelves");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> payService.applyBuy(request));
        assertEquals("message.ordersaveinfo.product_the_shelves", exception.getMessage());
    }

    @Test
    public void testApplyBuy_ExceedLimit() {
        // Arrange
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(PRODUCT_ID);
        request.setNumber(10);

        FookBusinessProduct product = createBusinessProduct();
        product.setSnapUp(SNAP_UP.getValue());
        product.setLimitedNumber(5);

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);

        when(messageSource.getMessage("message.ordersaveinfo.max_buy_number", null, Locale.ENGLISH))
        .thenReturn("message.ordersaveinfo.max_buy_number");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> payService.applyBuy(request));
        assertEquals("message.ordersaveinfo.max_buy_number", exception.getMessage());
    }

    @Test
    public void testApplyBuy_ExceedUserLimit() {
        // Arrange
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(PRODUCT_ID);
        request.setNumber(3);

        FookBusinessProduct product = createBusinessProduct();
        product.setUserLimitedNumber(2); // 设置用户限购数量为2

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);
        when(fookProductSnappingRecordDao.sumSnappingRecordNumber(PRODUCT_ID, USER_ID, null)).thenReturn(0);
        when(fookPlatformOrderDao.sumUserBuyNumber(USER_ID, PRODUCT_ID, product.getCreatedAt(), null)).thenReturn(0);

        when(fookBusinessStoreProductDao.countStoreDistanceListByProductId(product.getId())).thenReturn(1);
        when(messageSource.getMessage("message.ordersaveinfo.max_number", null, Locale.ENGLISH))
                .thenReturn("message.ordersaveinfo.max_number");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class,
                () -> payService.applyBuy(request));
        assertEquals("message.ordersaveinfo.max_number", exception.getMessage());
    }

    @Test
    public void testApplyBuy_WithinUserLimit() {
        // Arrange
        ApplyBuyRequest request = new ApplyBuyRequest();
        request.setProduct_id(PRODUCT_ID);
        request.setNumber(2);

        FookBusinessProduct product = createBusinessProduct();
        product.setUserLimitedNumber(3); // 设置用户限购数量为3
        product.setSnapUp(SNAP_UP.getValue());

        when(fookBusinessProductDao.selectByPrimaryKey(PRODUCT_ID)).thenReturn(product);
        when(fookProductSnappingRecordDao.sumSnappingRecordNumber(PRODUCT_ID, USER_ID, null)).thenReturn(0);
        when(fookPlatformOrderDao.sumUserBuyNumber(USER_ID, PRODUCT_ID, product.getCreatedAt(), null)).thenReturn(0);

        when(fookBusinessStoreProductDao.countStoreDistanceListByProductId(product.getId())).thenReturn(1);
        // Act & Assert
        assertDoesNotThrow(() -> payService.applyBuy(request));
    }

    @Test
    public void testQueryPay_OrderNotFound() {
        // Arrange
        int orderId = 123;
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(null);
        when(messageSource.getMessage("message.Macaupay.check_order", null, Locale.ENGLISH))
                .thenReturn("Order not found");
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("orderToken");

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(-1, response.getRespStatus());
        assertEquals("Order not found", response.getRespMsg());
        assertNotNull(response.getOrderToken());
    }

    @Test
    public void testQueryPay_OrderInfoNotFound() {
        // Arrange
        int orderId = 123;
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(orderId);
        
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(order);
        when(fookPlatformOrderinfoDao.selectOne(any())).thenReturn(null);
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("orderToken");
        when(messageSource.getMessage("message.Macaupay.check_order", null, Locale.ENGLISH))
                .thenReturn("Order info not found");

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(-1, response.getRespStatus());
        assertEquals("Order info not found", response.getRespMsg());
        assertNotNull(response.getOrderToken());
    }

    @Test
    public void testQueryPay_PaymentSuccess() {
        // Arrange
        int orderId = 123;
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(orderId);
        order.setStatus(2); // 已支付
        order.setRefundStatus(0); // 未退款

        FookPlatformOrderinfo orderinfo = new FookPlatformOrderinfo();
        orderinfo.setType(9); // 优惠券类型

        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(order);
        when(fookPlatformOrderinfoDao.selectOne(any())).thenReturn(orderinfo);
        when(fookPlatformOrdercodeDao.selectOne(any())).thenReturn(ordercode);
        when(messageSource.getMessage("message.Macaupay.pay", null, Locale.ENGLISH))
                .thenReturn("Payment success");
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("orderToken");
        when(ConfigUtils.getProperty("macaupass.coupon.redirect_uri", "")).thenReturn("https://example.com/redirect");

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(1, response.getRespStatus());
        assertEquals("Payment success", response.getRespMsg());
        assertEquals(9, response.getType());
        assertNotNull(response.getRedirectUrl());
        assertNotNull(response.getOrderToken());
    }
    @Test
    public void testQueryPay_DefaultResponse() {
        // Arrange
        int orderId = 123;
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(orderId);
        order.setStatus(0); // 默认状态
        order.setRefundStatus(0); // 未退款

        FookPlatformOrderinfo orderinfo = new FookPlatformOrderinfo();
        
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(order);
        when(fookPlatformOrderinfoDao.selectOne(any())).thenReturn(orderinfo);

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(0, response.getRespStatus());
        assertEquals("", response.getRespMsg());
        assertEquals("", response.getRedirectUrl());
        assertNull(response.getOrderToken());
    }

    @Test
    public void testQueryPay_Type9AndOrdercodeNull() {
        // Arrange
        int orderId = 123;
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(orderId);
        order.setStatus(2); // 已支付
        order.setRefundStatus(0); // 未退款

        FookPlatformOrderinfo orderinfo = new FookPlatformOrderinfo();
        orderinfo.setType(9); // 优惠券类型
        
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(order);
        when(fookPlatformOrderinfoDao.selectOne(any())).thenReturn(orderinfo);
        when(fookPlatformOrdercodeDao.selectOne(any())).thenReturn(null);
        when(messageSource.getMessage("message.Macaupay.mpay_coupons_fail", null, Locale.ENGLISH))
                .thenReturn("Network issue, points and amount will be refunded automatically");
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("orderToken");

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(-1, response.getRespStatus());
        assertEquals("Network issue, points and amount will be refunded automatically", response.getRespMsg());
        assertEquals("orderToken", response.getOrderToken());
    }

    @Test
    public void testQueryPay_Status1RefundStatus3() {
        // Arrange
        int orderId = 123;
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(orderId);
        order.setStatus(1); // 待支付
        order.setRefundStatus(3); // 退款中
        order.setUserid(USER_ID);

        FookPlatformOrderinfo orderinfo = new FookPlatformOrderinfo();
        
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(order);
        when(fookPlatformOrderinfoDao.selectOne(any())).thenReturn(orderinfo);
        when(fookPlatformOrderrefundDao.selectCount(any())).thenReturn(1); // 有退款记录
        when(messageSource.getMessage("message.Macaupay.integral_insufficient", null, Locale.ENGLISH))
                .thenReturn("Insufficient points, purchase failed, amount will be refunded automatically");
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("orderToken");

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(-1, response.getRespStatus());
        assertEquals("Insufficient points, purchase failed, amount will be refunded automatically", response.getRespMsg());
        assertEquals("orderToken", response.getOrderToken());
    }
    @Test
    public void testQueryPay_Status3() {
        // Arrange
        int orderId = 123;
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(orderId);
        order.setStatus(3); // 订单已失效
        order.setUserid(USER_ID);

        FookPlatformOrderinfo orderinfo = new FookPlatformOrderinfo();
        
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(order);
        when(fookPlatformOrderinfoDao.selectOne(any())).thenReturn(orderinfo);
        when(messageSource.getMessage("message.Macaupay.Invalid_order", null, Locale.ENGLISH))
                .thenReturn("Order has expired, please place a new order");
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("orderToken");

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(-1, response.getRespStatus());
        assertEquals("Order has expired, please place a new order", response.getRespMsg());
        assertEquals("orderToken", response.getOrderToken());
    }
    @Test
    public void testQueryPay_Status4() {
        // Arrange
        int orderId = 123;
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(orderId);
        order.setStatus(4); // 订单已失效
        order.setUserid(USER_ID);

        FookPlatformOrderinfo orderinfo = new FookPlatformOrderinfo();
        
        when(fookPlatformOrderDao.selectByPrimaryKey(orderId)).thenReturn(order);
        when(fookPlatformOrderinfoDao.selectOne(any())).thenReturn(orderinfo);
        when(messageSource.getMessage("message.Macaupay.integral_insufficient", null, Locale.ENGLISH))
                .thenReturn("Insufficient points, purchase failed, amount will be refunded automatically");
        when(orderTokenService.createOrderToken(USER_ID)).thenReturn("orderToken");

        // Act
        QueryPayResponse response = payService.queryPay(orderId);

        // Assert
        assertEquals(-1, response.getRespStatus());
        assertEquals("Insufficient points, purchase failed, amount will be refunded automatically", response.getRespMsg());
        assertEquals("orderToken", response.getOrderToken());
    }
}
