package com.mcoin.mall.service.common.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.mcoin.mall.bean.FookPlatformUsercollection;
import com.mcoin.mall.bean.UsercollectionCnt;
import com.mcoin.mall.bo.ProductsCollectBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookPlatformUsercollectionDao;
import com.mcoin.mall.model.Collectable;
import com.mcoin.mall.service.common.CollectService;

@Service
public class CollectServiceImpl implements CollectService {
    @Resource
    private FookPlatformUsercollectionDao fookPlatformUsercollectionDao;
    @Resource
    private ContextHolder contextHolder;
    @Resource
    private MessageSource messageSource;

    @Override
    public ProductsCollectBo getProductsCollect(List<Integer> productIds) {
        if(productIds == null || productIds.isEmpty()) {
            return new ProductsCollectBo().setProductCollections(Collections.emptyList())
                    .setUserCollections(Collections.emptyList());
        }
        List<UsercollectionCnt> productCollections = fookPlatformUsercollectionDao.getCountByIds(productIds);
        List<FookPlatformUsercollection> userCollections;
        if (contextHolder.getAuthUserInfo() != null) {
            userCollections = this.fookPlatformUsercollectionDao
                    .getUserEnabledByIds(productIds, contextHolder.getAuthUserInfo().getUserId());
        } else {
            userCollections = Collections.emptyList();
        }
        return new ProductsCollectBo().setProductCollections(productCollections).setUserCollections(userCollections);
    }

    @Override
    public void setCollectNumber(ProductsCollectBo collectBo, Integer productId, Collectable collectable, boolean showText) {
        List<UsercollectionCnt> productCollections = collectBo.getProductCollections();
        List<FookPlatformUsercollection> userCollections = collectBo.getUserCollections();
        int collect = userCollections.stream().filter(uc -> Objects.equals(uc.getCollectionid(), productId))
                .map(FookPlatformUsercollection::getEnable).findFirst().orElse(0);
        collectable.setCollect(collect);
        int collectCount = productCollections.stream().filter(c -> Objects.equals(c.getCollectionid(), productId))
                .map(UsercollectionCnt::getCnt).findFirst().orElse(0);
        String collectCountStr = showText?messageSource.getMessage("message.business.collect_count",
                new Object[]{collectCount}, LocaleContextHolder.getLocale()):String.valueOf(collectCount);
        collectable.setCollectCount(0 != collectCount ? collectCountStr : "");
    }
}
