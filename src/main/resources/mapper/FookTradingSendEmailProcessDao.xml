<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingSendEmailProcessDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookTradingSendEmailProcess">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="email_main" jdbcType="VARCHAR" property="emailMain" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="send" jdbcType="BIT" property="send" />
    <result column="sendtime" jdbcType="TIMESTAMP" property="sendtime" />
    <result column="if_upload" jdbcType="BIT" property="ifUpload" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="trading_id" jdbcType="INTEGER" property="tradingId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, createtime, email_main, email, send, sendtime, if_upload, upload_time, trading_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_trading_send_email_process
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_trading_send_email_process
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookTradingSendEmailProcess">
    insert into fook_trading_send_email_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="emailMain != null">
        email_main,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="send != null">
        send,
      </if>
      <if test="sendtime != null">
        sendtime,
      </if>
      <if test="ifUpload != null">
        if_upload,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
      <if test="tradingId != null">
        trading_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="emailMain != null">
        #{emailMain,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="send != null">
        #{send,jdbcType=BIT},
      </if>
      <if test="sendtime != null">
        #{sendtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifUpload != null">
        #{ifUpload,jdbcType=BIT},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradingId != null">
        #{tradingId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookTradingSendEmailProcess">
    update fook_trading_send_email_process
    <set>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="emailMain != null">
        email_main = #{emailMain,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="send != null">
        send = #{send,jdbcType=BIT},
      </if>
      <if test="sendtime != null">
        sendtime = #{sendtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifUpload != null">
        if_upload = #{ifUpload,jdbcType=BIT},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradingId != null">
        trading_id = #{tradingId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookTradingSendEmailProcess">
    update fook_trading_send_email_process
    set createtime = #{createtime,jdbcType=TIMESTAMP},
      email_main = #{emailMain,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      send = #{send,jdbcType=BIT},
      sendtime = #{sendtime,jdbcType=TIMESTAMP},
      if_upload = #{ifUpload,jdbcType=BIT},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      trading_id = #{tradingId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>