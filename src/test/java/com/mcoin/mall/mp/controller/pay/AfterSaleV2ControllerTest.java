package com.mcoin.mall.mp.controller.pay;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.pay.AfterSaleGoodsRequest;
import com.mcoin.mall.mp.model.pay.AfterSaleGoodsResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@Transactional
class AfterSaleV2ControllerTest extends BaseUnitTest {

    @Autowired
    private AfterSaleV2Controller afterSaleV2Controller;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
    }

    @Test
    void orderGoodsInfo_ShouldReturnGoodsInfo() {
        // Arrange
        AfterSaleGoodsRequest request = new AfterSaleGoodsRequest();
        request.setOrderGoodsId(123);
        request.setRefundMethod(1);

        // Act
        Response<AfterSaleGoodsResponse> response = afterSaleV2Controller.orderGoodsInfo(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }
} 