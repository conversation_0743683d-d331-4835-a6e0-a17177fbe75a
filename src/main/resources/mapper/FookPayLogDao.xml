<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPayLogDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPayLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="uid" jdbcType="INTEGER" property="uid" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="tradeno" jdbcType="VARCHAR" property="tradeno" />
    <result column="out_request_no" jdbcType="VARCHAR" property="outRequestNo" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="fee" jdbcType="DECIMAL" property="fee" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="oparemtion" jdbcType="VARCHAR" property="oparemtion" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="paytime" jdbcType="TIMESTAMP" property="paytime" />
    <result column="refund_id" jdbcType="INTEGER" property="refundId" />
    <result column="points" jdbcType="INTEGER" property="points" />
    <result column="mpay_coupons_type" jdbcType="INTEGER" property="mpayCouponsType" />
    <result column="mpay_coupons_code" jdbcType="VARCHAR" property="mpayCouponsCode" />
    <result column="mpay_coupons_reasoncontent" jdbcType="VARCHAR" property="mpayCouponsReasoncontent" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.FookPayLog">
    <result column="reasoncontent" jdbcType="LONGVARCHAR" property="reasoncontent" />
  </resultMap>
  <sql id="Base_Column_List">
    id, uuid, `uid`, orderid, tradeno, out_request_no, amount, fee, currency, oparemtion,
    `type`, `status`, createtime, reason, content, updatetime, paytime, refund_id, points,
    mpay_coupons_type, mpay_coupons_code, mpay_coupons_reasoncontent
  </sql>
  <sql id="Blob_Column_List">
    reasoncontent
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fook_pay_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_pay_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPayLog">
    insert into fook_pay_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="uuid != null">
        uuid,
      </if>
      <if test="uid != null">
        `uid`,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="tradeno != null">
        tradeno,
      </if>
      <if test="outRequestNo != null">
        out_request_no,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="oparemtion != null">
        oparemtion,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="updatetime != null">
        updatetime,
      </if>
      <if test="paytime != null">
        paytime,
      </if>
      <if test="refundId != null">
        refund_id,
      </if>
      <if test="points != null">
        points,
      </if>
      <if test="mpayCouponsType != null">
        mpay_coupons_type,
      </if>
      <if test="mpayCouponsCode != null">
        mpay_coupons_code,
      </if>
      <if test="mpayCouponsReasoncontent != null">
        mpay_coupons_reasoncontent,
      </if>
      <if test="reasoncontent != null">
        reasoncontent,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="tradeno != null">
        #{tradeno,jdbcType=VARCHAR},
      </if>
      <if test="outRequestNo != null">
        #{outRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="oparemtion != null">
        #{oparemtion,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="updatetime != null">
        #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="paytime != null">
        #{paytime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundId != null">
        #{refundId,jdbcType=INTEGER},
      </if>
      <if test="points != null">
        #{points,jdbcType=INTEGER},
      </if>
      <if test="mpayCouponsType != null">
        #{mpayCouponsType,jdbcType=INTEGER},
      </if>
      <if test="mpayCouponsCode != null">
        #{mpayCouponsCode,jdbcType=VARCHAR},
      </if>
      <if test="mpayCouponsReasoncontent != null">
        #{mpayCouponsReasoncontent,jdbcType=VARCHAR},
      </if>
      <if test="reasoncontent != null">
        #{reasoncontent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPayLog">
    update fook_pay_log
    <set>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        `uid` = #{uid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="tradeno != null">
        tradeno = #{tradeno,jdbcType=VARCHAR},
      </if>
      <if test="outRequestNo != null">
        out_request_no = #{outRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="fee != null">
        fee = #{fee,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="oparemtion != null">
        oparemtion = #{oparemtion,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="updatetime != null">
        updatetime = #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="paytime != null">
        paytime = #{paytime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundId != null">
        refund_id = #{refundId,jdbcType=INTEGER},
      </if>
      <if test="points != null">
        points = #{points,jdbcType=INTEGER},
      </if>
      <if test="mpayCouponsType != null">
        mpay_coupons_type = #{mpayCouponsType,jdbcType=INTEGER},
      </if>
      <if test="mpayCouponsCode != null">
        mpay_coupons_code = #{mpayCouponsCode,jdbcType=VARCHAR},
      </if>
      <if test="mpayCouponsReasoncontent != null">
        mpay_coupons_reasoncontent = #{mpayCouponsReasoncontent,jdbcType=VARCHAR},
      </if>
      <if test="reasoncontent != null">
        reasoncontent = #{reasoncontent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.FookPayLog">
    update fook_pay_log
    set uuid = #{uuid,jdbcType=VARCHAR},
      `uid` = #{uid,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      tradeno = #{tradeno,jdbcType=VARCHAR},
      out_request_no = #{outRequestNo,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      fee = #{fee,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      oparemtion = #{oparemtion,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      reason = #{reason,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      updatetime = #{updatetime,jdbcType=TIMESTAMP},
      paytime = #{paytime,jdbcType=TIMESTAMP},
      refund_id = #{refundId,jdbcType=INTEGER},
      points = #{points,jdbcType=INTEGER},
      mpay_coupons_type = #{mpayCouponsType,jdbcType=INTEGER},
      mpay_coupons_code = #{mpayCouponsCode,jdbcType=VARCHAR},
      mpay_coupons_reasoncontent = #{mpayCouponsReasoncontent,jdbcType=VARCHAR},
      reasoncontent = #{reasoncontent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPayLog">
    update fook_pay_log
    set uuid = #{uuid,jdbcType=VARCHAR},
      `uid` = #{uid,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      tradeno = #{tradeno,jdbcType=VARCHAR},
      out_request_no = #{outRequestNo,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      fee = #{fee,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      oparemtion = #{oparemtion,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      reason = #{reason,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      updatetime = #{updatetime,jdbcType=TIMESTAMP},
      paytime = #{paytime,jdbcType=TIMESTAMP},
      refund_id = #{refundId,jdbcType=INTEGER},
      points = #{points,jdbcType=INTEGER},
      mpay_coupons_type = #{mpayCouponsType,jdbcType=INTEGER},
      mpay_coupons_code = #{mpayCouponsCode,jdbcType=VARCHAR},
      mpay_coupons_reasoncontent = #{mpayCouponsReasoncontent,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>