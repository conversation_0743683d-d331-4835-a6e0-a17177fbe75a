<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookActiveMerchantDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookActiveMerchant">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="title_en" jdbcType="VARCHAR" property="titleEn" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file" jdbcType="VARCHAR" property="file" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="lucky_draw" jdbcType="TINYINT" property="luckyDraw" />
    <result column="voucher" jdbcType="TINYINT" property="voucher" />
    <result column="event" jdbcType="VARCHAR" property="event" />
    <result column="event_en" jdbcType="VARCHAR" property="eventEn" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `enable`, `status`, `count`, title, title_en, `name`, file_url, `file`, url, 
    lucky_draw, voucher, event, event_en, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_active_merchant
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_active_merchant
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookActiveMerchant">
    insert into fook_active_merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="count != null">
        `count`,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="titleEn != null">
        title_en,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="file != null">
        `file`,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="luckyDraw != null">
        lucky_draw,
      </if>
      <if test="voucher != null">
        voucher,
      </if>
      <if test="event != null">
        event,
      </if>
      <if test="eventEn != null">
        event_en,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="titleEn != null">
        #{titleEn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="file != null">
        #{file,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="luckyDraw != null">
        #{luckyDraw,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        #{voucher,jdbcType=TINYINT},
      </if>
      <if test="event != null">
        #{event,jdbcType=VARCHAR},
      </if>
      <if test="eventEn != null">
        #{eventEn,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookActiveMerchant">
    update fook_active_merchant
    <set>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="count != null">
        `count` = #{count,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="titleEn != null">
        title_en = #{titleEn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="file != null">
        `file` = #{file,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="luckyDraw != null">
        lucky_draw = #{luckyDraw,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        voucher = #{voucher,jdbcType=TINYINT},
      </if>
      <if test="event != null">
        event = #{event,jdbcType=VARCHAR},
      </if>
      <if test="eventEn != null">
        event_en = #{eventEn,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookActiveMerchant">
    update fook_active_merchant
    set `enable` = #{enable,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      `count` = #{count,jdbcType=INTEGER},
      title = #{title,jdbcType=VARCHAR},
      title_en = #{titleEn,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      `file` = #{file,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      lucky_draw = #{luckyDraw,jdbcType=TINYINT},
      voucher = #{voucher,jdbcType=TINYINT},
      event = #{event,jdbcType=VARCHAR},
      event_en = #{eventEn,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>