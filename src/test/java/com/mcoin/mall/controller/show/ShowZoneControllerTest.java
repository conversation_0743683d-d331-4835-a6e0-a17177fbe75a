package com.mcoin.mall.controller.show;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.ShowInfoResponse;
import com.mcoin.mall.model.ShowResponse;
import com.mcoin.mall.model.ShowSnapUpNextRequest;
import com.mcoin.mall.model.ShowSnapUpRequest;
import com.mcoin.mall.model.ShowZoneProductRequest;
import com.mcoin.mall.model.SnapUpNextResponse;
import com.mcoin.mall.model.SnapUpResponse;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.util.AESUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class ShowZoneControllerTest extends BaseUnitTest {

    @Resource
    private ShowZoneController showZoneController;

    @BeforeEach
    public void setUp() {
        // Mock locale
        Mockito.when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        Mockito.when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
    }

    @Test
    public void testQueryByShowId() {
        ShowZoneProductRequest request = new ShowZoneProductRequest();
        request.setShowId(1);
        request.setZoneId(2);

        Response<ShowResponse> result = showZoneController.queryByShowId(request,"HARMONY");

        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
        assertEquals("这是专场中文名称", result.getData().getShowName());
    }

    @Test
    public void testQueryByShowInfo() {
        assertThrows(BusinessException.class, () -> {
            showZoneController.queryByShowInfo("1");
        });

        Response<ShowInfoResponse> response = showZoneController.queryByShowInfo(AESUtil.encryptToHex("UNYmcFBSz30Sibfk","1"));
        
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getShowId());
    }

    @Test
    public void testSnapUp() {
        ShowSnapUpRequest request = new ShowSnapUpRequest();
        request.setShowId(1);

        Response<SnapUpResponse> response = showZoneController.snapUp(request,"HARMONY");
        
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertNotNull(response.getData().getSnatchList());
    }

    @Test
    public void testSnapUpNext() {
        ShowSnapUpNextRequest request = new ShowSnapUpNextRequest();
        request.setShowId(1);

        Response<SnapUpNextResponse> response = showZoneController.snapUpNext(request,"HARMONY");
        
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertNotNull(response.getData().getSnatchList());
    }
}