<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTemporaryProductDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookTemporaryProduct">
    <id column="temporary_id" jdbcType="INTEGER" property="temporaryId" />
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="zip_img" jdbcType="VARCHAR" property="zipImg" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="shelf_status" jdbcType="TINYINT" property="shelfStatus" />
    <result column="buy_start_time" jdbcType="TIMESTAMP" property="buyStartTime" />
    <result column="buy_end_time" jdbcType="TIMESTAMP" property="buyEndTime" />
    <result column="vaild_mode" jdbcType="TINYINT" property="vaildMode" />
    <result column="vaild_start_time" jdbcType="TIMESTAMP" property="vaildStartTime" />
    <result column="vaild_end_time" jdbcType="TIMESTAMP" property="vaildEndTime" />
    <result column="day_number" jdbcType="INTEGER" property="dayNumber" />
    <result column="snap_up" jdbcType="TINYINT" property="snapUp" />
    <result column="s_name" jdbcType="VARCHAR" property="sName" />
    <result column="i_name" jdbcType="VARCHAR" property="iName" />
    <result column="i_name_en" jdbcType="VARCHAR" property="iNameEn" />
    <result column="information_id" jdbcType="INTEGER" property="informationId" />
    <result column="stores_type" jdbcType="INTEGER" property="storesType" />
    <result column="stores_type_name" jdbcType="VARCHAR" property="storesTypeName" />
    <result column="t_product_name" jdbcType="VARCHAR" property="tProductName" />
    <result column="t_stores_name" jdbcType="VARCHAR" property="tStoresName" />
    <result column="t_stores_type_name" jdbcType="VARCHAR" property="tStoresTypeName" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="complex_name" jdbcType="VARCHAR" property="complexName" />
    <result column="complex_name_en" jdbcType="VARCHAR" property="complexNameEn" />
    <result column="only_point" jdbcType="TINYINT" property="onlyPoint" />
    <result column="actual_sales" jdbcType="INTEGER" property="actualSales" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
    <result column="is_hot" jdbcType="INTEGER" property="isHot" />
    <result column="is_great" jdbcType="INTEGER" property="isGreat" />
    <result column="business_categories_id" jdbcType="INTEGER" property="businessCategoriesId" />
  </resultMap>
  <sql id="Base_Column_List">
    temporary_id, id, img, zip_img, businessid, title, `type`, retail_price, price, shelf_status,
    buy_start_time, buy_end_time, vaild_mode, vaild_start_time, vaild_end_time, day_number,
    snap_up, s_name, i_name, i_name_en, information_id, stores_type, stores_type_name,
    t_product_name, t_stores_name, t_stores_type_name, created_time, complex_name, complex_name_en, 
    only_point, actual_sales, longitude, dimension, is_hot, is_great, business_categories_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_temporary_product
    where temporary_id = #{temporaryId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_temporary_product
    where temporary_id = #{temporaryId,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookTemporaryProduct">
    insert into fook_temporary_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="temporaryId != null">
        temporary_id,
      </if>
      <if test="id != null">
        id,
      </if>
      <if test="img != null">
        img,
      </if>
      <if test="zipImg != null">
        zip_img,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="shelfStatus != null">
        shelf_status,
      </if>
      <if test="buyStartTime != null">
        buy_start_time,
      </if>
      <if test="buyEndTime != null">
        buy_end_time,
      </if>
      <if test="vaildMode != null">
        vaild_mode,
      </if>
      <if test="vaildStartTime != null">
        vaild_start_time,
      </if>
      <if test="vaildEndTime != null">
        vaild_end_time,
      </if>
      <if test="dayNumber != null">
        day_number,
      </if>
      <if test="snapUp != null">
        snap_up,
      </if>
      <if test="sName != null">
        s_name,
      </if>
      <if test="iName != null">
        i_name,
      </if>
      <if test="iNameEn != null">
        i_name_en,
      </if>
      <if test="informationId != null">
        information_id,
      </if>
      <if test="storesType != null">
        stores_type,
      </if>
      <if test="storesTypeName != null">
        stores_type_name,
      </if>
      <if test="tProductName != null">
        t_product_name,
      </if>
      <if test="tStoresName != null">
        t_stores_name,
      </if>
      <if test="tStoresTypeName != null">
        t_stores_type_name,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="complexName != null">
        complex_name,
      </if>
      <if test="complexNameEn != null">
        complex_name_en,
      </if>
      <if test="onlyPoint != null">
        only_point,
      </if>
      <if test="actualSales != null">
        actual_sales,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="isHot != null">
        is_hot,
      </if>
      <if test="isGreat != null">
        is_great,
      </if>
      <if test="businessCategoriesId != null">
        business_categories_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="temporaryId != null">
        #{temporaryId,jdbcType=INTEGER},
      </if>
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="img != null">
        #{img,jdbcType=VARCHAR},
      </if>
      <if test="zipImg != null">
        #{zipImg,jdbcType=VARCHAR},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="shelfStatus != null">
        #{shelfStatus,jdbcType=TINYINT},
      </if>
      <if test="buyStartTime != null">
        #{buyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyEndTime != null">
        #{buyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildMode != null">
        #{vaildMode,jdbcType=TINYINT},
      </if>
      <if test="vaildStartTime != null">
        #{vaildStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildEndTime != null">
        #{vaildEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dayNumber != null">
        #{dayNumber,jdbcType=INTEGER},
      </if>
      <if test="snapUp != null">
        #{snapUp,jdbcType=TINYINT},
      </if>
      <if test="sName != null">
        #{sName,jdbcType=VARCHAR},
      </if>
      <if test="iName != null">
        #{iName,jdbcType=VARCHAR},
      </if>
      <if test="iNameEn != null">
        #{iNameEn,jdbcType=VARCHAR},
      </if>
      <if test="informationId != null">
        #{informationId,jdbcType=INTEGER},
      </if>
      <if test="storesType != null">
        #{storesType,jdbcType=INTEGER},
      </if>
      <if test="storesTypeName != null">
        #{storesTypeName,jdbcType=VARCHAR},
      </if>
      <if test="tProductName != null">
        #{tProductName,jdbcType=VARCHAR},
      </if>
      <if test="tStoresName != null">
        #{tStoresName,jdbcType=VARCHAR},
      </if>
      <if test="tStoresTypeName != null">
        #{tStoresTypeName,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="complexName != null">
        #{complexName,jdbcType=VARCHAR},
      </if>
      <if test="complexNameEn != null">
        #{complexNameEn,jdbcType=VARCHAR},
      </if>
      <if test="onlyPoint != null">
        #{onlyPoint,jdbcType=TINYINT},
      </if>
      <if test="actualSales != null">
        #{actualSales,jdbcType=INTEGER},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="isHot != null">
        #{isHot,jdbcType=INTEGER},
      </if>
      <if test="isGreat != null">
        #{isGreat,jdbcType=INTEGER},
      </if>
      <if test="businessCategoriesId != null">
        #{businessCategoriesId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookTemporaryProduct">
    update fook_temporary_product
    <set>
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      <if test="img != null">
        img = #{img,jdbcType=VARCHAR},
      </if>
      <if test="zipImg != null">
        zip_img = #{zipImg,jdbcType=VARCHAR},
      </if>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="shelfStatus != null">
        shelf_status = #{shelfStatus,jdbcType=TINYINT},
      </if>
      <if test="buyStartTime != null">
        buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyEndTime != null">
        buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildMode != null">
        vaild_mode = #{vaildMode,jdbcType=TINYINT},
      </if>
      <if test="vaildStartTime != null">
        vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildEndTime != null">
        vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dayNumber != null">
        day_number = #{dayNumber,jdbcType=INTEGER},
      </if>
      <if test="snapUp != null">
        snap_up = #{snapUp,jdbcType=TINYINT},
      </if>
      <if test="sName != null">
        s_name = #{sName,jdbcType=VARCHAR},
      </if>
      <if test="iName != null">
        i_name = #{iName,jdbcType=VARCHAR},
      </if>
      <if test="iNameEn != null">
        i_name_en = #{iNameEn,jdbcType=VARCHAR},
      </if>
      <if test="informationId != null">
        information_id = #{informationId,jdbcType=INTEGER},
      </if>
      <if test="storesType != null">
        stores_type = #{storesType,jdbcType=INTEGER},
      </if>
      <if test="storesTypeName != null">
        stores_type_name = #{storesTypeName,jdbcType=VARCHAR},
      </if>
      <if test="tProductName != null">
        t_product_name = #{tProductName,jdbcType=VARCHAR},
      </if>
      <if test="tStoresName != null">
        t_stores_name = #{tStoresName,jdbcType=VARCHAR},
      </if>
      <if test="tStoresTypeName != null">
        t_stores_type_name = #{tStoresTypeName,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="complexName != null">
        complex_name = #{complexName,jdbcType=VARCHAR},
      </if>
      <if test="complexNameEn != null">
        complex_name_en = #{complexNameEn,jdbcType=VARCHAR},
      </if>
      <if test="onlyPoint != null">
        only_point = #{onlyPoint,jdbcType=TINYINT},
      </if>
      <if test="actualSales != null">
        actual_sales = #{actualSales,jdbcType=INTEGER},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="isHot != null">
        is_hot = #{isHot,jdbcType=INTEGER},
      </if>
      <if test="isGreat != null">
        is_great = #{isGreat,jdbcType=INTEGER},
      </if>
      <if test="businessCategoriesId != null">
        business_categories_id = #{businessCategoriesId,jdbcType=INTEGER},
      </if>
    </set>
    where temporary_id = #{temporaryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookTemporaryProduct">
    update fook_temporary_product
    set id = #{id,jdbcType=INTEGER},
      img = #{img,jdbcType=VARCHAR},
      zip_img = #{zipImg,jdbcType=VARCHAR},
      businessid = #{businessid,jdbcType=INTEGER},
      title = #{title,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      price = #{price,jdbcType=DECIMAL},
      shelf_status = #{shelfStatus,jdbcType=TINYINT},
      buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      vaild_mode = #{vaildMode,jdbcType=TINYINT},
      vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      day_number = #{dayNumber,jdbcType=INTEGER},
      snap_up = #{snapUp,jdbcType=TINYINT},
      s_name = #{sName,jdbcType=VARCHAR},
      i_name = #{iName,jdbcType=VARCHAR},
      i_name_en = #{iNameEn,jdbcType=VARCHAR},
      information_id = #{informationId,jdbcType=INTEGER},
      stores_type = #{storesType,jdbcType=INTEGER},
      stores_type_name = #{storesTypeName,jdbcType=VARCHAR},
      t_product_name = #{tProductName,jdbcType=VARCHAR},
      t_stores_name = #{tStoresName,jdbcType=VARCHAR},
      t_stores_type_name = #{tStoresTypeName,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      complex_name = #{complexName,jdbcType=VARCHAR},
      complex_name_en = #{complexNameEn,jdbcType=VARCHAR},
      only_point = #{onlyPoint,jdbcType=TINYINT},
      actual_sales = #{actualSales,jdbcType=INTEGER},
      longitude = #{longitude,jdbcType=VARCHAR},
      dimension = #{dimension,jdbcType=VARCHAR},
      is_hot = #{isHot,jdbcType=INTEGER},
      is_great = #{isGreat,jdbcType=INTEGER},
      business_categories_id = #{businessCategoriesId,jdbcType=INTEGER}
    where temporary_id = #{temporaryId,jdbcType=INTEGER}
  </update>
</mapper>