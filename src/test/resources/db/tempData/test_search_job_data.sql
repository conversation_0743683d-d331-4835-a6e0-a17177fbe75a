-- Clear existing test data if any
DELETE FROM fook_temporary_product WHERE id BETWEEN 20001 AND 20010;
DELETE FROM fook_business_product WHERE id BETWEEN 20001 AND 20010;
DELETE FROM fook_business_store_product WHERE productid BETWEEN 20001 AND 20010;
DELETE FROM fook_stores WHERE id BETWEEN 20001 AND 20010;
DELETE FROM fook_business WHERE id BETWEEN 20001 AND 20005;

-- Insert test businesses
INSERT INTO fook_business (id, name, system_type, enbale, created_at, updated_at)
VALUES
(20001, 'Test Business 1', 1, 1, NOW(), NOW()),
(20002, 'Test Business 2', 1, 1, NOW(), NOW()),
(20003, 'Test Business 3', 1, 1, NOW(), NOW());

-- Insert test stores
INSERT INTO fook_stores (id, business_id, name, enable, created_at, updated_at)
VALUES
(20001, 20001, 'Test Store 1', 1, NOW(), NOW()),
(20002, 20001, 'Test Store 2', 1, NOW(), NOW()),
(20003, 20002, 'Test Store 3', 1, NOW(), NOW()),
(20004, 20002, 'Test Store 4', 1, NOW(), NOW()),
(20005, 20003, 'Test Store 5', 1, NOW(), NOW());

-- Insert test products in fook_business_product
INSERT INTO fook_business_product (id, areaid, `type`, businessid, img, zip_img, title, price, retail_price,
                                  stock, history_stock, sales, shelf_status, buy_start_time, buy_end_time,
                                  created_at, updated_at, `enable`, is_hot, is_great, collect_times, platform_type)
VALUES
-- Regular products with 'search job test' in the title for search
(20001, 1, 1, 20001, 'image/test/product1.jpg', 'image/test/zip_product1.jpg', 'Search Job Test Product 1', 10.00, 20.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1),
(20002, 1, 2, 20001, 'image/test/product2.jpg', 'image/test/zip_product2.jpg', 'Search Job Test Product 2', 15.00, 25.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 0, 0, 1),
(20003, 1, 3, 20002, 'image/test/product3.jpg', 'image/test/zip_product3.jpg', 'Search Job Test Product 3', 20.00, 30.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 0, 1, 0, 1),
(20004, 1, 4, 20002, 'image/test/product4.jpg', 'image/test/zip_product4.jpg', 'Search Job Test Product 4', 25.00, 35.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 0, 0, 0, 1),
(20005, 1, 5, 20003, 'image/test/product5.jpg', 'image/test/zip_product5.jpg', 'Search Job Test Product 5', 30.00, 40.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1);

-- Associate products with stores
INSERT INTO fook_business_store_product (productid, storeid, type, businessid)
VALUES
(20001, 20001, 1, 20001),
(20002, 20002, 1, 20001),
(20003, 20003, 1, 20002),
(20004, 20004, 1, 20002),
(20005, 20005, 1, 20003);
