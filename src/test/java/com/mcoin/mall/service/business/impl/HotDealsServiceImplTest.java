package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.model.HotDealsRequest;
import com.mcoin.mall.model.HotDealsResponse;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.MiniProgramDisplayUtil;
import com.mcoin.mall.constant.ClientTypeEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

public class HotDealsServiceImplTest{

    @Mock
    private ContextHolder contextHolder;

    @Mock
    private FookBusinessDao fookBusinessDao;

    @Mock
    private FookBusinessProductDao fookBusinessProductDao;

    @InjectMocks
    private HotDealsServiceImpl hotDealsService;

    private MockedStatic<ConfigUtils> configUtilsMock;
    private MockedStatic<MiniProgramDisplayUtil> miniProgramDisplayUtilMock;

    @BeforeEach
    public void setUp() {
        configUtilsMock = mockStatic(ConfigUtils.class);
        miniProgramDisplayUtilMock = mockStatic(MiniProgramDisplayUtil.class);
        MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    public void tearDown() {
        configUtilsMock.close();
        miniProgramDisplayUtilMock.close();
    }


    @Test
    public void testHotDealsValidProductList() {
        // Arrange
        HotDealsRequest request = new HotDealsRequest();
        List<FookBusinessProduct> productList = new ArrayList<>();
        FookBusinessProduct product = new FookBusinessProduct();
        product.setId(1);
        product.setTitle("Test Product");
        product.setPrice(new BigDecimal("100.00"));
        product.setRetailPrice(new BigDecimal("90.00"));
        product.setZipImg("zipImage.jpg");
        product.setBusinessid(1);
        product.setBuyEndTime(new java.util.Date());
        product.setBuyStartTime(new java.util.Date());
        product.setType(1);
        product.setHrefUrl("http://example.com");
        product.setGoodsId(12345);
        productList.add(product);

        when(MiniProgramDisplayUtil.filterMiniprogramProduct(any())).thenReturn(Boolean.FALSE);
        when(fookBusinessProductDao.selectHotDeals(any())).thenReturn(productList);
        when(fookBusinessDao.selectByPrimaryKeyTranslations(any(), anyString())).thenReturn(new FookBusiness());
        when(fookBusinessProductDao.selectByPrimaryKeyWithTranslations(any(), anyString())).thenReturn(product);
        when(contextHolder.getLocale()).thenReturn(Locale.ENGLISH);
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl")).thenReturn("http://oss.example.com/");

        // Act
        HotDealsResponse response = hotDealsService.hotDeals(request, null);

        // Assert
        assertEquals(1, response.getSnatchList().size());
        assertEquals("Test Product", response.getSnatchList().get(0).getTitle());
        assertEquals("http://example.com", response.getSnatchList().get(0).getHrefUrl());
        assertEquals("http://oss.example.com/zipImage.jpg", response.getSnatchList().get(0).getImg());
        assertEquals("100.00", response.getSnatchList().get(0).getPrice());
        assertEquals("90.00", response.getSnatchList().get(0).getRetailPrice());
    }

    @Test
    public void testHotDealsEmptyProductList() {
        // Arrange
        HotDealsRequest request = new HotDealsRequest();
        when(MiniProgramDisplayUtil.filterMiniprogramProduct(any())).thenReturn(Boolean.FALSE);
        when(fookBusinessProductDao.selectHotDeals(any())).thenReturn(new ArrayList<>());

        // Act
        HotDealsResponse response = hotDealsService.hotDeals(request, null);

        // Assert
        assertNull(response.getSnatchList());
    }

    @Test
    public void testHotDealsWithHarmonyClientAndSwitchEnabled() {
        // Arrange
        HotDealsRequest request = new HotDealsRequest();
        List<FookBusinessProduct> filteredProductList = new ArrayList<>();
        FookBusinessProduct product = new FookBusinessProduct();
        product.setId(1);
        product.setTitle("Test Product");
        product.setPrice(new BigDecimal("100.00"));
        product.setRetailPrice(new BigDecimal("90.00"));
        product.setZipImg("zipImage.jpg");
        product.setBusinessid(1);
        product.setBuyEndTime(new java.util.Date());
        product.setBuyStartTime(new java.util.Date());
        product.setType(1);
        // Set type to a non-miniprogram product type
        product.setType(5); // Not a miniprogram product (POINTS_REDEMPTION)
        product.setHrefUrl("http://example.com");
        product.setGoodsId(12345);
        filteredProductList.add(product);

        // Mock the MiniProgramDisplayUtil to return TRUE for HARMONY client type
        when(MiniProgramDisplayUtil.filterMiniprogramProduct(eq(ClientTypeEnum.HARMONY.toString()))).thenReturn(Boolean.TRUE);

        // Mock the DAO to return filtered products
        when(fookBusinessProductDao.selectHotDeals(eq(Boolean.TRUE))).thenReturn(filteredProductList);
        when(fookBusinessDao.selectByPrimaryKeyTranslations(any(), anyString())).thenReturn(new FookBusiness());
        when(fookBusinessProductDao.selectByPrimaryKeyWithTranslations(any(), anyString())).thenReturn(product);
        when(contextHolder.getLocale()).thenReturn(Locale.ENGLISH);
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl")).thenReturn("http://oss.example.com/");

        // Act
        HotDealsResponse response = hotDealsService.hotDeals(request, ClientTypeEnum.HARMONY.toString());

        // Assert
        assertEquals(1, response.getSnatchList().size());
        assertEquals("Test Product", response.getSnatchList().get(0).getTitle());

        // Verify that selectHotDeals was called with the correct parameter
        verify(fookBusinessProductDao).selectHotDeals(eq(Boolean.TRUE));
    }

    @Test
    public void testHotDealsWithNonHarmonyClient() {
        // Arrange
        HotDealsRequest request = new HotDealsRequest();
        List<FookBusinessProduct> productList = new ArrayList<>();
        FookBusinessProduct product = new FookBusinessProduct();
        product.setId(1);
        product.setTitle("Test Product");
        product.setPrice(new BigDecimal("100.00"));
        product.setRetailPrice(new BigDecimal("90.00"));
        product.setZipImg("zipImage.jpg");
        product.setBusinessid(1);
        product.setBuyEndTime(new java.util.Date());
        product.setBuyStartTime(new java.util.Date());
        product.setType(1);
        // Set type to the miniprogram product type
        product.setType(12); // This is a miniprogram product (PHYSICAL_LINK)
        product.setHrefUrl("http://example.com");
        product.setGoodsId(12345);
        productList.add(product);

        // Mock the MiniProgramDisplayUtil to return FALSE for non-HARMONY client type
        when(MiniProgramDisplayUtil.filterMiniprogramProduct(eq(ClientTypeEnum.IOS.toString()))).thenReturn(Boolean.FALSE);

        // Mock the DAO to return all products
        when(fookBusinessProductDao.selectHotDeals(eq(Boolean.FALSE))).thenReturn(productList);
        when(fookBusinessDao.selectByPrimaryKeyTranslations(any(), anyString())).thenReturn(new FookBusiness());
        when(fookBusinessProductDao.selectByPrimaryKeyWithTranslations(any(), anyString())).thenReturn(product);
        when(contextHolder.getLocale()).thenReturn(Locale.ENGLISH);
        when(ConfigUtils.getProperty("macaupass.oss_prefixUrl")).thenReturn("http://oss.example.com/");

        // Act
        HotDealsResponse response = hotDealsService.hotDeals(request, ClientTypeEnum.IOS.toString());

        // Assert
        assertEquals(1, response.getSnatchList().size());
        assertEquals("Test Product", response.getSnatchList().get(0).getTitle());

        // Verify that selectHotDeals was called with the correct parameter
        verify(fookBusinessProductDao).selectHotDeals(eq(Boolean.FALSE));
    }

}