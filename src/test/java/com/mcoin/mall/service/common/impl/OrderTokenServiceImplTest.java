package com.mcoin.mall.service.common.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;

import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import com.auth0.jwt.interfaces.DecodedJWT;
import com.mcoin.mall.constant.OrderTokenStatus;
import com.mcoin.mall.security.TokenManager;
import com.mcoin.mall.util.CacheNameUtils;
import com.mcoin.mall.util.ConfigUtils;

@ExtendWith(MockitoExtension.class)
class OrderTokenServiceImplTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private TokenManager tokenManager;

    @Mock
    private RBucket<String> bucket;

    @InjectMocks
    private OrderTokenServiceImpl orderTokenService;

    @BeforeEach
    void setUp() {
    }

    @Test
    void testCreateOrderToken() {
        when(redissonClient.<String>getBucket(anyString())).thenReturn(bucket);
        try (MockedStatic<ConfigUtils> configUtilsMock = Mockito.mockStatic(ConfigUtils.class);
             MockedStatic<CacheNameUtils> cacheNameUtilsMock = Mockito.mockStatic(CacheNameUtils.class)) {
            // Mock static methods
            configUtilsMock.when(() -> ConfigUtils.getProperty(anyString(), anyString())).thenReturn("120");
            cacheNameUtilsMock.when(() -> CacheNameUtils.getCreateOrderToken(anyInt())).thenReturn("testKey");

            // Mock token generation
            when(tokenManager.createOrderJwtToken(anyString(), anyLong())).thenReturn("testToken");

            // Execute
            String token = orderTokenService.createOrderToken(123);

            // Verify
            assertNotNull(token);
            assertEquals("testToken", token);
            verify(bucket).set(eq("testToken"), eq(120L), eq(TimeUnit.MINUTES));
        }
    }

    @Test
    void testRemoveOrderToken() {
        when(redissonClient.<String>getBucket(anyString())).thenReturn(bucket);
        try (MockedStatic<CacheNameUtils> cacheNameUtilsMock = Mockito.mockStatic(CacheNameUtils.class)) {
            // Mock static method
            cacheNameUtilsMock.when(() -> CacheNameUtils.getCreateOrderToken(anyInt())).thenReturn("testKey");

            // Mock bucket deletion
            when(bucket.delete()).thenReturn(true);

            // Execute
            boolean result = orderTokenService.removeOrderToken(123);

            // Verify
            assertTrue(result);
            verify(bucket).delete();
        }
    }

    @Test
    void testCheckOrderToken_Valid() {
        when(redissonClient.<String>getBucket(anyString())).thenReturn(bucket);
        try (MockedStatic<CacheNameUtils> cacheNameUtilsMock = Mockito.mockStatic(CacheNameUtils.class)) {
            // Mock static method
            cacheNameUtilsMock.when(() -> CacheNameUtils.getCreateOrderToken(anyInt())).thenReturn("testKey");

            // Mock token validation
            DecodedJWT decodedJWT = mock(DecodedJWT.class);
            when(tokenManager.getJwtToken(anyString())).thenReturn(decodedJWT);
            when(bucket.get()).thenReturn("testToken");

            // Execute
            OrderTokenStatus status = orderTokenService.checkOrderToken(123, "testToken");

            // Verify
            assertEquals(OrderTokenStatus.VALID, status);
        }
    }

    @Test
    void testCheckOrderToken_Expired() {
         // Mock token validation
         when(tokenManager.getJwtToken(anyString())).thenReturn(null);
            
         // Execute
         OrderTokenStatus status = orderTokenService.checkOrderToken(123, "invalidToken");
         
         // Verify
         assertEquals(OrderTokenStatus.EXPIRED, status);

        // 验证redissonClient.getBucket()没有被调用
        verify(redissonClient, never()).getBucket(anyString());
    }

    @Test
    void testCheckOrderToken_EmptyToken() {
        // Execute
        OrderTokenStatus status = orderTokenService.checkOrderToken(123, "");

        // Verify
        assertEquals(OrderTokenStatus.EXPIRED, status);
    }

    @Test
    void testCheckOrderToken_Invalid() {
        when(redissonClient.<String>getBucket(anyString())).thenReturn(bucket);
        try (MockedStatic<CacheNameUtils> cacheNameUtilsMock = Mockito.mockStatic(CacheNameUtils.class)) {
            // Mock static method
            cacheNameUtilsMock.when(() -> CacheNameUtils.getCreateOrderToken(anyInt())).thenReturn("testKey");

            // Mock token validation
            DecodedJWT decodedJWT = mock(DecodedJWT.class);
            when(tokenManager.getJwtToken(anyString())).thenReturn(decodedJWT);
            when(bucket.get()).thenReturn("differentToken");

            // Execute
            OrderTokenStatus status = orderTokenService.checkOrderToken(123, "testToken");

            // Verify
            assertEquals(OrderTokenStatus.INVALID, status);
        }
    }
} 