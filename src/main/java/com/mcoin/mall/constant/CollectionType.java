package com.mcoin.mall.constant;

public enum CollectionType {
    // 门店
    STORE(1),
    // 商品
    PRODUCT(2);

    // 定义一个私有成员变量，用于存储收藏类型的代码
    private final int code;

    // 枚举的构造函数必须是私有的
    private CollectionType(int code) {
        this.code = code;
    }

    // 提供一个公共方法，用于获取收藏类型的代码
    public int getCode() {
        return this.code;
    }

    // 提供一个根据代码获取枚举实例的静态方法（可选）
    public static CollectionType fromCode(int code) {
        for (CollectionType type : CollectionType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("No constant with code " + code + " found in "
                + "enum " + CollectionType.class.getSimpleName());
    }
}
