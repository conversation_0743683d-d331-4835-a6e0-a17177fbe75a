INSERT INTO `fook_show_snapup_session` (`id`,`start_time`, `end_time`, `cn_title`, `en_title`, `session_type`, `show_id`, `show_snapup_id`, `valid`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES ( 1,DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d 23:00:00'), DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d 23:00:00'), NULL, NULL, 0, NULL, NULL, 1, '2024-12-11 17:55:33', '2024-12-11 17:55:33', '会计test1', '会计test1');

INSERT INTO `fook_show_snapup_session` (`id`,`start_time`, `end_time`, `cn_title`, `en_title`, `session_type`, `show_id`, `show_snapup_id`, `valid`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES ( 2,DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00'), DATE_FORMAT(NOW(), '%Y-%m-%d 23:00:00'), NULL, NULL, 0, NULL, NULL, 1, '2024-12-11 17:55:33', '2024-12-11 17:55:33', '会计test1', '会计test1');

INSERT INTO `fook_show_snapup_session` (`id`,`start_time`, `end_time`, `cn_title`, `en_title`, `session_type`, `show_id`, `show_snapup_id`, `valid`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES (3, DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 DAY), '%Y-%m-%d 23:00:00'),DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 DAY), '%Y-%m-%d 23:00:00'), NULL, NULL, 0, NULL, NULL, 1, '2024-12-11 17:55:33', '2024-12-11 17:55:33', '会计test1', '会计test1');


INSERT INTO `fook_show_snapup_session` (`id`, `start_time`, `end_time`, `cn_title`, `en_title`, `session_type`, `show_id`, `show_snapup_id`, `valid`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES (180, '2025-04-02 00:00:00', '2025-04-03 00:00:00', NULL, NULL, 0, NULL, NULL, 1, '2025-04-02 09:41:28', '2025-04-02 09:41:28', '会计test1', '会计test1');


