package com.mcoin.mall.exception;

import com.mcoin.mall.model.Response;
import lombok.Getter;

@Getter
public class BusinessException extends RuntimeException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 2271515018718139494L;

	private Response.Code respCode;
	private String respMsg;
	private Object data;
	

	public BusinessException(Response.Code respCode, String respMsg){
		super(respMsg);
		this.respCode = respCode;
		this.respMsg = respMsg;
	}

	public BusinessException(Response.Code respCode, String respMsg, Throwable cause){
		super(respMsg, cause);
		this.respCode = respCode;
		this.respMsg = respMsg;
	}


	public BusinessException(Response.Code respCode, String respMsg, Object data) {
		super(respMsg);
		this.respCode = respCode;
		this.respMsg = respMsg;
		this.data = data;
	}
}
