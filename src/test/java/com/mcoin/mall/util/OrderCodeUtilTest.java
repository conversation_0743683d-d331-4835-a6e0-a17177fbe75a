package com.mcoin.mall.util;

import com.mcoin.mall.constant.BusinessProductIsExportEnum;
import com.mcoin.mall.constant.BusinessProductTypeEnum;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class OrderCodeUtilTest{


    @Test
    public void testRandOrderFor() {
        // 调用被测方法
        String result = OrderCodeUtil.randOrderFor();

        // 验证结果
        assertNotNull(result, "The result should not be null");
        assertEquals(16, result.length(), "The result should start with '1800'");
        assertTrue(result.startsWith("1800"), "The result should start with '1800'");
    }

    @Test
    public void testRand() {
        // Generate a random order code
        String orderCode = OrderCodeUtil.rand();
        // Check that the length of the generated code is 12 characters
        assertEquals(12, orderCode.length(), "Generated order code should be 12 characters long");
        // Check that the generated code only contains digits
        assertTrue(orderCode.matches("\\d+"), "Generated order code should only contain digits");
    }

    @Test
    public void testMatchImportTypeByCreateOrderCode_CoinCashCoupon() {
        // 调用被测方法
        boolean result = OrderCodeUtil.matchImportTypeByCreateOrderCode(
                BusinessProductTypeEnum.COIN_CASH_COUPON.getTypeId(),
                BusinessProductIsExportEnum.MANUAL_IMPORT.getType()
        );

        // 验证结果
        assertTrue(result, "The result should be true for COIN_CASH_COUPON and MANUAL_IMPORT");
    }

    @Test
    public void testMatchImportTypeByCreateOrderCode_TransactionVerificationCoupon() {
        // 调用被测方法
        boolean result = OrderCodeUtil.matchImportTypeByCreateOrderCode(
                BusinessProductTypeEnum.TRANSACTION_VERIFICATION_COUPON.getTypeId(),
                BusinessProductIsExportEnum.MANUAL_IMPORT.getType()
        );

        // 验证结果
        assertTrue(result, "The result should be true for TRANSACTION_VERIFICATION_COUPON and MANUAL_IMPORT");
    }

    @Test
    public void testMatchImportTypeByCreateOrderCode_ThirdPartyVoucher() {
        // 调用被测方法
        boolean result = OrderCodeUtil.matchImportTypeByCreateOrderCode(
                BusinessProductTypeEnum.THIRD_PARTY_VOUCHER.getTypeId(),
                BusinessProductIsExportEnum.MANUAL_IMPORT.getType()
        );

        // 验证结果
        assertTrue(result, "The result should be true for THIRD_PARTY_VOUCHER and MANUAL_IMPORT");
    }

    @Test
    public void testMatchImportTypeByCreateOrderCode_NonImport() {
        // 调用被测方法
        boolean result = OrderCodeUtil.matchImportTypeByCreateOrderCode(
                BusinessProductTypeEnum.COIN_CASH_COUPON.getTypeId(),
                BusinessProductIsExportEnum.SYSTEM_GENERATED.getType()
        );

        // 验证结果
        assertFalse(result, "The result should be false for COIN_CASH_COUPON and MANUAL_IMPORT");
    }

    @Test
    public void testMatchImportTypeByCreateOrderCode_NonCoinCashCoupon() {
        // 调用被测方法
        boolean result = OrderCodeUtil.matchImportTypeByCreateOrderCode(
                999,
                BusinessProductIsExportEnum.MANUAL_IMPORT.getType()
        );

        // 验证结果
        assertFalse(result, "The result should be false for non-COIN_CASH_COUPON and MANUAL_IMPORT");
    }

}