package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.model.SignConfigResponse;
import com.mcoin.mall.model.SignRequest;
import com.mcoin.mall.service.business.SignService;
import com.mcoin.mall.util.McoinMall;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SignServiceImpl implements SignService {

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public SignConfigResponse getSignConfig(SignRequest request) {
        String language = contextHolder.getLocale().getLanguage();
        String copyWritingPrefix = "sign_copywriting";
        String redisCopywritingKey = StringUtils.equals(McoinMall.LANG_EN,language)
                ? StringUtils.join(copyWritingPrefix,language):copyWritingPrefix;
        SignConfigResponse response = new SignConfigResponse();
        Integer showImg = ObjectUtils.defaultIfNull(request.getShowImg(),0);
        String imageUrl = (String) redissonClient.getBucket("sign_image_url").get();
        String signHttpUrl = (String) redissonClient.getBucket("sign_http_url").get();
        String title = (String) redissonClient.getBucket(redisCopywritingKey).get();
        if (StringUtils.isBlank(imageUrl) || 0 == showImg) {
            imageUrl = "";
            signHttpUrl = "";
        }
        response.setImageUrl(imageUrl);
        response.setHttpUrl(signHttpUrl);
        response.setTitle(title);
        return response;
    }
}