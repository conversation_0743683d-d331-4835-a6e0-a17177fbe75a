<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookCouponSynDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookCouponSyn">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="code_id" jdbcType="INTEGER" property="codeId" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, code_id, `operation`, `type`, message, `status`, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_coupon_syn
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_coupon_syn
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookCouponSyn">
    insert into fook_coupon_syn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="codeId != null">
        code_id,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="codeId != null">
        #{codeId,jdbcType=INTEGER},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookCouponSyn">
    update fook_coupon_syn
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="codeId != null">
        code_id = #{codeId,jdbcType=INTEGER},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookCouponSyn">
    update fook_coupon_syn
    set order_id = #{orderId,jdbcType=INTEGER},
      code_id = #{codeId,jdbcType=INTEGER},
      `operation` = #{operation,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      message = #{message,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>