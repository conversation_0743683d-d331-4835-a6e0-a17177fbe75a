package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookBusiness;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessProductTranslations;
import com.mcoin.mall.bo.StoreDistanceBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookBusinessDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.ShareProductResponse;
import com.mcoin.mall.service.business.BusinessService;
import com.mcoin.mall.util.AESUtil;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Locale;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static org.apache.commons.lang3.StringUtils.defaultIfBlank;

@Slf4j
@Service
public class BusinessServiceImpl implements BusinessService {
    @Resource
    private MessageSource messageSource;
    @Resource
    private ContextHolder contextHolder;
    @Resource
    private FookBusinessProductDao fookBusinessProductDao;
    @Resource
    private FookBusinessDao fookBusinessDao;
    @Resource
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;
    @Resource
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;
    @Override
    public ShareProductResponse getShareProduct(String id) {
        Locale locale = contextHolder.getLocale();
        String key = ConfigUtils.getProperty("security.product.share.secret");
        Integer productId = null;
        try {
            productId = Integer.valueOf(AESUtil.decryptFromHex(key, id));
        } catch (Exception e) {
            log.error("解密失败", e);
            String message = messageSource.getMessage("message.basic.parameter", null, locale);
            throw new BusinessException(Response.Code.BAD_REQUEST, message);
        }

        FookBusinessProduct product = fookBusinessProductDao.selectByPrimaryKey(productId);
        if (product == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.platform", null, locale));
        }
        FookBusiness business = fookBusinessDao.selectByPrimaryKey(product.getBusinessid());
        if (business == null) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.platform", null, locale));
        }

        int mpay_val = product.getPointRatio();
        int minPoint = defaultIfNull(product.getMinPoint(), 0);
        int point = business.getSystemType() == 3 ? product.getMaximumPoints() :
                (product.getOnlyPoint() == 1 ? (product.getPrice().multiply(BigDecimal.valueOf(mpay_val))).intValue() :
                        (product.getOnlyPoint() == 2 ? 0 : minPoint));
        BigDecimal preferential = product.getOnlyPoint() == 1 ? BigDecimal.ZERO : (product.getOnlyPoint() == 2 ? product.getPrice() :
                (product.getPrice().subtract(BigDecimal.valueOf(minPoint > 0 ? minPoint : (mpay_val / 2)).divide(BigDecimal.valueOf(mpay_val), 2, RoundingMode.HALF_UP))));

        //穫取語言
        String title = product.getTitle();
        if (McoinMall.LANG_EN.equals(locale.getLanguage())) {
            FookBusinessProductTranslations fookBusinessProductTranslations =
                    fookBusinessProductTranslationsDao.getTranslationsById(productId, locale.getLanguage());
            if (fookBusinessProductTranslations != null) {
                title = defaultIfBlank(fookBusinessProductTranslations.getTTitle(), title);
            }
        }
        List<StoreDistanceBo> storeDistanceBos = fookBusinessStoreProductDao.getStoreDistanceListByProductId(productId);
        //此福利已下架
        if (storeDistanceBos.isEmpty()) {
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.order.platform", null, locale));
        }
        StoreDistanceBo storeDistanceBo = storeDistanceBos.get(0);

        ShareProductResponse response = new ShareProductResponse();
        response.setImg(OssUtil.getProductImg(product.getImg()));
        response.setName(title);
        response.setRetailPrice(product.getRetailPrice());
        response.setPoint(point);
        response.setPreferential(preferential);
        response.setStoreName(storeDistanceBo.getStoreName());
        response.setSnapUp(product.getSnapUp());
        String romoteUrl = ConfigUtils.getProperty("macaupass.romote_url", "");
        response.setRedirectUrl(romoteUrl.replace("product_id", productId + ""));
        return response;
    }
}
