package com.mcoin.mall.service.pay.impl;

import com.mcoin.mall.bean.*;
import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.mq.model.RefundSuccessMessage;
import com.mcoin.mall.service.base.MqLocalService;
import com.mcoin.mall.util.ConfigUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 测试PayBaseServiceImpl的uploadRefundSuccess方法
 */
public class PayBaseServiceImplUploadRefundSuccessTest {

    @Mock
    private FookPlatformOrderDao fookPlatformOrderDao;
    
    @Mock
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    
    @Mock
    private FookMacaupassUserDao fookMacaupassUserDao;
    
    @Mock
    private FookBusinessDao fookBusinessDao;
    
    @Mock
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;
    
    @Mock
    private FookPayLogDao fookPayLogDao;
    
    @Mock
    private MqLocalService mqLocalService;
    
    @Mock
    private ApplicationContext applicationContext;
    
    @InjectMocks
    private PayBaseServiceImpl payBaseService;
    
    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }
    
    /**
     * 测试场景：商家在黑名单中，不上传退款记录
     * 输入：
     *   - 退款支付日志
     *   - 退款场景
     * 期望输出：
     *   - 不执行上传操作
     *   - 记录日志信息
     * 说明：当商家在黑名单中时，系统应跳过上传退款记录的操作
     */
    @Test
    void testUploadRefundSuccess_SellerInBlacklist() {
        // given
        FookPayLog payLog = createMockPayLog();
        String refundScene = RefundSceneEnum.APPROVAL_REFUND.getCode();
        
        FookPlatformOrder order = createMockOrder();
        order.setSellerid(4); // 设置为黑名单中的商家ID
        
        try (MockedStatic<ConfigUtils> mockedStatic = Mockito.mockStatic(ConfigUtils.class)) {
            // 模拟黑名单配置
            mockedStatic.when(() -> ConfigUtils.getProperty(eq("task.unupload.uploadRefundSuccess.sellers")))
                       .thenReturn("3,4,5");
            
            when(fookPlatformOrderDao.selectByPrimaryKey(anyInt())).thenReturn(order);
            
            // when
            payBaseService.uploadRefundSuccess(payLog, refundScene);
            
            // then
            verify(fookPlatformOrderDao, times(1)).selectByPrimaryKey(eq(payLog.getOrderid()));
            verify(fookPlatformOrderrefundDao, never()).selectByPrimaryKey(anyInt());
            verify(fookMacaupassUserDao, never()).selectOne(any());
            verify(fookBusinessDao, never()).selectByPrimaryKey(anyInt());
            verify(mqLocalService, never()).saveMqLocal(any(FookMqLocal.class));
            verify(applicationContext, never()).publishEvent(any());
        }
    }
    
    /**
     * 测试场景：退款场景为INVALID_ORDER但没有有效的支付记录
     * 输入：
     *   - 退款支付日志
     *   - 退款场景为INVALID_ORDER
     * 期望输出：
     *   - 不执行上传操作
     *   - 记录日志信息
     * 说明：当退款场景为INVALID_ORDER但找不到有效的支付记录时，系统应跳过上传退款记录的操作
     */
    @Test
    void testUploadRefundSuccess_InvalidOrderSceneNoPayRecord() {
        // given
        FookPayLog payLog = createMockPayLog();
        String refundScene = RefundSceneEnum.INVALID_ORDER.getCode();
        
        FookPlatformOrder order = createMockOrder();
        
        try (MockedStatic<ConfigUtils> mockedStatic = Mockito.mockStatic(ConfigUtils.class)) {
            // 模拟黑名单配置
            mockedStatic.when(() -> ConfigUtils.getProperty(eq("task.unupload.uploadRefundSuccess.sellers")))
                       .thenReturn("3,5,7");
            
            when(fookPlatformOrderDao.selectByPrimaryKey(anyInt())).thenReturn(order);
            when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(createMockOrderRefund());
            when(fookPayLogDao.selectOne(any())).thenReturn(null); // 没有有效的支付记录
            
            // when
            payBaseService.uploadRefundSuccess(payLog, refundScene);
            
            // then
            verify(fookPlatformOrderDao, times(1)).selectByPrimaryKey(eq(payLog.getOrderid()));
            verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(payLog.getRefundId()));
            verify(fookPayLogDao, times(1)).selectOne(any());
            verify(fookMacaupassUserDao, never()).selectOne(any());
            verify(fookBusinessDao, never()).selectByPrimaryKey(anyInt());
            verify(mqLocalService, never()).saveMqLocal(any(FookMqLocal.class));
            verify(applicationContext, never()).publishEvent(any());
        }
    }
    
    /**
     * 测试场景：成功上传退款记录
     * 输入：
     *   - 退款支付日志
     *   - 退款场景为APPROVAL_REFUND
     * 期望输出：
     *   - 构建退款请求并保存到MQ本地表
     *   - 发布退款成功消息事件
     * 说明：测试正常情况下上传退款记录的完整流程
     */
    @Test
    void testUploadRefundSuccess_Success() {
        // given
        FookPayLog payLog = createMockPayLog();
        String refundScene = RefundSceneEnum.APPROVAL_REFUND.getCode();
        
        FookPlatformOrder order = createMockOrder();
        FookMacaupassUser macaupassUser = createMockMacaupassUser();
        FookBusiness business = createMockBusiness();
        FookPlatformOrderrefund refund = createMockOrderRefund();
        List<FookPlatformOrderrefund> refundList = new ArrayList<>();
        refundList.add(refund);
        
        try (MockedStatic<ConfigUtils> mockedStatic = Mockito.mockStatic(ConfigUtils.class)) {
            // 模拟黑名单配置
            mockedStatic.when(() -> ConfigUtils.getProperty(eq("task.unupload.uploadRefundSuccess.sellers")))
                       .thenReturn("3,5,7");
            
            when(fookPlatformOrderDao.selectByPrimaryKey(anyInt())).thenReturn(order);
            when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(refund);
            when(fookMacaupassUserDao.selectOne(any())).thenReturn(macaupassUser);
            when(fookBusinessDao.selectByPrimaryKey(anyInt())).thenReturn(business);
            when(fookPlatformOrderrefundDao.selectList(any())).thenReturn(refundList);
            when(mqLocalService.saveMqLocal(any(FookMqLocal.class))).thenReturn(1);
            doNothing().when(applicationContext).publishEvent(any(RefundSuccessMessage.class));
            
            // when
            payBaseService.uploadRefundSuccess(payLog, refundScene);
            
            // then
            verify(fookPlatformOrderDao, times(1)).selectByPrimaryKey(eq(payLog.getOrderid()));
            verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(payLog.getRefundId()));
            verify(fookMacaupassUserDao, times(1)).selectOne(any());
            verify(fookBusinessDao, times(1)).selectByPrimaryKey(eq(order.getSellerid()));
            verify(fookPlatformOrderrefundDao, times(1)).selectList(any());
            verify(mqLocalService, times(1)).saveMqLocal(any(FookMqLocal.class));
            verify(applicationContext, times(1)).publishEvent(any(RefundSuccessMessage.class));
        }
    }
    
    /**
     * 测试场景：退款场景为INVALID_ORDER且有有效的支付记录
     * 输入：
     *   - 退款支付日志
     *   - 退款场景为INVALID_ORDER
     * 期望输出：
     *   - 构建退款请求并保存到MQ本地表
     *   - 发布退款成功消息事件
     * 说明：测试退款场景为INVALID_ORDER且有有效支付记录时的处理流程
     */
    @Test
    void testUploadRefundSuccess_InvalidOrderSceneWithPayRecord() {
        // given
        FookPayLog payLog = createMockPayLog();
        String refundScene = RefundSceneEnum.INVALID_ORDER.getCode();
        
        FookPlatformOrder order = createMockOrder();
        FookMacaupassUser macaupassUser = createMockMacaupassUser();
        FookBusiness business = createMockBusiness();
        FookPlatformOrderrefund refund = createMockOrderRefund();
        List<FookPlatformOrderrefund> refundList = new ArrayList<>();
        refundList.add(refund);
        FookPayLog payRecord = createMockPayLog();
        payRecord.setOparemtion("pay");
        payRecord.setStatus(1);
        
        try (MockedStatic<ConfigUtils> mockedStatic = Mockito.mockStatic(ConfigUtils.class)) {
            // 模拟黑名单配置
            mockedStatic.when(() -> ConfigUtils.getProperty(eq("task.unupload.uploadRefundSuccess.sellers")))
                       .thenReturn("3,5,7");
            
            when(fookPlatformOrderDao.selectByPrimaryKey(anyInt())).thenReturn(order);
            when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(refund);
            when(fookPayLogDao.selectOne(any())).thenReturn(payRecord);
            when(fookMacaupassUserDao.selectOne(any())).thenReturn(macaupassUser);
            when(fookBusinessDao.selectByPrimaryKey(anyInt())).thenReturn(business);
            when(fookPlatformOrderrefundDao.selectList(any())).thenReturn(refundList);
            when(mqLocalService.saveMqLocal(any(FookMqLocal.class))).thenReturn(1);
            doNothing().when(applicationContext).publishEvent(any(RefundSuccessMessage.class));
            
            // when
            payBaseService.uploadRefundSuccess(payLog, refundScene);
            
            // then
            verify(fookPlatformOrderDao, times(1)).selectByPrimaryKey(eq(payLog.getOrderid()));
            verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(payLog.getRefundId()));
            verify(fookPayLogDao, times(1)).selectOne(any());
            verify(fookMacaupassUserDao, times(1)).selectOne(any());
            verify(fookBusinessDao, times(1)).selectByPrimaryKey(eq(order.getSellerid()));
            verify(fookPlatformOrderrefundDao, times(1)).selectList(any());
            verify(mqLocalService, times(1)).saveMqLocal(any(FookMqLocal.class));
            verify(applicationContext, times(1)).publishEvent(any(RefundSuccessMessage.class));
        }
    }
    
    // 创建模拟对象的辅助方法
    private FookPayLog createMockPayLog() {
        FookPayLog payLog = new FookPayLog();
        payLog.setId(1);
        payLog.setOrderid(100);
        payLog.setUid(200);
        payLog.setRefundId(300);
        payLog.setAmount(new BigDecimal("100.00"));
        return payLog;
    }
    
    private FookPlatformOrder createMockOrder() {
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(100);
        order.setUserid(200);
        order.setSellerid(1);
        order.setOrderNo("ORDER123456");
        order.setPaymentTime(new Date());
        order.setOrderAmount(new BigDecimal("100.00"));
        order.setTotalAmount(new BigDecimal("100.00"));
        return order;
    }
    
    private FookPlatformOrderinfo createMockOrderInfo() {
        FookPlatformOrderinfo orderInfo = new FookPlatformOrderinfo();
        orderInfo.setId(1);
        orderInfo.setOrderid(100);
        return orderInfo;
    }
    
    private FookMacaupassUser createMockMacaupassUser() {
        FookMacaupassUser user = new FookMacaupassUser();
        user.setId(1);
        user.setUserId(200);
        user.setPhone("12345678");
        user.setCustomid("CUST123456");
        return user;
    }
    
    private FookBusiness createMockBusiness() {
        FookBusiness business = new FookBusiness();
        business.setId(1);
        business.setCode("BUS123456");
        return business;
    }
    
    private FookPlatformOrderrefund createMockOrderRefund() {
        FookPlatformOrderrefund refund = new FookPlatformOrderrefund();
        refund.setId(300);
        refund.setOrderid(100);
        refund.setRefundOrderno("REFUND123456");
        refund.setRefundTime(new Date());
        refund.setRefundAmount(new BigDecimal("100.00"));
        refund.setPlatformDealStatus(3);
        refund.setActualRefundAmount(new BigDecimal("100.00"));
        return refund;
    }
}
