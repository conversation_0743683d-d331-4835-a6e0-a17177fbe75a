package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.OrderCodeCheckRepeatRequest;
import com.mcoin.mall.service.job.OrderCodeJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/order/code")
@Slf4j
public class OrderCodeJobController {
    @Resource
    private OrderCodeJobService orderCodeJobService;

    /**
     * 每分鐘檢測一次券碼是否有重複
     * PHP(code:update)
     * @return
     */
    @PostMapping(value = "/checkRepeat")
    public Response<String> checkRepeat(@Valid OrderCodeCheckRepeatRequest request) {
        orderCodeJobService.checkRepeat(request);
        return Responses.ok("success");
    }
}
