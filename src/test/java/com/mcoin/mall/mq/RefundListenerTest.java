package com.mcoin.mall.mq;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Locale;

import javax.annotation.Resource;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookExternalVcode;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.bean.FookPlatformOrderrefund;
import com.mcoin.mall.client.model.MPayRefundRequest;
import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookExternalVcodeDao;
import com.mcoin.mall.dao.FookPayLogDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundDao;
import com.mcoin.mall.mq.model.RefundMessage;
import com.mcoin.mall.service.chennel.RefundService;
import com.mcoin.mall.util.SignUtil;

import ch.vorburger.exec.ManagedProcessException;
import lombok.extern.slf4j.Slf4j;

/**
 * 集成测试 RefundListener
 * 测试退款消息处理功能
 */
@Slf4j
public class RefundListenerTest extends BaseUnitTest {

    @Resource
    private RefundListener refundListener;

    @Resource
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;

    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Resource
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Resource
    private FookPayLogDao fookPayLogDao;
    
    @Resource
    private FookBusinessProductDao fookBusinessProductDao;
    
    @Resource
    private FookExternalVcodeDao fookExternalVcodeDao;
    
    @Resource
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;

    @Resource
    private RefundService refundService;


    @BeforeEach
    void setUp() throws ManagedProcessException {
        // 模拟Locale
        when(contextHolder.getLocale()).thenReturn(Locale.CHINESE);

        // 加载测试数据
        try {
            log.info("Loading test data for RefundListenerTest");
            // Load test data
            DB.getDB().source("db/tempData/refund_case.sql", "fooku");
            log.info("Test data loaded successfully");
        } catch (Exception e) {
            log.error("Failed to load test data: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 测试处理退款消息 - 成功场景
     * 验证退款消息被正确处理，并且调用了退款服务
     */
    @Test
    @DisplayName("测试处理退款消息 - 成功场景")
    void testOnMessageSuccess() throws BlockException {
        // 模拟SignUtil.mPayVerifySign方法，使其始终返回true，避免验签失败
        try (MockedStatic<SignUtil> mockedSignUtil = Mockito.mockStatic(SignUtil.class)) {
            // 对所有调用SignUtil.mPayVerifySign的地方都返回true，包括refund和queryRefund方法
            mockedSignUtil.when(() -> SignUtil.mPayVerifySign(anyString(), anyString())).thenReturn(true);
        // 准备测试数据 - 使用已存在的测试数据
        Integer refundId = 10001;
        Integer orderCodeId = 10001;

        // 模拟MPayClient.refund方法返回成功响应
        when(mPayClient.refund(any(MPayRefundRequest.class))).thenReturn(
            "{\"respCode\":\"0000\",\"respMsg\":\"Success\",\"Data\":{\"out_trade_no\":\"TEST_OUT_TRADE_NO\",\"trade_no\":\"TEST_TRADE_NO\",\"out_request_no\":\"TEST_OUT_REQUEST_NO\",\"refund_amount\":100.00,\"gmt_refund_pay\":\"2023-01-01T12:00:00\",\"fund_change\":\"Y\"}}"
        );

        // 模拟MPayClient.queryRefund方法返回成功响应
        when(mPayClient.queryRefund(any())).thenReturn(
            "{\"respCode\":\"0000\",\"respMsg\":\"Success\",\"Data\":{\"out_trade_no\":\"TEST_OUT_TRADE_NO\",\"trade_no\":\"TEST_TRADE_NO\",\"out_request_no\":\"TEST_OUT_REQUEST_NO\",\"refund_amount\":100.00,\"gmt_refund_pay\":\"2023-01-01T12:00:00\",\"fund_change\":\"Y\"}}"
        );

        // 创建退款消息
        RefundMessage refundMessage = new RefundMessage();
        refundMessage.setRefundId(refundId);
        refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO");
        refundMessage.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());

        // 创建RabbitMQ消息
        MessageProperties messageProperties = new MessageProperties();
        Message message = new Message(JSON.toJSONBytes(refundMessage), messageProperties);

        // 调用被测试方法
        refundListener.onMessage(message);

        // 验证MPayClient.refund方法被调用
        verify(mPayClient, times(1)).refund(any(MPayRefundRequest.class));

        // 验证退款记录状态已更新
        FookPlatformOrderrefund orderrefund = fookPlatformOrderrefundDao.selectByPrimaryKey(refundId);
        assertNotNull(orderrefund, "应该能找到退款记录");

        // 验证订单码的退款状态已更新为3（全额退款）
        // 查询订单码表中的退款状态
        FookPlatformOrdercode orderCode = fookPlatformOrdercodeDao.selectById(orderCodeId);
        assertNotNull(orderCode, "应该能找到订单码");
        assertEquals(3, orderCode.getRefundStatus(), "订单码的退款状态应该更新为3（全额退款）");
        }
    }
    
    /**
     * 测试 automaticRefunds 方法 - 验证退款过程中的状态变更和库存处理
     */
    @Test
    @DisplayName("测试 automaticRefunds 方法")
    void testAutomaticRefunds() {
        // 使用测试数据ID 10002，这是专门为此测试准备的数据
        Integer refundId = 10002;
        
        // 获取退款前的状态
        FookPlatformOrderrefund orderRefund = fookPlatformOrderrefundDao.selectByPrimaryKey(refundId);
        assertNotNull(orderRefund, "应该能找到退款记录");
        
        FookPlatformOrder order = fookPlatformOrderDao.selectByPrimaryKey(orderRefund.getOrderid());
        assertNotNull(order, "应该能找到订单");
        
        FookPlatformOrderinfo orderInfo = fookPlatformOrderinfoDao.selectOne(
                new LambdaQueryWrapper<FookPlatformOrderinfo>()
                .eq(FookPlatformOrderinfo::getOrderid, order.getId()));
        assertNotNull(orderInfo, "应该能找到订单信息");
        
        FookBusinessProduct product = fookBusinessProductDao.selectByPrimaryKey(orderInfo.getProdcutid());
        assertNotNull(product, "应该能找到产品");
        
        // 记录退款前的库存和销量
        int initialStock = product.getStock();
        int initialSales = product.getSales();
        
        // 获取关联的订单码并设置其退款状态为退款中(2)，以便自动退款处理
        List<FookPlatformOrdercode> orderCodes = fookPlatformOrdercodeDao.selectList(
                new LambdaQueryWrapper<FookPlatformOrdercode>()
                .eq(FookPlatformOrdercode::getOrderid, order.getId())
                .eq(FookPlatformOrdercode::getRefundid, refundId));
        
        assertTrue(orderCodes.size() > 0, "应该有退款相关的订单码");
        
        // 更新订单码的退款状态和状态
        for (FookPlatformOrdercode code : orderCodes) {
            FookPlatformOrdercode updateCode = new FookPlatformOrdercode();
            updateCode.setId(code.getId());
            // 设置为失效状态和已完成状态
            updateCode.setRefundStatus(3); // 设置为已完成状态
            updateCode.setStatus(3); // 设置为失效状态
            fookPlatformOrdercodeDao.updateById(updateCode);
        }
        
        FookBusinessProduct updateProduct = new FookBusinessProduct();
        updateProduct.setId(product.getId());
        updateProduct.setStock(product.getStock() + orderCodes.size());
        updateProduct.setSales(product.getSales() - orderCodes.size());
        updateProduct.setActualSales(product.getActualSales() - orderCodes.size());
        fookBusinessProductDao.updateById(updateProduct);
        
        // 执行自动退款方法
        refundService.automaticRefunds(refundId);
        
        // 验证退款后状态 - 订单状态
        FookPlatformOrder updatedOrder = fookPlatformOrderDao.selectByPrimaryKey(order.getId());
        
        // 根据是否全部退款判断状态
        int totalOrderCodes = fookPlatformOrdercodeDao.selectCount(
                new LambdaQueryWrapper<FookPlatformOrdercode>()
                .eq(FookPlatformOrdercode::getOrderid, order.getId()));
        
        if (orderInfo.getNumber() == orderCodes.size() && orderCodes.size() == totalOrderCodes) {
            // 全部退款
            assertEquals(4, updatedOrder.getStatus(), "全部退款时订单状态应为4(退款订单)");
            assertEquals(3, updatedOrder.getRefundStatus(), "全部退款时退款状态应为3(子订单全退)");
        } else {
            // 部分退款
            assertEquals(2, updatedOrder.getStatus(), "部分退款时订单状态应为2(已付款)");
            assertEquals(2, updatedOrder.getRefundStatus(), "部分退款时退款状态应为2(部分退款)");
        }
        
        // 验证订单码状态更新
        for (FookPlatformOrdercode code : orderCodes) {
            FookPlatformOrdercode updatedCode = fookPlatformOrdercodeDao.selectById(code.getId());
            assertEquals(3, updatedCode.getStatus(), "订单码状态应更新为3(已失效)");
            assertEquals(3, updatedCode.getRefundStatus(), "订单码退款状态应更新为3(已完成)");
        }
        
        // 验证退款后状态 - 商品库存和销量
        FookBusinessProduct updatedProduct = fookBusinessProductDao.selectByPrimaryKey(product.getId());
        assertEquals(initialStock + orderCodes.size(), updatedProduct.getStock(), 
                "退款后库存应增加退款码数量");
        assertEquals(initialSales - orderCodes.size(), updatedProduct.getSales(), 
                "退款后销量应减少退款码数量");
        
        // 如果是外部券码，验证券码状态更新
        if (product.getIsexport() != null && product.getIsexport() > 0) {
            for (FookPlatformOrdercode code : orderCodes) {
                List<FookExternalVcode> vcodes = fookExternalVcodeDao.selectList(
                        new LambdaQueryWrapper<FookExternalVcode>()
                        .eq(FookExternalVcode::getProductId, product.getId())
                        .eq(FookExternalVcode::getVcode, code.getCode()));
                
                for (FookExternalVcode vcode : vcodes) {
                    assertEquals(0, vcode.getIsascription(), "券码应更新为未绑定状态");
                    assertEquals(1, vcode.getEnable(), "券码应为启用状态");
                }
            }
        }
    }
    
    /**
     * 测试处理退款消息 - 验证MqLocalId更新功能
     * 验证退款消息被正确处理，并且调用了更新MqLocal状态的方法
     */
    @Test
    @DisplayName("测试处理退款消息 - MqLocalId更新功能")
    void testMqLocalIdHandling() throws BlockException {
        // 模拟SignUtil.mPayVerifySign方法，使其始终返回true，避免验签失败
        try (MockedStatic<SignUtil> mockedSignUtil = Mockito.mockStatic(SignUtil.class)) {
            // 对所有调用SignUtil.mPayVerifySign的地方都返回true
            mockedSignUtil.when(() -> SignUtil.mPayVerifySign(anyString(), anyString())).thenReturn(true);
            
            // 准备测试数据 - 使用已存在的测试数据
            Integer refundId = 10001;
            Long mqLocalId = 10001L; // 使用测试用的mqLocalId
            
            // 模拟MPayClient.refund方法返回成功响应
            when(mPayClient.refund(any(MPayRefundRequest.class))).thenReturn(
                "{\"respCode\":\"0000\",\"respMsg\":\"Success\",\"Data\":{\"out_trade_no\":\"TEST_OUT_TRADE_NO\",\"trade_no\":\"TEST_TRADE_NO\",\"out_request_no\":\"TEST_OUT_REQUEST_NO\",\"refund_amount\":100.00,\"gmt_refund_pay\":\"2023-01-01T12:00:00\",\"fund_change\":\"Y\"}}"
            );
            
            // 模拟MPayClient.queryRefund方法返回成功响应
            when(mPayClient.queryRefund(any())).thenReturn(
                "{\"respCode\":\"0000\",\"respMsg\":\"Success\",\"Data\":{\"out_trade_no\":\"TEST_OUT_TRADE_NO\",\"trade_no\":\"TEST_TRADE_NO\",\"out_request_no\":\"TEST_OUT_REQUEST_NO\",\"refund_amount\":100.00,\"gmt_refund_pay\":\"2023-01-01T12:00:00\",\"fund_change\":\"Y\"}}"
            );
            
            // 创建退款消息，设置mqLocalId
            RefundMessage refundMessage = new RefundMessage();
            refundMessage.setRefundId(refundId);
            refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO");
            refundMessage.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());
            refundMessage.setMqLocalId(mqLocalId);
            
            // 创建RabbitMQ消息
            MessageProperties messageProperties = new MessageProperties();
            Message message = new Message(JSON.toJSONBytes(refundMessage), messageProperties);
            
            // 调用被测试方法
            refundListener.onMessage(message);
            
            // 验证MPayClient.refund方法被调用
            verify(mPayClient, times(1)).refund(any(MPayRefundRequest.class));
            
        }
    }
}
