<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns="http://www.springframework.org/schema/beans" xmlns:tx="http://www.springframework.org/schema/tx"
	   xmlns:aop="http://www.springframework.org/schema/aop" xmlns:p="http://www.springframework.org/schema/p"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
     http://www.springframework.org/schema/tx   
     http://www.springframework.org/schema/tx/spring-tx-4.2.xsd


     http://www.springframework.org/schema/aop        
     http://www.springframework.org/schema/aop/spring-aop-4.2.xsd">

	<!-- configuration bean -->

	<aop:config>
		<aop:advisor advice-ref="txAdvice"
			pointcut="execution(* com.mcoin.mall.service..*.*(..))" />
	</aop:config>

	<tx:advice id="txAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="add*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="save*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="insert*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="create*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="modify*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="upd*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="update*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="del*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="delete*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
			<tx:method name="transaction*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
		</tx:attributes>
	</tx:advice>

</beans>
