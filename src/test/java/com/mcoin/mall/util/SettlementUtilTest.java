package com.mcoin.mall.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class SettlementUtilTest{

    @Test
    public void testGetCodeMessage_CodeNotRevocable() {
        String message = SettlementUtil.getCodeMessage(130);
        assertEquals("当前优惠券不可撤销", message);
    }

    @Test
    public void testGetCodeMessage_CodeNotStarted() {
        String message = SettlementUtil.getCodeMessage(107);
        assertEquals("券碼未到開始時間", message);
    }

    @Test
    public void testGetCodeMessage_NotSatisfiedCondition() {
        String message = SettlementUtil.getCodeMessage(112);
        assertEquals("不滿足滿減條件", message);
    }

    @Test
    public void testGetCodeMessage_CodeSettlementInProgress() {
        String message = SettlementUtil.getCodeMessage(133);
        assertEquals("正在核銷", message);
    }

    @Test
    public void testGetCodeMessage_RuleNotAvailable() {
        String message = SettlementUtil.getCodeMessage(161);
        assertEquals("券規則不可用", message);
    }

    @Test
    public void testGetCodeMessage_SystemError() {
        String message = SettlementUtil.getCodeMessage(500);
        assertEquals("系統出錯", message);
    }

    @Test
    public void testGetCodeMessage_VerificationFailed() {
        String message = SettlementUtil.getCodeMessage(499);
        assertEquals("驗簽失敗", message);
    }

    @Test
    public void testGetCodeMessage_UnknownError() {
        String message = SettlementUtil.getCodeMessage(404);
        assertEquals("未知錯誤", message);
    }

    @Test
    public void testGetCodeMessage_StoreNotExist() {
        String message = SettlementUtil.getCodeMessage(105);
        assertEquals("門店不存在", message);
    }

    @Test
    public void testGetCodeMessage_Success() {
        String message = SettlementUtil.getCodeMessage(100);
        assertEquals("該券碼已核銷", message);
    }

    @Test
    public void testGetCodeMessage_CodeRevocationSuccess() {
        String message = SettlementUtil.getCodeMessage(132);
        assertEquals("撤銷成功", message);
    }

    @Test
    public void testGetCodeMessage_InvalidCodeType() {
        String message = SettlementUtil.getCodeMessage(111);
        assertEquals("福利券類型不正確", message);
    }

    @Test
    public void testGetCodeMessage_CodeSettlementSuccess() {
        String message = SettlementUtil.getCodeMessage(131);
        assertEquals("核銷成功", message);
    }

    @Test
    public void testGetCodeMessage_CodeExpiredOrInvalid() {
        String message = SettlementUtil.getCodeMessage(160);
        assertEquals("該券碼已失效", message);
    }

    @Test
    public void testGetCodeMessage_CodeRevocationFailed() {
        String message = SettlementUtil.getCodeMessage(136);
        assertEquals("撤銷失敗", message);
    }

    @Test
    public void testGetCodeMessage_CodeExpired() {
        String message = SettlementUtil.getCodeMessage(106);
        assertEquals("券碼已過期", message);
    }

    @Test
    public void testGetCodeMessage_CodeNotExist() {
        String message = SettlementUtil.getCodeMessage(101);
        assertEquals("該券碼不存在", message);
    }

    @Test
    public void testGetCodeMessage_BenefitNotExist() {
        String message = SettlementUtil.getCodeMessage(201);
        assertEquals("福利不存在", message);
    }

    @Test
    public void testGetCodeMessage_CodeRevocationInProgress() {
        String message = SettlementUtil.getCodeMessage(134);
        assertEquals("正在撤銷", message);
    }

    @Test
    public void testGetCodeMessage_CodeRefunded() {
        String message = SettlementUtil.getCodeMessage(108);
        assertEquals("券碼為退款狀態", message);
    }

    @Test
    public void testGetCodeMessage_NotSuccess() {
        String message = SettlementUtil.getCodeMessage(102);
        assertEquals("核銷不成功", message);
    }

    @Test
    public void testGetCodeMessage_IdempotentSettlement() {
        String message = SettlementUtil.getCodeMessage(103);
        assertEquals("冪等核銷", message);
    }

    @Test
    public void testGetCodeMessage_CodeNotAvailable() {
        String message = SettlementUtil.getCodeMessage(120);
        assertEquals("券碼在不可用時間", message);
    }

    @Test
    public void testGetCodeMessage_Unhandled() {
        String message = SettlementUtil.getCodeMessage(110);
        assertEquals("未處理", message);
    }

    @Test
    public void testGetCodeMessage_CodeSettlementFailed() {
        String message = SettlementUtil.getCodeMessage(135);
        assertEquals("核銷失敗", message);
    }

    @Test
    public void testGetCodeMessage_BenefitNotApplicable() {
        String message = SettlementUtil.getCodeMessage(451);
        assertEquals("非單獨核銷券不能單獨核銷", message);
    }

    @Test
    public void testGetCodeMessage_RevocationFailed() {
        String message = SettlementUtil.getCodeMessage(109);
        assertEquals("撤銷失敗，參數與原始終端請求號不一致", message);
    }

}