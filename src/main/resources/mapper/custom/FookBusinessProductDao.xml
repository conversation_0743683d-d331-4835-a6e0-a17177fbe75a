<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductDao">

    <select id="countBusinessProductByStoreId" resultType="integer">
        select count(*) from (
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="getBusinessProductByStoreId_part1"/>
        union
        <!-- == 未开卖的抢购商品挂了近2天内秒杀场次（没有到可购买开始时间）允许店铺可见 == -->
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="getBusinessProductByStoreId_part2"/>
        ) t
    </select>

    <select id="getBusinessProductByStoreId" resultMap="BaseResultMap">
        select * from (
            SELECT
            <include refid="Base_Column_List"/>
            <include refid="getBusinessProductByStoreId_part1"/>
            union
            <!-- == 未开卖的抢购商品挂了近2天内秒杀场次（没有到可购买开始时间）允许店铺可见 == -->
            SELECT
            <include refid="Base_Column_List"/>
            <include refid="getBusinessProductByStoreId_part2"/>
        ) t
        <if test="offset != null">
            limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
        </if>
    </select>

    <sql id="getBusinessProductByStoreId_part1">
        FROM fook_business_product fbp
        where fbp.id in (
        select fbsp.productid from fook_business_store_product fbsp where fbsp.storeid = #{storeId,jdbcType=INTEGER}
        )
        and fbp.buy_start_time <![CDATA[ < ]]> #{currentTime}
        and fbp.buy_end_time > #{currentTime,jdbcType=VARCHAR}
        and fbp.shelf_status = 1
        and fbp.`enable` = 1
        and fbp.is_redeem = 0
        and fbp.platform_type = 1
        <if test="harmonySwitch == true">
            <![CDATA[
            AND fbp.type <> 12
            ]]>
        </if>
    </sql>
    <sql id="getBusinessProductByStoreId_part2">
        FROM fook_business_product fbp
        where fbp.id in (
            select fbsp.productid
            from fook_business_store_product fbsp
            join fook_show_snapup_session_product sp on fbsp.productid = sp.product_id
            join fook_show_snapup_session s on sp.session_id = s.id
            where fbsp.storeid = #{storeId,jdbcType=INTEGER}
        and date(s.start_time) >= CURDATE()
        and date(s.start_time) <![CDATA[<=]]> DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        and s.end_time > now()
        and s.valid = 1
        )
        and fbp.shelf_status = 1
        and fbp.`enable` = 1
        and fbp.is_redeem = 0
        and fbp.platform_type = 1
        <if test="harmonySwitch == true">
            <![CDATA[
            AND fbp.type <> 12
            ]]>
        </if>
    </sql>

    <sql id="getSnapUpList_column">
        p.id,
        p.businessid businessId,
        p.title,
        p.zip_img zipImg,
        p.img,
        p.seckill_img seckillImg,
        p.price,
        p.only_point onlyPoint,
        p.snap_up snapUp,
        p.retail_price retailPrice,
        p.stock,
        p.actual_sales actualSales,
        p.orderBy,
        p.maximum_points maximumPoints,
        b.system_type systemType,
        b.name as businessName,
        p.buy_end_time buyEndTime,
        p.buy_start_time buyStartTime,
        p.min_point minPoint,
        p.point_ratio pointRatio,
        p.type type,
        p.href_url hrefUrl,
        p.goods_id goodsId,
        p.subsidy_amount subsidyAmount
    </sql>

    <sql id="getSnapUpList_from">
        from fook_business_product p
        LEFT JOIN fook_business b on p.businessid = b.id
        <include refid="getSnapUpList_where"/>
        and p.subsidy_amount = 0
    </sql>

    <sql id="getSnapUpList_where">
        where p.shelf_status = 1
        and p.`enable` = 1
        and p.is_redeem = 0
        <![CDATA[
          and p.snap_up <> 0
      ]]>
        and p.snap_up_index in (1,3)
        and p.limited_offer = 1
        <if test="harmonySwitch == true">
            <![CDATA[ AND p.type <> 12 ]]>
        </if>
    </sql>

    <sql id="getZoneSnapUpList_from">
        from fook_business_product p
        LEFT JOIN fook_business b on p.businessid = b.id
        where p.id IN (
            SELECT DISTINCT(fszp.product_id)
            FROM
                fook_show fs
                LEFT JOIN fook_show_zone fsz ON fs.id = fsz.show_id
                LEFT JOIN fook_show_zone_product fszp ON fszp.zone_id = fsz.id
            WHERE
                fs.id = #{showId,jdbcType=INTEGER}
                <if test="filterShowInZone">
                    and fszp.show_in_zone = 1
                </if>
                AND fszp.product_id IS NOT NULL
            )
            AND p.shelf_status = 1
        and p.`enable` = 1
        and p.is_redeem = 0
        <![CDATA[
          and p.snap_up <> 0
      ]]>
        and p.snap_up_index in (2,3)
        and p.limited_offer = 1
        <if test="harmonySwitch == true">
            <![CDATA[
            AND p.type <> 12
            ]]>
        </if>
    </sql>

    <sql id="getSnapUpList_orderby">
        ORDER BY p.orderby != 0 desc, p.orderBy asc, (case p.stock when 0 then 0 else 1 end) desc
    </sql>

    <select id="getSnapUpListByNextTime" resultType="com.mcoin.mall.bo.SnapUpItemBo">
        select
        <include refid="getSnapUpList_column"/>
        <include refid="getSnapUpList_from"/>
        and p.buy_start_time >= #{nextTime,jdbcType=TIMESTAMP}
        <include refid="getSnapUpList_orderby"/>
        limit #{limit,jdbcType=INTEGER}
    </select>


    <select id="getSnapUpListByNow" resultType="com.mcoin.mall.bo.SnapUpItemBo">
        select
        <include refid="getSnapUpList_column"/>
        <include refid="getSnapUpList_from"/>
        <![CDATA[
        and p.buy_start_time <= #{now,jdbcType=TIMESTAMP}
        ]]>
        and p.buy_end_time > #{now,jdbcType=TIMESTAMP}
        <include refid="getSnapUpList_orderby"/>
        limit #{limit,jdbcType=INTEGER}
    </select>
    <select id="getZoneSnapUpListByNextTime" resultType="com.mcoin.mall.bo.SnapUpItemBo">
        select
        <include refid="getSnapUpList_column"/>
        <include refid="getZoneSnapUpList_from"/>
        and p.buy_start_time >= #{nextTime,jdbcType=TIMESTAMP}
        <include refid="getSnapUpList_orderby"/>
        limit #{limit,jdbcType=INTEGER}
    </select>


    <select id="getZoneSnapUpListByNow" resultType="com.mcoin.mall.bo.SnapUpItemBo">
        select
        <include refid="getSnapUpList_column"/>
        <include refid="getZoneSnapUpList_from"/>
        <![CDATA[
        and p.buy_start_time <= #{now,jdbcType=TIMESTAMP}
        ]]>
        and p.buy_end_time > #{now,jdbcType=TIMESTAMP}
        <include refid="getSnapUpList_orderby"/>
        limit #{limit,jdbcType=INTEGER}
    </select>


    <select id="getSnapUpListByPage" resultType="com.mcoin.mall.bo.SnapUpItemBo">
        select
        <include refid="getSnapUpList_column"/>
        <include refid="getSnapUpList_from"/>
        <![CDATA[
        and p.buy_start_time <= #{now,jdbcType=TIMESTAMP}
        ]]>
        and p.buy_end_time > #{now,jdbcType=TIMESTAMP}
        <include refid="getSnapUpList_orderby"/>
        limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>

    <select id="getSnapUpListCount" resultType="integer">
        select count(p.id)
        <include refid="getSnapUpList_from"/>
        <![CDATA[
        and p.buy_start_time <= #{now,jdbcType=TIMESTAMP}
        ]]>
        and p.buy_end_time > #{now,jdbcType=TIMESTAMP}
        <include refid="getSnapUpList_orderby"/>
    </select>

    <select id="getShowSnapUpIndexListBySessionId" resultType="com.mcoin.mall.bo.SnapUpItemBo">
        select
        <include refid="getSnapUpList_column"/>
        <include refid="getShowSnapUpListBySessionId_from"/>
        <include refid="getShowSnapUpListBySessionId_where"/>
        <include refid="getShowSnapUpListBySessionId_orderby"/>
        limit #{limit,jdbcType=INTEGER}
    </select>

    <sql id="getShowSnapUpListBySessionId_where">
        and s.`valid` = 1
        and s.session_type in (0, 2)
        and s.id = #{sessionId,jdbcType=INTEGER}
    </sql>

    <sql id="getShowSnapUpListBySessionId_from">
        from fook_show_snapup_session s
        left join fook_show_snapup_session_product sp on s.id = sp.session_id
        left join fook_business_product p on sp.product_id = p.id
        LEFT JOIN fook_business b on p.businessid = b.id
        <include refid="getSnapUpList_where"/>
    </sql>

    <sql id="getShowSnapUpListBySessionId_orderby">
        ORDER BY  (case p.stock when 0 then 0 else 1 end) desc,
            sp.product_sort != 0 desc, sp.product_sort asc, p.subsidy_amount != 0 desc, p.created_at desc
    </sql>

    <select id="getShowSnapUpIndexListPageBySessionId" resultType="com.mcoin.mall.bo.SnapUpItemBo">
        select
        <include refid="getSnapUpList_column"/>
        <include refid="getShowSnapUpListBySessionId_from"/>
        <include refid="getShowSnapUpListBySessionId_where"/>
        <include refid="getShowSnapUpListBySessionId_orderby"/>
        limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>

    <select id="getShowSnapUpIndexListCountBySessionId" resultType="integer">
        select count(p.id)
        <include refid="getShowSnapUpListBySessionId_from"/>
        <include refid="getShowSnapUpListBySessionId_where"/>
    </select>

    <select id="selectHotDeals" resultType="com.mcoin.mall.bean.FookBusinessProduct">
        select
        <include refid="Base_Column_List"/>
        from fook_business_product a
        where a.id in (select DISTINCT productid
        from fook_business_store_product x
        left join fook_stores y on x.storeid = y.id
        where y.`enable` = 1)
        and now() between a.buy_start_time and a.buy_end_time
        and a.shelf_status = 1
        and a.`enable` = 1
        and a.is_redeem = 0
        and a.is_great = 1
        and a.snap_up = 0
        and a.platform_type = 1
        and is_mpay_open = 1
        <if test="harmonySwitch == true">
            <![CDATA[ AND a.type <> 12 ]]>
        </if>
        order by orderby != 0 desc, orderby asc
    </select>

    <select id="selectBusinessProductIdsForSearchValue" resultType="java.lang.Integer">
        select id
        from fook_business_product a left join fook_business_product_translations b
        on a.id = b.business_product_id
        where a.shelf_status = 1
        and a.`enable` = 1
        and a.is_redeem = 0
        and now() between a.buy_start_time and a.buy_end_time
        and a.platform_type = 1
        and a.businessid in (select id from fook_business where system_type in (1,3))
        <choose>
            <when test="lang == 'en'">
                AND b.t_title like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
            </when>
            <otherwise>
                AND a.title like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
            </otherwise>
        </choose>
    </select>
    <select id="selectProductIdCountForSearchValue" resultType="java.lang.Integer">
        select count(id)
        from fook_business_product a left join fook_business_product_translations b
        on a.id = b.business_product_id
        where a.is_mpay_open = 1
        and a.shelf_status = 1
        and a.`enable` = 1
        and a.is_redeem = 0
        and now() between a.buy_start_time and a.buy_end_time
        and a.platform_type = 1
        and a.businessid in (select id from fook_business where system_type in (1,3))
        <if test="searchTerm != null">
            <choose>
                <when test="lang == 'en'">
                    AND b.t_title like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
                </when>
                <otherwise>
                    AND a.title like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
                </otherwise>
            </choose>
        </if>
        <if test="ids != null">
            AND id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and id in (select productid from fook_business_store_product)
    </select>

    <update id="updateDecrStock">
        update fook_business_product set stock = stock - #{decrNumber,jdbcType=INTEGER}
        where id = #{productId,jdbcType=INTEGER}
        and stock >= #{decrNumber,jdbcType=INTEGER}
    </update>

    <update id="updateIncrStock">
        update fook_business_product set stock = stock + #{incrNumber,jdbcType=INTEGER}
        where id = #{productId,jdbcType=INTEGER}
    </update>


    <update id="updateLockStock">
        update fook_business_product
        set stock = stock - #{decrNumber,jdbcType=INTEGER},
            lock_stock = lock_stock + #{decrNumber,jdbcType=INTEGER}
        where id = #{productId,jdbcType=INTEGER}
        and stock >= #{decrNumber,jdbcType=INTEGER}
    </update>

    <update id="updateUnlockStock">
        update fook_business_product
        set stock = stock + #{incrNumber,jdbcType=INTEGER},
            lock_stock = lock_stock - #{incrNumber,jdbcType=INTEGER}
        where id = #{productId,jdbcType=INTEGER}
    </update>

    <sql id="searchProductSql">
        SELECT
        p.id AS id,
        p.img AS img,
        p.zip_img AS zipImg,
        p.businessid AS businessid,
        p.title AS title,
        p.`type` AS type,
        p.retail_price AS retailPrice,
        p.price AS price,
        p.shelf_status AS shelfStatus,
        p.buy_start_time AS buyStartTime,
        p.buy_end_time AS buyEndTime,
        p.vaild_mode AS vaildMode,
        p.vaild_start_time AS vaildStartTime,
        p.vaild_end_time AS vaildEndTime,
        p.day_number AS dayNumber,
        p.snap_up AS snapUp,
        p.only_point AS onlyPoint,
        p.actual_sales AS actualSales,
        p.is_hot AS isHot,
        p.is_great AS isGreat,
        MIN(s.id) AS storeId,
        MIN(bpt.id) AS bptId
        FROM
        `fook_business_product` AS p
        LEFT JOIN fook_business_store_product AS bsp ON p.id = bsp.productid
        LEFT JOIN fook_stores AS s ON s.id = bsp.storeid
        LEFT JOIN fook_business_information AS i ON i.id = s.business_information_id
        LEFT JOIN fook_business_product_translations AS bpt ON bpt.business_product_id = p.id
        WHERE
        p.`is_mpay_open` = 1
        AND s.`enable` = 1
        AND p.`shelf_status` = 1
        AND p.`enable` = 1
        AND p.`is_redeem` = 0
        AND p.`buy_end_time` > NOW()
        AND p.`buy_start_time` <![CDATA[ < ]]> NOW()
        AND p.`platform_type` = 1
        <if test="businessCategoriesIds != null  and businessCategoriesIds.size > 0">
            AND SUBSTRING_INDEX(p.`category_path`, '-', 1)  in
            <foreach collection="businessCategoriesIds" item="businessCategoriesId" open="(" separator="," close=")">
                #{businessCategoriesId,jdbcType=INTEGER}
            </foreach>

        </if>
        AND EXISTS ( SELECT * FROM `fook_business` WHERE p.`businessid` = `fook_business`.`id` AND
        `fook_business`.`system_type` in (1,3) )
        <choose>
            <when test="types != null">
                AND p.`type` in
                <foreach collection="types" item="type" open="(" separator="," close=")">
                    #{type,jdbcType=INTEGER}
                </foreach>
            </when>
            <when test="harmonySwitch == true">
                <![CDATA[ AND p.`type` <> 12 ]]>
            </when>
        </choose>
        <if test="storesTypes != null">
            AND s.id in (
            SELECT stores_id FROM fook_stores_type_class fstc WHERE fstc.stores_type_id IN
            <foreach collection="storesTypes" item="storesType" open="(" separator="," close=")">
                #{storesType,jdbcType=INTEGER}
            </foreach>
            )
        </if>
        <if test="informationIds != null">
            AND i.id in
            <foreach collection="informationIds" item="informationId" open="(" separator="," close=")">
                #{informationId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="search != null">
            AND (CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,''), ifnull(i.name_en,''),
            ifnull(bpt.t_title,'')) like concat('%',#{search,jdbcType=VARCHAR},'%')
            OR CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,'')) like concat('%',#{searchSimple,jdbcType=VARCHAR},'%')
            OR CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,'')) like concat('%',#{searchTraditional,jdbcType=VARCHAR},'%')
            OR CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,'')) like concat('%',#{searchTwTraditional,jdbcType=VARCHAR},'%'))
        </if>
        group by p.id,
        p.img ,
        p.zip_img,
        p.businessid,
        p.title,
        p.`type`,
        p.retail_price,
        p.price,
        p.shelf_status ,
        p.buy_start_time ,
        p.buy_end_time ,
        p.vaild_mode ,
        p.vaild_start_time ,
        p.vaild_end_time ,
        p.day_number ,
        p.snap_up ,
        p.only_point ,
        p.actual_sales ,
        p.is_hot ,
        p.is_great
    </sql>

    <select id="searchProduct" parameterType="com.mcoin.mall.bean.SearchProductQuery"
            resultType="com.mcoin.mall.bean.FookSearchProduct" timeout="5">
        <include refid="searchProductSql"/>
        <choose>
            <when test="orderType == 0 and sortType != 2">
                order by
                ACOS(SIN((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) *SIN((s.dimension * 3.1415) / 180 )
                + COS((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) * COS((s.dimension * 3.1415) / 180 )
                * COS((#{lot,jdbcType=VARCHAR} * 3.1415) / 180 - (s.longitude * 3.1415) / 180 ) ) * 6380 ASC
            </when>
            <when test="orderType == 0 and sortType == 2">
                order by
                ACOS(SIN((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) *SIN((s.dimension * 3.1415) / 180 )
                + COS((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) * COS((s.dimension * 3.1415) / 180 )
                * COS((#{lot,jdbcType=VARCHAR} * 3.1415) / 180 - (s.longitude * 3.1415) / 180 ) ) * 6380 DESC
            </when>
            <when test="orderType == 1  and sortType != 2">
                order by price ASC,id
            </when>
            <when test="orderType == 1  and sortType == 2">
                order by price DESC,id
            </when>
            <when test="orderType == 2">
                order by actual_sales DESC,id
            </when>
            <otherwise>
                order by id
            </otherwise>
        </choose>
        limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>
    <select id="searchProductCount" resultType="java.lang.Integer" timeout="5">
        SELECT COUNT(*)
        FROM
        (
        <include refid="searchProductSql"/>
        ) AS a
    </select>

    <update id="updateIncrSales">
        update fook_business_product
        set sales = sales + #{incrNumber,jdbcType=INTEGER},
        actual_sales = actual_sales + #{incrNumber,jdbcType=INTEGER},
        lock_stock = lock_stock - #{incrNumber,jdbcType=INTEGER}
        where id = #{productId,jdbcType=INTEGER}
    </update>

    <select id="getBusinessProductByIds" resultType="com.mcoin.mall.bean.FookBusinessProduct">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_business_product
        WHERE id in
        <foreach collection="productIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>

    <select id="getBusinessProductByMpayCouponsCodeId" resultType="com.mcoin.mall.bean.FookBusinessProduct">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_business_product
        WHERE mpay_coupons_code_id = #{mpayCouponsCodeId}
    </select>

    <select id="getProductByIdsAndActive" resultType="com.mcoin.mall.bean.FookBusinessProduct">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_business_product
        WHERE id in
        <foreach collection="productIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and shelf_status = 1 and `enable` = 1  and buy_end_time &gt; now() and snap_up = 0
        <if test="harmonySwitch == true">
            <![CDATA[
            AND type <> 12
            ]]>
        </if>
    </select>

    <select id="selectByPrimaryKeyWithTranslations" resultType="com.mcoin.mall.bean.FookBusinessProduct">
        select a.id,
        a.areaid, a.`type`, a.businessid, a.img, a.zip_img, a.imgs, a.zip_imgs, a.price, a.retail_price,
        a.stock, a.history_stock, a.sales, a.actual_sales, a.limited_number, a.is_allow_refund, a.score,
        a.comment_number, a.shelf_status, a.view_number, a.buy_start_time, a.buy_end_time, a.vaild_mode,
        a.vaild_start_time, a.vaild_end_time, a.day_number, a.is_weekend, a.no_useday, a.use_start_time,
        a.use_end_time, a.is_vacation, a.fee_rate, a.`enable`, a.is_hot, a.momecoins_option,
        a.integral_option, threshord, is_redeem, is_momecoin, is_great, collect_times, isexport,
        is_mpay_exchange, a.is_mcoin_open, a.is_mpay_open, a.user_limited_number, a.limited_offer,
        a.snap_up, a.snap_up_index, a.platform_type, a.member_integral, a.`location`, a.coupon_id, a.shop_id,
        a.coupon_type, a.maximum_points, a.istemporary, a.only_point, a.is_settlement, a.img_en, a.mpay_coupons_code_id,
        a.relation_sales, a.product_ids, a.trans_coupon_type, a.meet_money, a.dec_money, a.discount,
        a.max_discount_money, a.stock_a, a.is_a_open, a.third_party_settlement_price, a.a_fee_type,
        a.min_point,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(b.t_title, a.title), a.title) as title,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(b.t_tnc, a.tnc), a.tnc) as tnc,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(b.t_receive_method, a.receive_method), a.receive_method) as
        receive_method
        from fook_business_product a left join fook_business_product_translations b
        on a.id = b.business_product_id
        where a.id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getBusinessProductCntByStoreIds" resultType="com.mcoin.mall.bean.StoreBusinessProductCnt">
        SELECT fbsp.storeid as storesId,count(distinct fbp.id) as cnt FROM fook_business_product fbp
        left join fook_business_store_product fbsp on fbp.id = fbsp.productid
        where fbsp.storeid in
        <foreach collection="storeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND fbp.is_redeem = 0
        AND fbp.shelf_status = 1
        AND fbp.`enable` = 1
        AND fbp.buy_end_time <![CDATA[ > ]]> #{currentTime}
        AND fbp.buy_start_time  <![CDATA[ < ]]> #{currentTime}
        AND fbp.shelf_status = 1
        AND fbp.`enable` = 1
        AND fbp.is_redeem = 0
        AND fbp.snap_up = 0
        AND fbp.platform_type = 1
        <if test="harmonySwitch == true">
            AND (fbp.`type` IS NULL OR fbp.`type` <![CDATA[ <> ]]> 12)
        </if>
        group by fbsp.storeid order by fbsp.storeid
    </select>

    <select id="getStoreProductTypes" resultType="com.mcoin.mall.bean.StoreBusinessProductCnt">
        SELECT fbsp.storeid as storesId, fbp.`type` as type FROM fook_business_product fbp
        left join fook_business_store_product fbsp on fbp.id = fbsp.productid
        where fbsp.storeid in
        <foreach collection="storeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND fbp.is_redeem = 0
        AND fbp.shelf_status = 1
        AND fbp.`enable` = 1
        AND fbp.buy_end_time <![CDATA[ > ]]> #{currentTime}
        AND fbp.buy_start_time  <![CDATA[ < ]]> #{currentTime}
        AND fbp.shelf_status = 1
        AND fbp.`enable` = 1
        AND fbp.is_redeem = 0
        AND fbp.snap_up = 0
        AND fbp.platform_type = 1
    </select>

    <update id="updateStockSalesIncr">
        UPDATE fook_business_product
        SET stock = stock - 1,
        sales = sales + 1,
        actual_sales = actual_sales + 1
        WHERE id = #{id,jdbcType=INTEGER}
        AND stock > 0
        AND is_a_open != 1;
    </update>

    <update id="updateDecrSales">
        update fook_business_product
        set sales = sales - #{decrNumber,jdbcType=INTEGER}
        where id = #{productId,jdbcType=INTEGER}
        AND sales > 0
    </update>

    <update id="updateDecrActualSales">
        update fook_business_product
        set
        actual_sales = actual_sales - #{decrNumber,jdbcType=INTEGER}
        where id = #{productId,jdbcType=INTEGER}
        AND actual_sales > 0
    </update>

    <update id="updateMiniGoodsByUuId">
        update fook_business_product
        <set>
            <if test="stock != null">
                stock = #{stock,jdbcType=INTEGER},
            </if>
            <if test="sales != null">
                sales = #{sales,jdbcType=INTEGER},
            </if>
            <if test="actualSales != null">
                actual_sales = #{actualSales,jdbcType=INTEGER},
            </if>
        </set>
        where mpay_coupons_code_id = #{uuid,jdbcType=VARCHAR}
        AND `type` = 12
    </update>

    <select id="selectStoreTypeProductById" resultType="com.mcoin.mall.bo.StoreTypeProductsBo">
        select sc.stores_type_id as typeId,
                p.id as pid,
                p.goods_id as goodsId,
                p.businessid as businessId,
                p.title,
                p.img,
                s.id as storeId,
                s.name as storeName,
                p.sales,
                p.type,
                p.href_url as hrefUrl,
                p.price,
                p.point_ratio as pointRatio,
                p.retail_price as retailPrice,
                p.min_point as minPoint,
                p.only_point as onlyPoint,
                s.created_at as createdAt,
                p.buy_start_time as buyStartTime,
                p.buy_end_time as buyEndTime,
                s.longitude,
                s.dimension
        from fook_stores s
        left join fook_business_store_product bsp on s.id = bsp.storeid
        left join fook_stores_type_class sc on  sc.stores_id = s.id
        left join fook_stores_type st on  sc.stores_type_id = st.id
        left join fook_business_product p on bsp.productid = p.id
        left join fook_business b on p.businessid = b.id
        where  s.enable = 1 and p.shelf_status = 1
          and p.buy_start_time <![CDATA[ <= ]]> now()
          and p.buy_end_time > now()
          and p.stock > 0
        and p.snap_up = 0 and st.sort != 0 and st.enable = 1 and b.system_type = 1
        and p.id = #{productId,jdbcType=INTEGER}
        order by bsp.storeid,p.sales
        limit 1
    </select>

    <select id="selectUuidsByTypeAndDateRange" resultType="java.lang.String">
        SELECT mpay_coupons_code_id FROM fook_business_product
        WHERE type = #{type} AND buy_end_time BETWEEN #{startDate} AND #{endDate} and shelf_status = 1
    </select>

</mapper>