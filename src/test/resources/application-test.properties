# knife4jéç½®
knife4j.enable=false
knife4j.production=false

# MYSQL
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=false
spring.transaction.default-timeout=300
# master
spring.datasource.dynamic.datasource.master.url=********************************************************************************************************************************
spring.datasource.dynamic.datasource.master.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.dynamic.datasource.master.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=root
spring.datasource.dynamic.datasource.master.hikari.maximum-pool-size=5
spring.datasource.dynamic.datasource.master.hikari.minimum-idle=1
spring.datasource.dynamic.datasource.master.hikari.idle-timeout=240000
spring.datasource.dynamic.datasource.master.hikari.max-lifetime=1800000
spring.datasource.dynamic.datasource.master.hikari.keepalive-time=120000

# slave
spring.datasource.dynamic.datasource.slave.url=********************************************************************************************************************************
spring.datasource.dynamic.datasource.slave.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.dynamic.datasource.slave.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.dynamic.datasource.slave.username=root
spring.datasource.dynamic.datasource.slave.password=root
spring.datasource.dynamic.datasource.slave.hikari.maximum-pool-size=5
spring.datasource.dynamic.datasource.slave.hikari.minimum-idle=1

# JPA Configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MariaDBDialect


# ä½¿ç¨ç®åç¼å­
spring.cache.type=simple

# ç¦ç¨ Redis
spring.redis.enabled=true
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=0
spring.redis.password=
spring.redis.timeout=500
spring.redis.lettuce.pool.max-active=2
spring.redis.lettuce.pool.max-idle=1
spring.redis.lettuce.pool.min-idle=1
spring.redis.lettuce.pool.max-wait=100ms

# ç¦ç¨AMQP
spring.rabbitmq.enabled=false
spring.rabbitmq.listener.direct.auto-startup=false
spring.rabbitmq.listener.simple.auto-startup=false

# ç¦ç¨ä¸å¿è¦çåè½
spring.cloud.nacos.discovery.enabled=false
spring.cloud.nacos.config.enabled=false
spring.cloud.sentinel.enabled=false
spring.cloud.loadbalancer.enabled=false
spring.cloud.alibaba.seata.enabled=false

# ç¦ç¨å®å¨
spring.security.enabled=false

# Redissonéç½®ï¼æå°åï¼
redisson.enabled=true
redisson.single-server.enabled=true
redisson.single-server.address=redis://localhost:6379
redisson.single-server.database=0
redisson.single-server.connect-timeout=500
redisson.single-server.timeout=500
redisson.single-server.retry-attempts=1
redisson.single-server.retry-interval=100
redisson.idle-connection-timeout=500
redisson.subscriptions-per-connection=1
redisson.connection-minimum-idle-size=1
redisson.connection-pool-size=2

# ç¦ç¨éç¾¤æ¨¡å¼
redisson_cluster_enabled=false

# ç¦ç¨ä¸å¿è¦çèªå¨éç½®
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration,\
                             org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration,\
                             com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration,\
                             org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration,\
                             org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration,\
                             com.alibaba.cloud.sentinel.SentinelWebAutoConfiguration,\
                             com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration




spring.config.import=classpath:conf_local/it-security-share.properties,\
    classpath:conf_local/jdbc-mcoin-mall-ext.properties,\
    classpath:conf_local/jdbc-mysql-share.properties,\
    classpath:conf_local/jdbc-mcoin-mall-ext.properties,\
      classpath:conf_local/mcoin-mall.properties,\
classpath:conf_local/mcoin-mall-job.properties,\
classpath:conf_local/mcoin-mall-report-settlement-opt-tpl.properties,\
classpath:conf_local/mcoin-mall-report-settlement-req-tpl.properties,\
      classpath:conf_local/rabbitmq-mcoin-mall-ext.properties,\
classpath:conf_local/rabbitmq-share.properties,\
      classpath:conf_local/redisson-mcoin-mall-ext.properties,\
    classpath:conf_local/redisson-share.properties,\
    classpath:conf_local/task-mcoin-mall-ext.properties
