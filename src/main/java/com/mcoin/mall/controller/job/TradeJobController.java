package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.TradingCycleCreateRequest;
import com.mcoin.mall.model.job.TradingDataCreateFileRequest;
import com.mcoin.mall.model.job.TradingDataRequest;
import com.mcoin.mall.model.job.TradingDataSendEmailRequest;
import com.mcoin.mall.service.job.TradeDataJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/trading")
@Slf4j
public class TradeJobController {
    @Resource
    private TradeDataJobService tradeDataJobService;


    /**
     * mCoin交易統計數據(設定的時間要晚於每日訂單統計結算報表、退款統計報表生成時間)
     * PHP(trading:data)
     * @return
     */
    @PostMapping(value = "/createTradingData")
    public Response<String> createTradingData(@Valid TradingDataRequest request) {
        tradeDataJobService.doCreateTradingData(request);
        return Responses.ok("success");
    }

    /**
     * mCoin交易統計週期
     * PHP(trading:trading)
     * @return
     */
    @PostMapping(value = "/createTradingCycle")
    public Response<String> createTradingCycle(@Valid TradingCycleCreateRequest request) {
        tradeDataJobService.doCreateTradingCycle(request);
        return Responses.ok("success");
    }

    /**
     * 每分鐘mCoin交易統計週期Excel處理
     * PHP(trading:create-excel)
     * @return
     */
    @PostMapping(value = "/createExcel")
    public Response<String> createExcel(@Valid TradingDataCreateFileRequest request) {
        tradeDataJobService.doCreateTrade2Excel(request);
        return Responses.ok("success");
    }

    /**
     * mCoin交易統計數據郵件發送
     * PHP(send:trading-data-email)
     * @return
     */
    @PostMapping(value = "/sendTradingEmail")
    public Response<String> sendTradingEmail(@Valid TradingDataSendEmailRequest request) {
        tradeDataJobService.sendTradingEmail(request);
        return Responses.ok("success");
    }
}
