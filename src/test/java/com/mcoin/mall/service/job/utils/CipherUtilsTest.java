package com.mcoin.mall.service.job.utils;

import com.mcoin.mall.util.CipherUtils;
import org.junit.jupiter.api.Test;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class CipherUtilsTest {

    @Test
    public void testFilter() {
        Map<String, String> params = new HashMap<>();
        params.put("key1", "value1");
        params.put("key2", "");
        params.put("sign", "signature");
        Map<String, String> filtered = CipherUtils.filter(params);
        assertEquals(1, filtered.size());
        assertTrue(filtered.containsKey("key1"));
    }

    @Test
    public void testCreateLinkString() {
        Map<String, String> params = new HashMap<>();
        params.put("key1", "value1");
        params.put("key2", "value2");
        String result = CipherUtils.createLinkString(params, false);
        assertEquals("key1=value1&key2=value2", result);
    }


}
