package com.mcoin.mall.security;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import com.alibaba.fastjson.JSON;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.DefendAttackUtil;

import lombok.extern.slf4j.Slf4j;

import static com.mcoin.mall.util.DefendAttackUtil.*;

@Slf4j
public class FingerprintValidationFilter extends OncePerRequestFilter {
    
    private static final String FINGERPRINT_HEADER = "X-Finger";

    private static final String TIMEZONE_HEADER = "X-Tz";
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        if (SecurityContextHolder.getContext().getAuthentication() == null || 
            !SecurityContextHolder.getContext().getAuthentication().isAuthenticated()) {
            filterChain.doFilter(request, response);
            return;
        }
        // 获取当前用户ID
        UserInfo userInfo = (UserInfo)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String userId = String.valueOf(userInfo.getUserId()); 
        
        // 从配置文件获取灰度比例和名单
        int grayRatio = Integer.parseInt(ConfigUtils.getProperty("security.fingerprint.grayRatio", "0"));
        String whitelist = ConfigUtils.getProperty("security.fingerprint.whitelist", "");

        // 初始化名单
        List<String> whitelistUsers = Arrays.asList(whitelist.split(","));
        
        // 灰度验证 - 基于用户ID最后两位
        int lastTwoDigits = 0;
        try {
            lastTwoDigits = Integer.parseInt(userId.substring(Math.max(0, userId.length() - 2)));
        } catch (NumberFormatException ignored) {
        }
        // 验证指纹
        String fingerprint = request.getHeader(FINGERPRINT_HEADER);
        String timezone = request.getHeader(TIMEZONE_HEADER);
        timezone = StringUtils.isBlank(timezone) ? DEFAULT_TIMEZONE : timezone;
        boolean validFingerprint = isValidFingerprint(fingerprint, timezone);
        // 如不在白名单，则判断灰度百分比
        if (!whitelistUsers.contains(userId)) {
            // 将最后两位数转换为0-99的百分比, 如果用户后两位大于等于灰度比例, 则不进行指纹验证
            if (lastTwoDigits >= (grayRatio - 1)) {
                filterChain.doFilter(request, response);
                return;
            }
        }
        if (!validFingerprint) {
            log.info("权限异常:{}","Invalid client fingerprint");
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            try (PrintWriter out = response.getWriter()){
                String data = JSON.toJSONString(Responses.error(new BusinessException(Response.Code.FORBIDDEN, "Forbidden")));
                log.info("uri:{}, 权限异常返回报文：{}", request.getRequestURI(), data);
                out.print(data);
                out.flush();
                return;
            }
        }
        filterChain.doFilter(request, response);
    }
    
    private boolean isValidFingerprint(String fingerprint, String timeZone) {
        long delayTime = Long.parseLong(ConfigUtils.getProperty("security.fingerprint.delayTime", String.valueOf(DEFAULT_DELAY)));
        long deviationTime = Long.parseLong(ConfigUtils.getProperty("security.fingerprint.deviationTime", String.valueOf(DEFAULT_DEVIATION)));
        // 这里实现指纹验证逻辑
        return DefendAttackUtil.check(fingerprint,  deviationTime, delayTime, timeZone);
    }
}