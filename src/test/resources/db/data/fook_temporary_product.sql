-- Insert test data for fook_temporary_product table
-- These are used for testing the temporary product search functionality

-- Insert test data
INSERT INTO `fook_temporary_product` (`temporary_id`, `id`, `businessid`, `title`, `img`, `zip_img`, `retail_price`, `price`, `type`, `shelf_status`, `buy_start_time`, `buy_end_time`, `vaild_mode`, `vaild_start_time`, `vaild_end_time`, `day_number`, `snap_up`, `created_time`, `only_point`, `actual_sales`, `is_hot`, `is_great`, `complex_name`, `complex_name_en`)
VALUES
-- Regular products
(1, 1, 1, '測試商品1', 'image/product/test1.jpg', 'image/zip_test1.jpg', 100.00, 80.00, 1, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 100, 1, 1, '測試商品1 test product 1', 'Test Product 1'),
(2, 2, 2, '測試商品2', 'image/product/test2.jpg', 'image/zip_test2.jpg', 200.00, 160.00, 2, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 80, 0, 0, '測試商品2 test product 2', 'Test Product 2'),

-- Products with 'test' in the name (for search testing)
(3, 3, 1, '咖啡測試特惠', 'image/product/coffee_test.jpg', 'image/zip_coffee_test.jpg', 50.00, 30.00, 1, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 50, 1, 0, '咖啡測試特惠 coffee test special', 'Coffee Test Special'),
(4, 4, 2, '餐廳測試套餐', 'image/product/restaurant_test.jpg', 'image/zip_restaurant_test.jpg', 300.00, 250.00, 2, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 30, 0, 1, '餐廳測試套餐 restaurant test set', 'Restaurant Test Set'),
(5, 5, 4, '甜品測試優惠', 'image/product/dessert_test.jpg', 'image/zip_dessert_test.jpg', 80.00, 60.00, 3, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 120, 1, 1, '甜品測試優惠 dessert test offer', 'Dessert Test Offer'),

-- Products for HARMONY client testing
(6, 6, 246, '和諧客戶端測試', 'image/product/harmony_test.jpg', 'image/zip_harmony_test.jpg', 150.00, 120.00, 4, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 45, 1, 0, '和諧客戶端測試 harmony client test', 'Harmony Client Test'),
(7, 7, 519, '非和諧客戶端測試', 'image/product/non_harmony_test.jpg', 'image/zip_non_harmony_test.jpg', 150.00, 120.00, 5, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 45, 1, 0, '非和諧客戶端測試 non harmony client test', 'Non-Harmony Client Test'),

-- Products with different types for category filtering
(8, 8, 557, '類型1測試商品', 'image/product/type1_test.jpg', 'image/zip_type1_test.jpg', 120.00, 100.00, 1, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 60, 0, 0, '類型1測試商品 type1 test product', 'Type1 Test Product'),
(9, 9, 558, '類型2測試商品', 'image/product/type2_test.jpg', 'image/zip_type2_test.jpg', 220.00, 180.00, 2, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 40, 0, 0, '類型2測試商品 type2 test product', 'Type2 Test Product'),
(10, 10, 810, '類型3測試商品', 'image/product/type3_test.jpg', 'image/zip_type3_test.jpg', 320.00, 280.00, 3, 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 1, '2023-01-01 00:00:00', '2025-12-31 23:59:59', 365, 0, '2023-01-01 00:00:00', 0, 20, 0, 0, '類型3測試商品 type3 test product', 'Type3 Test Product');
