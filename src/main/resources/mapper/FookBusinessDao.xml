<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusiness">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areas_id" jdbcType="BIT" property="areasId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="js_account" jdbcType="VARCHAR" property="jsAccount" />
    <result column="js_name" jdbcType="VARCHAR" property="jsName" />
    <result column="js_bank" jdbcType="VARCHAR" property="jsBank" />
    <result column="head_name" jdbcType="VARCHAR" property="headName" />
    <result column="head_tel" jdbcType="VARCHAR" property="headTel" />
    <result column="head_email" jdbcType="VARCHAR" property="headEmail" />
    <result column="sex" jdbcType="BIT" property="sex" />
    <result column="enbale" jdbcType="BIT" property="enbale" />
    <result column="login_pass" jdbcType="VARCHAR" property="loginPass" />
    <result column="macau_pass_merchant_number" jdbcType="VARCHAR" property="macauPassMerchantNumber" />
    <result column="macau_pass_terminal_number" jdbcType="VARCHAR" property="macauPassTerminalNumber" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="uid" jdbcType="INTEGER" property="uid" />
    <result column="if_allow_connect" jdbcType="BIT" property="ifAllowConnect" />
    <result column="oa_code_id" jdbcType="INTEGER" property="oaCodeId" />
    <result column="business_number" jdbcType="VARCHAR" property="businessNumber" />
    <result column="file_number" jdbcType="VARCHAR" property="fileNumber" />
    <result column="taxpayer_number" jdbcType="VARCHAR" property="taxpayerNumber" />
    <result column="istemporary" jdbcType="BIT" property="istemporary" />
    <result column="government_number" jdbcType="VARCHAR" property="governmentNumber" />
    <result column="system_type" jdbcType="TINYINT" property="systemType" />
    <result column="member_business_id" jdbcType="VARCHAR" property="memberBusinessId" />
    <result column="member_type" jdbcType="TINYINT" property="memberType" />
    <result column="optional_type" jdbcType="TINYINT" property="optionalType" />
    <result column="is_old_redeem" jdbcType="TINYINT" property="isOldRedeem" />
  </resultMap>
  <sql id="Base_Column_List">
    id, areas_id, code, `name`, company_name, account, `status`, tel, address, logo,
    js_account, js_name, js_bank, head_name, head_tel, head_email, sex, enbale, login_pass,
    macau_pass_merchant_number, macau_pass_terminal_number, created_at, updated_at, `uid`,
    if_allow_connect, oa_code_id, business_number, file_number, taxpayer_number, istemporary,
    government_number, system_type, member_business_id, member_type, optional_type, is_old_redeem
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusiness">
    insert into fook_business
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areasId != null">
        areas_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="tel != null">
        tel,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="logo != null">
        logo,
      </if>
      <if test="jsAccount != null">
        js_account,
      </if>
      <if test="jsName != null">
        js_name,
      </if>
      <if test="jsBank != null">
        js_bank,
      </if>
      <if test="headName != null">
        head_name,
      </if>
      <if test="headTel != null">
        head_tel,
      </if>
      <if test="headEmail != null">
        head_email,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="enbale != null">
        enbale,
      </if>
      <if test="loginPass != null">
        login_pass,
      </if>
      <if test="macauPassMerchantNumber != null">
        macau_pass_merchant_number,
      </if>
      <if test="macauPassTerminalNumber != null">
        macau_pass_terminal_number,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="uid != null">
        `uid`,
      </if>
      <if test="ifAllowConnect != null">
        if_allow_connect,
      </if>
      <if test="oaCodeId != null">
        oa_code_id,
      </if>
      <if test="businessNumber != null">
        business_number,
      </if>
      <if test="fileNumber != null">
        file_number,
      </if>
      <if test="taxpayerNumber != null">
        taxpayer_number,
      </if>
      <if test="istemporary != null">
        istemporary,
      </if>
      <if test="governmentNumber != null">
        government_number,
      </if>
      <if test="systemType != null">
        system_type,
      </if>
      <if test="memberBusinessId != null">
        member_business_id,
      </if>
      <if test="memberType != null">
        member_type,
      </if>
      <if test="optionalType != null">
        optional_type,
      </if>
      <if test="isOldRedeem != null">
        is_old_redeem,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areasId != null">
        #{areasId,jdbcType=BIT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        #{logo,jdbcType=VARCHAR},
      </if>
      <if test="jsAccount != null">
        #{jsAccount,jdbcType=VARCHAR},
      </if>
      <if test="jsName != null">
        #{jsName,jdbcType=VARCHAR},
      </if>
      <if test="jsBank != null">
        #{jsBank,jdbcType=VARCHAR},
      </if>
      <if test="headName != null">
        #{headName,jdbcType=VARCHAR},
      </if>
      <if test="headTel != null">
        #{headTel,jdbcType=VARCHAR},
      </if>
      <if test="headEmail != null">
        #{headEmail,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=BIT},
      </if>
      <if test="enbale != null">
        #{enbale,jdbcType=BIT},
      </if>
      <if test="loginPass != null">
        #{loginPass,jdbcType=VARCHAR},
      </if>
      <if test="macauPassMerchantNumber != null">
        #{macauPassMerchantNumber,jdbcType=VARCHAR},
      </if>
      <if test="macauPassTerminalNumber != null">
        #{macauPassTerminalNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=INTEGER},
      </if>
      <if test="ifAllowConnect != null">
        #{ifAllowConnect,jdbcType=BIT},
      </if>
      <if test="oaCodeId != null">
        #{oaCodeId,jdbcType=INTEGER},
      </if>
      <if test="businessNumber != null">
        #{businessNumber,jdbcType=VARCHAR},
      </if>
      <if test="fileNumber != null">
        #{fileNumber,jdbcType=VARCHAR},
      </if>
      <if test="taxpayerNumber != null">
        #{taxpayerNumber,jdbcType=VARCHAR},
      </if>
      <if test="istemporary != null">
        #{istemporary,jdbcType=BIT},
      </if>
      <if test="governmentNumber != null">
        #{governmentNumber,jdbcType=VARCHAR},
      </if>
      <if test="systemType != null">
        #{systemType,jdbcType=TINYINT},
      </if>
      <if test="memberBusinessId != null">
        #{memberBusinessId,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        #{memberType,jdbcType=TINYINT},
      </if>
      <if test="optionalType != null">
        #{optionalType,jdbcType=TINYINT},
      </if>
      <if test="isOldRedeem != null">
        #{isOldRedeem,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusiness">
    update fook_business
    <set>
      <if test="areasId != null">
        areas_id = #{areasId,jdbcType=BIT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="tel != null">
        tel = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        logo = #{logo,jdbcType=VARCHAR},
      </if>
      <if test="jsAccount != null">
        js_account = #{jsAccount,jdbcType=VARCHAR},
      </if>
      <if test="jsName != null">
        js_name = #{jsName,jdbcType=VARCHAR},
      </if>
      <if test="jsBank != null">
        js_bank = #{jsBank,jdbcType=VARCHAR},
      </if>
      <if test="headName != null">
        head_name = #{headName,jdbcType=VARCHAR},
      </if>
      <if test="headTel != null">
        head_tel = #{headTel,jdbcType=VARCHAR},
      </if>
      <if test="headEmail != null">
        head_email = #{headEmail,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=BIT},
      </if>
      <if test="enbale != null">
        enbale = #{enbale,jdbcType=BIT},
      </if>
      <if test="loginPass != null">
        login_pass = #{loginPass,jdbcType=VARCHAR},
      </if>
      <if test="macauPassMerchantNumber != null">
        macau_pass_merchant_number = #{macauPassMerchantNumber,jdbcType=VARCHAR},
      </if>
      <if test="macauPassTerminalNumber != null">
        macau_pass_terminal_number = #{macauPassTerminalNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="uid != null">
        `uid` = #{uid,jdbcType=INTEGER},
      </if>
      <if test="ifAllowConnect != null">
        if_allow_connect = #{ifAllowConnect,jdbcType=BIT},
      </if>
      <if test="oaCodeId != null">
        oa_code_id = #{oaCodeId,jdbcType=INTEGER},
      </if>
      <if test="businessNumber != null">
        business_number = #{businessNumber,jdbcType=VARCHAR},
      </if>
      <if test="fileNumber != null">
        file_number = #{fileNumber,jdbcType=VARCHAR},
      </if>
      <if test="taxpayerNumber != null">
        taxpayer_number = #{taxpayerNumber,jdbcType=VARCHAR},
      </if>
      <if test="istemporary != null">
        istemporary = #{istemporary,jdbcType=BIT},
      </if>
      <if test="governmentNumber != null">
        government_number = #{governmentNumber,jdbcType=VARCHAR},
      </if>
      <if test="systemType != null">
        system_type = #{systemType,jdbcType=TINYINT},
      </if>
      <if test="memberBusinessId != null">
        member_business_id = #{memberBusinessId,jdbcType=VARCHAR},
      </if>
      <if test="memberType != null">
        member_type = #{memberType,jdbcType=TINYINT},
      </if>
      <if test="optionalType != null">
        optional_type = #{optionalType,jdbcType=TINYINT},
      </if>
      <if test="isOldRedeem != null">
        is_old_redeem = #{isOldRedeem,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusiness">
    update fook_business
    set areas_id = #{areasId,jdbcType=BIT},
      code = #{code,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      tel = #{tel,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      logo = #{logo,jdbcType=VARCHAR},
      js_account = #{jsAccount,jdbcType=VARCHAR},
      js_name = #{jsName,jdbcType=VARCHAR},
      js_bank = #{jsBank,jdbcType=VARCHAR},
      head_name = #{headName,jdbcType=VARCHAR},
      head_tel = #{headTel,jdbcType=VARCHAR},
      head_email = #{headEmail,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=BIT},
      enbale = #{enbale,jdbcType=BIT},
      login_pass = #{loginPass,jdbcType=VARCHAR},
      macau_pass_merchant_number = #{macauPassMerchantNumber,jdbcType=VARCHAR},
      macau_pass_terminal_number = #{macauPassTerminalNumber,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      `uid` = #{uid,jdbcType=INTEGER},
      if_allow_connect = #{ifAllowConnect,jdbcType=BIT},
      oa_code_id = #{oaCodeId,jdbcType=INTEGER},
      business_number = #{businessNumber,jdbcType=VARCHAR},
      file_number = #{fileNumber,jdbcType=VARCHAR},
      taxpayer_number = #{taxpayerNumber,jdbcType=VARCHAR},
      istemporary = #{istemporary,jdbcType=BIT},
      government_number = #{governmentNumber,jdbcType=VARCHAR},
      system_type = #{systemType,jdbcType=TINYINT},
      member_business_id = #{memberBusinessId,jdbcType=VARCHAR},
      member_type = #{memberType,jdbcType=TINYINT},
      optional_type = #{optionalType,jdbcType=TINYINT},
      is_old_redeem = #{isOldRedeem,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>