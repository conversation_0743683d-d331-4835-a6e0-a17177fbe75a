<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookShowZoneDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookShowZone">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="show_id" jdbcType="INTEGER" property="showId" />
    <result column="cn_title" jdbcType="VARCHAR" property="cnTitle" />
    <result column="en_title" jdbcType="VARCHAR" property="enTitle" />
    <result column="valid" jdbcType="BIT" property="valid" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    id, show_id, cn_title, en_title, `valid`, created_at, updated_at, created_by, updated_by, 
    sort
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_show_zone
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_show_zone
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookShowZone">
    insert into fook_show_zone
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="showId != null">
        show_id,
      </if>
      <if test="cnTitle != null">
        cn_title,
      </if>
      <if test="enTitle != null">
        en_title,
      </if>
      <if test="valid != null">
        `valid`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="showId != null">
        #{showId,jdbcType=INTEGER},
      </if>
      <if test="cnTitle != null">
        #{cnTitle,jdbcType=VARCHAR},
      </if>
      <if test="enTitle != null">
        #{enTitle,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookShowZone">
    update fook_show_zone
    <set>
      <if test="showId != null">
        show_id = #{showId,jdbcType=INTEGER},
      </if>
      <if test="cnTitle != null">
        cn_title = #{cnTitle,jdbcType=VARCHAR},
      </if>
      <if test="enTitle != null">
        en_title = #{enTitle,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        `valid` = #{valid,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookShowZone">
    update fook_show_zone
    set show_id = #{showId,jdbcType=INTEGER},
      cn_title = #{cnTitle,jdbcType=VARCHAR},
      en_title = #{enTitle,jdbcType=VARCHAR},
      `valid` = #{valid,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>