<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrderrefundRecordDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformOrderrefundRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="refundid" jdbcType="INTEGER" property="refundid" />
    <result column="old_status" jdbcType="INTEGER" property="oldStatus" />
    <result column="new_status" jdbcType="INTEGER" property="newStatus" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="operation_id" jdbcType="INTEGER" property="operationId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="out_request_no" jdbcType="VARCHAR" property="outRequestNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, refundid, old_status, new_status, operation_time, operation_type, operation_id, 
    remark,out_request_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_orderrefund_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_orderrefund_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformOrderrefundRecord">
    insert into fook_platform_orderrefund_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refundid != null">
        refundid,
      </if>
      <if test="oldStatus != null">
        old_status,
      </if>
      <if test="newStatus != null">
        new_status,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="operationId != null">
        operation_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="outRequestNo != null">
        out_request_no
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="refundid != null">
        #{refundid,jdbcType=INTEGER},
      </if>
      <if test="oldStatus != null">
        #{oldStatus,jdbcType=INTEGER},
      </if>
      <if test="newStatus != null">
        #{newStatus,jdbcType=INTEGER},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="operationId != null">
        #{operationId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="outRequestNo != null">
        #{outRequestNo,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformOrderrefundRecord">
    update fook_platform_orderrefund_record
    <set>
      <if test="refundid != null">
        refundid = #{refundid,jdbcType=INTEGER},
      </if>
      <if test="oldStatus != null">
        old_status = #{oldStatus,jdbcType=INTEGER},
      </if>
      <if test="newStatus != null">
        new_status = #{newStatus,jdbcType=INTEGER},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="operationId != null">
        operation_id = #{operationId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformOrderrefundRecord">
    update fook_platform_orderrefund_record
    set refundid = #{refundid,jdbcType=INTEGER},
      old_status = #{oldStatus,jdbcType=INTEGER},
      new_status = #{newStatus,jdbcType=INTEGER},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      operation_type = #{operationType,jdbcType=INTEGER},
      operation_id = #{operationId,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>