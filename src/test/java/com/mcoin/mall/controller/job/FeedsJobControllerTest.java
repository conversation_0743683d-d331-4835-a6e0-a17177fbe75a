package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.UpdateRecommendCacheRequest;
import com.mcoin.mall.service.job.FeedsJobService;
import org.junit.jupiter.api.Test;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;

import static com.mcoin.mall.util.McoinMall.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 FeedsJobController
 * 使用真实服务和数据库，测试内容推荐任务相关功能
 */
class FeedsJobControllerTest extends BaseUnitTest {

    @Resource
    private FeedsJobController feedsJobController;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private FeedsJobService feedsJobService;

    @Test
    void testUpdateRecommendCache() {

        RScoredSortedSet<Integer> recommendSet = redissonClient.getScoredSortedSet(PRODUCT_RECOMMEND_KEY_PREFIX);
        recommendSet.add(4900116630255L, 2972);
        recommendSet.add(4900116630254L, 2973);
        recommendSet.add(4900116630253L, 2975);

        RScoredSortedSet<Integer> harmonySet = redissonClient.getScoredSortedSet(PRODUCT_RECOMMEND_KEY_PREFIX + FEEDS_HARMONY_SUFFIX);
        harmonySet.add(4900116630255L, 2982);
        harmonySet.add(4900116630254L, 2966);
        harmonySet.add(4900116630253L, 2986);


        RScoredSortedSet<Integer> typeSet = redissonClient.getScoredSortedSet(PRODUCT_CREATE_TIME_KEY_PREFIX);
        typeSet.add(1, 32982);
        typeSet.add(2, 32966);
        typeSet.add(3, 32986);


        RScoredSortedSet<Integer> harmonyTypeSet = redissonClient.getScoredSortedSet(PRODUCT_CREATE_TIME_KEY_PREFIX + FEEDS_HARMONY_SUFFIX);
        harmonyTypeSet.add(1, 32982);
        harmonyTypeSet.add(2, 32966);
        harmonyTypeSet.add(3, 32986);

        // 创建请求对象
        UpdateRecommendCacheRequest request = new UpdateRecommendCacheRequest();
        request.setClearSwitchCache(true);
        // 直接调用控制器方法
        feedsJobService.doUpdateRecommendCache(request);
        
    }
} 