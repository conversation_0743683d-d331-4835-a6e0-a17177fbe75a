<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformUsercollectionDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformUsercollection">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="collectionid" jdbcType="INTEGER" property="collectionid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    id, userid, `type`, collectionid, create_time, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_usercollection
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_usercollection
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformUsercollection">
    insert into fook_platform_usercollection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="collectionid != null">
        collectionid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="collectionid != null">
        #{collectionid,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformUsercollection">
    update fook_platform_usercollection
    <set>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="collectionid != null">
        collectionid = #{collectionid,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformUsercollection">
    update fook_platform_usercollection
    set userid = #{userid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      collectionid = #{collectionid,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>