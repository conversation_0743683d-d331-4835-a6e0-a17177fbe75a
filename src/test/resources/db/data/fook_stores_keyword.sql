-- Insert test data for fook_stores_keyword table
-- These are used for testing the storeKeywords functionality

-- Regular keywords (visible to all client types)
INSERT INTO `fook_stores_keyword` (`id`, `pid`, `name`, `english_name`, `sort`, `enable`, `icon`, `if_index_show`, `created_at`, `updated_at`, `src`, `src_type`, `extend_info`)
VALUES
(1, 0, '餐廳', 'Restaurant', 100, 1, 'image/test/keyword_restaurant.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '1,2,3'),
(2, 0, '購物', 'Shopping', 90, 1, 'image/test/keyword_shopping.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '4,5,6'),
(3, 0, '娛樂', 'Entertainment', 80, 1, 'image/test/keyword_entertainment.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '7,8,9'),
(4, 0, '美容', 'Beauty', 70, 1, 'image/test/keyword_beauty.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '10,11,12'),
(5, 0, '健康', 'Health', 60, 1, 'image/test/keyword_health.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '13,14,15');

-- Mini program link keywords (should be filtered out for HARMONY client)
INSERT INTO `fook_stores_keyword` (`id`, `pid`, `name`, `english_name`, `sort`, `enable`, `icon`, `if_index_show`, `created_at`, `updated_at`, `src`, `src_type`, `extend_info`)
VALUES
(6, 0, '小程序1', 'Mini Program 1', 50, 1, 'image/test/keyword_miniprogram1.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'miniprogram/page1', 4, NULL),
(7, 0, '小程序2', 'Mini Program 2', 40, 1, 'image/test/keyword_miniprogram2.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'miniprogram/page2', 4, NULL);

-- Test-related keywords
INSERT INTO `fook_stores_keyword` (`id`, `pid`, `name`, `english_name`, `sort`, `enable`, `icon`, `if_index_show`, `created_at`, `updated_at`, `src`, `src_type`, `extend_info`)
VALUES
(9, 0, '測試', 'Test', 35, 1, 'image/test/keyword_test.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '1,2,4'),
(10, 0, '特惠', 'Special Offer', 34, 1, 'image/test/keyword_special.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '1,3,5'),
(11, 0, '和諧測試', 'Harmony Test', 33, 1, 'image/test/keyword_harmony.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, '6,7');

-- Disabled keywords (should not appear in results)
INSERT INTO `fook_stores_keyword` (`id`, `pid`, `name`, `english_name`, `sort`, `enable`, `icon`, `if_index_show`, `created_at`, `updated_at`, `src`, `src_type`, `extend_info`)
VALUES
(8, 0, '禁用關鍵字', 'Disabled Keyword', 30, 0, 'image/test/keyword_disabled.jpg', 1, '2023-01-01 00:00:00', '2023-01-01 00:00:00', 'classify', 6, NULL);
