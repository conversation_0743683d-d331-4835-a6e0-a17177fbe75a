package com.mcoin.mall.service.settlement;

import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookReportMpaycoin;
import com.mcoin.mall.bo.MpaycoinRefundDetailBo;
import com.mcoin.mall.bo.OrderAmountBo;
import com.mcoin.mall.component.ExplicitTransaction;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundDao;
import com.mcoin.mall.dao.FookReportMpaycoinDao;
import com.mcoin.mall.service.core.FookReportMpaycoinOrdercodeService;
import com.mcoin.mall.service.core.impl.SettlementJobCoreServiceImpl;
import com.mcoin.mall.util.ConfigUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/12/27
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class SettlementJobCoreServiceImplTest {

    @Mock
    private FookReportMpaycoinDao fookReportMpaycoinDao;

    @Mock
    private FookPlatformOrderDao fookPlatformOrderDao;


    @Mock
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;

    @Mock
    private FookReportMpaycoinOrdercodeService fookReportMpaycoinOrdercodeService;

    @InjectMocks private SettlementJobCoreServiceImpl settlementJobCoreService;

    MockedStatic<ConfigUtils> configUtilsStatic;

    @Mock
    private ExplicitTransaction longQueryReadOnlyTransaction;

    @Mock
    private ExplicitTransaction batchTransaction;


    @BeforeEach
    public void setUp() {
        configUtilsStatic = mockStatic(ConfigUtils.class);
        MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    public void tearDown() {
        configUtilsStatic.close();
    }


    @Test
    public void testGenerateMcoinOrderInMpayReport() {
        when(fookReportMpaycoinDao.selectOne(any())).thenReturn(null);
        when(fookPlatformOrderDao.sumSettlementMpayOrderAmount(any(), any())).thenReturn(mockOrderAmountBo());
        when(fookPlatformOrderDao.selectCountSettlementOrderList(any(), any())).thenReturn(1);
        when(fookPlatformOrderrefundDao.queryByStatusAndPlatformStatusInRefundTime(anyInt(),
                anyInt(), any(Date.class), any(Date.class))).thenReturn(mockRefundBos());
        when(fookReportMpaycoinDao.insert(any(FookReportMpaycoin.class))).thenReturn(1);
        when(fookPlatformOrderDao.selectSettlementOrderList(any(),any(),anyInt(),anyInt())).thenReturn(orders());
        when(fookReportMpaycoinOrdercodeService.saveBatch(anyCollection())).thenReturn(Boolean.TRUE);
        when(longQueryReadOnlyTransaction.invokeWithNewTransaction(any(Callable.class))).thenReturn(mockRefundBos());
        when(ConfigUtils.getProperty(anyString(),anyString()))
                .thenReturn("1000");
        Date date = new Date();
        settlementJobCoreService.generateMcoinOrderInMpayReport(date,date,date,"test");
    }

    private  OrderAmountBo mockOrderAmountBo() {
        OrderAmountBo amountBo = new OrderAmountBo();
        BigDecimal amount = new BigDecimal(1.1);
        amountBo.setTotalAmount(amount);
        amountBo.setTotalScore(amount);
        amountBo.setTotalMpayIntegral(amount);
        amountBo.setPayAmount(amount);
        amountBo.setRefundAmount(amount);
        amountBo.setSubsidyAmount(amount);
        return amountBo;
    }


    private List<MpaycoinRefundDetailBo> mockRefundBos() {
        List<MpaycoinRefundDetailBo> list = new ArrayList<>();
        MpaycoinRefundDetailBo refundDetail = new MpaycoinRefundDetailBo();
        refundDetail.setId(1);
        refundDetail.setUserid(202);
        refundDetail.setSellerid(303);
        refundDetail.setType(1);
        refundDetail.setOrderNo("ORDER123");
        refundDetail.setCreateTime(new Date());
        refundDetail.setPaymentType(1);
        refundDetail.setPaymentTransaction("PAYTRANS123");
        refundDetail.setStatus(0);

        refundDetail.setCompleteTime(new Date());
        refundDetail.setOrderTransaction("TRANS123");
        refundDetail.setMpayintegral(100);
        refundDetail.setScore(BigDecimal.valueOf(10.00));
        refundDetail.setPlatform(1);
        refundDetail.setOrderAmount(BigDecimal.valueOf(100.00));
        refundDetail.setPointRatio(1);
        refundDetail.setCurrency("MOP");
        refundDetail.setTotalAmount(BigDecimal.valueOf(100.00));
        refundDetail.setTotalAmountCurrency("MOP");
        refundDetail.setTransactionAmount(BigDecimal.valueOf(100.00));
        refundDetail.setTransactionAmountCurrency("MOP");
        refundDetail.setPaymentAmount(BigDecimal.valueOf(90.00));
        refundDetail.setPaymentAmountCurrency("MOP");
        refundDetail.setPaymentTime(new Date());
        refundDetail.setQueryMark(0);
        refundDetail.setAccountSuffix("1234");
        refundDetail.setExpirationMonth(12);
        refundDetail.setExpirationYear(2025);
        refundDetail.setCardType("VISA");
        refundDetail.setCreditCardid(1);
        refundDetail.setReasonCode("NO_REASON");
        refundDetail.setProcessType(1);
        refundDetail.setBankCharges(BigDecimal.valueOf(2.00));
        refundDetail.setBankSettlementAmount(BigDecimal.valueOf(98.00));
        refundDetail.setSubsidyAmount(BigDecimal.valueOf(5.00));
        refundDetail.setOrdercodeId(404);
        refundDetail.setRefundid(505);
        refundDetail.setOrdercodeStatus(1);
        refundDetail.setRefundStatus(0);
        refundDetail.setIsMpay("Y");
        refundDetail.setIsMember(1);
        refundDetail.setMemberintegral(BigDecimal.valueOf(10.00));
        refundDetail.setIsVoucher(0);
        refundDetail.setMpayCouponsStatus(1);
        refundDetail.setMpayCouponsCode("COUPON123");
        refundDetail.setRefundAmount(BigDecimal.valueOf(10.00));
        refundDetail.setRefundMpayintegral(50L);
        refundDetail.setRefundRessonFront("No reason");
        refundDetail.setUserRemark("User remark");
        refundDetail.setCustomerRemark("Customer remark");
        list.add(refundDetail);
        return list;
    }

    private List<FookPlatformOrder> orders() {
        List<FookPlatformOrder> list = new ArrayList<>();
        FookPlatformOrder order = new FookPlatformOrder()
                .setId(1)
                .setAreaid(1)
                .setUserid(1001)
                .setSellerid(2002)
                .setType(1)
                .setOrderNo("ORDER001")
                .setCreateTime(new Date())
                .setPaymentType(1)
                .setPaymentTransaction("PAYTRANS001")
                .setStatus(2)
                .setCompleteTime(new Date())
                .setOrderTransaction("TRANS001")
                .setMpayintegral(50)
                .setScore(BigDecimal.valueOf(20.00))
                .setPlatform(1)
                .setOrderAmount(BigDecimal.valueOf(200.00))
                .setPointRatio(1)
                .setCurrency("MOP")
                .setTotalAmount(BigDecimal.valueOf(200.00))
                .setTotalAmountCurrency("MOP")
                .setTransactionAmount(BigDecimal.valueOf(190.00))
                .setTransactionAmountCurrency("MOP")
                .setPaymentAmount(BigDecimal.valueOf(190.00))
                .setPaymentAmountCurrency("MOP")
                .setPaymentTime(new Date())
                .setQueryMark(0)
                .setAccountSuffix("5678")
                .setExpirationMonth(12)
                .setExpirationYear(2026)
                .setCardType("MASTERCARD")
                .setCreditCardid(2)
                .setReasonCode("NO_REASON")
                .setProcessType(1)
                .setBankCharges(BigDecimal.valueOf(5.00))
                .setBankSettlementAmount(BigDecimal.valueOf(185.00))
                .setOrdercodeId(3003)
                .setRefundid(4004)
                .setOrdercodeStatus(1)
                .setRefundStatus(0)
                .setIsMpay("Y")
                .setIsMember(1)
                .setMemberintegral(BigDecimal.valueOf(15.00))
                .setIsVoucher(0)
                .setMpayCouponsStatus(1)
                .setMpayCouponsCode("COUPON001")
                .setSubsidyAmount(BigDecimal.valueOf(10.00))
                .setSessionId(5005);
        list.add(order);
        return list;
    }
}

