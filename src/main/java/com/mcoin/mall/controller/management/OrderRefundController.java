package com.mcoin.mall.controller.management;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.mcoin.mall.model.OrderRefundApprovalRequest;
import com.mcoin.mall.model.OrderRefundApprovalResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.service.order.OrderService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "订单退款管理")
@RestController
@RequestMapping("/api/management/order")
public class OrderRefundController {
    
    @Resource
    private OrderService orderService;

    /**
     * 审批退款
     * @param request 退款审批请求
     * @return 审批结果
     */
    @ApiOperation("后台审批退款")
    @PostMapping(value = "/refundApprovalProcess", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderRefundApprovalResponse> refundApprovalProcess(@Valid @RequestBody OrderRefundApprovalRequest request) {
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);
        return Responses.ok(response);
    }
} 