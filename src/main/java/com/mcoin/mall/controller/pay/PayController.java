package com.mcoin.mall.controller.pay;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.*;
import com.mcoin.mall.service.pay.PayService;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.context.MessageSource;
import org.springframework.http.MediaType;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Locale;

@RestController
public class PayController {

    @Resource
    private PayService payService;
    @Resource
    private RetryTemplate retryTemplate;
    @Resource
    private MessageSource messageSource;
    @Resource
    private ContextHolder contextHolder;

    /**
     * 福利詳情
     * @return
     */
    @GetMapping(value = "/api/auth/obtainPay", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ObtainPayResponse> obtainPay(@Valid ObtainPayRequest request) {
        request.convertWsgToGsj();
        ObtainPayResponse response = payService.getObtainPay(request);
        return Responses.ok(response);
    }

    /**
     * 下單扣庫存
     * @return
     */
    @ApiResponses({
            @ApiResponse(responseCode = "10003", description = "表示售罄")
    })
    @PostMapping(value = "/api/auth/mpaysaveorderinfo", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<CreateOrderResponse> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        CreateOrderResponse response = payService.createOrder(request);
        return Responses.ok(response);
    }
    
    /**
     * 用户同意产品协议
     * @return
     */
    @PostMapping(value = "/api/auth/productProtocol", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ProductProtocolResponse> productProtocol(@Valid @RequestBody ProductProtocolRequest request) {
        payService.productProtocol(request);
        return Responses.ok(new ProductProtocolResponse());
    }

    /**
     * 拉起收銀台
     * @return
     */
    @GetMapping(value = "/pay/macaupayjs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<PopUpCashierResponse> popUpCashier(@PathVariable Integer id) {
        PopUpCashierResponse response = payService.updPopUpCashier(id);
        return Responses.ok(response);
    }

    /**
     * 取消支付
     * @return
     */
    @GetMapping(value = "/pay/macaupaycanceljs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<CancelPayResponse> cancelPay(@PathVariable Integer id) {
        return Responses.ok(payService.updCancelPay(id));
    }

    /**
     * 支付查詢
     * @return
     */
    @GetMapping(value = "/pay/macaupayqueryjs/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<QueryPayResponse> queryPay(@PathVariable Integer id) {
        QueryPayResponse response = payService.queryPay(id);
        return Responses.ok(response);
    }

    /**
     * 支付通知回調
     * @return
     */
    @PostMapping(value = "/pay/macaupayjsback", consumes = {
            MediaType.APPLICATION_FORM_URLENCODED_VALUE
    })
    public String payCallback(MPayCallbackRequest request) {
        payService.updMPayCallback(request);
        return "success";
    }

    /**
     * 限时抢购登记
     * @return
     */
    @ApiResponses({
            @ApiResponse(responseCode = "10003", description = "表示售罄")
    })
    @PostMapping(value = "/api/auth/applybuy", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ApplyBuyResponse> applyBuy(@Valid @RequestBody ApplyBuyRequest request) {
        ApplyBuyResponse response = payService.applyBuy(request);
        return Responses.ok(response);
    }

    /**
     * 查询是否登记成功
     * @return
     */

     @ApiResponses({
        @ApiResponse(responseCode = "10003", description = "表示售罄")
    })
    @PostMapping(value = "/api/auth/snapUpSuccess", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<SnapUpSuccessResponse> snapUpSuccess(@Valid @RequestBody SnapUpSuccessRequest request) {
        Entry entry = null;
        try {
            entry = SphU.entry("/internal/api/auth/snapUpSuccess", EntryType.IN, 1, request.getProduct_id());
            SnapUpSuccessResponse response = retryTemplate.execute(context-> payService.updStockAndSnapUpSuccess(request));
            return Responses.ok(response);
        } catch (BlockException ex) {
            Locale locale = contextHolder.getLocale();
            SnapUpSuccessResponse data = new SnapUpSuccessResponse();
            data.setStatus(0);
            throw new BusinessException(Response.Code.BAD_REQUEST,
                    messageSource.getMessage("message.business.register_failed", null, locale), data);
        } finally {
            if (entry != null) {
                entry.exit(1, request.getProduct_id());
            }
        }
    }


}
