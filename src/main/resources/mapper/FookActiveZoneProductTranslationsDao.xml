<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookActiveZoneProductTranslationsDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookActiveZoneProductTranslations">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="active_zone_id" jdbcType="INTEGER" property="activeZoneId" />
    <result column="business_product_id" jdbcType="INTEGER" property="businessProductId" />
    <result column="order_by" jdbcType="INTEGER" property="orderBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, active_zone_id, business_product_id, order_by, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_active_zone_product_translations
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_active_zone_product_translations
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookActiveZoneProductTranslations">
    insert into fook_active_zone_product_translations
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activeZoneId != null">
        active_zone_id,
      </if>
      <if test="businessProductId != null">
        business_product_id,
      </if>
      <if test="orderBy != null">
        order_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="activeZoneId != null">
        #{activeZoneId,jdbcType=INTEGER},
      </if>
      <if test="businessProductId != null">
        #{businessProductId,jdbcType=INTEGER},
      </if>
      <if test="orderBy != null">
        #{orderBy,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookActiveZoneProductTranslations">
    update fook_active_zone_product_translations
    <set>
      <if test="activeZoneId != null">
        active_zone_id = #{activeZoneId,jdbcType=INTEGER},
      </if>
      <if test="businessProductId != null">
        business_product_id = #{businessProductId,jdbcType=INTEGER},
      </if>
      <if test="orderBy != null">
        order_by = #{orderBy,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookActiveZoneProductTranslations">
    update fook_active_zone_product_translations
    set active_zone_id = #{activeZoneId,jdbcType=INTEGER},
      business_product_id = #{businessProductId,jdbcType=INTEGER},
      order_by = #{orderBy,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>