<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportSettlementDataDao">

    <select id="countSettlementData" resultType="integer">
        select count(ros.id)
        from fook_report_ordercode_settlement ros
                 left join fook_platform_ordercode oc on ros.ordercodeid = oc.id
                 left join fook_platform_orderinfo oi on oc.orderid = oi.orderid
                 left join fook_platform_order o on oc.orderid = o.id
                 left join fook_stores s on oc.shopid = s.id
        where  ros.usetime  between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
    </select>

  <select id="selectSettlementDataByPage" resultType="com.mcoin.mall.bean.FookReportSettlementData">
    select o.type,
           o.order_no as orderNo,
           ros.billamount as retailPrice,
           ros.userpaymentamount as billFinalAmount,
           oc.apportion_mpayintegral * if (ros.billamount >= 0, 1, -1) as point,
           oc.apportion_mpayintegral/o.point_ratio * if (ros.billamount >= 0, 1, -1) as pointAmount,
           ros.commission,
           ros.billamount - ros.commission as jqAmount,
           oc.code,
           oc.user_time as useTime,
           s.store_number as shopid,
           s.name as shopName,
           oi.title_snapshots as titleSnapshots,
           if (ros.billamount >= 0, 1, 2) as transType
    from fook_report_ordercode_settlement ros
           left join fook_platform_ordercode oc on ros.ordercodeid = oc.id
           left join fook_platform_orderinfo oi on oc.orderid = oi.orderid
           left join fook_platform_order o on oc.orderid = o.id
           left join fook_stores s on oc.shopid = s.id
    where  ros.usetime  between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
    limit #{offset, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
  </select>
</mapper>