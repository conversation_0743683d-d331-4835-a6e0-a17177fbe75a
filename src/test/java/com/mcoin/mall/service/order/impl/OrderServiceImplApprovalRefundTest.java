package com.mcoin.mall.service.order.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

import com.mcoin.mall.bean.FookMqLocal;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrderrefund;
import com.mcoin.mall.bean.FookPlatformOrderrefundRecord;
import com.mcoin.mall.constant.RefundResponseCodeEnum;
import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundRecordDao;
import com.mcoin.mall.model.OrderRefundApprovalRequest;
import com.mcoin.mall.model.OrderRefundApprovalResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.mq.model.RefundMessage;
import com.mcoin.mall.service.base.MqLocalService;
import com.mcoin.mall.service.chennel.MPayChannelService;
import com.mcoin.mall.service.common.AtomicSeqService;

@ExtendWith(MockitoExtension.class)
public class OrderServiceImplApprovalRefundTest {

    private static final Logger log = LoggerFactory.getLogger(OrderServiceImplApprovalRefundTest.class);

    @Mock
    private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;

    @Mock
    private FookPlatformOrderrefundRecordDao fookPlatformOrderrefundRecordDao;

    @Mock
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Mock
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Mock
    private AtomicSeqService atomicSeqService;

    @Mock
    private MPayChannelService mPayChannelService;

    @Mock
    private MqLocalService mqLocalService;

    @Mock
    private ApplicationContext applicationContext;

    @InjectMocks
    private OrderServiceImpl orderService;

    @BeforeEach
    void setup() {
        // MockitoExtension handles initialization automatically
        // Set up system properties that the implementation needs
        System.setProperty("mcoin.gradient.interval.REFUND", "1m,10m,1h,4h,12h,24h,48h");
    }

    /**
     * 测试场景：退款申请不存在
     * 输入：退款ID为1的审批请求
     * 期望输出：返回错误码101，表示退款信息不存在
     * 说明：当系统找不到对应ID的退款申请时，应返回错误码101并终止处理
     */
    @Test
    void testApprovalRefund_OrderRefundNotFound() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(null);

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(101, response.getCode());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
    }

    /**
     * 测试场景：退款申请已经被审批通过
     * 输入：退款ID为1且状态为2(已审批通过)的退款申请
     * 期望输出：返回错误码1005，表示退款已审批通过
     * 说明：当退款申请状态为2时，表示已经审批通过，系统应拒绝重复审批并返回错误码1005
     */
    @Test
    void testApprovalRefund_OrderRefundStatusAlreadyApproved() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(2); // Already approved

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(1005, response.getCode());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
    }

    /**
     * 测试场景：退款申请已经被拒绝
     * 输入：退款ID为1且状态为3(已拒绝)的退款申请
     * 期望输出：返回错误码1006，表示退款已被拒绝
     * 说明：当退款申请状态为3时，表示已经被拒绝，系统应拒绝重复审批并返回错误码1006
     */
    @Test
    void testApprovalRefund_OrderRefundStatusAlreadyRejected() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(3); // Already rejected

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(1006, response.getCode());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
    }

    /**
     * 测试场景：退款申请已退款到账
     * 输入：退款ID为1且状态为6(已退款到账)的退款申请
     * 期望输出：返回状态码200，表示已处理完成
     * 说明：当退款申请状态为6时，表示已经退款到账，应返回成功状态
     */
    @Test
    void testApprovalRefund_OrderRefundStatusAlreadyCompleted() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(6); // Already completed

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(200, response.getStatus());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
    }

    /**
     * 测试场景：成功审批通过退款申请
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为1(同意)
     *   - 退款场景为APPROVAL_REFUND
     * 期望输出：
     *   - 返回状态码200，表示处理成功
     *   - 退款状态被更新为已审批通过
     *   - 生成退款记录
     *   - 更新订单退款状态
     *   - 调用支付渠道进行退款
     * 说明：这是正常审批通过退款的完整流程，包括状态更新、记录生成和调用支付渠道
     */
    @Test
    void testApprovalRefund_ApproveRefund_Success() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(1); // Approve
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());
        request.setOperationType(1);
        request.setOperationId(123);

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(1); // Pending approval
        orderRefund.setOrderid(100);
        orderRefund.setOrdercodeId("1,2,3");

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);
        when(fookPlatformOrderrefundDao.update(any(FookPlatformOrderrefund.class), any())).thenReturn(1);
        when(atomicSeqService.getOutTradeNo()).thenReturn("OUT123456");
        when(fookPlatformOrderrefundRecordDao.insertSelective(any(FookPlatformOrderrefundRecord.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.selectCount(any())).thenReturn(3, 3); // totalCount, refundCount
        when(fookPlatformOrderDao.updateByPrimaryKeySelective(any(FookPlatformOrder.class))).thenReturn(1);
        when(mPayChannelService.updRefund(any(RefundMessage.class))).thenReturn(1); // Success

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertNotNull(response);
        log.info("Response is not null");
        if (response.getStatus() != null) {
            log.info("Response.getStatus() = {}", response.getStatus());
        } else {
            log.warn("Response.getStatus() is null");
        }
        assertNotNull(response.getStatus());
        assertEquals(Response.Code.SUCCESS.get(), response.getStatus().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, times(1)).update(any(FookPlatformOrderrefund.class), any());
        verify(fookPlatformOrderrefundRecordDao, times(1)).insertSelective(any(FookPlatformOrderrefundRecord.class));
        verify(fookPlatformOrdercodeDao, times(2)).selectCount(any());
        verify(fookPlatformOrderDao, times(1)).updateByPrimaryKeySelective(any(FookPlatformOrder.class));
        verify(mPayChannelService, times(1)).updRefund(any(RefundMessage.class));
    }

    /**
     * 测试场景：审批通过但支付渠道退款失败
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为1(同意)
     *   - 退款场景为APPROVAL_REFUND
     * 期望输出：
     *   - 返回错误码101，表示退款失败
     *   - 退款状态被更新为已审批通过
     *   - 生成退款记录
     *   - 更新订单退款状态
     *   - 支付渠道退款返回失败
     * 说明：测试当支付渠道返回失败结果时的处理逻辑，系统应返回退款失败错误码
     */
    @Test
    void testApprovalRefund_ApproveRefund_UpdRefundFails() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(1); // Approve
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());
        request.setOperationType(1);
        request.setOperationId(123);

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(1); // Pending approval
        orderRefund.setOrderid(100);
        orderRefund.setOrdercodeId("1,2,3");

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);
        when(fookPlatformOrderrefundDao.update(any(FookPlatformOrderrefund.class), any())).thenReturn(1);
        when(atomicSeqService.getOutTradeNo()).thenReturn("OUT123456");
        when(fookPlatformOrderrefundRecordDao.insertSelective(any(FookPlatformOrderrefundRecord.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.selectCount(any())).thenReturn(3, 3); // totalCount, refundCount
        when(fookPlatformOrderDao.updateByPrimaryKeySelective(any(FookPlatformOrder.class))).thenReturn(1);
        when(mPayChannelService.updRefund(any(RefundMessage.class))).thenReturn(-1); // Fail

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(RefundResponseCodeEnum.REFUND_FAILED.getCode(), response.getCode().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, times(1)).update(any(FookPlatformOrderrefund.class), any());
        verify(fookPlatformOrderrefundRecordDao, times(1)).insertSelective(any(FookPlatformOrderrefundRecord.class));
        verify(fookPlatformOrdercodeDao, times(2)).selectCount(any());
        verify(fookPlatformOrderDao, times(1)).updateByPrimaryKeySelective(any(FookPlatformOrder.class));
        verify(mPayChannelService, times(1)).updRefund(any(RefundMessage.class));
    }

    /**
     * 测试场景：审批通过但支付渠道退款抛出异常
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为1(同意)
     *   - 退款场景为APPROVAL_REFUND
     * 期望输出：
     *   - 返回错误码101，表示退款失败
     *   - 退款状态被更新为已审批通过
     *   - 生成退款记录
     *   - 更新订单退款状态
     *   - 支付渠道退款抛出异常
     * 说明：测试当支付渠道调用抛出异常时的异常处理逻辑，系统应捕获异常并返回退款失败错误码
     */
    @Test
    void testApprovalRefund_ApproveRefund_UpdRefundThrowsException() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(1); // Approve
        request.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());
        request.setOperationType(1);
        request.setOperationId(123);

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(1); // Pending approval
        orderRefund.setOrderid(100);
        orderRefund.setOrdercodeId("1,2,3");

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);
        when(fookPlatformOrderrefundDao.update(any(FookPlatformOrderrefund.class), any())).thenReturn(1);
        when(atomicSeqService.getOutTradeNo()).thenReturn("OUT123456");
        when(fookPlatformOrderrefundRecordDao.insertSelective(any(FookPlatformOrderrefundRecord.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.selectCount(any())).thenReturn(3, 3); // totalCount, refundCount
        when(fookPlatformOrderDao.updateByPrimaryKeySelective(any(FookPlatformOrder.class))).thenReturn(1);
        when(mPayChannelService.updRefund(any(RefundMessage.class))).thenThrow(new RuntimeException("Test exception"));

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(RefundResponseCodeEnum.REFUND_FAILED.getCode(), response.getCode().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, times(1)).update(any(FookPlatformOrderrefund.class), any());
        verify(fookPlatformOrderrefundRecordDao, times(1)).insertSelective(any(FookPlatformOrderrefundRecord.class));
        verify(fookPlatformOrdercodeDao, times(2)).selectCount(any());
        verify(fookPlatformOrderDao, times(1)).updateByPrimaryKeySelective(any(FookPlatformOrder.class));
        verify(mPayChannelService, times(1)).updRefund(any(RefundMessage.class));
    }

    /**
     * 测试场景：成功拒绝退款申请
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为2(拒绝)
     * 期望输出：
     *   - 返回状态码200，表示处理成功
     *   - 退款状态被更新为已拒绝
     *   - 生成退款记录
     *   - 更新订单退款状态
     *   - 查询优惠券状态
     *   - 同步优惠券信息
     * 说明：测试拒绝退款的完整流程，包括状态更新、记录生成、订单状态更新和优惠券处理
     */
    @Test
    void testApprovalRefund_RejectRefund_Success() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(2); // Reject
        request.setOperationType(1);
        request.setOperationId(123);

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(1); // Pending approval
        orderRefund.setOrderid(100);
        orderRefund.setOrdercodeId("1,2,3");
        orderRefund.setUserid(456);

        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(100);
        order.setOrderNo("ORD123456");

        List<FookPlatformOrdercode> ordercodes = new ArrayList<>();
        FookPlatformOrdercode ordercode1 = new FookPlatformOrdercode();
        ordercode1.setId(1);
        ordercode1.setOrderid(100);
        ordercode1.setRefundid(1);
        ordercodes.add(ordercode1);

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);
        when(fookPlatformOrderrefundDao.updateByPrimaryKeySelective(any(FookPlatformOrderrefund.class))).thenReturn(1);
        when(fookPlatformOrderrefundRecordDao.insertSelective(any(FookPlatformOrderrefundRecord.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.updateStatusByRefund(anyInt(), anyInt())).thenReturn(1);
        when(fookPlatformOrdercodeDao.selectCount(any())).thenReturn(3, 1); // totalCount, refuseRefundCount
        when(fookPlatformOrderDao.selectById(anyInt())).thenReturn(order);
        when(mPayChannelService.queryCoupon(any(), any())).thenReturn("{\"code\":\"200\",\"Data\":{\"status\":\"02\"}}");
        when(fookPlatformOrderDao.updateByPrimaryKeySelective(any(FookPlatformOrder.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.selectList(any())).thenReturn(ordercodes);
        when(mPayChannelService.insertCouponSync(anyInt(), anyInt(), any(), any(), anyString())).thenReturn(1);

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(Response.Code.SUCCESS.get(), response.getStatus().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, times(1)).updateByPrimaryKeySelective(any(FookPlatformOrderrefund.class));
        verify(fookPlatformOrderrefundRecordDao, times(1)).insertSelective(any(FookPlatformOrderrefundRecord.class));
        verify(fookPlatformOrdercodeDao, times(1)).updateStatusByRefund(eq(1), eq(1));
        verify(fookPlatformOrdercodeDao, times(2)).selectCount(any());
        verify(fookPlatformOrderDao, times(1)).selectById(eq(100));
        verify(mPayChannelService, times(1)).queryCoupon(eq(456), eq("ORD123456"));
        verify(fookPlatformOrderDao, times(1)).updateByPrimaryKeySelective(any(FookPlatformOrder.class));
        verify(fookPlatformOrdercodeDao, times(1)).selectList(any());
        verify(mPayChannelService, times(1)).insertCouponSync(anyInt(), anyInt(), any(), any(), anyString());
    }

    /**
     * 测试场景：无效的审批类型
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为3(无效类型，非1或2)
     * 期望输出：
     *   - 返回状态码200，表示请求已处理
     *   - 不更新退款状态
     *   - 不生成退款记录
     * 说明：当审批类型既不是同意(1)也不是拒绝(2)时，系统应忽略处理但返回成功状态码
     */
    @Test
    void testApprovalRefund_InvalidType() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(3); // Invalid type

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(1); // Pending approval

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(Response.Code.SUCCESS.get(), response.getStatus().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, never()).update(any(FookPlatformOrderrefund.class), any());
        verify(fookPlatformOrderrefundRecordDao, never()).insertSelective(any(FookPlatformOrderrefundRecord.class));
    }

    /**
     * 测试场景：无效的退款状态
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为1(同意)
     *   - 退款状态为4(无效状态，非1、2或3)
     * 期望输出：
     *   - 返回状态码200，表示请求已处理
     *   - 不更新退款状态
     *   - 不生成退款记录
     * 说明：当退款状态不是待审批(1)、已同意(2)或已拒绝(3)时，系统应忽略处理但返回成功状态码
     */
    @Test
    void testApprovalRefund_InvalidStatus() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(1); // Approve

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(4); // Invalid status (not 1, 2, or 3)

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(Response.Code.SUCCESS.get(), response.getStatus().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, never()).update(any(FookPlatformOrderrefund.class), any());
        verify(fookPlatformOrderrefundRecordDao, never()).insertSelective(any(FookPlatformOrderrefundRecord.class));
    }

    /**
     * 测试场景：更新退款状态失败
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为1(同意)
     *   - 退款状态为1(待审批)
     * 期望输出：
     *   - 返回状态码200，表示请求已处理
     *   - 尝试更新退款状态但失败(返回0行受影响)
     *   - 不生成退款记录
     * 说明：测试当更新退款状态操作失败时的处理逻辑，可能是因为并发操作导致状态已被其他进程修改
     */
    @Test
    void testApprovalRefund_UpdateRefundFails() {
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(1); // Approve

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(1); // Pending approval

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);
        when(fookPlatformOrderrefundDao.update(any(FookPlatformOrderrefund.class), any())).thenReturn(0); // Update fails

        // when
        OrderRefundApprovalResponse response = orderService.approvalRefund(request);

        // then
        assertEquals(Response.Code.SUCCESS.get(), response.getStatus().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, times(1)).update(any(FookPlatformOrderrefund.class), any());
        verify(fookPlatformOrderrefundRecordDao, never()).insertSelective(any(FookPlatformOrderrefundRecord.class));
    }

    /**
     * 测试场景：非审批退款场景
     * 输入：
     *   - 退款ID为1的审批请求
     *   - 审批类型为1(同意)
     *   - 退款场景为OTHER_SCENE(非APPROVAL_REFUND)
     * 期望输出：
     *   - 返回状态码200，表示处理成功
     *   - 退款状态被更新为已审批通过
     *   - 生成退款记录
     *   - 更新订单退款状态
     *   - 不调用支付渠道退款，而是发布事件
     * 说明：测试非审批退款场景的处理逻辑，此时不直接调用支付渠道，而是通过事件机制异步处理
     */
    @Test
    void testApprovalRefund_NonApprovalRefundScene() {
        // Mock ConfigUtils to avoid NPE
        try (org.mockito.MockedStatic<com.mcoin.mall.util.ConfigUtils> configUtilsMock = 
             org.mockito.Mockito.mockStatic(com.mcoin.mall.util.ConfigUtils.class)) {
            
            configUtilsMock.when(() -> com.mcoin.mall.util.ConfigUtils.getProperty(
                org.mockito.ArgumentMatchers.anyString(), 
                org.mockito.ArgumentMatchers.anyString()))
                .thenReturn("1m,10m,1h,4h,12h,24h,48h");
        
        // given
        OrderRefundApprovalRequest request = new OrderRefundApprovalRequest();
        request.setId(1);
        request.setType(1); // Approve
        // Set a non-null refund scene that is not APPROVAL_REFUND
        request.setRefundScene("OTHER_SCENE");
        request.setOperationType(1);
        request.setOperationId(123);

        // Mock the applicationContext.publishEvent method
        doNothing().when(applicationContext).publishEvent(any(RefundMessage.class));
        
        // Mock saveMqLocal to set ID on the record (simulating DB auto-increment)
        when(mqLocalService.saveMqLocal(any(FookMqLocal.class))).thenAnswer(invocation -> {
            FookMqLocal record = invocation.getArgument(0);
            record.setId(999L); // Simulate setting the auto-generated ID
            return 1;
        });

        FookPlatformOrderrefund orderRefund = new FookPlatformOrderrefund();
        orderRefund.setId(1);
        orderRefund.setStatus(1); // Pending approval
        orderRefund.setOrderid(100);
        orderRefund.setOrdercodeId("1,2,3");

        when(fookPlatformOrderrefundDao.selectByPrimaryKey(anyInt())).thenReturn(orderRefund);
        when(fookPlatformOrderrefundDao.update(any(FookPlatformOrderrefund.class), any())).thenReturn(1);
        when(atomicSeqService.getOutTradeNo()).thenReturn("OUT123456");
        when(fookPlatformOrderrefundRecordDao.insertSelective(any(FookPlatformOrderrefundRecord.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.selectCount(any())).thenReturn(3, 3); // totalCount, refundCount
        when(fookPlatformOrderDao.updateByPrimaryKeySelective(any(FookPlatformOrder.class))).thenReturn(1);

        // when
        OrderRefundApprovalResponse response = null;
        try {
            response = orderService.approvalRefund(request);
        } catch (Exception e) {
            log.error("Exception in approvalRefund: ", e);
            throw e;
        }

        // then
        log.info("Response object: {}", response);
        assertNotNull(response);
        log.info("Response is not null");
        if (response.getStatus() != null) {
            log.info("Response.getStatus() = {}", response.getStatus());
        } else {
            log.warn("Response.getStatus() is null");
        }
        if (response.getCode() != null) {
            log.info("Response.getCode() = {}", response.getCode());
        } else {
            log.warn("Response.getCode() is null");
        }
        assertNotNull(response.getStatus());
        assertEquals(Response.Code.SUCCESS.get(), response.getStatus().intValue());
        verify(fookPlatformOrderrefundDao, times(1)).selectByPrimaryKey(eq(1));
        verify(fookPlatformOrderrefundDao, times(1)).update(any(FookPlatformOrderrefund.class), any());
        verify(fookPlatformOrderrefundRecordDao, times(1)).insertSelective(any(FookPlatformOrderrefundRecord.class));
        verify(fookPlatformOrdercodeDao, times(2)).selectCount(any());
        verify(fookPlatformOrderDao, times(1)).updateByPrimaryKeySelective(any(FookPlatformOrder.class));
        verify(mPayChannelService, never()).updRefund(any(RefundMessage.class));
        verify(mqLocalService, times(1)).saveMqLocal(any(FookMqLocal.class));
        verify(applicationContext, times(1)).publishEvent(any(RefundMessage.class));
        
        } // Close the try-with-resources block for ConfigUtils mock
    }
}