package com.mcoin.mall.mp.controller.print;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.print.PrintListResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Transactional
class PrintRecordControllerTest extends BaseUnitTest {

    @Autowired
    private PrintRecordController printRecordController;

    private UserInfo testUser;

    @BeforeEach
    void setUp() {
        // 设置测试用户
        testUser = new UserInfo();
        testUser.setUserId(123);
        testUser.setSid(1);
        testUser.setTerminal(1);

        // 设置安全上下文
        SecurityContextHolder.getContext().setAuthentication(
            new UsernamePasswordAuthenticationToken(testUser, null)
        );
    }

    @Test
    void queryGoodsDetailById_ShouldReturnPrintList() {
        // Act
        Response<PrintListResponse> response = printRecordController.queryGoodsDetailById();

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
        
        // 验证返回的足迹列表数据
        PrintListResponse printListResponse = response.getData();
        assertNotNull(printListResponse.getStatus());
        assertNotNull(printListResponse.getLists());
    }
} 