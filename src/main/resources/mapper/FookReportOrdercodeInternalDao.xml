<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportOrdercodeInternalDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportOrdercodeInternal">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="momecoins" jdbcType="DECIMAL" property="momecoins" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="test_bill_amount" jdbcType="DECIMAL" property="testBillAmount" />
    <result column="test_settlement_amount" jdbcType="DECIMAL" property="testSettlementAmount" />
    <result column="test_commission" jdbcType="DECIMAL" property="testCommission" />
    <result column="test_momecoins" jdbcType="DECIMAL" property="testMomecoins" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, start_time, end_time, bill_amount, settlement_amount, commission, momecoins, 
    settlement_time, test_bill_amount, test_settlement_amount, test_commission, test_momecoins, 
    `status`, subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_ordercode_internal
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_ordercode_internal
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportOrdercodeInternal">
    insert into fook_report_ordercode_internal
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="momecoins != null">
        momecoins,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
      <if test="testBillAmount != null">
        test_bill_amount,
      </if>
      <if test="testSettlementAmount != null">
        test_settlement_amount,
      </if>
      <if test="testCommission != null">
        test_commission,
      </if>
      <if test="testMomecoins != null">
        test_momecoins,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testBillAmount != null">
        #{testBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="testSettlementAmount != null">
        #{testSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="testCommission != null">
        #{testCommission,jdbcType=DECIMAL},
      </if>
      <if test="testMomecoins != null">
        #{testMomecoins,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportOrdercodeInternal">
    update fook_report_ordercode_internal
    <set>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        momecoins = #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testBillAmount != null">
        test_bill_amount = #{testBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="testSettlementAmount != null">
        test_settlement_amount = #{testSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="testCommission != null">
        test_commission = #{testCommission,jdbcType=DECIMAL},
      </if>
      <if test="testMomecoins != null">
        test_momecoins = #{testMomecoins,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportOrdercodeInternal">
    update fook_report_ordercode_internal
    set start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      momecoins = #{momecoins,jdbcType=DECIMAL},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      test_bill_amount = #{testBillAmount,jdbcType=DECIMAL},
      test_settlement_amount = #{testSettlementAmount,jdbcType=DECIMAL},
      test_commission = #{testCommission,jdbcType=DECIMAL},
      test_momecoins = #{testMomecoins,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>