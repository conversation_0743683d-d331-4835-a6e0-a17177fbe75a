<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMerchantSettlementProcesssDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportMerchantSettlementProcesss">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="settlementid" jdbcType="INTEGER" property="settlementid" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="upload" jdbcType="INTEGER" property="upload" />
    <result column="uploadtime" jdbcType="TIMESTAMP" property="uploadtime" />
    <result column="send" jdbcType="INTEGER" property="send" />
    <result column="sendtime" jdbcType="TIMESTAMP" property="sendtime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, settlementid, businessid, createtime, email, upload, uploadtime, send, sendtime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_merchant_settlement_processs
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_merchant_settlement_processs
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportMerchantSettlementProcesss">
    insert into fook_report_merchant_settlement_processs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="settlementid != null">
        settlementid,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="upload != null">
        upload,
      </if>
      <if test="uploadtime != null">
        uploadtime,
      </if>
      <if test="send != null">
        send,
      </if>
      <if test="sendtime != null">
        sendtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="settlementid != null">
        #{settlementid,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="upload != null">
        #{upload,jdbcType=INTEGER},
      </if>
      <if test="uploadtime != null">
        #{uploadtime,jdbcType=TIMESTAMP},
      </if>
      <if test="send != null">
        #{send,jdbcType=INTEGER},
      </if>
      <if test="sendtime != null">
        #{sendtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportMerchantSettlementProcesss">
    update fook_report_merchant_settlement_processs
    <set>
      <if test="settlementid != null">
        settlementid = #{settlementid,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="upload != null">
        upload = #{upload,jdbcType=INTEGER},
      </if>
      <if test="uploadtime != null">
        uploadtime = #{uploadtime,jdbcType=TIMESTAMP},
      </if>
      <if test="send != null">
        send = #{send,jdbcType=INTEGER},
      </if>
      <if test="sendtime != null">
        sendtime = #{sendtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportMerchantSettlementProcesss">
    update fook_report_merchant_settlement_processs
    set settlementid = #{settlementid,jdbcType=INTEGER},
      businessid = #{businessid,jdbcType=INTEGER},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      email = #{email,jdbcType=VARCHAR},
      upload = #{upload,jdbcType=INTEGER},
      uploadtime = #{uploadtime,jdbcType=TIMESTAMP},
      send = #{send,jdbcType=INTEGER},
      sendtime = #{sendtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>