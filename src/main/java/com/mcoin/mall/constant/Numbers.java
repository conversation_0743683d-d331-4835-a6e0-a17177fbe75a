package com.mcoin.mall.constant;

public enum Numbers {
    ZERO(0, "0"),
    ONE(1, "1"),
    TWO(2, "2"),
    THRE<PERSON>(3, "3"),
    FOUR(4, "4"),
    <PERSON>IVE(5, "5"),
    <PERSON><PERSON>(6, "6"),
    <PERSON><PERSON><PERSON>(7, "7"),
    <PERSON><PERSON><PERSON>(8, "8"),
    <PERSON>IN<PERSON>(9, "9"),
    TEN(10, "10"),
    ONE_MINUTE_MILSEC(60000, "60000"),
    TWO_MINUTE_MILSEC(120000, "120000");

    private final int intValue;
    private final String stringValue;

    Numbers(int intValue, String stringValue) {
        this.intValue = intValue;
        this.stringValue = stringValue;
    }

    public int getIntValue() {
        return intValue;
    }

    public String getStringValue() {
        return stringValue;
    }
} 