package com.mcoin.mall.controller.management;


import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.management.SyncSessionInfoRequest;
import com.mcoin.mall.model.management.SyncSessionInfoResponse;
import com.mcoin.mall.service.management.SnapUpManagementService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/management/snapUp/")
public class SnapUpManagementController {
    @Resource
    private SnapUpManagementService snapUpManagementService;

    /**
     * change the session time to product buy time
     * @return
     */

    @PostMapping(value = "/syncSessionInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<SyncSessionInfoResponse> syncSessionInfo(@Valid @RequestBody SyncSessionInfoRequest request) {
        snapUpManagementService.syncSessionInfo(request);
        return Responses.ok(new SyncSessionInfoResponse());
    }
}
