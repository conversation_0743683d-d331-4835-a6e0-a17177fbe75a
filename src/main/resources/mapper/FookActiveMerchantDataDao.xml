<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookActiveMerchantDataDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookActiveMerchantData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="active_id" jdbcType="INTEGER" property="activeId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_en" jdbcType="VARCHAR" property="nameEn" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="address_en" jdbcType="VARCHAR" property="addressEn" />
    <result column="lucky_draw" jdbcType="TINYINT" property="luckyDraw" />
    <result column="voucher" jdbcType="TINYINT" property="voucher" />
    <result column="event" jdbcType="VARCHAR" property="event" />
    <result column="event_en" jdbcType="VARCHAR" property="eventEn" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, active_id, `name`, name_en, address, address_en, lucky_draw, voucher, event, 
    event_en, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_active_merchant_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_active_merchant_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookActiveMerchantData">
    insert into fook_active_merchant_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activeId != null">
        active_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="nameEn != null">
        name_en,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="addressEn != null">
        address_en,
      </if>
      <if test="luckyDraw != null">
        lucky_draw,
      </if>
      <if test="voucher != null">
        voucher,
      </if>
      <if test="event != null">
        event,
      </if>
      <if test="eventEn != null">
        event_en,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="activeId != null">
        #{activeId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="addressEn != null">
        #{addressEn,jdbcType=VARCHAR},
      </if>
      <if test="luckyDraw != null">
        #{luckyDraw,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        #{voucher,jdbcType=TINYINT},
      </if>
      <if test="event != null">
        #{event,jdbcType=VARCHAR},
      </if>
      <if test="eventEn != null">
        #{eventEn,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookActiveMerchantData">
    update fook_active_merchant_data
    <set>
      <if test="activeId != null">
        active_id = #{activeId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        name_en = #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="addressEn != null">
        address_en = #{addressEn,jdbcType=VARCHAR},
      </if>
      <if test="luckyDraw != null">
        lucky_draw = #{luckyDraw,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        voucher = #{voucher,jdbcType=TINYINT},
      </if>
      <if test="event != null">
        event = #{event,jdbcType=VARCHAR},
      </if>
      <if test="eventEn != null">
        event_en = #{eventEn,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookActiveMerchantData">
    update fook_active_merchant_data
    set active_id = #{activeId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      name_en = #{nameEn,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      address_en = #{addressEn,jdbcType=VARCHAR},
      lucky_draw = #{luckyDraw,jdbcType=TINYINT},
      voucher = #{voucher,jdbcType=TINYINT},
      event = #{event,jdbcType=VARCHAR},
      event_en = #{eventEn,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>