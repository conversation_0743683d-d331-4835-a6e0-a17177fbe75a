<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMacaupassOrderDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMacaupassOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="orderinfo_id" jdbcType="INTEGER" property="orderinfoId" />
    <result column="ordercode_id" jdbcType="INTEGER" property="ordercodeId" />
    <result column="mpayintegral" jdbcType="INTEGER" property="mpayintegral" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, orderinfo_id, ordercode_id, mpayintegral, `status`, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_macaupass_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_macaupass_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMacaupassOrder">
    insert into fook_macaupass_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderinfoId != null">
        orderinfo_id,
      </if>
      <if test="ordercodeId != null">
        ordercode_id,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderinfoId != null">
        #{orderinfoId,jdbcType=INTEGER},
      </if>
      <if test="ordercodeId != null">
        #{ordercodeId,jdbcType=INTEGER},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMacaupassOrder">
    update fook_macaupass_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderinfoId != null">
        orderinfo_id = #{orderinfoId,jdbcType=INTEGER},
      </if>
      <if test="ordercodeId != null">
        ordercode_id = #{ordercodeId,jdbcType=INTEGER},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMacaupassOrder">
    update fook_macaupass_order
    set order_id = #{orderId,jdbcType=INTEGER},
      orderinfo_id = #{orderinfoId,jdbcType=INTEGER},
      ordercode_id = #{ordercodeId,jdbcType=INTEGER},
      mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>