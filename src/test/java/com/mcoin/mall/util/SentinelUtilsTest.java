package com.mcoin.mall.util;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import org.junit.jupiter.api.Test;

import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class SentinelUtilsTest {

    @Test
    void testHandleBlockExceptionWithBusinessException() {
        // Arrange
        Callable<String> callable = () -> {
            throw new BusinessException(Response.Code.BAD_REQUEST, "Business error");
        };
        String blockedReturn = "Blocked";
    
        // Act
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            SentinelUtils.handleBlockException(callable, blockedReturn);
        });
    
        // Assert
        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("Business error", exception.getMessage());
    }

    @Test
    void testHandleBlockExceptionWithCallableSuccess() {
        // Arrange
        Callable<String> callable = () -> "Success";
        String blockedReturn = "Blocked";
    
        // Act
        String result = SentinelUtils.handleBlockException(callable, blockedReturn);
    
        // Assert
        assertEquals("Success", result);
    }

    @Test
    void testHandleBlockExceptionWithBlockException() {
        // Arrange
        Callable<String> callable = () -> {
            throw new DegradeException("");
        };
        String blockedReturn = "Blocked";
    
        // Act
        String result = SentinelUtils.handleBlockException(callable, blockedReturn);
    
        // Assert
        assertEquals(blockedReturn, result);
    }

    @Test
    void testHandleBlockExceptionWithException() {
        // Arrange
        Callable<String> callable = () -> {
            throw new RuntimeException("Some error");
        };
        String blockedReturn = "Blocked";
    
        // Act
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            SentinelUtils.handleBlockException(callable, blockedReturn);
        });
    
        // Assert
        assertEquals(Response.Code.BAD_REQUEST, exception.getRespCode());
        assertEquals("Some error", exception.getMessage());
    }

}