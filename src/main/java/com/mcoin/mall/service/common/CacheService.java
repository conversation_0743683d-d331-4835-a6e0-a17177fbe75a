package com.mcoin.mall.service.common;

import com.google.common.base.Joiner;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.model.ActiveMerchantListRequest;
import com.mcoin.mall.model.DaygainListRequest;
import com.mcoin.mall.model.RecommendZoneRequest;
import com.mcoin.mall.security.UserInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Locale;

import static com.mcoin.mall.util.JodaTimeUtil.*;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class CacheService {

    public static final String CACHE_SEPARATOR = ":";
    public static final String CACHE_DEFAULT_KEY = "default";
    public static final String CACHE_APP_TOKEN = "cache.app.token";
    public static final String CACHE_SETTINGS_VALUE = "cache.settings.value";

    public static final String CACHE_DAY_GAIN = "cache.day.gain";
    public static final String CACHE_ACTIVE_MERCHANT = "cache.active.merchant";
    public static final String CACHE_ASIA_MILES_USERS = "cache.asia.miles.users";
    public static final String CACHE_BUSINESS_INFORMATION = "cache.business.information";
    public static final String CACHE_INDEX_BANNER = "cache.index.banner";
    public static final String CACHE_POPULAR_SEARCHES = "cache.popular.searches";
    public static final String CACHE_INDEX_POPULAR_SEARCHES = "cache.index.popular.searches";
    public static final String CACHE_STORE_KEY_WORDS = "cache.store.key.words";
    public static final String CACHE_STORE_KEY_WORDS_V1 = "cache.store.key.words.one";
    public static final String CACHE_STORE_KEY_WORDS_V2 = "cache.store.key.words.two";
    public static final String CACHE_ACTIVE_ZONE = "cache.active.zone";
    public static final String CACHE_ACTIVE_ZONE_PRODUCT = "cache.active.zone.product";
    public static final String CACHE_STORES_TYPE = "cache.stores.type";
    public static final String CACHE_SNAP_UP_NOW = "cache.snap.up.now";
    public static final String CACHE_SNAP_UP_NEXT = "cache.snap.up.next";
    public static final String CACHE_SNAP_UP_MORE = "cache.snap.up.more";
    public static final String CACHE_STORE_DISTANCE = "cache.store.distance";
    public static final String CACHE_STORE_TYPE_PRODUCT = "cache.store.type.product";
    public static final String CACHE_USER_INFO = "cache.user.info";
    public static final String CACHE_INDEX = "cache.index.module";
    public static final String CACHE_ZONE_SNAPUP = "cache.zone.snapup";
    public static final String CACHE_ZONE_SNAPUPNEXT = "cache.zone.snapupnext";
    public static final String CACHE_SNAP_UP_SESSION = "cache.snap.up.session";
    public static final String CACHE_SNAP_UP_SESSION_PRODUCT = "cache.snap.up.session.product";
    public static final String CACHE_STORE_TYPE_PRODUCT_INFO = "cache.store.type.product.info";

    @Resource
    private ContextHolder contextHolder;


    public String getDayGainKey(DaygainListRequest request) {
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(request.getId(), request.getPage(), getLanguage());
    }

    public String getActiveMerchantKey(ActiveMerchantListRequest request){
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(request.getActive_id(), request.getLucky_draw(),
                request.getVoucher(), request.getPage(), getLanguage());
    }
    public boolean isCacheActiveMerchant(ActiveMerchantListRequest request) {
        return isBlank(request.getName()) && isBlank(request.getAddress()) && isBlank(request.getEvent());
    }

    public String getActiveZoneProductKey(String type, Integer zoneId, Boolean harmonySwitch){
        if (harmonySwitch) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join(zoneId, type, getLanguage(), ClientTypeEnum.HARMONY.toString());
        }
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(zoneId, type, getLanguage());
    }

    public String getSnapUpKey(Date now, int limit, Boolean harmonySwitch){
        if (harmonySwitch) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDD), limit, ClientTypeEnum.HARMONY.toString());
        }
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDD), limit);
    }

    public String getSnapUpNowKey(Date now, int limit, Boolean harmonySwitch){
        // 每分钟一个KEY
        if (harmonySwitch) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDDHHM), limit, ClientTypeEnum.HARMONY.toString());
        }
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDDHHM), limit);
    }

    public String getSnapUpShowKey(Date now, int limit,int showId,boolean filterShowInZone,boolean harmonySwitch){
        if (harmonySwitch) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDD), limit,showId,filterShowInZone,ClientTypeEnum.HARMONY.toString());
        }
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDD), limit,showId,filterShowInZone);
    }

    public String getSnapUpNowShowKey(Date now, int limit,int showId,boolean filterShowInZone,boolean harmonySwitch){
        if (harmonySwitch) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDDHHM), limit,showId,filterShowInZone,ClientTypeEnum.HARMONY.toString());
        }
        // 每分钟一个KEY
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDDHHM), limit,showId,filterShowInZone);
    }

    public String getSnapUpMoreKey(Date now, int limit, int offset, Boolean harmonySwitch){
        // 每分钟一个KEY
        if (harmonySwitch) {
            Joiner.on(CACHE_SEPARATOR).skipNulls().join("data", format(now,YYYYMMDDHHM), limit, offset, ClientTypeEnum.HARMONY.toString());
        }
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join("data", format(now,YYYYMMDDHHM), limit, offset);
    }

    public String getSnapUpMoreCountKey(Date now, Boolean harmonySwitch){
        // 每分钟一个KEY
        if (harmonySwitch) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join("count", format(now,YYYYMMDDHHM), ClientTypeEnum.HARMONY.toString());
        }
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join("count", format(now,YYYYMMDDHHM));
    }

    public String getLanguage(){
        Locale local = contextHolder.getLocale();
        return local.getLanguage();
    }

    public String getStoreKeyWordsV2Key(Boolean harmonySwitch) {
        if (Boolean.TRUE.equals(harmonySwitch)) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join(CACHE_DEFAULT_KEY, ClientTypeEnum.HARMONY.toString());
        }
        return CACHE_DEFAULT_KEY;
    }

    public String getUserId(){
        UserInfo userInfo = contextHolder.getAuthUserInfo();
        return String.valueOf(userInfo.getUserId());
    }

    public String getSnapUpSessionKey(Date now, Integer limit){
        // 每10秒+limit 一个key
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDDHHM),
                getSecondsWithoutSecondsLastDigit(now), limit);
    }

    public String getSnapUpSessionProductKey(Integer sessionId, Integer limit, Integer offset, Boolean harmonySwitch){
        // 每10秒+sessionId+limit+offset 一个key
        Date now = new Date();
        if (harmonySwitch) {
            return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDDHHM),
                    getSecondsWithoutSecondsLastDigit(now), sessionId, limit, offset, ClientTypeEnum.HARMONY.toString());
        }
        return Joiner.on(CACHE_SEPARATOR).skipNulls().join(format(now,YYYYMMDDHHM),
                getSecondsWithoutSecondsLastDigit(now), sessionId, limit, offset);
    }

}
