package com.mcoin.mall.service.job.utils;

import com.mcoin.mall.client.MPayCouponClient;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class MD5UtilsTest {

    @Test
    public void testToHex() {
        byte[] hash = {0x1a, 0x2b, 0x3c, 0x4d};
        String hex = MPayCouponClient.MD5Utils.toHex(hash);
        assertEquals("1a2b3c4d", hex);
    }

    @Test
    public void testGetMD5WithDefaultSalt() {
        String testObject = "Test String";
        String md5 = MPayCouponClient.MD5Utils.getMD5(testObject);
        assertNotNull(md5);
        assertFalse(md5.isEmpty());
    }


}
