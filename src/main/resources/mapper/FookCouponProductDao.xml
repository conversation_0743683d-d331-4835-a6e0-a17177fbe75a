<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookCouponProductDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookCouponProduct">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="productid" jdbcType="INTEGER" property="productid" />
    <result column="couponid" jdbcType="VARCHAR" property="couponid" />
    <result column="batchid" jdbcType="VARCHAR" property="batchid" />
    <result column="stock" jdbcType="INTEGER" property="stock" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, productid, couponid, batchid, stock, `status`, createtime, updatetime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_coupon_product
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_coupon_product
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookCouponProduct">
    insert into fook_coupon_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productid != null">
        productid,
      </if>
      <if test="couponid != null">
        couponid,
      </if>
      <if test="batchid != null">
        batchid,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="updatetime != null">
        updatetime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productid != null">
        #{productid,jdbcType=INTEGER},
      </if>
      <if test="couponid != null">
        #{couponid,jdbcType=VARCHAR},
      </if>
      <if test="batchid != null">
        #{batchid,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null">
        #{updatetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookCouponProduct">
    update fook_coupon_product
    <set>
      <if test="productid != null">
        productid = #{productid,jdbcType=INTEGER},
      </if>
      <if test="couponid != null">
        couponid = #{couponid,jdbcType=VARCHAR},
      </if>
      <if test="batchid != null">
        batchid = #{batchid,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null">
        updatetime = #{updatetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookCouponProduct">
    update fook_coupon_product
    set productid = #{productid,jdbcType=INTEGER},
      couponid = #{couponid,jdbcType=VARCHAR},
      batchid = #{batchid,jdbcType=VARCHAR},
      stock = #{stock,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      updatetime = #{updatetime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>