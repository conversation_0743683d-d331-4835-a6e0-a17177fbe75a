-- Clear existing test data if any
DELETE FROM fook_temporary_product WHERE id BETWEEN 10001 AND 10020;
DELETE FROM fook_business_product WHERE id BETWEEN 10001 AND 10020;
DELETE FROM fook_business_store_product WHERE productid BETWEEN 10001 AND 10020;
DELETE FROM fook_stores WHERE id BETWEEN 10001 AND 10010;
DELETE FROM fook_business WHERE id BETWEEN 10001 AND 10005;

-- Insert test businesses
INSERT INTO fook_business (id, areas_id, code, name, company_name, account, status, tel, address, sex, enbale, system_type) VALUES
(10001, 1, 'TEST001', 'Test Business 1', 'Test Company 1', 'TEST001', 1, '*********', 'Test Address 1', 1, 1, 1),
(10002, 1, 'TEST002', 'Test Business 2', 'Test Company 2', 'TEST002', 1, '*********', 'Test Address 2', 1, 1, 1),
(10003, 1, 'TEST003', 'Test Business 3', 'Test Company 3', 'TEST003', 1, '*********', 'Test Address 3', 1, 1, 1),
(10004, 1, 'TEST004', 'Test Business 4', 'Test Company 4', 'TEST004', 1, '*********', 'Test Address 4', 1, 1, 1),
(10005, 1, 'TEST005', 'Test Business 5', 'Test Company 5', 'TEST005', 1, '*********', 'Test Address 5', 1, 1, 1);

-- Insert test stores
INSERT INTO fook_stores (id, store_number, name, business_id, `enable`, longitude, dimension) VALUES
(10001, 'TS10001', 'Test Store 1', 10001, 1, '113.5', '22.2'),
(10002, 'TS10002', 'Test Store 2', 10001, 1, '113.6', '22.3'),
(10003, 'TS10003', 'Test Store 3', 10002, 1, '113.7', '22.4'),
(10004, 'TS10004', 'Test Store 4', 10002, 1, '113.8', '22.5'),
(10005, 'TS10005', 'Test Store 5', 10003, 1, '113.9', '22.6'),
(10006, 'TS10006', 'Test Store 6', 10003, 1, '114.0', '22.7'),
(10007, 'TS10007', 'Test Store 7', 10004, 1, '114.1', '22.8'),
(10008, 'TS10008', 'Test Store 8', 10004, 1, '114.2', '22.9'),
(10009, 'TS10009', 'Test Store 9', 10005, 1, '114.3', '23.0'),
(10010, 'TS10010', 'Test Store 10', 10005, 1, '114.4', '23.1');

-- Insert test products in fook_business_product
INSERT INTO fook_business_product (id, areaid, `type`, businessid, img, zip_img, title, price, retail_price,
                                  stock, history_stock, sales, shelf_status, buy_start_time, buy_end_time,
                                  created_at, updated_at, `enable`, is_hot, is_great, collect_times, platform_type, only_point)
VALUES
-- Regular products (type 1-5) with 'test' in the title for search
(10001, 1, 1, 10001, 'image/test/product1.jpg', 'image/test/zip_product1.jpg', 'Test Product 1', 10.00, 20.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

(10002, 1, 2, 10001, 'image/test/product2.jpg', 'image/test/zip_product2.jpg', 'Test Product 2', 15.00, 30.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

(10003, 1, 3, 10002, 'image/test/product3.jpg', 'image/test/zip_product3.jpg', 'Test Product 3', 20.00, 40.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

(10004, 1, 4, 10002, 'image/test/product4.jpg', 'image/test/zip_product4.jpg', 'Test Product 4', 25.00, 50.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

(10005, 1, 5, 10003, 'image/test/product5.jpg', 'image/test/zip_product5.jpg', 'Test Product 5', 30.00, 60.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

-- Type 12 products (should be filtered for HARMONY clients)
(10006, 1, 12, 10003, 'image/test/product6.jpg', 'image/test/zip_product6.jpg', 'Test Product 6 (Type 12)', 35.00, 70.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

(10007, 1, 12, 10004, 'image/test/product7.jpg', 'image/test/zip_product7.jpg', 'Test Product 7 (Type 12)', 40.00, 80.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

-- Products with different titles for search testing
(10008, 1, 1, 10004, 'image/test/product8.jpg', 'image/test/zip_product8.jpg', 'Special Test Offer', 45.00, 90.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

(10009, 1, 2, 10005, 'image/test/product9.jpg', 'image/test/zip_product9.jpg', 'Discount Test Item', 50.00, 100.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0),

(10010, 1, 3, 10005, 'image/test/product10.jpg', 'image/test/zip_product10.jpg', 'Limited Test Offer', 55.00, 110.00,
 100, 100, 0, 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59',
 '2023-01-01 00:00:00', '2023-01-01 00:00:00', 1, 1, 1, 0, 1, 0);

-- Insert same products in fook_temporary_product (for testing consistency)
INSERT INTO fook_temporary_product (temporary_id, id, img, zip_img, businessid, title, type, retail_price, price,
                                   shelf_status, buy_start_time, buy_end_time, vaild_mode, vaild_start_time,
                                   vaild_end_time, day_number, snap_up, only_point, actual_sales, is_hot, is_great,
                                   complex_name, complex_name_en)
VALUES
-- Regular products (type 1-5)
(10001, 10001, 'image/test/product1.jpg', 'image/test/zip_product1.jpg', 10001, 'Test Product 1', 1, 20.00, 10.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Test Product 1', 'Test Product 1'),

(10002, 10002, 'image/test/product2.jpg', 'image/test/zip_product2.jpg', 10001, 'Test Product 2', 2, 30.00, 15.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Test Product 2', 'Test Product 2'),

(10003, 10003, 'image/test/product3.jpg', 'image/test/zip_product3.jpg', 10002, 'Test Product 3', 3, 40.00, 20.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Test Product 3', 'Test Product 3'),

(10004, 10004, 'image/test/product4.jpg', 'image/test/zip_product4.jpg', 10002, 'Test Product 4', 4, 50.00, 25.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Test Product 4', 'Test Product 4'),

(10005, 10005, 'image/test/product5.jpg', 'image/test/zip_product5.jpg', 10003, 'Test Product 5', 5, 60.00, 30.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Test Product 5', 'Test Product 5'),

-- Type 12 products (should be filtered for HARMONY clients)
(10006, 10006, 'image/test/product6.jpg', 'image/test/zip_product6.jpg', 10003, 'Test Product 6 (Type 12)', 12, 70.00, 35.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Test Product 6 (Type 12)', 'Test Product 6 (Type 12)'),

(10007, 10007, 'image/test/product7.jpg', 'image/test/zip_product7.jpg', 10004, 'Test Product 7 (Type 12)', 12, 80.00, 40.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Test Product 7 (Type 12)', 'Test Product 7 (Type 12)'),

-- Products with different titles for search testing
(10008, 10008, 'image/test/product8.jpg', 'image/test/zip_product8.jpg', 10004, 'Special Test Offer', 1, 90.00, 45.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Special Test Offer', 'Special Test Offer'),

(10009, 10009, 'image/test/product9.jpg', 'image/test/zip_product9.jpg', 10005, 'Discount Test Item', 2, 100.00, 50.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Discount Test Item', 'Discount Test Item'),

(10010, 10010, 'image/test/product10.jpg', 'image/test/zip_product10.jpg', 10005, 'Limited Test Offer', 3, 110.00, 55.00,
 1, '2023-01-01 00:00:00', '2030-12-31 23:59:59', 0, NULL, NULL, 0, 0, 0, 0, 1, 1,
 'Limited Test Offer', 'Limited Test Offer');

-- Associate products with stores
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES
(20001, 10001, 10001, 1, 10001),
(20002, 10002, 10002, 1, 10001),
(20003, 10003, 10003, 1, 10002),
(20004, 10004, 10004, 1, 10002),
(20005, 10005, 10005, 1, 10003),
(20006, 10006, 10006, 1, 10003),
(20007, 10007, 10007, 1, 10004),
(20008, 10008, 10008, 1, 10004),
(20009, 10009, 10009, 1, 10005),
(20010, 10010, 10010, 1, 10005);
