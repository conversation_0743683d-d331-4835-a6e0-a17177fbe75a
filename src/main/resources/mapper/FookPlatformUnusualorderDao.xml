<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformUnusualorderDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformUnusualorder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="sellerid" jdbcType="INTEGER" property="sellerid" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="payment_type" jdbcType="INTEGER" property="paymentType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="point_ratio" jdbcType="INTEGER" property="pointRatio" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="bank_charges" jdbcType="DECIMAL" property="bankCharges" />
    <result column="is_mpay" jdbcType="TINYINT" property="isMpay" />
    <result column="is_member" jdbcType="TINYINT" property="isMember" />
    <result column="memberintegral" jdbcType="DECIMAL" property="memberintegral" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="score_tradeno" jdbcType="VARCHAR" property="scoreTradeno" />
    <result column="pay_tradeno" jdbcType="VARCHAR" property="payTradeno" />
    <result column="query_count" jdbcType="INTEGER" property="queryCount" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="pay_uuid" jdbcType="VARCHAR" property="payUuid" />
    <result column="score_uuid" jdbcType="VARCHAR" property="scoreUuid" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="score_status" jdbcType="TINYINT" property="scoreStatus" />
    <result column="refundid" jdbcType="INTEGER" property="refundid" />
    <result column="refund_code" jdbcType="INTEGER" property="refundCode" />
    <result column="refund_orderno" jdbcType="VARCHAR" property="refundOrderno" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="result_status" jdbcType="TINYINT" property="resultStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, userid, sellerid, orderid, order_no, create_time, payment_type, `status`, complete_time, 
    mpayintegral, order_amount, currency, total_amount, payment_amount, point_ratio,
    payment_time, bank_charges, is_mpay, is_member, memberintegral, business_name, product_id,
    product_name, phone, score_tradeno, pay_tradeno, query_count, `type`, pay_uuid, score_uuid,
    pay_status, score_status, refundid, refund_code, refund_orderno, created_at, updated_at,
    result_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_unusualorder
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_unusualorder
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformUnusualorder">
    insert into fook_platform_unusualorder
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="sellerid != null">
        sellerid,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="pointRatio != null">
        point_ratio,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="bankCharges != null">
        bank_charges,
      </if>
      <if test="isMpay != null">
        is_mpay,
      </if>
      <if test="isMember != null">
        is_member,
      </if>
      <if test="memberintegral != null">
        memberintegral,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="scoreTradeno != null">
        score_tradeno,
      </if>
      <if test="payTradeno != null">
        pay_tradeno,
      </if>
      <if test="queryCount != null">
        query_count,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="payUuid != null">
        pay_uuid,
      </if>
      <if test="scoreUuid != null">
        score_uuid,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="scoreStatus != null">
        score_status,
      </if>
      <if test="refundid != null">
        refundid,
      </if>
      <if test="refundCode != null">
        refund_code,
      </if>
      <if test="refundOrderno != null">
        refund_orderno,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="resultStatus != null">
        result_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="sellerid != null">
        #{sellerid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankCharges != null">
        #{bankCharges,jdbcType=DECIMAL},
      </if>
      <if test="isMpay != null">
        #{isMpay,jdbcType=TINYINT},
      </if>
      <if test="isMember != null">
        #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="scoreTradeno != null">
        #{scoreTradeno,jdbcType=VARCHAR},
      </if>
      <if test="payTradeno != null">
        #{payTradeno,jdbcType=VARCHAR},
      </if>
      <if test="queryCount != null">
        #{queryCount,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="payUuid != null">
        #{payUuid,jdbcType=VARCHAR},
      </if>
      <if test="scoreUuid != null">
        #{scoreUuid,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="scoreStatus != null">
        #{scoreStatus,jdbcType=TINYINT},
      </if>
      <if test="refundid != null">
        #{refundid,jdbcType=INTEGER},
      </if>
      <if test="refundCode != null">
        #{refundCode,jdbcType=INTEGER},
      </if>
      <if test="refundOrderno != null">
        #{refundOrderno,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="resultStatus != null">
        #{resultStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformUnusualorder">
    update fook_platform_unusualorder
    <set>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="sellerid != null">
        sellerid = #{sellerid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        point_ratio = #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankCharges != null">
        bank_charges = #{bankCharges,jdbcType=DECIMAL},
      </if>
      <if test="isMpay != null">
        is_mpay = #{isMpay,jdbcType=TINYINT},
      </if>
      <if test="isMember != null">
        is_member = #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        memberintegral = #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="scoreTradeno != null">
        score_tradeno = #{scoreTradeno,jdbcType=VARCHAR},
      </if>
      <if test="payTradeno != null">
        pay_tradeno = #{payTradeno,jdbcType=VARCHAR},
      </if>
      <if test="queryCount != null">
        query_count = #{queryCount,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="payUuid != null">
        pay_uuid = #{payUuid,jdbcType=VARCHAR},
      </if>
      <if test="scoreUuid != null">
        score_uuid = #{scoreUuid,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="scoreStatus != null">
        score_status = #{scoreStatus,jdbcType=TINYINT},
      </if>
      <if test="refundid != null">
        refundid = #{refundid,jdbcType=INTEGER},
      </if>
      <if test="refundCode != null">
        refund_code = #{refundCode,jdbcType=INTEGER},
      </if>
      <if test="refundOrderno != null">
        refund_orderno = #{refundOrderno,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="resultStatus != null">
        result_status = #{resultStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformUnusualorder">
    update fook_platform_unusualorder
    set userid = #{userid,jdbcType=INTEGER},
      sellerid = #{sellerid,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      payment_type = #{paymentType,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      point_ratio = #{pointRatio,jdbcType=INTEGER},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      bank_charges = #{bankCharges,jdbcType=DECIMAL},
      is_mpay = #{isMpay,jdbcType=TINYINT},
      is_member = #{isMember,jdbcType=TINYINT},
      memberintegral = #{memberintegral,jdbcType=DECIMAL},
      business_name = #{businessName,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      product_name = #{productName,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      score_tradeno = #{scoreTradeno,jdbcType=VARCHAR},
      pay_tradeno = #{payTradeno,jdbcType=VARCHAR},
      query_count = #{queryCount,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      pay_uuid = #{payUuid,jdbcType=VARCHAR},
      score_uuid = #{scoreUuid,jdbcType=VARCHAR},
      pay_status = #{payStatus,jdbcType=TINYINT},
      score_status = #{scoreStatus,jdbcType=TINYINT},
      refundid = #{refundid,jdbcType=INTEGER},
      refund_code = #{refundCode,jdbcType=INTEGER},
      refund_orderno = #{refundOrderno,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      result_status = #{resultStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>