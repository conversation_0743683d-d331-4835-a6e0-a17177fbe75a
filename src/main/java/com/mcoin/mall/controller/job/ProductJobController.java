package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.CheckSnappingStockRequest;
import com.mcoin.mall.service.job.ProductJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/product")
@Slf4j
public class ProductJobController {
    @Resource
    private ProductJobService productJobService;
    /**
     * 檢查已搶購庫存
     *
     * @return
     */
    @PostMapping(value = "/checkSnappingStock")
    public Response<String> checkSnappingStock(@Valid CheckSnappingStockRequest request) {
        productJobService.checkSnappingStock(request);
        return Responses.ok("success");
    }

    /**
     * 福利下架
     * 将已过售卖时间所有上架状态的福利产品下架
     *
     * @return 标准响应格式
     */
    @PostMapping(value = "/afterSalesTime/offline")
    public Response<String> batchOfflineProducts() {
        productJobService.offlineProducts();
        return Responses.ok("success");
    }
}
