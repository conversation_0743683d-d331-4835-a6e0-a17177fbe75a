package com.mcoin.mall.service.amap.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.mcoin.mall.client.AmapClient;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class AmapServiceImplTest{

    @Mock
    private AmapClient amapClient;

    @InjectMocks
    private AmapServiceImpl amapService;

    @Value("${map.tencent_map_key}")
    private String mapTencentMapkey;

    @BeforeEach
    public void setUp() {
        // Set the mockTencentMapkey value
        mapTencentMapkey = "mockKey";
    }

    @Test
    public void testGeocoderCoordinateBlankLocation() {
        // Given
        String location = "";
    
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            amapService.geocoderCoordinate(location);
        });
        assertEquals(Response.Code.UNKNOWN_ERROR, exception.getRespCode());
        assertEquals("system error", exception.getMessage());
    }

    @Test
    public void testGeocoderCoordinateNullLocation() {
        // Given
        String location = null;
    
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            amapService.geocoderCoordinate(location);
        });
        assertEquals(Response.Code.UNKNOWN_ERROR, exception.getRespCode());
        assertEquals("system error", exception.getMessage());
    }

    @Test
    public void testGeocoderCoordinateBlankMapTencentMapkey() throws Exception {
        // Given
        mapTencentMapkey = "";

        Field field = AmapServiceImpl.class.getDeclaredField("mapTencentMapkey");
        field.setAccessible(true);
        field.set(amapService, mapTencentMapkey);
    
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            amapService.geocoderCoordinate("123456");
        });
        assertEquals(Response.Code.UNKNOWN_ERROR, exception.getRespCode());
        assertEquals("system error", exception.getMessage());
    }

    @Test
    public void testGeocoderCoordinateNullMapTencentMapkey() throws Exception {
        // Given
        mapTencentMapkey = null;

        Field field = AmapServiceImpl.class.getDeclaredField("mapTencentMapkey");
        field.setAccessible(true);
        field.set(amapService, mapTencentMapkey);
    
        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            amapService.geocoderCoordinate("123456");
        });
        assertEquals(Response.Code.UNKNOWN_ERROR, exception.getRespCode());
        assertEquals("system error", exception.getMessage());
    }

    @Test
    public void testGeocoderCoordinateSuccess() throws Exception {
        Field field = AmapServiceImpl.class.getDeclaredField("mapTencentMapkey");
        field.setAccessible(true);
        field.set(amapService, mapTencentMapkey);

        // Given
        String location = "123456";
        String responseJson = "{\"status\":0,\"info\":\"OK\",\"infocode\":0,\"count\":1,\"lng\":116.405288,\"lat\":39.915085}";
        when(amapClient.getLocation(location, mapTencentMapkey)).thenReturn(responseJson);
    
        // When
        Map<String, Object> result = amapService.geocoderCoordinate(location);
    
        // Then
        assertNotNull(result);
        assertEquals(6, result.size());
        assertEquals(new BigDecimal("116.405288"), result.get("lng"));
        assertEquals(new BigDecimal("39.915085"), result.get("lat"));
    }

}