package com.mcoin.mall.service.show.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.mcoin.mall.bean.FookShowSnapupSession;
import com.mcoin.mall.bo.SnapUpItemBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookShowSnapupSessionDao;
import com.mcoin.mall.model.*;
import com.mcoin.mall.service.core.SnapUpConvertService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;

import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.PageUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.context.MessageSource;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

public class ShowSnapUpServiceImplTest {

    @Mock private MessageSource messageSource;

    @Mock private ContextHolder contextHolder;

    @Mock private SnapUpConvertService snapUpConvertService;

    @Mock private FookBusinessProductDao fookBusinessProductDao;

    @Mock private FookShowSnapupSessionDao fookShowSnapupSessionDao;

    @InjectMocks private ShowSnapUpServiceImpl showSnapUpService;

    MockedStatic<ConfigUtils> configUtilsStatic;
    @BeforeEach
    void setUp() {
        configUtilsStatic = mockStatic(ConfigUtils.class);
        MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void tearDown() {
        configUtilsStatic.close();
    }

    @Test
    void testGetIndexSnapUpProductWithNoSession() throws ParseException {
        // Arrange
        Date now = new Date();
        Date today = JodaTimeUtil.getDateWithoutTime(now);
        Date tomorrow = JodaTimeUtil.plusDayToDate(today, 1);
        when(fookShowSnapupSessionDao.selectCurrentSession(now, tomorrow)).thenReturn(null);

        // Act
        SnapUpSessionIndexResponse response =
                showSnapUpService.getIndexSnapUpProduct(new SnapUpSessionIndexRequest(), ClientTypeEnum.HARMONY.toString());

        // Assert
        assertNotNull(response);
        assertNull(response.getSessionId());
        assertNull(response.getSessionStartTime());
        assertNull(response.getSessionEndTime());
        assertTrue(response.getSnatchList().isEmpty());
    }

    @Test
    void testGetIndexSnapUpProduct() throws ParseException {

        when(ConfigUtils.getProperty("show.snapup.index.product.num", "3")).thenReturn("5");

        // Arrange
        Date now = new Date();
        Date today = JodaTimeUtil.getDateWithoutTime(now);
        Date tomorrow = JodaTimeUtil.plusDayToDate(today, 1);
        FookShowSnapupSession session = new FookShowSnapupSession();
        session.setId(1);
        session.setStartTime(today);
        session.setEndTime(tomorrow);
        when(fookShowSnapupSessionDao.selectCurrentSession(any(Date.class), eq(tomorrow))).thenReturn(session);

        List<SnapUpItemBo> snapUpItemBos = new ArrayList<>();
        SnapUpItemBo snapUpItemBo = new SnapUpItemBo();
        snapUpItemBo.setTitle("test");
        snapUpItemBos.add(snapUpItemBo);
        when(fookBusinessProductDao.getShowSnapUpIndexListBySessionId(anyInt(), anyInt(), anyBoolean()))
                .thenReturn(snapUpItemBos);

        List<SnapUpSessionProductItem> snatchList = new ArrayList<>();
        SnapUpSessionProductItem snatch = new SnapUpSessionProductItem();
        snatch.setTitle("test");
        snatchList.add(snatch);
        when(snapUpConvertService.convertToSessionSnatchList(any(), anyList()))
                .thenReturn(snatchList);

        // Act
        SnapUpSessionIndexResponse response =
                showSnapUpService.getIndexSnapUpProduct(new SnapUpSessionIndexRequest(), ClientTypeEnum.HARMONY.toString());

        // Assert
        assertNotNull(response);
        assertEquals(session.getId(), response.getSessionId());
        assertNotNull(response.getSessionStartTime());
        assertNotNull(response.getSessionEndTime());
        assertEquals(snatchList, response.getSnatchList());

    }
     @Test
     void testGetIndexSnapUpProductWithCustomNum() throws ParseException {
         // Arrange
         when(ConfigUtils.getProperty("show.snapup.index.product.num", "3")).thenReturn("5");

         Date now = new Date();
         Date today = JodaTimeUtil.getDateWithoutTime(now);
         Date tomorrow = JodaTimeUtil.plusDayToDate(today, 1);
         FookShowSnapupSession session = new FookShowSnapupSession();
         session.setId(1);
         session.setStartTime(today);
         session.setEndTime(tomorrow);
         when(fookShowSnapupSessionDao.selectCurrentSession(any(Date.class), eq(tomorrow))).thenReturn(session);

         List<SnapUpItemBo> snapUpItemBos = new ArrayList<>();
         for (int i = 0; i < 5; i++) {
             SnapUpItemBo snapUpItemBo = new SnapUpItemBo();
             snapUpItemBo.setTitle("test" + i);
             snapUpItemBos.add(snapUpItemBo);
         }
         when(fookBusinessProductDao.getShowSnapUpIndexListBySessionId(anyInt(), eq(5), anyBoolean()))
                 .thenReturn(snapUpItemBos);

         List<SnapUpSessionProductItem> snatchList = new ArrayList<>();
         for (int i = 0; i < 5; i++) {
             SnapUpSessionProductItem snatch = new SnapUpSessionProductItem();
             snatch.setTitle("test" + i);
             snatchList.add(snatch);
         }
         when(snapUpConvertService.convertToSessionSnatchList(any(), anyList()))
                 .thenReturn(snatchList);

         // Act
         SnapUpSessionIndexResponse response = showSnapUpService.getIndexSnapUpProduct(new SnapUpSessionIndexRequest()
                 , ClientTypeEnum.HARMONY.toString());

         // Assert
         assertNotNull(response);
         assertEquals(session.getId(), response.getSessionId());
         assertNotNull(response.getSessionStartTime());
         assertNotNull(response.getSessionEndTime());
         assertEquals(5, response.getSnatchList().size());
     }

    @Test
    public void testGetSnapUpSessionInfo() {
        int defaultNum = 5;
        when(ConfigUtils.getProperty("show.snapup.session.num", "4"))
                .thenReturn(String.valueOf(defaultNum));

        Date now = new Date();
        Date today = JodaTimeUtil.getDateWithoutTime(now);
        Date next = JodaTimeUtil.plusMinuteToDate(now, 1);
        Date tomorrow = JodaTimeUtil.plusDayToDate(today, 1);
        List<FookShowSnapupSession> sessions = new ArrayList<>();
        FookShowSnapupSession session1 = new FookShowSnapupSession();
        session1.setId(1);
        session1.setStartTime(now);
        session1.setEndTime(tomorrow);
        sessions.add(session1);
        FookShowSnapupSession session2 = new FookShowSnapupSession();
        session2.setId(2);
        session2.setStartTime(tomorrow);
        session2.setEndTime(tomorrow);
        sessions.add(session2);
        FookShowSnapupSession session3 = new FookShowSnapupSession();
        session3.setId(3);
        session3.setStartTime(next);
        session3.setEndTime(tomorrow);
        sessions.add(session3);
        when(fookShowSnapupSessionDao.selectCurrentAndNextSession(any(), any(), eq(defaultNum)))
                .thenReturn(sessions);
        when(messageSource.getMessage(
                eq("message.show.snapup.session.date_txt_now"), any(), any()))
                .thenReturn("now");
        when(messageSource.getMessage(
                eq("message.show.snapup.session.date_txt_next"), any(), any()))
                .thenReturn("next");
        when(messageSource.getMessage(
                        eq("message.show.snapup.session.date_txt_tomorrow"), any(), any()))
                .thenReturn("tomorrow");

        SnapUpSessionInfoResponse response = showSnapUpService.getSnapUpSessionInfo(null);
        assertEquals("now", response.getSessionInfoList().get(0).getSessionDateTxt());
        assertEquals("tomorrow", response.getSessionInfoList().get(1).getSessionDateTxt());
        assertEquals("next", response.getSessionInfoList().get(2).getSessionDateTxt());
        assertEquals(JodaTimeUtil.format(now, "HH:mm"), response.getSessionInfoList().get(0).getSessionName());
        assertEquals("00:00", response.getSessionInfoList().get(1).getSessionName());
        assertEquals(JodaTimeUtil.format(next, "HH:mm"), response.getSessionInfoList().get(2).getSessionName());
        assertEquals(now, response.getSessionInfoList().get(0).getStartTime());
        assertEquals(tomorrow, response.getSessionInfoList().get(0).getEndTime());
        assertEquals(tomorrow, response.getSessionInfoList().get(1).getStartTime());
        assertEquals(tomorrow, response.getSessionInfoList().get(1).getEndTime());
        assertEquals(next, response.getSessionInfoList().get(2).getStartTime());
        assertEquals(tomorrow, response.getSessionInfoList().get(2).getEndTime());
    }
    @Test
    public void testGetSnapUpSessionProductWithClickedItemOnFirstPage() {
        // Arrange
        int clickedItemId = 2; // Assuming the clicked item is the second item on the first page
        int pageSize = McoinMall.DEFAULT_PAGE_SIZE;
        int offset = PageUtil.getOffset(1, pageSize);
        List<SnapUpItemBo> firstPageItems = new ArrayList<>();
        firstPageItems.add(createSnapUpItemBo(1, 10)); // Item 1
        firstPageItems.add(createSnapUpItemBo(clickedItemId, 10)); // Item 2 (clicked)
        // Add more items if needed to simulate a full page
        FookShowSnapupSession session = new FookShowSnapupSession();
        session.setId(1);
        session.setStartTime(new Date());
        session.setEndTime(new Date());

        when(fookShowSnapupSessionDao.selectByPrimaryKey(1)).thenReturn(session);
        when(fookBusinessProductDao.getShowSnapUpIndexListCountBySessionId(1, true)).thenReturn(firstPageItems.size());
        when(fookBusinessProductDao.getShowSnapUpIndexListPageBySessionId(1, pageSize, offset, true)).thenReturn(firstPageItems);
        when(snapUpConvertService.convertToSessionSnatchList(any(), anyList())).thenReturn(createSnapUpSessionProductItemList(firstPageItems));

        SnapUpSessionProductRequest request = new SnapUpSessionProductRequest();
        request.setSessionId(1);
        request.setPage(1);
        request.setProductId(clickedItemId);

        // Act
        SnapUpSessionProductResponse response = showSnapUpService.getSnapUpSessionProduct(request, ClientTypeEnum.HARMONY.toString());

        // Assert
        assertNotNull(response);
        List<SnapUpSessionProductItem> snatchList = response.getSnatchList();
        assertNotNull(snatchList);
        assertEquals(clickedItemId, snatchList.get(0).getId()); // 确保点击的商品排在了第一位
        assertEquals(firstPageItems.size(), snatchList.size()); // 确保商品列表数量正确
    }

    @Test
    public void testGetSnapUpSessionProductWithClickedItemNotInFirstPage() {
        // Arrange
        int clickedItemId = 5; // Assuming the clicked item is not on the first page
        int pageSize = 2; // Assuming page size is 2 for this test
        int totalCount = 5; // Assuming total items are 5
        int offset = PageUtil.getOffset(1, pageSize);
        List<SnapUpItemBo> firstPageItems = new ArrayList<>();
        firstPageItems.add(createSnapUpItemBo(1)); // Item 1
        firstPageItems.add(createSnapUpItemBo(2)); // Item 2
        // Add more items if needed to simulate second page

        List<SnapUpItemBo> allItems = new ArrayList<>(firstPageItems);
        for (int i = 3; i <= totalCount; i++) {
            allItems.add(createSnapUpItemBo(i, i));
        }

        FookShowSnapupSession session = new FookShowSnapupSession();
        session.setId(1);
        session.setStartTime(new Date());
        session.setEndTime(new Date());

        when(fookShowSnapupSessionDao.selectByPrimaryKey(1)).thenReturn(session);
        when(fookBusinessProductDao.getShowSnapUpIndexListCountBySessionId(1, true)).thenReturn(totalCount);
        when(fookBusinessProductDao.getShowSnapUpIndexListPageBySessionId(1, pageSize, offset, true)).thenReturn(firstPageItems);
        when(snapUpConvertService.convertToSessionSnatchList(any(), anyList())).thenReturn(createSnapUpSessionProductItemList(allItems));

        SnapUpSessionProductRequest request = new SnapUpSessionProductRequest();
        request.setSessionId(1);
        request.setPage(1);
        request.setProductId(clickedItemId);

        // Act
        SnapUpSessionProductResponse response = showSnapUpService.getSnapUpSessionProduct(request, ClientTypeEnum.HARMONY.toString());

        // Assert
        assertNotNull(response);
        List<SnapUpSessionProductItem> snatchList = response.getSnatchList();
        assertNotNull(snatchList);
        // 由于clickedItemId不在第一页，它应该按照原始顺序出现在列表中
        boolean foundClickedItem = false;
        for (int i = 0; i < snatchList.size(); i++) {
            if (snatchList.get(i).getId().equals(clickedItemId)) {
                foundClickedItem = true;
                break;
            }
        }
        assertTrue(foundClickedItem, "Clicked item not found in the response list");
    }

    private SnapUpItemBo createSnapUpItemBo(int id) {
        return createSnapUpItemBo(id, 0);
    }
    private SnapUpItemBo createSnapUpItemBo(int id, int stock) {
        SnapUpItemBo itemBo = new SnapUpItemBo();
        itemBo.setId(id);
        itemBo.setTitle("Product " + id);
        itemBo.setImg("image_" + id);
        itemBo.setPrice(new BigDecimal("100.00"));
        itemBo.setStock(stock);
        return itemBo;
    }

    private List<SnapUpSessionProductItem> createSnapUpSessionProductItemList(List<SnapUpItemBo> itemBos) {
        List<SnapUpSessionProductItem> itemList = new ArrayList<>();
        for (SnapUpItemBo itemBo : itemBos) {
            SnapUpSessionProductItem item = new SnapUpSessionProductItem();
            item.setId(itemBo.getId());
            item.setTitle(itemBo.getTitle());
            item.setImage(itemBo.getImg());
            item.setPrice(itemBo.getPrice());
            item.setStock(itemBo.getStock());
            itemList.add(item);
        }
        return itemList;
    }
}
