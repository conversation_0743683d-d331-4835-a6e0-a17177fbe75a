package com.mcoin.mall.service.business;

import com.mcoin.mall.bean.SearchProductQuery;
import com.mcoin.mall.model.*;

public interface SearchService {
    SearchStoreResponse searchShop(SearchStoreQuery query);

    PopularSearchesResponse getPopularSearches();

    BusinessInformationResponse getInformation();

    CheckSearchValueResponse checkSearchValue(CheckSearchValueRequest request, String clientType);

    SearchProductResponse searchProduct(SearchProductQuery searchProductQuery);

    Integer searchProductCount(SearchProductQuery searchProductQuery);

    boolean isProductTempHasData();

    Integer searchProductTmpCount(SearchProductQuery query);

    SearchProductResponse searchProductTmp(SearchProductQuery query);

    Integer searchShopCnt(SearchStoreQuery query);

    boolean buildSearchShopQuery(SearchStoreQuery query);
}
