<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingDao">
  <select id="selectTradingData" resultType="com.mcoin.mall.bo.TradingDataBo">
    select
      s.total_amount as tradingTotalAmount,
      s.subsidy_amount as subsidyAmount,
      sum(d.mpayintegral / d.point_ratio) as integralConversionAmount,
      count(distinct d.userid) as userPayNum,
      count(distinct d.sellerid) as tradingBusinessNum,
      count(d.id) as tradingNum,
      sum(d.payment_amount) as paymentAmountSum
    from fook_order_settlement as s
           join fook_order_settlement_data as d on s.id = d.order_settlement_id
    where s.start_time = #{startTime,jdbcType=TIMESTAMP}
      and s.end_time = #{endTime,jdbcType=TIMESTAMP}
      group by s.total_amount, s.subsidy_amount
      order by s.total_amount desc, s.subsidy_amount desc
    limit 1
  </select>

  <select id="selectTradingSettlementData" resultType="com.mcoin.mall.bo.TradingSettlementDataBo">
    select
      count(distinct businessid) as settlementBusinessNum,
      count(id) as settlementNum,
      sum(momecoinsamount) as momecoinsamountSum,
      sum(userpaymentamount) as userpaymentamountSum,
      sum(billamount) as billamountSum
    from fook_report_ordercode_settlement
    where usetime between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
  </select>
  <select id="selectTradingRefundData" resultType="com.mcoin.mall.bo.TradingRefundDataBo">
    select
      r.refund_amount as refundAmount,
      r.subsidy_amount as refundSubsidyAmount,
      r.mpayintegral as refundMpayintegral,
      count(d.id) as tradingNum,
      sum(d.order_amount) as orderAmount,
      sum(r.score) as refundConversionAmount
    from fook_refund_order as r
    join fook_refund_order_data as d on r.id = d.refund_order_id
    where r.start_time = #{startTime,jdbcType=TIMESTAMP}
      and r.end_time= #{endTime,jdbcType=TIMESTAMP}
      group by r.refund_amount, r.subsidy_amount, r.mpayintegral
    limit 1
  </select>
  <select id="selectTradingProductMaxData" resultType="com.mcoin.mall.bo.TradingProductMaxDataBo">
    select
      sum(i.number) as productNum,
      i.prodcutid as prodcutid,
      sum(d.order_amount) as orderAmountSum,
      i.title_snapshots as productTitle
    from fook_order_settlement as s
           join fook_order_settlement_data as d on s.id = d.order_settlement_id
           join fook_platform_orderinfo as i on d.order_id = i.orderid
    where s.start_time = #{startTime,jdbcType=TIMESTAMP}
      and s.end_time= #{endTime,jdbcType=TIMESTAMP}
    group by i.prodcutid, i.title_snapshots
    order by orderAmountSum desc
      limit 1
  </select>
  <select id="selectTradingProductHotData" resultType="com.mcoin.mall.bo.TradingProductHotDataBo">
    select
      sum(i.number) as productNum,
      i.prodcutid as prodcutid,
      sum(d.order_amount) as orderAmountSum,
      i.title_snapshots as productTitle
    from fook_order_settlement as s
           join fook_order_settlement_data as d on s.id = d.order_settlement_id
           join fook_platform_orderinfo as i on d.order_id = i.orderid
    where s.start_time = #{startTime,jdbcType=TIMESTAMP}
      and s.end_time= #{endTime,jdbcType=TIMESTAMP}
    group by i.prodcutid, i.title_snapshots
    order by productNum desc
    limit 1
  </select>

  <select id="selectTradingProductHotDataList" resultType="com.mcoin.mall.bo.TradingProductHotDataBo">
    select
      sum(i.number) as productNum,
      i.prodcutid as prodcutid,
      sum(d.order_amount) as orderAmountSum,
      i.title_snapshots as productTitle
    from fook_order_settlement as s
           join fook_order_settlement_data as d on s.id = d.order_settlement_id
           join fook_platform_orderinfo as i on d.order_id = i.orderid
    where s.start_time >= #{startTime,jdbcType=TIMESTAMP}
      <![CDATA[
      and s.end_time <= #{endTime,jdbcType=TIMESTAMP}
          ]]>
    group by i.prodcutid,i.title_snapshots
  </select>

</mapper>