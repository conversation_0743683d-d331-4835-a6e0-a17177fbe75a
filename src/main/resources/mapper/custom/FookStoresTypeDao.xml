<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresTypeDao">
  <select id="getStoresTypeByStoreId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fook_stores_type fst
    where fst.id in (
    select fstc.stores_type_id from fook_stores_type_class fstc
    where fstc.stores_id = #{id,jdbcType=INTEGER}
    )
  </select>


  <select id="getStoresTypes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fook_stores_type
    where
    sort > 0
    and enable = 1
    order by sort asc
  </select>

    <select id="selectStoreTypeIds" resultType="string">
        select id from fook_stores_type where enable = 1 and sort > 0 order by sort asc
    </select>
</mapper>