package com.mcoin.mall.controller.api;


import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.api.system.SystemTimeResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/api/system")
public class ApiSystemController {
    @GetMapping(value = "/time", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<SystemTimeResponse> time() {
        return Responses.ok(new SystemTimeResponse().setSysTime(new Date()));
    }
}
