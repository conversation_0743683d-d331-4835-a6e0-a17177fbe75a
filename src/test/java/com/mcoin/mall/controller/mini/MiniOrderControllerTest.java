package com.mcoin.mall.controller.mini;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.MiniOrderIsAfterRequest;
import com.mcoin.mall.model.MiniOrderIsAfterResponse;
import com.mcoin.mall.model.MiniOrderRequest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class MiniOrderControllerTest extends BaseUnitTest {


    @Autowired
    private MiniOrderController miniOrderController;


    @BeforeEach
    public void setUp() {
        // Mock locale
        Mockito.when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        Mockito.when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
    }


    @Test
    void testGetList() {
        MiniOrderRequest request = new MiniOrderRequest();

        assertThrows(BusinessException.class, () -> {
            miniOrderController.getList(request);
        });
    }

    @Test
    void testGetOrderIsAfter() {
        MiniOrderIsAfterRequest request = new MiniOrderIsAfterRequest();
        request.setOrderId(123456);

        Response<MiniOrderIsAfterResponse> orderIsAfter = miniOrderController.getOrderIsAfter(request);
        assertEquals(200, orderIsAfter.getCode());
    }
}