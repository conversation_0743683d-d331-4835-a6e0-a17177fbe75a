<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookDayGainDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookDayGain">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_en" jdbcType="VARCHAR" property="nameEn" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="img_en" jdbcType="VARCHAR" property="imgEn" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="discount_num" jdbcType="INTEGER" property="discountNum" />
    <result column="item_num" jdbcType="INTEGER" property="itemNum" />
    <result column="theme_color" jdbcType="VARCHAR" property="themeColor" />
    <result column="subtitle_color" jdbcType="VARCHAR" property="subtitleColor" />
    <result column="themetext_color" jdbcType="VARCHAR" property="themetextColor" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, name_en, img, img_en, `status`, url, discount_num, item_num, theme_color,
    subtitle_color, themetext_color, color, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_day_gain
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_day_gain
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookDayGain">
    insert into fook_day_gain
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="nameEn != null">
        name_en,
      </if>
      <if test="img != null">
        img,
      </if>
      <if test="imgEn != null">
        img_en,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="discountNum != null">
        discount_num,
      </if>
      <if test="itemNum != null">
        item_num,
      </if>
      <if test="themeColor != null">
        theme_color,
      </if>
      <if test="subtitleColor != null">
        subtitle_color,
      </if>
      <if test="themetextColor != null">
        themetext_color,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="img != null">
        #{img,jdbcType=VARCHAR},
      </if>
      <if test="imgEn != null">
        #{imgEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="discountNum != null">
        #{discountNum,jdbcType=INTEGER},
      </if>
      <if test="itemNum != null">
        #{itemNum,jdbcType=INTEGER},
      </if>
      <if test="themeColor != null">
        #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="subtitleColor != null">
        #{subtitleColor,jdbcType=VARCHAR},
      </if>
      <if test="themetextColor != null">
        #{themetextColor,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookDayGain">
    update fook_day_gain
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        name_en = #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="img != null">
        img = #{img,jdbcType=VARCHAR},
      </if>
      <if test="imgEn != null">
        img_en = #{imgEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="discountNum != null">
        discount_num = #{discountNum,jdbcType=INTEGER},
      </if>
      <if test="itemNum != null">
        item_num = #{itemNum,jdbcType=INTEGER},
      </if>
      <if test="themeColor != null">
        theme_color = #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="subtitleColor != null">
        subtitle_color = #{subtitleColor,jdbcType=VARCHAR},
      </if>
      <if test="themetextColor != null">
        themetext_color = #{themetextColor,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookDayGain">
    update fook_day_gain
    set `name` = #{name,jdbcType=VARCHAR},
      name_en = #{nameEn,jdbcType=VARCHAR},
      img = #{img,jdbcType=VARCHAR},
      img_en = #{imgEn,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      url = #{url,jdbcType=VARCHAR},
      discount_num = #{discountNum,jdbcType=INTEGER},
      item_num = #{itemNum,jdbcType=INTEGER},
      theme_color = #{themeColor,jdbcType=VARCHAR},
      subtitle_color = #{subtitleColor,jdbcType=VARCHAR},
      themetext_color = #{themetextColor,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>