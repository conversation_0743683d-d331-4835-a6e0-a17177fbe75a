package com.mcoin.mall.service.business;

import com.mcoin.mall.bean.FookBanner;
import com.mcoin.mall.bean.FookModule;
import com.mcoin.mall.bean.FookPlatformSuggest;
import com.mcoin.mall.bean.FookStoresKeyword;
import com.mcoin.mall.model.*;

import java.util.List;

public interface IndexService {

    List<FookBanner> getIndexPoster();

    List<FookStoresKeyword> getStoreKeyWords();

    List<FookPlatformSuggest> getPopularSearches();

    List<FookPlatformSuggest> getIndexPopularSearches();

    List<ZoneTypeResponse> getActiveZone();

    List<RecommendZoneResponse>  getActiveZoneProduct(RecommendZoneRequest request, String clientType);

    StoreTypeResponse getStoresType();

    ShopTypeResponse getShopType(ShopTypeRequest request, String clientType);

    SnapUpNextResponse getSnapUpNext(SnapUpNextRequest request, String clientType);

    SnapUpResponse getSnapUp(SnapUpRequest request, String clientType);

    SnapUpMoreResponse getSnapUpMore(SnapUpMoreRequest request, String clientType);

    List<FookStoresKeyword> getStoreKeyWordsV2(Boolean harmonySwitch);

    List<FookModule> getIndex();
    
    ShopIntervalResponse getShopInterval();
}
