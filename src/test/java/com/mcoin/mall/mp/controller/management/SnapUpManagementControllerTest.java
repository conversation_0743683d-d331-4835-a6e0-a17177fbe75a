package com.mcoin.mall.mp.controller.management;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.management.ActivityInfoDeleteRequest;
import com.mcoin.mall.mp.model.management.ActivityInfoDeleteResponse;
import com.mcoin.mall.mp.model.management.ActivityInfoSyncRequest;
import com.mcoin.mall.mp.model.management.ActivityInfoSyncResponse;
import com.mcoin.mall.mp.model.management.ActivityTimeToGoodsBuyTimeRequest;
import com.mcoin.mall.mp.model.management.ActivityTimeToGoodsBuyTimeResponse;

@Transactional
class SnapUpManagementControllerTest extends BaseUnitTest {

    @Autowired
    private SnapUpManagementController snapUpManagementController;
    @Test
    void activityTimeToGoodsBuyTime_ShouldSyncTime() {
        // Arrange
        ActivityTimeToGoodsBuyTimeRequest request = new ActivityTimeToGoodsBuyTimeRequest();
        request.setSessionId(1);

        // Act
        Response<ActivityTimeToGoodsBuyTimeResponse> response = snapUpManagementController.activityTimeToGoodsBuyTime(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void deleteActivityInfo_ShouldDeleteActivity() {
        // Arrange
        ActivityInfoDeleteRequest request = new ActivityInfoDeleteRequest();
        request.setSessionId(1);

        // Act
        Response<ActivityInfoDeleteResponse> response = snapUpManagementController.deleteActivityInfo(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void syncActivityInfo_ShouldSyncActivity() {
        // Arrange
        ActivityInfoSyncRequest request = new ActivityInfoSyncRequest();
        request.setSessionId(1);
        request.setName("Test Activity");
        request.setStartTime("2023-01-01 00:00:00");
        request.setEndTime("2023-12-31 23:59:59");

        // Act
        Response<ActivityInfoSyncResponse> response = snapUpManagementController.syncActivityInfo(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }
} 