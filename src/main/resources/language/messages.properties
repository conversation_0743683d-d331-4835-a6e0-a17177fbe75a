message.basic.nodata=æ²æç¸éç¦å©è³æ
message.basic.successful=æä½æå
message.basic.failure=æä½å¤±æ
message.basic.rush_to_buy_time=æ¶è³¼è³:yå¹´:mæ:dæ¥
message.basic.parameter=åæ¸é¯èª¤
message.basic.system_error=ç³»çµ±åºé¯è«è¯ç¹«ç®¡çå¡
message.basic.positioning=æ«ç¡å®ä½
message.basic.unused=æªä½¿ç¨
message.basic.used=å·²ä½¿ç¨
message.basic.expired=å·²éæ
message.basic.has_expired=å·²å¤±æ
message.basic.refunded=éæ¬¾æå
message.basic.refunding=éæ¬¾å¯©æ ¸ä¸­
message.basic.system_error_contact=ç³»çµ±ç°å¸¸ï¼å¦æåé¡è«è´é»æ¾³ééå®¢æ¶æåç±ç·ï¼(853)28727688



message.order.platform=æ­¤åªæ æ«æªæ¼å¹³å°éæ¾
message.order.nodata=æ¾ä¸å°æ¸æ
message.order.no_order=æ¾ä¸å°è¨å®
message.order.order_number=è¨å®æ¸éä¸å¹é
message.order.order_partial_use=ç¶åå¸ç¢¼ä¸ç¬¦åéæ¬¾è¦æ±(é¨åå·²ä½¿ç¨)
message.order.refund=éæ¬¾å¤±æ
message.order.mb=æ­¤ç¢åä¸å¯éæ¬¾
message.order.no_parameter=ç³»çµ±ç°å¸¸ (åæ¸)
message.order.no_product=æ¾ä¸å°åªæ 
message.order.error=ç³»çµ±ç°å¸¸
message.order.number_insufficient=ç¦å©æ¸éä¸è¶³
message.order.overdue=å·²éæ
message.order.not_yet_effective=æ¶è³¼åé¡å°æªçæ
message.order.available_day=æ¬å¸èªè³¼è²·æ¥èµ·{0}å¤©å§å¯ç¨
message.order.available_date=æ¬å¸èªè³¼è²·æ¥èµ·è³{0}å¯ç¨
message.order.for_weekends=æ¬å¸é©ç¨æ¼é±å­æ¥
message.order.not_for_weekends=æ¬å¸ä¸é©ç¨æ¼é±å­æ¥
message.order.applicable_holidays=é©ç¨å¬ç¾åæ
message.order.not_applicable_holidays=ä¸é©ç¨å¬ç¾åæ
message.order.is_refund=<br/>é¾æä½¿ç¨ç¡æä¸¦ä¸å¯éæ¬¾éå
message.order.sales_less=å·²å®{0}ä»½
message.order.sales_many=å·²å®{0}è¬+
message.order.overdue_refund=å·²éæä¸åè¨±éæ¬¾
message.order.repeat_refund=ä¸åè¨±éè¤éæ¬¾
message.order.refund_requested=å·²ç³è«éæ¬¾


message.ordersaveinfo.point_no=æä½¿ç¨çç©åæ¸ä¸ç¸ç­
message.ordersaveinfo.max_number=æ¨é¸æçç¦å©æ¸éå·²è¶éå¯è³¼è²·æå¤§æ¸é
message.ordersaveinfo.product_ran_out=æ­¤ç¦å©æ­£å¨è£è²¨ä¸­
message.ordersaveinfo.max_buy_number=è¶éå¯è³¼è²·æ¸é
message.ordersaveinfo.but_time=æ­¤ç¦å©æªéæ¾è³¼è²·
message.ordersaveinfo.but_time_over=æ­¤ç¦å©ç¼å®æéå·²çµæ
message.ordersaveinfo.product_the_shelves=è©²å¸å·²ç¶ä¸æ¶å¦ï½
message.ordersaveinfo.stock_insufficient=åº«å­ä¸è¶³
message.ordersaveinfo.m_options=mCoiné¸é ä¸å­å¨
message.ordersaveinfo.m_number=mCoinæ¸éä¸è¶³
message.ordersaveinfo.integral_options=ç©åé¸é ä¸å­å¨
message.ordersaveinfo.data_error=ç¶åæåå¨ç¹å¿ï¼è«ç¨å¾åè©¦
message.ordersaveinfo.discount=åªæ æ¹å¼ä¸å­å¨
message.ordersaveinfo.discount_status=åªæ æ¹å¼å·²éé
message.ordersaveinfo.discount_start=æªåªæ æ¹å¼éå§æé
message.ordersaveinfo.discount_end=åªæ æ¹å¼æéå·²çµæ
message.ordersaveinfo.discount_ismpay=è©²åªæ æ¹å¼å¨ç¶åå¹³èºå·²ééï¼è«éæ°å·æ°é é¢
message.ordersaveinfo.miles_name_family=è¼¸å¥æå¡å§æ°
message.ordersaveinfo.miles_name_given=è«è¼¸å¥æå¡åå­
message.ordersaveinfo.miles_member=è«è¼¸å¥æå¡èç¢¼
message.ordersaveinfo.miles_member_repeat=è«åæ¬¡è¼¸å¥æå¡èç¢¼
message.ordersaveinfo.no_miles_member=æå¡èç¢¼é¯èª¤,è«éæ°è¼¸å¥
message.ordersaveinfo.miles_member_verification=æå¡èç¢¼é¯èª¤,è«éæ°è¼¸å¥
message.ordersaveinfo.mcurrency=ç©ååæ¸é¯èª¤
message.ordersaveinfo.miles_milage=äºæ´²è¬éééæ¸åæ¸é¯èª¤
message.ordersaveinfo.miles_milage_number=è³¼è²·æ¸éç°å¸¸
message.ordersaveinfo.no_miles_milage=éç¨åææ¸éç°å¸¸
message.ordersaveinfo.day_mileage=å·²è¶åºå®æ¥åææé«éæ¸ ({0}é)
message.ordersaveinfo.month_mileage=å·²è¶åºç¶æåææé«éæ¸ ({0}é)
message.ordersaveinfo.last_name_no_chinese=æå¡å§æ°ä¸è½å«ææ¸å­
message.ordersaveinfo.first_name_no_chinese=æå¡åå­ä¸è½å«ææ¸å­
#message.ordersaveinfo.first_name_no_chinese=æå¡å§æ°ä¸è½å«ææ¸å­
message.ordersaveinfo.last_name_no_num=æå¡åå­ä¸è½å«ææ¸å­
message.ordersaveinfo.first_name_no_num=
message.ordersaveinfo.last_name_null_no_more_than_2=æå¡å§æ°å«æç©ºæ ¼ä¸è¶2å
message.ordersaveinfo.first_name_no_more_than_2=æå¡åå­å«æç©ºæ ¼ä¸è¶2å
message.ordersaveinfo.last_name_length_40=æå¡å§æ°é·åº¦ä¸è½è¶é40åå­ç¬¦
message.ordersaveinfo.first_name_length_40=æå¡åå­é·åº¦ä¸è½è¶é40åå­ç¬¦
message.ordersaveinfo.tips=æ¯ :n mCoin ç©ååæ:måãäºæ´²è¬ééãéæ¸
message.ordersaveinfo.miles_exchange_failure=éæ¸åæå¤±æ


message.business.platform=æ­¤åªæ æ«æªæ¼å¹³å°éæ¾
message.business.nodata=æ¾ä¸å°æ¸æ
message.business.no_order=æ¾ä¸å°è¨å®
message.business.apply_failed=ç³è«æ¶è³¼å¤±æ
message.business.apply_success=ç³è«æ¶è³¼æå
message.business.no_apply_welfare=éæ¶è³¼ç¦å©
message.business.snapped_up=æ¶è³¼ä¸­
message.business.register_success=æ¶è³¼ç»è¨æå
message.business.register_failed=æ¶è³¼å¤±æ
message.business.snapped_up_success=æ¶è³¼å·²å®æ
message.business.no_snapped_up=ç¡æ¶è³¼ç»è¨è¨é
message.business.collect_count={0} äººé¾æ
message.business.collection_success=å å¥æ¶è
message.business.collection_cancel=å·²åæ¶æ¶è
message.business.product_count={0} åèæ¸
message.business.snap_up_start=é¦¬ä¸æ¶
message.business.snap_up_end=å·²æ¶å®
message.business.snap_up_not=æªéå§
message.business.expiring_soon=å¿«éæ
message.business.start=éå§=
message.business.end=çµæ=

message.merchant.nodata=ç¡ç¸éçµæ
message.merchant.business=åæ¶å·²åçµ


message.mpay.point_deduction=ç©åæ£é¤å¤±æ
message.mpay.Insufficient_points=ç¶åç©åé¤é¡ä¸è¶³
message.mpay.user_exist=ç¨æ¶ä¸å­å¨
message.mpay.order_repeat=è©²è¨å®å·²ç¶æ¯ä»æå,è«å¿éè¤ä¸å®
message.mpay.number_incorrect=ç¨æ¶ç·¨èæèª¤
message.mpay.simulation=ç°å¨ç§¯åæ¥å£è¿åäºæ¨¡ææ£æ¬¾æåï¼ååºè¿åå¤±è´¥æåµ
message.mpay.deduction=æ¨¡ææ£æ¬¾æå,ååºè¿åå¤±è´¥æåµ
message.mpay.parameter=è«æ±åæ¸ä¸åæ³
message.mpay.system_business=ç³»çµ±æ¥­åç°å¸¸
message.mpay.profession=æ¥­åè¶æ
message.mpay.later=ç¨å¾éè©¦
message.mpay.request=è«æ±å¤±æ
message.mpay.check=é©ç°½
message.mpay.system_busy=ç³»çµ±ç¹å¿
message.mpay.system_bustling=ç³»ç»ç¹å¿
message.mpay.login_out=ç»éè¸¢åº
message.mpay.merchant=è¯¥åæ·æ è®¿é®æ­¤åè½æé



message.Macaupay.check_order=è¨å®ä¸å­å¨,è«æ ¸å°
message.Macaupay.pay=æ¯ä»æå
message.Macaupay.integral_insufficient=ç±æ¼ç©åä¸è¶³,è³¼è²·ç¶åç¦å©å¸å¤±æ,ç¦å©å¸éé¡å°èªåéå
message.Macaupay.mpay_coupons_fail=ç¶²çµ¡åé¡ï¼ç©ååéé¡å°èªåéå
message.Macaupay.Invalid_order=è¨å®å·²å¤±æ,è«éæ°ä¸å®



message.orderRepository.verification_code_data=æ ¸é·ç¢¼æ¸æå·²å­å¨

message.category.pickpocket_welfare=ç±éåªæ 
message.category.new_welfare=æ°åªæ 
message.category.cash_welfare=ç¾éå¸
message.category.one_welfare=1èåªæ 
message.category.integral_welfare=ç©åæé 
message.category.m_welfare=mCoinç¾éå¸
message.redeem.verification_password=æ ¸é·å¯ç¢¼ä¸æ­£ç¢º
message.redeem.verification_failure=æ ¸é·å¤±æ
message.redeem.the_code_does=è©²å¸ç¢¼ä¸å­å¨
message.redeem.code_verification=è©²å¸ç¢¼å·²æ ¸é·
message.redeem.error_admin=æ¸æåºé¯ï¼è«è¯ç¹«ç®¡çå¡
message.redeem.system_error=ç³»çµ±åºé¯ï¼è«è¯ç¹«ç®¡çå¡
message.redeem.product_code_overdue=ç¦å©å·²éæ
message.redeem.time_available=æªå°å¯ä½¿ç¨æé
message.redeem.time_notuse=ç¶åæéä¸å¯ç¨
message.OrderSettlementRepository.add_verification_failure=æ·»å æ ¸é·å¤±æï¼æ ¸é·ç¢¼ä¸è½åæä½¿ç¨
message.redeem.refunded=è©²å¸ç¢¼å·²éæ¬¾
message.redeem.status_invalid=è©²å¸ç¢¼å·²å¤±æ
message.address.phone_verification=è«è¼¸å¥æ­£ç¢ºçææ©èç¢¼\

message.address.limit=æ¨çæ¶è²¨å°åå·²éä¸éï¼è«æ¸çåç¹¼çºæ·»å æ°å°å

message.show.snapup.session.date_txt_now=éæ¶ä¸­
message.show.snapup.session.date_txt_next=å³å°éæ¶
message.show.snapup.session.date_txt_tomorrow=ææ¥éæ¶
message.OrderSettlementRepository.cancel_verification_failure=æ¤é·æ ¸é·å¤±æ
message.redeem.code_stop_use=å¸è¦åä¸å¯ç¨

message.activity.end=ç§æ®ºå·²çµæ
