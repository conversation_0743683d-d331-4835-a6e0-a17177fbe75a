<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookClauseConfirmDao">
    <select id="selectClauseConfirm" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fook_clause_confirm
        where userid = #{userid,jdbcType=INTEGER}
        and clauseid = #{clauseid,jdbcType=INTEGER}
        <if test="status != null">
            AND status = #{status,jdbcType=INTEGER}
        </if>
        order by updated_at desc limit 1
    </select>
</mapper>