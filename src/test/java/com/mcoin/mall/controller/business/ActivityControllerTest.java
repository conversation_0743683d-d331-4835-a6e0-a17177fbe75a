package com.mcoin.mall.controller.business;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.*;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

/**
 * 集成测试 ActivityController
 * 使用真实服务和数据库，测试活动相关功能
 */
class ActivityControllerTest extends BaseUnitTest {

    @Resource
    private ActivityController activityController;

    @BeforeEach
    void setUp() {
        when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
    }

    @Test
    void testDayGainList() {
        // 创建请求对象
        DaygainListRequest request = new DaygainListRequest();
        request.setId(14); // 设置活动ID
        request.setPage(1);
        
        // 直接调用控制器方法
        Response<DaygainListResponse> response = activityController.dayGainList(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testActiveMerchantList() {
        // 创建请求对象
        ActiveMerchantListRequest request = new ActiveMerchantListRequest();
        request.setActive_id(1); // 设置活动ID
        request.setPage(1);
        
        // 直接调用控制器方法
        Response<ActiveMerchantListResponse> response = activityController.activeMerchantList(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 