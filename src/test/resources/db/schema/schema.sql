use fooku;


-- fooku_prd_20231123.data_types definition

CREATE TABLE `data_types` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `name` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '關聯數據庫表名',
  `slug` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `display_name_singular` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `display_name_plural` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '顯示名稱',
  `icon` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '圖標',
  `model_name` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '關聯的Model',
  `policy_name` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '關聯的Model授權',
  `controller` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '關聯的controller',
  `description` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '描述',
  `generate_permissions` tinyint(1) NOT NULL DEFAULT '0' COMMENT '權限控制',
  `server_side` tinyint(4) NOT NULL DEFAULT '0' COMMENT '搜索框控制',
  `details` text COLLATE utf8_unicode_ci COMMENT '參數配置',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `data_types_slug_unique` (`slug`) USING BTREE,
  UNIQUE KEY `data_types_name_unique` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=194 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='管理后臺記錄的數據表';


-- fooku_prd_20231123.fook_a_fee_type definition

CREATE TABLE `fook_a_fee_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT NULL COMMENT '收費類型名稱',
  `rate` decimal(4,2) unsigned DEFAULT '0.00' COMMENT '費率，3.5%',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_active_merchant definition

CREATE TABLE `fook_active_merchant` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `enable` tinyint(4) DEFAULT '0' COMMENT '(0:導入excel,1:導入excel,2:上傳oss)',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态(0:凍結,1:開啟)',
  `count` int(11) DEFAULT NULL COMMENT '數量',
  `title` varchar(255) DEFAULT NULL COMMENT '列表名稱',
  `title_en` varchar(255) DEFAULT NULL COMMENT '列表名稱(英文)',
  `name` varchar(255) DEFAULT NULL COMMENT 'excel名稱',
  `file_url` varchar(255) DEFAULT NULL COMMENT '本地路徑',
  `file` varchar(255) DEFAULT NULL COMMENT '上傳的excel oss文件路徑',
  `url` varchar(255) DEFAULT NULL COMMENT 'url路徑',
  `lucky_draw` tinyint(4) DEFAULT '0' COMMENT '抽獎機會(1:是,0:否)',
  `voucher` tinyint(4) DEFAULT '0' COMMENT '電子消費券(1:是,0:否)',
  `event` varchar(255) DEFAULT NULL COMMENT '商戶活動內容',
  `event_en` varchar(255) DEFAULT NULL COMMENT '商戶活動內容(英文)',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='活動商戶excel表';


-- fooku_prd_20231123.fook_active_merchant_data definition

CREATE TABLE `fook_active_merchant_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `active_id` int(11) DEFAULT NULL COMMENT '活動商戶表id',
  `name` varchar(255) DEFAULT NULL COMMENT '商戶名稱',
  `name_en` varchar(255) DEFAULT NULL COMMENT '商戶名稱（英文）',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `address_en` varchar(255) DEFAULT NULL COMMENT '地址（英文）',
  `lucky_draw` tinyint(4) DEFAULT '1' COMMENT '抽獎機會（1：是，0：否）',
  `voucher` tinyint(4) DEFAULT '1' COMMENT '電子消費券（1：是，0：否）',
  `event` varchar(255) DEFAULT NULL COMMENT '商戶活動內容',
  `event_en` varchar(255) DEFAULT NULL COMMENT '商戶活動內容（英文）',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `address` (`address`,`address_en`) USING BTREE,
  KEY `active_id` (`active_id`) USING BTREE,
  KEY `name` (`name`,`name_en`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7132639 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_active_zone definition

CREATE TABLE `fook_active_zone` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '專區id',
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '專區名稱',
  `name_en` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '專區名稱-英文',
  `status` tinyint(1) DEFAULT '1' COMMENT '類型：0、 下架  1、上架',
  `type` tinyint(1) DEFAULT '1' COMMENT '類型：1、 mcoin  2、h5',
  `sort` tinyint(1) DEFAULT '0' COMMENT '排序',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='專區信息表';


-- fooku_prd_20231123.fook_active_zone_product_translations definition

CREATE TABLE `fook_active_zone_product_translations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `active_zone_id` int(11) DEFAULT NULL COMMENT '專區id',
  `business_product_id` int(11) DEFAULT NULL COMMENT '產品id',
  `order_by` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `fook_active_zone_product_translations_active_zone_id` (`active_zone_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25725 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='專區福利關聯表';


-- fooku_prd_20231123.fook_advertisement definition

CREATE TABLE `fook_advertisement` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵ID',
  `type` varchar(50) DEFAULT NULL COMMENT '類型(1類別 2地區 3五十大 4新加盟 5跟住佢玩 6個人中心推廣 7搶福)',
  `title` varchar(255) DEFAULT ' ' COMMENT '標題',
  `img` varchar(255) DEFAULT ' ' COMMENT '圖片',
  `url` varchar(255) DEFAULT ' ' COMMENT '跳轉鏈接',
  `order` mediumint(8) unsigned DEFAULT NULL COMMENT '排序',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  `enable` int(11) DEFAULT '1' COMMENT '是否显示(0、不显示 1、显示)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='廣告表';


-- fooku_prd_20231123.fook_allow_refund_log definition

CREATE TABLE `fook_allow_refund_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵ID',
  `order_id` int(11) unsigned DEFAULT NULL COMMENT '訂單表ID',
  `content` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '操作',
  `user_id` int(11) DEFAULT NULL COMMENT '操作者ID',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '操作時間',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=810 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='修改訂單允許退款記錄表';


-- fooku_prd_20231123.fook_announcement definition

CREATE TABLE `fook_announcement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(255) DEFAULT NULL COMMENT '公告標題',
  `content` longtext COMMENT '公告內容',
  `user_id` int(11) DEFAULT NULL COMMENT '管理員id',
  `enable` tinyint(4) DEFAULT NULL COMMENT '是否有效',
  `link` varchar(255) DEFAULT NULL COMMENT '原文鏈接地址',
  `sort` tinyint(4) DEFAULT '0' COMMENT '排序 默認0 0不參與排序',
  `created_at` datetime DEFAULT NULL COMMENT '修改時間',
  `updated_at` datetime DEFAULT NULL COMMENT '創建時間',
  `source` varchar(255) DEFAULT NULL COMMENT '來源',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1053 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='公告表，目前是同步澳門新聞局的信息';


-- fooku_prd_20231123.fook_app_log definition

CREATE TABLE `fook_app_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) DEFAULT NULL COMMENT '版本号',
  `os` varchar(255) DEFAULT NULL COMMENT '操作系统',
  `ip` varchar(255) DEFAULT NULL COMMENT '用户ip',
  `uid` int(11) DEFAULT NULL COMMENT '用户id',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `token` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=39142 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄app每次返回token的log';


-- fooku_prd_20231123.fook_app_push definition

CREATE TABLE `fook_app_push` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `title` varchar(150) DEFAULT '' COMMENT '標題',
  `content` text COMMENT '推送內容',
  `target_type` tinyint(1) DEFAULT '0' COMMENT '目標平台(1 用戶版，2 商家版)',
  `platform_type` varchar(10) DEFAULT '' COMMENT '平台類型(0 ios，1 Android)',
  `push_type` tinyint(4) DEFAULT '1' COMMENT '推送類型(0 系統功能類，1營銷活動類，2福利活動類)',
  `url` varchar(255) DEFAULT '' COMMENT '鏈接地址(推送類型為營銷活動才有)',
  `product_id` int(11) unsigned DEFAULT NULL COMMENT '福利id',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效(0 無效，1有效)',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='推送App信息記錄表';


-- fooku_prd_20231123.fook_app_push_list definition

CREATE TABLE `fook_app_push_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `token` varchar(255) DEFAULT NULL COMMENT '用户推送token',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '推送时间',
  `status` varchar(255) DEFAULT NULL COMMENT '推送状态',
  `push_id` int(11) DEFAULT NULL COMMENT '推送表id',
  `os` varchar(255) DEFAULT NULL COMMENT '设备',
  `type` int(11) DEFAULT NULL COMMENT '版本:1用户版,2商家版',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='推送App信息消息隊列表';


-- fooku_prd_20231123.fook_areas definition

CREATE TABLE `fook_areas` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '地區ID',
  `area_name` varchar(150) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '地區名稱',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效 1有效 0無效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='地區表';


-- fooku_prd_20231123.fook_article_stores definition

CREATE TABLE `fook_article_stores` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `article_id` int(11) DEFAULT NULL COMMENT '门店ID',
  `stores_id` int(11) DEFAULT NULL COMMENT '玩乐日志id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `stores_id` (`stores_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=190 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='玩樂日志和門店關聯的表';


-- fooku_prd_20231123.fook_article_topic definition

CREATE TABLE `fook_article_topic` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `topic` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '主題',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `Icon` varchar(1000) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图标',
  `type` int(11) DEFAULT NULL COMMENT '类型',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效',
  `areas_id` int(11) DEFAULT '1' COMMENT '地區',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `areas_id` (`areas_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='玩樂日志主題表';


-- fooku_prd_20231123.fook_articles definition

CREATE TABLE `fook_articles` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT '作者',
  `title` varchar(100) COLLATE utf8_unicode_ci NOT NULL COMMENT '標題',
  `area` text COLLATE utf8_unicode_ci COMMENT '地区',
  `content` longtext CHARACTER SET utf8mb4 COMMENT '内容',
  `cover` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '封面',
  `topic_id` int(11) DEFAULT NULL COMMENT '关联主题id',
  `meta_keywords` text COLLATE utf8_unicode_ci COMMENT '关键字标签',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '發佈時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `view_number` int(11) DEFAULT NULL COMMENT '浏览量',
  `pre_content` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '内容预览',
  `enable` int(2) DEFAULT '1' COMMENT '是否有效',
  `is_index` int(2) DEFAULT '0' COMMENT '是否首页显示(0、不顯示 1、顯示)',
  `fb_img` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '臉書提取圖片',
  `sort` int(2) DEFAULT NULL COMMENT '排序',
  `icon` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图标',
  `stores_id` int(11) DEFAULT NULL COMMENT '关联门店链接ID',
  `authorid` int(11) DEFAULT NULL COMMENT '作者',
  `content_preview` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '預覽',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `topic_id` (`topic_id`) USING BTREE,
  KEY `authorid` (`authorid`) USING BTREE,
  KEY `stores_id` (`stores_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=310 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='玩樂日志列表';


-- fooku_prd_20231123.fook_asia_miles_docking_log definition

CREATE TABLE `fook_asia_miles_docking_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `create_txt_num` int(10) unsigned DEFAULT NULL COMMENT '生成txt文本條數',
  `create_txt_filename` varchar(150) DEFAULT NULL COMMENT '生成txt文檔名稱',
  `create_txt_time` datetime DEFAULT NULL COMMENT '生成txt文本時間',
  `encrypt_filename` varchar(150) DEFAULT NULL COMMENT '上傳亞洲萬裡通站點加密文件名稱',
  `encrypt_time` datetime DEFAULT NULL COMMENT '加密時間',
  `upload_time` datetime DEFAULT NULL COMMENT '上傳時間',
  `upload_oss_file_url` varchar(255) DEFAULT NULL COMMENT '生成文本上傳oss地址',
  `download_filename_hb` varchar(150) DEFAULT NULL COMMENT '下載文件名稱',
  `download_filename_hb_time` datetime DEFAULT NULL COMMENT '下載源文件時間',
  `download_filename_hb_oss` varchar(255) DEFAULT NULL COMMENT '下載解密文件oss地址',
  `download_filename_hb_oss_time` datetime DEFAULT NULL COMMENT '下載解密文件上傳oss時間',
  `download_filename_hb_num` int(11) DEFAULT NULL COMMENT '下載數量',
  `hb_status` tinyint(1) DEFAULT NULL COMMENT '狀態(1下載成功,2解密成功,3上傳成功,4處理完成)',
  `download_filename_mis` varchar(150) DEFAULT NULL COMMENT '下載文件名稱',
  `download_filename_mis_time` datetime DEFAULT NULL COMMENT '下載源文件時間',
  `download_filename_mis_oss` varchar(255) DEFAULT NULL COMMENT '下載解密文件oss地址',
  `download_filename_mis_oss_time` datetime DEFAULT NULL COMMENT '下載解密文件上傳oss時間',
  `download_filename_mis_num` int(11) DEFAULT NULL COMMENT '下載數量',
  `mis_status` tinyint(1) DEFAULT NULL COMMENT '狀態(1下載成功,2解密成功,3上傳成功,4處理完成)',
  `download_filename_rpt` varchar(150) DEFAULT NULL COMMENT '下載文件名稱',
  `download_filename_rpt_time` datetime DEFAULT NULL COMMENT '下載源文件時間',
  `download_filename_rpt_oss` varchar(255) DEFAULT NULL COMMENT '下載解密文件oss地址',
  `download_filename_rpt_oss_time` datetime DEFAULT NULL COMMENT '下載解密文件上傳oss時間',
  `download_filename_rpt_num` int(11) DEFAULT NULL COMMENT '下載數量',
  `rpt_status` tinyint(1) DEFAULT NULL COMMENT '狀態(1下載成功,2解密成功,3上傳成功,4處理完成)',
  `status` tinyint(1) DEFAULT '0' COMMENT '狀態（0生成txt文檔,1生成文檔上傳oss,2文檔加密成功,3已上傳亞洲萬里通站點）',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `update_at` datetime DEFAULT NULL COMMENT '更新時間',
  `start_time` datetime DEFAULT NULL COMMENT '兌換週期開始時間',
  `end_time` datetime DEFAULT NULL COMMENT '兌換週期結束時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1322 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='亞洲萬裡通對接記錄log';


-- fooku_prd_20231123.fook_bank definition

CREATE TABLE `fook_bank` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) NOT NULL COMMENT '銀行名稱',
  `english_name` varchar(255) DEFAULT NULL COMMENT '英文名稱',
  `enable` int(2) DEFAULT '1' COMMENT '狀態：1：正常，0：凍結',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='结算银行表';


-- fooku_prd_20231123.fook_banner definition

CREATE TABLE `fook_banner` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '門店類別ID',
  `name` varchar(255) DEFAULT '' COMMENT '描述',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `enable` tinyint(1) DEFAULT '1' COMMENT '1有效 0無效',
  `icon` varchar(255) DEFAULT ' ' COMMENT '圖表',
  `zip_icon` varchar(255) DEFAULT NULL COMMENT '壓縮圖片',
  `icon_en` varchar(255) DEFAULT NULL COMMENT '英文圖片',
  `zip_icon_en` varchar(255) DEFAULT NULL COMMENT '壓縮英文圖片',
  `if_index_show` tinyint(1) DEFAULT '1' COMMENT '是否首頁顯示 1顯示 0不顯示',
  `if_hot` tinyint(1) DEFAULT '1' COMMENT '是否熱門 1熱門 0不熱門',
  `code` varchar(255) DEFAULT ' ' COMMENT '編碼',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  `src` varchar(255) DEFAULT NULL COMMENT '跳轉地址',
  `promote_src` varchar(255) DEFAULT NULL COMMENT '推廣跳轉鏈接',
  `uuid` int(11) DEFAULT NULL COMMENT '門店/商家id',
  `type` tinyint(2) DEFAULT '0' COMMENT '類型：0無、1優惠券、2門店',
  `location` tinyint(4) DEFAULT '1' COMMENT '廣告位置：1顶部廣告、 2中部廣告',
  `is_broadcast` tinyint(4) DEFAULT '0' COMMENT '是否設置時間段投播,0否，1是',
  `broadcast_start_time` datetime DEFAULT NULL COMMENT '投播開始時間',
  `broadcast_end_time` datetime DEFAULT NULL COMMENT '投播結束時間',
  `banner_style` int(11) DEFAULT NULL COMMENT '所属模块',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_api` (`enable`,`location`,`icon`,`if_index_show`) USING BTREE,
  KEY `idx_banner_style` (`banner_style`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=179 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='推介banner表';


-- fooku_prd_20231123.fook_booking definition

CREATE TABLE `fook_booking` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '預約表Id',
  `business_id` int(11) DEFAULT NULL COMMENT '商家Id(暫時先記錄)',
  `stores_id` int(11) DEFAULT NULL COMMENT '門店Id',
  `today` datetime DEFAULT NULL COMMENT '當天日期',
  `b_time` varchar(60) DEFAULT '' COMMENT '預約開始時間',
  `e_time` varchar(60) DEFAULT '' COMMENT '預約結束時間',
  `deposit` decimal(10,2) DEFAULT NULL COMMENT '按金',
  `has_discounted` varchar(50) DEFAULT NULL COMMENT '已預付訂金折扣',
  `discounted` varchar(50) DEFAULT ' ' COMMENT '冇預付訂金折扣',
  `stock` int(11) DEFAULT NULL COMMENT '庫存',
  `his_stock` tinyint(4) DEFAULT NULL COMMENT '歷史總庫存',
  `peoples_max` int(11) DEFAULT NULL COMMENT '預約最多人數',
  `before_minutes` int(11) DEFAULT NULL COMMENT '開始前N分鐘內不能取消',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效（1有效，2无效）',
  `create_at` datetime DEFAULT NULL COMMENT '創建時間',
  `update_at` datetime DEFAULT NULL COMMENT '更改時間',
  `expand_a` varchar(255) DEFAULT ' ' COMMENT '拓展字段A',
  `expand_b` varchar(255) DEFAULT ' ' COMMENT '拓展字段B',
  `expand_c` varchar(255) DEFAULT ' ' COMMENT '拓展字段C',
  `expand_d` varchar(255) DEFAULT ' ' COMMENT '拓展字段D',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_id` (`business_id`) USING BTREE,
  KEY `stores_id` (`stores_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='預約表';


-- fooku_prd_20231123.fook_booking_template definition

CREATE TABLE `fook_booking_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '預約模板表Id',
  `e_num` varchar(255) DEFAULT ' ' COMMENT '模板類型（1週一，2週二，3週三，4週四，5週五，6週六，7週日，8公眾假期）',
  `business_id` int(11) DEFAULT NULL COMMENT '商家Id(暫時先記錄)',
  `stores_id` int(11) DEFAULT NULL COMMENT '門店Id',
  `b_time` varchar(100) DEFAULT ' ' COMMENT '入座時間',
  `e_time` varchar(100) DEFAULT ' ' COMMENT '預約結束時間(廢棄)',
  `deposit` decimal(10,2) DEFAULT NULL COMMENT '按金',
  `has_discounted` varchar(50) DEFAULT ' ' COMMENT '已預付訂金折扣',
  `discounted` varchar(50) DEFAULT ' ' COMMENT '不付按金折扣',
  `stock` mediumint(8) DEFAULT NULL COMMENT '庫存',
  `peoples_max` mediumint(8) DEFAULT NULL COMMENT '預約最多人數',
  `before_minutes` varchar(50) DEFAULT ' ' COMMENT '開始前N分鐘內不能取消',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效（1有效,2無效）',
  `create_at` datetime DEFAULT NULL COMMENT '創建時間',
  `update_at` datetime DEFAULT NULL COMMENT '更新時間',
  `expand_a` varchar(255) DEFAULT ' ' COMMENT '拓展字段A',
  `expand_b` varchar(255) DEFAULT ' ' COMMENT '拓展字段B',
  `expand_c` varchar(255) DEFAULT ' ' COMMENT '拓展字段C',
  `expand_d` varchar(255) DEFAULT ' ' COMMENT '拓展字段D',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_id` (`business_id`) USING BTREE,
  KEY `stores_id` (`stores_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='預約模板表';


-- fooku_prd_20231123.fook_business definition

CREATE TABLE `fook_business` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '商家id',
  `areas_id` tinyint(1) NOT NULL DEFAULT '1' COMMENT '地區id',
  `code` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '商家編號',
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '商家名稱',
  `company_name` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '公司名稱',
  `account` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '登入賬號',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '狀態(0凍結 1正常)',
  `tel` varchar(30) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '商家電話',
  `address` varchar(100) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '商家地址',
  `logo` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '商家Logo',
  `js_account` varchar(30) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '結算銀行賬戶',
  `js_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '結算賬戶姓名',
  `js_bank` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '結算銀行',
  `head_name` varchar(30) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '負責人姓名',
  `head_tel` varchar(30) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '負責人聯繫電話',
  `head_email` varchar(1000) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '負責人郵箱',
  `sex` tinyint(1) DEFAULT NULL COMMENT '負責人性別(1男 2女)',
  `enbale` tinyint(1) DEFAULT '1' COMMENT '是否有效（1有效，2無效）',
  `login_pass` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '登錄密碼',
  `macau_pass_merchant_number` varchar(200) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '澳門通商戶號',
  `macau_pass_terminal_number` varchar(200) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '澳門通終端號',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  `uid` int(11) DEFAULT NULL,
  `if_allow_connect` tinyint(1) DEFAULT '0' COMMENT '是否核銷關聯商家',
  `oa_code_id` int(11) DEFAULT NULL COMMENT '關聯fook_oa_code表id',
  `business_number` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '公司商業編號（支持英文,數字及符號10位）',
  `file_number` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '開業M1營業檔案編號（10位數字）',
  `taxpayer_number` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'M8納稅人編號（10位數字）',
  `istemporary` tinyint(1) DEFAULT NULL,
  `government_number` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '政府登記編號（小販專用）',
  `system_type` tinyint(4) DEFAULT '1' COMMENT '商戶歸屬哪個系統(1.mCoin,2.會員卡,3亞洲萬裡通商戶)',
  `member_business_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '會員卡商戶id',
  `member_type` tinyint(4) DEFAULT NULL COMMENT '會員卡商家類型：1、商家，2商圈、3大賣場',
  `optional_type` tinyint(4) DEFAULT '0' COMMENT '選填發報表類型：0不發，1每天發送，2每7天發送，3每月發送',
  `is_old_redeem` tinyint(4) DEFAULT '1' COMMENT '是否允許舊核銷接口,0禁止，1允許，默認1',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_systemtype` (`system_type`) USING BTREE,
  KEY `areas_id` (`areas_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9864 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='商家信息表';


-- fooku_prd_20231123.fook_business_20230719 definition

CREATE TABLE `fook_business_20230719` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '商家id',
  `areas_id` tinyint(1) NOT NULL DEFAULT '1' COMMENT '地區id',
  `code` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '商家編號',
  `name` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '商家名稱',
  `company_name` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '公司名稱',
  `account` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '登入賬號',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '狀態(0凍結 1正常)',
  `tel` varchar(30) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '商家電話',
  `address` varchar(100) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '商家地址',
  `logo` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '商家Logo',
  `js_account` varchar(30) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '結算銀行賬戶',
  `js_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '結算賬戶姓名',
  `js_bank` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '結算銀行',
  `head_name` varchar(30) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '負責人姓名',
  `head_tel` varchar(30) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '負責人聯繫電話',
  `head_email` varchar(1000) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '負責人郵箱',
  `sex` tinyint(1) DEFAULT NULL COMMENT '負責人性別(1男 2女)',
  `enbale` tinyint(1) DEFAULT '1' COMMENT '是否有效（1有效，2無效）',
  `login_pass` varchar(50) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '登錄密碼',
  `macau_pass_merchant_number` varchar(200) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '澳門通商戶號',
  `macau_pass_terminal_number` varchar(200) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '澳門通終端號',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  `uid` int(11) DEFAULT NULL,
  `if_allow_connect` tinyint(1) DEFAULT '0' COMMENT '是否核銷關聯商家',
  `oa_code_id` int(11) DEFAULT NULL COMMENT '關聯fook_oa_code表id',
  `business_number` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '公司商業編號（支持英文,數字及符號10位）',
  `file_number` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '開業M1營業檔案編號（10位數字）',
  `taxpayer_number` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'M8納稅人編號（10位數字）',
  `istemporary` tinyint(1) DEFAULT NULL,
  `government_number` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '政府登記編號（小販專用）',
  `system_type` tinyint(4) DEFAULT '1' COMMENT '商戶歸屬哪個系統(1.mCoin,2.會員卡,3亞洲萬裡通商戶)',
  `member_business_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '會員卡商戶id',
  `member_type` tinyint(4) DEFAULT NULL COMMENT '會員卡商家類型：1、商家，2商圈、3大賣場',
  `optional_type` tinyint(4) DEFAULT '0' COMMENT '選填發報表類型：0不發，1每天發送，2每7天發送，3每月發送',
  `is_old_redeem` tinyint(4) DEFAULT '1' COMMENT '是否允許舊核銷接口,0禁止，1允許，默認1',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_systemtype` (`system_type`) USING BTREE,
  KEY `areas_id` (`areas_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8590 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='商家信息表';


-- fooku_prd_20231123.fook_business_categories definition

CREATE TABLE `fook_business_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '类目ID',
  `parent_id` int(11) DEFAULT NULL COMMENT '父类目ID，如果是一级类目，那么他的parent_id就等于0',
  `category_name` varchar(100) NOT NULL COMMENT '类目名称',
  `sort` int(11) DEFAULT NULL COMMENT '排序值',
  `is_leaf` tinyint(4) NOT NULL COMMENT '是否为叶子类,没有子类目意味着为叶子类目',
  `enable` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态，1：正常 2：删除',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '修改者',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE,
  KEY `idx_category_name` (`category_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1217 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='福利多级类目表';


-- fooku_prd_20231123.fook_business_information definition

CREATE TABLE `fook_business_information` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '門店商圈ID',
  `area_id` smallint(4) DEFAULT NULL COMMENT '地區ID',
  `pid` int(11) DEFAULT NULL COMMENT '父級ID',
  `type` varchar(100) DEFAULT ' ' COMMENT '類型',
  `name` varchar(255) DEFAULT NULL COMMENT '名稱',
  `name_zh` varchar(255) DEFAULT NULL COMMENT '名稱（簡體）',
  `name_en` varchar(255) DEFAULT NULL COMMENT '名稱(英文)',
  `sort` int(11) DEFAULT NULL COMMENT '排序(0首頁不顯示，其他首頁顯示并排序)',
  `icon` varchar(255) DEFAULT ' ' COMMENT '圖標',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效(0無效 1有效)',
  `if_index_show` tinyint(1) DEFAULT '1' COMMENT '是否首頁展示(0不展示 1展示)',
  `if_hot` tinyint(1) DEFAULT '1' COMMENT '是否熱門(0 不熱門 1熱門)',
  `code` varchar(150) DEFAULT ' ' COMMENT '編碼',
  `longitude` varchar(255) DEFAULT ' ' COMMENT '經度',
  `dimension` varchar(255) DEFAULT ' ' COMMENT '維度',
  `google_longitude` varchar(255) DEFAULT ' ' COMMENT '谷歌經度',
  `google_dimension` varchar(255) DEFAULT ' ' COMMENT '谷歌維度',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `area_id` (`area_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商圈信息表';


-- fooku_prd_20231123.fook_business_msg definition

CREATE TABLE `fook_business_msg` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL COMMENT '消息分類（1預約，2福利，3後台）',
  `message` varchar(255) DEFAULT NULL COMMENT '消息',
  `business_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `is_read` int(11) DEFAULT NULL COMMENT '是否已讀(1是0否)',
  `url` varchar(255) DEFAULT NULL COMMENT '鏈接',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `read_time` datetime DEFAULT NULL COMMENT '查看时间',
  `enable` int(11) DEFAULT NULL COMMENT 'enable(是否有效 0无效1有效)',
  `store_id` int(11) DEFAULT NULL COMMENT '门店ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_id` (`business_id`) USING BTREE,
  KEY `idx_url_business_id_type` (`url`,`business_id`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2314125 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商家消息';


-- fooku_prd_20231123.fook_business_msg_20230926 definition

CREATE TABLE `fook_business_msg_20230926` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL COMMENT '消息分類（1預約，2福利，3後台）',
  `message` varchar(255) DEFAULT NULL COMMENT '消息',
  `business_id` int(11) DEFAULT NULL COMMENT '商家ID',
  `is_read` int(11) DEFAULT NULL COMMENT '是否已讀(1是0否)',
  `url` varchar(255) DEFAULT NULL COMMENT '鏈接',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `read_time` datetime DEFAULT NULL COMMENT '查看时间',
  `enable` int(11) DEFAULT NULL COMMENT 'enable(是否有效 0无效1有效)',
  `store_id` int(11) DEFAULT NULL COMMENT '门店ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_id` (`business_id`) USING BTREE,
  KEY `idx_url_business_id_type` (`url`,`business_id`,`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2168322 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商家消息';


-- fooku_prd_20231123.fook_business_product definition

CREATE TABLE `fook_business_product` (
                                         `id` int NOT NULL AUTO_INCREMENT COMMENT '產品Id',
                                         `areaid` int DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
                                         `type` int DEFAULT NULL COMMENT '產品類型（對應fook_business_productcategory表 1、撚手小菜，2、新菜式, 3、現金券,4、1蚊福利,5、積分換領,6、M 幣現金券）',
                                         `businessid` int DEFAULT NULL COMMENT '商家Id',
                                         `img` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '福利圖片',
                                         `zip_img` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '壓縮後的福利圖片',
                                         `imgs` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci COMMENT '福利圖片',
                                         `zip_imgs` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '壓縮後的產品圖片',
                                         `title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '產品標題',
                                         `desc` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '產品描述',
                                         `price` decimal(8,2) DEFAULT NULL COMMENT '售出價格',
                                         `retail_price` decimal(8,2) DEFAULT NULL COMMENT '門市價',
                                         `point_ratio` int unsigned DEFAULT '300' COMMENT '福利积分比例',
                                         `stock` int DEFAULT NULL COMMENT '庫存',
                                         `history_stock` int DEFAULT '0' COMMENT '历史总库存',
                                         `sales` int unsigned DEFAULT '0' COMMENT '銷量',
                                         `actual_sales` int unsigned DEFAULT '0' COMMENT '實際銷量',
                                         `limited_number` int DEFAULT NULL COMMENT '限購數量',
                                         `is_allow_refund` int DEFAULT NULL COMMENT '是否允許退款（1、允許，0、不允許）',
                                         `details` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '圖文詳情',
                                         `tnc` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '細則',
                                         `score` decimal(8,2) DEFAULT NULL COMMENT '評分',
                                         `comment_number` int DEFAULT '0' COMMENT '總評價人數',
                                         `shelf_status` tinyint(1) DEFAULT '1' COMMENT '狀態（1、上架，2、下架）',
                                         `view_number` int DEFAULT '0' COMMENT '瀏覽量',
                                         `buy_start_time` datetime DEFAULT NULL COMMENT '可購買開始日期',
                                         `buy_end_time` datetime DEFAULT NULL COMMENT '可購買結束日期',
                                         `vaild_mode` int DEFAULT NULL COMMENT '有效期方式(1时间段2购买起多少天内)',
                                         `vaild_start_time` datetime DEFAULT NULL COMMENT '使用有效期開始日期',
                                         `vaild_end_time` datetime DEFAULT NULL COMMENT '使用有效期結束日期',
                                         `day_number` int DEFAULT NULL COMMENT '有效天数',
                                         `is_weekend` int DEFAULT NULL COMMENT '周六日是否可用（1可用0不可用）',
                                         `no_useday` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '不可用日期',
                                         `use_start_time` datetime DEFAULT NULL COMMENT '可使用开始时间',
                                         `use_end_time` datetime DEFAULT NULL COMMENT '可使用结束时间',
                                         `is_vacation` int DEFAULT NULL COMMENT '是否假期可用(1可用0不可用)',
                                         `fee_rate` decimal(8,2) DEFAULT NULL COMMENT '佣金',
                                         `created_at` datetime DEFAULT NULL COMMENT '創建時間',
                                         `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
                                         `enable` int DEFAULT '1' COMMENT '是否有效（1、有效，0、無效  ）',
                                         `is_hot` int DEFAULT NULL COMMENT '是否熱門（0 否, 1 是）',
                                         `momecoins_option` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT 'M幣選項',
                                         `integral_option` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '積分選項',
                                         `threshord` int DEFAULT NULL COMMENT '搶福閾值',
                                         `is_redeem` int DEFAULT '0' COMMENT '是否兑换代码福利（0 否, 1 是）',
                                         `is_momecoin` int DEFAULT '0' COMMENT '是否M幣福利（0 否, 1 是）',
                                         `is_great` int DEFAULT NULL COMMENT '是否搶福（0 否, 1 是）',
                                         `collect_times` int unsigned DEFAULT '0' COMMENT '收藏次數',
                                         `isexport` tinyint DEFAULT '1' COMMENT 'M幣現金券是否支持人工導入(1.人工導入，2.系統生成)',
                                         `receive_method` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '領取方法',
                                         `is_mpay_exchange` int DEFAULT NULL COMMENT '是否开启mpay兑换 (1是,0否)',
                                         `is_mcoin_open` int DEFAULT '1' COMMENT '是否mcoin開放',
                                         `is_mpay_open` int DEFAULT '1' COMMENT '是否mpay開放',
                                         `user_limited_number` int DEFAULT NULL COMMENT '用戶限購數量',
                                         `orderby` tinyint DEFAULT '0',
                                         `limited_offer` tinyint DEFAULT '0' COMMENT '是否限時優惠',
                                         `snap_up` tinyint DEFAULT '0' COMMENT '搶購方式 0非搶購 1按庫存 2隨機分配',
                                         `snap_up_index` tinyint DEFAULT '0' COMMENT '0不显示, 1首页抢购显示, 2H5抢购显示, 3所有抢购显示',
                                         `platform_type` tinyint DEFAULT '1' COMMENT '開放平台類別 1mcoin 2會員卡系統 3.亚洲万里',
                                         `member_integral` decimal(8,2) DEFAULT '0.00' COMMENT '會員卡兌換積分',
                                         `location` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '優惠券所在位置',
                                         `coupon_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '會員卡系統優惠券id',
                                         `shop_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '會員卡門店id',
                                         `coupon_type` tinyint DEFAULT '1' COMMENT '0通用券，1特定門店',
                                         `maximum_points` int DEFAULT NULL COMMENT '最大限制積分',
                                         `istemporary` tinyint DEFAULT NULL,
                                         `only_point` tinyint DEFAULT NULL COMMENT '0: 混合支付  1：純積分  2： 純金額',
                                         `is_settlement` tinyint DEFAULT '1' COMMENT '是否結算(1是，0否)',
                                         `img_en` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '圖片(英文)',
                                         `mpay_coupons_code_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Mpay優惠券碼ID',
                                         `only_money` tinyint DEFAULT '0' COMMENT '是否纯金额支付 0：否 1：是',
                                         `relation_sales` int DEFAULT '0' COMMENT '關聯銷量',
                                         `product_ids` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '關聯福利ID',
                                         `trans_coupon_type` tinyint DEFAULT '0' COMMENT '券類型：0滿減券，1折扣券',
                                         `meet_money` decimal(11,1) unsigned DEFAULT '0.0' COMMENT '滿足金額',
                                         `dec_money` decimal(11,1) unsigned DEFAULT '0.0' COMMENT '減免金額',
                                         `discount` float(4,2) unsigned DEFAULT '0.00' COMMENT '折扣，8折=0.8',
                                         `max_discount_money` decimal(11,1) unsigned DEFAULT '0.0' COMMENT '最大折扣金額',
                                         `stock_a` int unsigned DEFAULT '0' COMMENT 'A+庫存',
                                         `is_a_open` tinyint unsigned DEFAULT '0' COMMENT '是否對A+開放',
                                         `third_party_settlement_price` decimal(11,2) unsigned DEFAULT '0.00' COMMENT '第三方渠道結算價格',
                                         `a_fee_type` tinyint unsigned DEFAULT '0' COMMENT '收費類型，從收費列表中讀取',
                                         `min_point` int unsigned DEFAULT '0' COMMENT '最小限制積分',
                                         `href_url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '跳转链接',
                                         `goods_id` int DEFAULT NULL COMMENT '零售小程序的货物id（商品Id）',
                                         `category_path` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '分类路径',
                                         `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
                                         `seckill_img` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '秒杀图片',
                                         `lock_stock` int NOT NULL DEFAULT 0 COMMENT '锁定庫存',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         KEY `businessid` (`businessid`) USING BTREE,
                                         KEY `type` (`type`) USING BTREE,
                                         KEY `trans_coupon_type` (`trans_coupon_type`) USING BTREE,
                                         KEY `shelf_status` (`shelf_status`) USING BTREE,
                                         KEY `enable` (`enable`) USING BTREE,
                                         KEY `is_a_open` (`is_a_open`) USING BTREE,
                                         KEY `idx_buy_end_time` (`buy_end_time`) USING BTREE,
                                         KEY `idx_buy_start_time` (`buy_start_time`) USING BTREE,
                                         KEY `idx_mpay_coupons_code_id` (`mpay_coupons_code_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3057 DEFAULT CHARSET=utf8mb3 COMMENT='產品信息表';


-- fooku_prd_20231123.fook_business_product_bak20220729 definition

CREATE TABLE `fook_business_product_bak20220729` (
  `id` int(11) NOT NULL DEFAULT '0' COMMENT '產品Id',
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
  `type` int(11) DEFAULT NULL COMMENT '產品類型（對應fook_business_productcategory表 1、撚手小菜，2、新菜式, 3、現金券,4、1蚊福利,5、積分換領,6、M 幣現金券）',
  `businessid` int(11) DEFAULT NULL COMMENT '商家Id',
  `img` varchar(255) DEFAULT NULL COMMENT '福利圖片',
  `zip_img` varchar(225) DEFAULT NULL COMMENT '壓縮後的福利圖片',
  `imgs` varchar(1000) DEFAULT NULL COMMENT '產品圖片',
  `zip_imgs` varchar(1000) DEFAULT NULL COMMENT '壓縮後的產品圖片',
  `title` varchar(255) DEFAULT NULL COMMENT '產品標題',
  `desc` longtext COMMENT '產品描述',
  `price` decimal(8,2) DEFAULT NULL COMMENT '售出價格',
  `retail_price` decimal(8,2) DEFAULT NULL COMMENT '門市價',
  `stock` int(11) DEFAULT NULL COMMENT '庫存',
  `history_stock` int(11) DEFAULT '0' COMMENT '历史总库存',
  `sales` int(11) unsigned DEFAULT '0' COMMENT '銷量',
  `actual_sales` int(11) unsigned DEFAULT '0' COMMENT '實際銷量',
  `limited_number` int(11) DEFAULT NULL COMMENT '限購數量',
  `is_allow_refund` int(2) DEFAULT NULL COMMENT '是否允許退款（1、允許，0、不允許）',
  `details` longtext COMMENT '圖文詳情',
  `tnc` text COMMENT '細則',
  `score` decimal(8,2) DEFAULT NULL COMMENT '評分',
  `comment_number` int(11) DEFAULT '0' COMMENT '總評價人數',
  `shelf_status` tinyint(1) DEFAULT '1' COMMENT '狀態（1、上架，2、下架）',
  `view_number` int(11) DEFAULT '0' COMMENT '瀏覽量',
  `buy_start_time` datetime DEFAULT NULL COMMENT '可購買開始日期',
  `buy_end_time` datetime DEFAULT NULL COMMENT '可購買結束日期',
  `vaild_mode` int(11) DEFAULT NULL COMMENT '有效期方式(1时间段2购买起多少天内)',
  `vaild_start_time` datetime DEFAULT NULL COMMENT '使用有效期開始日期',
  `vaild_end_time` datetime DEFAULT NULL COMMENT '使用有效期結束日期',
  `day_number` int(11) DEFAULT NULL COMMENT '有效天数',
  `is_weekend` int(11) DEFAULT NULL COMMENT '周六日是否可用（1可用0不可用）',
  `no_useday` varchar(255) DEFAULT NULL COMMENT '不可用日期',
  `use_start_time` datetime DEFAULT NULL COMMENT '可使用开始时间',
  `use_end_time` datetime DEFAULT NULL COMMENT '可使用结束时间',
  `is_vacation` int(11) DEFAULT NULL COMMENT '是否假期可用(1可用0不可用)',
  `fee_rate` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  `enable` int(11) DEFAULT '1' COMMENT '是否有效（1、有效，0、無效  ）',
  `is_hot` int(11) DEFAULT NULL COMMENT '是否熱門（0 否, 1 是）',
  `momecoins_option` varchar(255) DEFAULT '0' COMMENT 'M幣選項',
  `integral_option` varchar(255) DEFAULT '0' COMMENT '積分選項',
  `threshord` int(11) DEFAULT NULL COMMENT '搶福閾值',
  `is_redeem` int(11) DEFAULT '0' COMMENT '是否兑换代码福利（0 否, 1 是）',
  `is_momecoin` int(11) DEFAULT '0' COMMENT '是否M幣福利（0 否, 1 是）',
  `is_great` int(11) DEFAULT NULL COMMENT '是否搶福（0 否, 1 是）',
  `collect_times` int(11) unsigned DEFAULT '0' COMMENT '收藏次數',
  `isexport` tinyint(4) DEFAULT '1' COMMENT 'M幣現金券是否支持人工導入(1.人工導入，2.系統生成)',
  `receive_method` text COMMENT '領取方法',
  `is_mpay_exchange` int(11) DEFAULT NULL COMMENT '是否开启mpay兑换 (1是,0否)',
  `is_mcoin_open` int(2) DEFAULT '1' COMMENT '是否mcoin開放',
  `is_mpay_open` int(2) DEFAULT '1' COMMENT '是否mpay開放',
  `user_limited_number` int(11) DEFAULT NULL COMMENT '用戶限購數量',
  `orderby` tinyint(4) DEFAULT '0',
  `limited_offer` tinyint(2) DEFAULT '0' COMMENT '是否限時優惠',
  `snap_up` tinyint(2) DEFAULT '0' COMMENT '搶購方式 0非搶購 1按庫存 2隨機分配',
  `snap_up_index` tinyint(2) DEFAULT '0' COMMENT '搶購首頁顯示 1顯示 0不顯示',
  `platform_type` tinyint(4) DEFAULT '1' COMMENT '開放平台類別 1mcoin 2會員卡系統',
  `member_integral` decimal(8,2) DEFAULT '0.00' COMMENT '會員卡兌換積分',
  `location` varchar(255) DEFAULT NULL COMMENT '優惠券所在位置',
  `coupon_id` varchar(255) DEFAULT NULL COMMENT '會員卡系統優惠券id',
  `shop_id` varchar(255) DEFAULT NULL COMMENT '會員卡門店id',
  `coupon_type` tinyint(4) DEFAULT '1' COMMENT '0通用券，1特定門店',
  `maximum_points` int(11) DEFAULT NULL COMMENT '最大限制積分',
  `istemporary` tinyint(4) DEFAULT NULL,
  `only_point` tinyint(2) DEFAULT '0' COMMENT '是否只能純積分支付（1、是，0否）',
  `is_settlement` tinyint(4) DEFAULT '1' COMMENT '是否結算(1是，0否)',
  `img_en` varchar(255) DEFAULT NULL COMMENT '圖片（英文）',
  `mpay_coupons_code_id` varchar(100) DEFAULT NULL COMMENT 'Mpay優惠券碼ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_business_product_class definition

CREATE TABLE `fook_business_product_class` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `code` varchar(64) NOT NULL COMMENT '类目编码',
  `name` varchar(64) NOT NULL COMMENT '类目名称',
  `level` tinyint(11) NOT NULL COMMENT '类目级别，1级、2级类推',
  `parent_code` varchar(64) DEFAULT NULL COMMENT '父类目编码',
  `valid` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效，0否1是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `un_code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='福利的类目（类别）表';


-- fooku_prd_20231123.fook_business_product_limit definition

CREATE TABLE `fook_business_product_limit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `productid` int(11) DEFAULT NULL COMMENT '產品ID',
  `status` int(2) DEFAULT NULL COMMENT '限購狀態（1正常，2關閉）',
  `stock` int(11) DEFAULT NULL COMMENT '限購庫存',
  `limit_number` int(11) DEFAULT NULL COMMENT '限購數量',
  `start_time` datetime DEFAULT NULL COMMENT '限購開始時間',
  `end_time` datetime DEFAULT NULL COMMENT '限購結束時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='產品限購表';


-- fooku_prd_20231123.fook_business_product_ordercode definition

CREATE TABLE `fook_business_product_ordercode` (
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='核銷商家關聯福利表';


-- fooku_prd_20231123.fook_business_product_show definition

CREATE TABLE `fook_business_product_show` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '產品Id',
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
  `type` int(11) DEFAULT NULL COMMENT '產品類型（對應fook_business_productcategory表 1、撚手小菜，2、新菜式, 3、現金券,4、1蚊福利,5、積分換領,6、M 幣現金券）',
  `businessid` int(11) DEFAULT NULL COMMENT '商家Id',
  `img` varchar(255) DEFAULT NULL COMMENT '福利圖片',
  `imgs` varchar(1000) DEFAULT NULL COMMENT '產品圖片',
  `title` varchar(255) DEFAULT NULL COMMENT '產品標題',
  `desc` longtext COMMENT '產品描述',
  `price` decimal(8,2) DEFAULT NULL COMMENT '售出價格',
  `retail_price` decimal(8,2) DEFAULT NULL COMMENT '門市價',
  `stock` int(11) DEFAULT NULL COMMENT '庫存',
  `history_stock` int(11) DEFAULT '0' COMMENT '历史总库存',
  `sales` int(11) unsigned DEFAULT '0' COMMENT '銷量',
  `limited_number` int(11) DEFAULT NULL COMMENT '限購數量',
  `is_allow_refund` int(2) DEFAULT NULL COMMENT '是否允許退款（1、允許，0、不允許）',
  `details` longtext COMMENT '圖文詳情',
  `tnc` varchar(1000) DEFAULT NULL COMMENT '細則',
  `score` decimal(8,2) DEFAULT NULL COMMENT '評分',
  `comment_number` int(11) DEFAULT '0' COMMENT '總評價人數',
  `shelf_status` tinyint(1) DEFAULT '1' COMMENT '狀態（1、上架，2、下架）',
  `view_number` int(11) DEFAULT '0' COMMENT '瀏覽量',
  `buy_start_time` datetime DEFAULT NULL COMMENT '可購買開始日期',
  `buy_end_time` datetime DEFAULT NULL COMMENT '可購買結束日期',
  `vaild_mode` int(11) DEFAULT NULL COMMENT '有效期方式(1时间段2购买起多少天内)',
  `vaild_start_time` datetime DEFAULT NULL COMMENT '使用有效期開始日期',
  `vaild_end_time` datetime DEFAULT NULL COMMENT '使用有效期結束日期',
  `day_number` int(11) DEFAULT NULL COMMENT '有效天数',
  `is_weekend` int(11) DEFAULT NULL COMMENT '周六日是否可用（1可用0不可用）',
  `no_useday` varchar(255) DEFAULT NULL COMMENT '不可用日期',
  `use_start_time` datetime DEFAULT NULL COMMENT '可使用开始时间',
  `use_end_time` datetime DEFAULT NULL COMMENT '可使用结束时间',
  `is_vacation` int(11) DEFAULT NULL COMMENT '是否假期可用(1可用0不可用)',
  `fee_rate` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  `enable` int(11) DEFAULT '1' COMMENT '是否有效（1、有效，0、無效  ）',
  `is_hot` int(11) DEFAULT NULL COMMENT '是否熱門（0 否, 1 是）',
  `momecoins_option` varchar(255) DEFAULT '0' COMMENT 'M幣選項',
  `integral_option` varchar(255) DEFAULT '0' COMMENT '積分選項',
  `threshord` int(11) DEFAULT NULL COMMENT '搶福閾值',
  `is_redeem` int(11) DEFAULT NULL COMMENT '是否兑换代码福利（0 否, 1 是）',
  `is_momecoin` int(11) DEFAULT NULL COMMENT '是否M幣福利（0 否, 1 是）',
  `is_great` int(11) DEFAULT NULL COMMENT '是否搶福（0 否, 1 是）',
  `collect_times` int(11) unsigned DEFAULT '0' COMMENT '收藏次數',
  `isexport` tinyint(4) DEFAULT '1' COMMENT 'M幣現金券是否支持人工導入(1.人工導入，2.系統生成)',
  `receive_method` text COMMENT '領取方法',
  `is_mpay_exchange` int(11) DEFAULT NULL COMMENT '是否开启mpay兑换 (1是,0否)',
  `is_mcoin_open` int(2) DEFAULT '1' COMMENT '是否mcoin開放',
  `is_mpay_open` int(2) DEFAULT '1' COMMENT '是否mpay開放',
  `user_limited_number` int(11) DEFAULT NULL COMMENT '用戶限購數量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1007 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='產品信息表';


-- fooku_prd_20231123.fook_business_product_translations definition

CREATE TABLE `fook_business_product_translations` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `business_product_id` int(11) DEFAULT NULL COMMENT '產品id',
  `t_title` varchar(191) DEFAULT NULL COMMENT '產品名',
  `t_tnc` varchar(5000) DEFAULT NULL COMMENT '細則',
  `t_receive_method` varchar(255) DEFAULT NULL COMMENT '領取方式',
  `locale` varchar(191) DEFAULT '' COMMENT '語言类型(en 英语 zh-TW中文繁体 zh-CN中文简体)',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `fook_business_product_translatio_product_id_local_unique` (`business_product_id`,`locale`) USING BTREE,
  KEY `fook_business_product_translations_locale_index` (`locale`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5012 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='福利翻译表';


-- fooku_prd_20231123.fook_business_productcategory definition

CREATE TABLE `fook_business_productcategory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分類Id',
  `parent_id` int(11) DEFAULT NULL COMMENT '父級Id',
  `name` varchar(50) DEFAULT NULL COMMENT '分類名稱',
  `english_name` varchar(255) DEFAULT NULL COMMENT ' 分類英語名稱',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `icon` varchar(255) DEFAULT NULL COMMENT '圖標',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效',
  `type` int(11) DEFAULT NULL COMMENT '類型(1,一級地區2,二級地區3,地標)',
  `mcoin_platform` tinyint(4) DEFAULT '0' COMMENT 'mcoin平台開放 0否 1是',
  `mcard_platform` tinyint(4) DEFAULT '0' COMMENT 'mcard平台開放 0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='福利分類表';


-- fooku_prd_20231123.fook_business_productstock definition

CREATE TABLE `fook_business_productstock` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '福利庫存變動記錄表Id',
  `productid` int(11) DEFAULT NULL COMMENT '福利Id',
  `number` int(11) DEFAULT NULL COMMENT '數量(加增減扣)',
  `type` int(11) DEFAULT NULL COMMENT '理由Id(1新增初始化庫存,2加減庫存,3取消訂單補庫存,4刪除訂單補庫存,5下單扣庫存,6下單過程中異常補庫存,7取消訂單補庫存,8用戶更改訂單補庫存,9用戶更改訂單扣庫存,99失效訂單補庫存)',
  `operation_type` int(11) DEFAULT NULL COMMENT '操作者類型(1管理員,2商家,3門店,4用戶,5其它)管理員(1新增初始化庫存,2加減庫存,3取消訂單補庫存,4刪除訂單補庫存)商  家(1新增初始化庫存,2加減庫存,3取消訂單補庫存,4刪除訂單補庫存)門  店(1新增初始化庫存,2加減庫存,3取消訂單補庫存,4刪除訂單補庫存)用  戶(5下單扣庫存,6下單過程中異常補庫存,7取消訂單補庫存,8用戶更改訂單補庫存,9用戶更改訂單扣庫存)其  他(99失效訂單補庫存)',
  `operationid` int(11) DEFAULT NULL COMMENT '操作者Id',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `create_time` datetime DEFAULT NULL COMMENT '變動時間',
  `status` int(2) DEFAULT NULL COMMENT '庫存是否補迴 1已補迴，0未補迴',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `productid` (`productid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4580825 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='福利券庫存';


-- fooku_prd_20231123.fook_business_push definition

CREATE TABLE `fook_business_push` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `msgid` int(11) DEFAULT NULL COMMENT '關聯用戶消息表id',
  `type` int(11) DEFAULT NULL COMMENT '消息分類（1預約，2福利，3後台）',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `message` varchar(1000) DEFAULT NULL COMMENT '通知信息',
  `error` varchar(1000) DEFAULT NULL COMMENT '推送異常信息',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `registratationid` varchar(80) DEFAULT NULL COMMENT '推送註冊id',
  `token` varchar(255) DEFAULT NULL COMMENT '发送的Token',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2318208 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商家推送信息';


-- fooku_prd_20231123.fook_business_store_product definition

CREATE TABLE `fook_business_store_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '關聯ID',
  `productid` int(11) DEFAULT NULL COMMENT '產品/預定Id',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `type` int(11) DEFAULT NULL COMMENT '類型(1福利適用門店，2預定適用門店)',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `productid` (`productid`) USING BTREE,
  KEY `idx_productid_storeid` (`productid`,`storeid`) USING BTREE,
  KEY `businessid` (`businessid`) USING BTREE,
  KEY `storeid` (`storeid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28471 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='福利券關聯門店和商家的關聯表';


-- fooku_prd_20231123.fook_business_store_product_show definition

CREATE TABLE `fook_business_store_product_show` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '關聯ID',
  `productid` int(11) DEFAULT NULL COMMENT '產品/預定Id',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `type` int(11) DEFAULT NULL COMMENT '類型(1福利適用門店，2預定適用門店)',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `productid` (`productid`) USING BTREE,
  KEY `businessid` (`businessid`) USING BTREE,
  KEY `storeid` (`storeid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1007 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)福利券關聯門店和商家的關聯表';


-- fooku_prd_20231123.fook_business_translations definition

CREATE TABLE `fook_business_translations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  `locale` varchar(255) DEFAULT NULL COMMENT '語言',
  `t_name` varchar(255) DEFAULT NULL COMMENT '商家名稱',
  `t_company_name` varchar(255) DEFAULT NULL COMMENT '公司名稱',
  `t_address` varchar(255) DEFAULT NULL COMMENT '地址',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `fook_business_translations_business_id_locale_unique` (`business_id`,`locale`) USING BTREE,
  KEY `fook_business_translations_locale_index` (`locale`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=432 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家信息對應的多語言表';


-- fooku_prd_20231123.fook_clause definition

CREATE TABLE `fook_clause` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adminid` int(11) NOT NULL COMMENT '操作人ID',
  `content` longtext NOT NULL COMMENT '条款内容',
  `enable` tinyint(4) DEFAULT '0' COMMENT '是否显示：0隐藏，1显示',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `content_en` longtext NOT NULL COMMENT '條款内容英文',
  `systemid` tinyint(4) DEFAULT '0' COMMENT '系統類別:0 mcoin；',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `fook_clause_id_uindex` (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='条款管理表';


-- fooku_prd_20231123.fook_clause_confirm definition

CREATE TABLE `fook_clause_confirm` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) NOT NULL COMMENT '用户ID',
  `clauseid` int(11) NOT NULL COMMENT '条款ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否确认，0未确认，1确认',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `fook_clause_confirm_id_uindex` (`id`) USING BTREE,
  KEY `clauseid` (`clauseid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=747137 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='用户确认条款记录表';


-- fooku_prd_20231123.fook_coupon_business definition

CREATE TABLE `fook_coupon_business` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `businessid` int(11) DEFAULT NULL COMMENT 'mcoin商户ID',
  `code` varchar(255) DEFAULT NULL COMMENT '商家編碼',
  `macaupasscode` varchar(255) DEFAULT NULL COMMENT '澳門通商戶號',
  `couponid` varchar(255) DEFAULT NULL COMMENT '券系统ID',
  `marketid` varchar(255) DEFAULT NULL COMMENT '商圈ID',
  `source` int(11) DEFAULT NULL COMMENT '商戶來源：0-券系統商戶，1-mcoin商戶',
  `name` varchar(255) DEFAULT NULL COMMENT '商戶名稱',
  `marketname` varchar(255) DEFAULT NULL COMMENT '商圈名稱\r\n',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `district_id` int(11) DEFAULT NULL COMMENT 'fook_coupon_district表id 商圈id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='關聯券系統商戶的數據';


-- fooku_prd_20231123.fook_coupon_district definition

CREATE TABLE `fook_coupon_district` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  `area_name` varchar(255) DEFAULT NULL COMMENT '商圈名',
  `area_id` varchar(255) DEFAULT NULL COMMENT '券系統返回的商圈id',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `updatetime` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='券系統商圈表';


-- fooku_prd_20231123.fook_coupon_log definition

CREATE TABLE `fook_coupon_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `content` text COMMENT '请求数据',
  `message` varchar(2000) DEFAULT NULL COMMENT '信息',
  `type` varchar(50) DEFAULT NULL COMMENT '请求方法',
  `createtime` datetime DEFAULT NULL COMMENT '请求时间',
  `url` varchar(255) DEFAULT NULL COMMENT '请求url',
  `reasoncontent` text COMMENT '返回信息',
  `updatetime` datetime DEFAULT NULL COMMENT '返回时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3508340 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='请求券系统日志';


-- fooku_prd_20231123.fook_coupon_product definition

CREATE TABLE `fook_coupon_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `productid` int(11) DEFAULT NULL COMMENT 'mcoin券ID',
  `couponid` varchar(255) DEFAULT NULL COMMENT '券系统ID',
  `batchid` varchar(255) DEFAULT NULL COMMENT '批次ID',
  `stock` int(11) DEFAULT NULL COMMENT '库存',
  `status` tinyint(4) DEFAULT '1' COMMENT '批次上下架状态1上架2下架',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='關聯券系統福利券的數據';


-- fooku_prd_20231123.fook_coupon_store definition

CREATE TABLE `fook_coupon_store` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT ' ',
  `couponbusinessid` int(11) DEFAULT NULL COMMENT '關聯的券系統商戶ID',
  `storeid` int(11) DEFAULT NULL COMMENT 'mcoin門店ID',
  `macaupasscode` varchar(255) DEFAULT NULL COMMENT '澳門通門店號',
  `couponid` varchar(255) DEFAULT NULL COMMENT '券系统ID',
  `source` int(11) DEFAULT NULL COMMENT '門店來源：0-券系統門店，1-mcoin門店',
  `name` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `district_id` int(11) DEFAULT NULL COMMENT 'fook_coupon_district表id 商圈id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='關聯券系統門店的數據';


-- fooku_prd_20231123.fook_coupon_syn definition

CREATE TABLE `fook_coupon_syn` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單ID',
  `code_id` int(11) DEFAULT NULL COMMENT 'ordercode表id',
  `operation` varchar(255) DEFAULT NULL COMMENT '操作',
  `type` int(11) DEFAULT NULL COMMENT '1新增、2更新、3查詢',
  `message` varchar(255) DEFAULT NULL COMMENT '錯誤信息',
  `status` int(11) DEFAULT '0' COMMENT '狀態1成功、0失敗',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_operation_type_status` (`operation`,`type`,`status`) USING BTREE,
  KEY `idx_codeid_operation_type` (`code_id`,`operation`,`type`) USING BTREE,
  KEY `idx_orderid` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5796295 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='同步券平台优惠券信息';


-- fooku_prd_20231123.fook_day_discount definition

CREATE TABLE `fook_day_discount` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `gain_id` int(11) DEFAULT NULL COMMENT 'gain表ID',
  `discount` varchar(255) DEFAULT NULL COMMENT '優惠分類名稱',
  `discount_en` varchar(255) DEFAULT NULL COMMENT '優惠分類名稱（英文）',
  `status` tinyint(4) DEFAULT '1' COMMENT '狀態(正常：1、凍結0)',
  `sort` int(11) DEFAULT '1' COMMENT '排序',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `gain_id` (`gain_id`) USING BTREE,
  KEY `discount` (`discount`,`discount_en`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_day_gain definition

CREATE TABLE `fook_day_gain` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) DEFAULT NULL COMMENT '名稱',
  `name_en` varchar(255) DEFAULT NULL COMMENT '名稱（英文）',
  `img` varchar(255) DEFAULT NULL COMMENT '圖片頂部',
  `img_en` varchar(255) DEFAULT NULL COMMENT '圖片（英文）',
  `status` tinyint(4) DEFAULT '1' COMMENT '狀態（1正常、0凍結）',
  `url` varchar(255) DEFAULT NULL COMMENT 'url地址',
  `discount_num` int(11) DEFAULT '0' COMMENT '優惠分類數',
  `item_num` int(11) DEFAULT '0' COMMENT '商戶數',
  `theme_color` varchar(100) DEFAULT '#FD8204' COMMENT '主題顏色（#FD8204）',
  `subtitle_color` varchar(100) DEFAULT '#666666' COMMENT '副標題顏色（#666666）',
  `themetext_color` varchar(100) DEFAULT '#222222' COMMENT '主題文字顏色（#222222）',
  `color` varchar(100) DEFAULT '#F7F8FA' COMMENT '背景顏色（#F7F8FA）',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `name` (`name`,`name_en`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_day_item definition

CREATE TABLE `fook_day_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `gain_id` int(11) DEFAULT NULL,
  `discount_id` int(11) DEFAULT NULL COMMENT 'day_discount表ID',
  `business_name` varchar(255) DEFAULT NULL COMMENT '商戶名稱',
  `business_name_en` varchar(255) DEFAULT NULL COMMENT '商戶名稱（英文）',
  `discount_name` varchar(255) DEFAULT NULL COMMENT '優惠名稱',
  `discount_name_en` varchar(255) DEFAULT NULL COMMENT '優惠名稱（英文）',
  `logo` varchar(255) DEFAULT NULL COMMENT 'Logo圖（720*720）1mb以下',
  `logo_en` varchar(255) DEFAULT NULL COMMENT 'Logo英文圖（720*720）1mb以下',
  `start_time` datetime DEFAULT NULL COMMENT '開始時間',
  `end_time` datetime DEFAULT NULL COMMENT '結束時間',
  `url` varchar(255) DEFAULT NULL COMMENT '跳轉鏈接',
  `status` tinyint(4) DEFAULT '1' COMMENT '狀態（1：正常，0：凍結）',
  `sort` int(11) DEFAULT '1' COMMENT '排序(越小越前)',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `business_name` (`business_name`,`business_name_en`) USING BTREE,
  KEY `discount_name` (`discount_name`,`discount_name_en`) USING BTREE,
  KEY `gain_id` (`gain_id`) USING BTREE,
  KEY `discount_id` (`discount_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_discount_log definition

CREATE TABLE `fook_discount_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `discount_id` int(11) DEFAULT NULL COMMENT '優惠id',
  `old_product_id` int(11) DEFAULT NULL COMMENT '原福利id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id',
  `user_id` int(11) DEFAULT NULL COMMENT '管理員id',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip',
  `remarks` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='記錄福利優惠方式的日志';


-- fooku_prd_20231123.fook_evaluation definition

CREATE TABLE `fook_evaluation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '評價記錄Id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶Id',
  `product_id` int(11) DEFAULT NULL COMMENT '產品Id',
  `v_id` int(11) DEFAULT NULL COMMENT '核销码ID或预约详情ID',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單Id',
  `area_id` mediumint(4) DEFAULT NULL COMMENT '地區Id',
  `business_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `stores_id` int(1) DEFAULT NULL COMMENT '門店Id',
  `scores` int(1) DEFAULT NULL COMMENT '評分',
  `consumption` decimal(10,2) DEFAULT NULL COMMENT '人均消費',
  `flavor_scores` decimal(10,2) DEFAULT NULL COMMENT '口味評分',
  `service_scores` decimal(10,2) DEFAULT NULL COMMENT '服務評分',
  `environment_scores` decimal(10,2) DEFAULT NULL COMMENT '環境評分',
  `content` varchar(255) DEFAULT ' ' COMMENT '評價內容',
  `create_at` datetime DEFAULT NULL COMMENT '評價時間',
  `reply` varchar(255) DEFAULT ' ' COMMENT '商家回復',
  `reply_time` datetime DEFAULT NULL COMMENT '回復時間',
  `page_view` int(11) DEFAULT NULL COMMENT '瀏覽量',
  `status` tinyint(1) DEFAULT '0' COMMENT '狀態（0未審核、1通過并顯示、2不通過、3通過不顯示）',
  `enable` tinyint(1) DEFAULT NULL COMMENT '是否有效(1有效，2無效)',
  `is_anonymou` tinyint(1) DEFAULT NULL COMMENT '是否匿名（0不匿名1匿名）',
  `enum` tinyint(1) DEFAULT NULL COMMENT '評價類型（1下單福利評價 2下單預約評價 3 直接門店評價）',
  `img` varchar(255) DEFAULT NULL COMMENT '单张图片路径',
  `category` tinyint(4) DEFAULT NULL COMMENT '分類（0.未分類 , 1.菜品, 2.環境 , 3.價目表 ）',
  `dishes_id` int(11) DEFAULT NULL COMMENT '推薦菜Id',
  `updated_at` timestamp NULL DEFAULT NULL,
  `title` varchar(50) DEFAULT NULL COMMENT '標題',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE,
  KEY `business_id` (`business_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用戶評價記錄表';


-- fooku_prd_20231123.fook_evaluation_images definition

CREATE TABLE `fook_evaluation_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '圖片資源Id',
  `image_url` varchar(1000) DEFAULT ' ' COMMENT '圖片路徑',
  `evaluation_id` int(11) DEFAULT NULL COMMENT '評價記錄Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用戶上傳圖片資源表';


-- fooku_prd_20231123.fook_external_vcode definition

CREATE TABLE `fook_external_vcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `product_id` int(11) DEFAULT NULL COMMENT '兌換福利id',
  `vcode` varchar(100) DEFAULT ' ' COMMENT '兌換碼',
  `isascription` tinyint(1) DEFAULT '0' COMMENT '兌換碼狀態(0未綁定 1已綁定)',
  `title` varchar(255) DEFAULT NULL COMMENT '描述',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效(1有效0無效)',
  `status` tinyint(1) DEFAULT '0' COMMENT '0、待核銷 1、已核銷',
  `created_at` datetime DEFAULT NULL COMMENT 'M幣現金券生成時間',
  `update_time` datetime DEFAULT NULL COMMENT '更新時間',
  `is_a_open` tinyint(1) DEFAULT '0' COMMENT '是否A+券碼，0否，1是',
  `build_status` tinyint(1) DEFAULT '0' COMMENT '生成訂單的狀態:0未生成，1生成中，2已生成',
  `is_build_order` tinyint(1) DEFAULT '0' COMMENT '是否創建訂單，0否，1是',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE,
  KEY `vcode` (`vcode`) USING BTREE,
  KEY `created_at` (`created_at`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `build_status` (`build_status`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE,
  KEY `is_build_order` (`is_build_order`) USING BTREE,
  KEY `created_at_idx` (`created_at`) USING BTREE,
  KEY `idx_product_enable_isascription_status` (`product_id`,`enable`,`isascription`,`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=730780 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='M幣現金券表';


-- fooku_prd_20231123.fook_external_vcode_tmp definition

CREATE TABLE `fook_external_vcode_tmp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vcode` varchar(100) NOT NULL COMMENT '券碼',
  `status` tinyint(4) DEFAULT '0' COMMENT '0未生成，1生成中，2已生成',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `product_id` int(11) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `fook_external_vcode_tmp_vcode_uindex` (`vcode`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=445788 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- fooku_prd_20231123.fook_holiday definition

CREATE TABLE `fook_holiday` (
  `id` mediumint(8) NOT NULL AUTO_INCREMENT COMMENT '公眾假期表Id',
  `holiday` datetime DEFAULT NULL COMMENT '假期',
  `name` varchar(100) DEFAULT ' ' COMMENT '名稱',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  `enable` tinyint(4) DEFAULT NULL COMMENT '是否有效(1有效，2無效)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='公眾假期表';


-- fooku_prd_20231123.fook_macaupass_code definition

CREATE TABLE `fook_macaupass_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storeid` int(11) DEFAULT NULL COMMENT '门店ID',
  `macaupass_businesscode` varchar(255) DEFAULT NULL COMMENT '澳门通商户号',
  `macaupass_storecode` varchar(255) DEFAULT NULL COMMENT '澳门通的门店号',
  `macaupass_terminalcode` varchar(255) DEFAULT NULL COMMENT '澳门通的终端号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `macaupass_businesscode` (`macaupass_businesscode`) USING BTREE,
  KEY `macaupass_storecode` (`macaupass_storecode`) USING BTREE,
  KEY `macaupass_terminalcode` (`macaupass_terminalcode`) USING BTREE,
  KEY `idx_storeid` (`storeid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=602206 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##澳门通终端号数据表##';


-- fooku_prd_20231123.fook_macaupass_log definition

CREATE TABLE `fook_macaupass_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) DEFAULT NULL COMMENT '记录ip地址',
  `content` text COMMENT '回调的参数列表',
  `code` varchar(10) DEFAULT NULL COMMENT '返回的状态值',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2827109 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##澳门通调用日志##';


-- fooku_prd_20231123.fook_macaupass_order definition

CREATE TABLE `fook_macaupass_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT 'orderinfo id',
  `ordercode_id` int(11) DEFAULT NULL COMMENT 'ordercode id',
  `mpayintegral` int(11) DEFAULT NULL COMMENT 'Mpay 积分',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3662291 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='澳門通MPay用戶訂單記錄表';


-- fooku_prd_20231123.fook_macaupass_token definition

CREATE TABLE `fook_macaupass_token` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token` varchar(255) DEFAULT NULL COMMENT 'app_token',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `expires_in` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='澳門通MPay用戶token表';
INSERT INTO `fook_macaupass_token` (`id`,`token`,`create_time`,`expires_in`,`type`) VALUES ('3','d3a2569bcaf1e75452b5b4323572ccae','2025-06-23 16:49:00','7200','apptoken'),('4','fa837df4bc81f30319780ffd91946665','2025-06-23 15:11:00','7200','ticket');

-- fooku_prd_20231123.fook_macaupass_user definition

CREATE TABLE `fook_macaupass_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '主用户id',
  `openid` varchar(255) NOT NULL COMMENT 'macaupass openid',
  `access_token` varchar(255) DEFAULT NULL COMMENT 'macaupass token ',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT 'refresh_token',
  `point` int(11) DEFAULT NULL COMMENT 'macaupass 积分',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '是否更新帳戶狀態',
  `code` varchar(255) DEFAULT NULL COMMENT '采用哪個code解析出來的',
  `phone` varchar(255) DEFAULT NULL COMMENT 'macaupass用戶手機號碼',
  `area` varchar(255) DEFAULT NULL COMMENT 'macaupass手機區號',
  `customid` varchar(50) DEFAULT NULL COMMENT 'macaupass用戶ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `openid` (`openid`) USING BTREE,
  KEY `customid` (`customid`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `idx_area_phone` (`area`,`phone`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=687183 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='澳門通MPay用戶列表';


-- fooku_prd_20231123.fook_macaupass_user_login_log definition

CREATE TABLE `fook_macaupass_user_login_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) NOT NULL COMMENT '主用户id',
  `openid` varchar(255) NOT NULL COMMENT 'macaupass openid',
  `phone` varchar(255) DEFAULT NULL COMMENT '手機號碼',
  `access_token` varchar(255) DEFAULT NULL COMMENT 'macaupass token ',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT 'refresh_token',
  `point` int(11) DEFAULT NULL COMMENT 'macaupass 积分',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userid` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2599543 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='澳門通MPay用戶登陸日志';


-- fooku_prd_20231123.fook_macaupass_user_login_log1 definition

CREATE TABLE `fook_macaupass_user_login_log1` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) NOT NULL COMMENT '主用户id',
  `openid` varchar(255) NOT NULL COMMENT 'macaupass openid',
  `phone` varchar(255) DEFAULT NULL COMMENT '手機號碼',
  `access_token` varchar(255) DEFAULT NULL COMMENT 'macaupass token ',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT 'refresh_token',
  `point` int(11) DEFAULT NULL COMMENT 'macaupass 积分',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_createtime_userid` (`create_time`,`user_id`) USING BTREE,
  KEY `userid` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=26708951 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='澳門通MPay用戶登陸日志';


-- fooku_prd_20231123.fook_macaupass_user_login_log2 definition

CREATE TABLE `fook_macaupass_user_login_log2` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) NOT NULL COMMENT '主用户id',
  `openid` varchar(255) NOT NULL COMMENT 'macaupass openid',
  `phone` varchar(255) DEFAULT NULL COMMENT '手機號碼',
  `access_token` varchar(255) DEFAULT NULL COMMENT 'macaupass token ',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT 'refresh_token',
  `point` int(11) DEFAULT NULL COMMENT 'macaupass 积分',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_createtime_userid` (`create_time`,`user_id`) USING BTREE,
  KEY `userid` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11012239 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='澳門通MPay用戶登陸日志';


-- fooku_prd_20231123.fook_member_benefits definition

CREATE TABLE `fook_member_benefits` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  `rights` text COMMENT '積分說明',
  `level` int(11) DEFAULT NULL COMMENT '會員級別（保留金卡、銀卡、普通卡等類型）目前尚未有',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家對應的會員積分說明表';


-- fooku_prd_20231123.fook_member_log definition

CREATE TABLE `fook_member_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `uuid` varchar(255) DEFAULT NULL COMMENT '商家\\門店\\福利券id',
  `opt_type` tinyint(4) DEFAULT NULL COMMENT '1新增2更新',
  `content` text COMMENT '请求数据',
  `type` varchar(50) DEFAULT NULL COMMENT '類別',
  `message` varchar(2000) DEFAULT NULL COMMENT '信息',
  `url` varchar(255) DEFAULT NULL COMMENT '请求url',
  `reasoncontent` text COMMENT '返回信息',
  `created_at` datetime DEFAULT NULL COMMENT '请求时间',
  `updated_at` datetime DEFAULT NULL COMMENT '返回时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='會員卡请求mcoin日志';


-- fooku_prd_20231123.fook_member_order definition

CREATE TABLE `fook_member_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `memberintegral` varchar(255) DEFAULT NULL COMMENT '會員卡積分',
  `status` int(11) DEFAULT NULL COMMENT '状态1未支付、2支付成功',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='會員卡訂單積分支付記錄表';


-- fooku_prd_20231123.fook_member_user definition

CREATE TABLE `fook_member_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `merchant_id` varchar(255) DEFAULT NULL COMMENT '商户id',
  `user_id` int(11) DEFAULT NULL COMMENT '主用户id',
  `cust_id` varchar(255) DEFAULT NULL COMMENT 'member cust_id',
  `point` decimal(10,2) DEFAULT NULL COMMENT '會員卡积分',
  `area_code` varchar(50) DEFAULT NULL COMMENT '區號',
  `mobile` varchar(50) DEFAULT NULL COMMENT '會員卡手機號碼',
  `status` tinyint(1) DEFAULT '1' COMMENT '是否更新帳戶狀態',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='會員卡用戶表';


-- fooku_prd_20231123.fook_member_user_login_log definition

CREATE TABLE `fook_member_user_login_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `merchant_id` varchar(255) DEFAULT NULL COMMENT '商户id',
  `user_id` int(11) DEFAULT NULL COMMENT '主用户id',
  `cust_id` varchar(255) DEFAULT NULL COMMENT 'cust_id',
  `mobile` varchar(255) DEFAULT NULL COMMENT '手机号码',
  `point` int(11) DEFAULT NULL COMMENT 'macaupass 积分',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='會員卡用戶登錄記錄表';


-- fooku_prd_20231123.fook_message_updatesystem definition

CREATE TABLE `fook_message_updatesystem` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `area` int(11) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `createtime` datetime DEFAULT NULL,
  `updatetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='用于記錄更新系統的日志（暫時沒用）';


-- fooku_prd_20231123.fook_mini_order_settlement definition

CREATE TABLE `fook_mini_order_settlement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `uuid` varchar(64) NOT NULL COMMENT '商品唯一标识',
  `order_no` varchar(64) NOT NULL COMMENT '零售订单号',
  `product_id` int(11) NOT NULL COMMENT '福利商品id',
  `product_price` varchar(64) DEFAULT NULL COMMENT '福利价格',
  `goods_num` int(11) DEFAULT NULL COMMENT '商品数量',
  `bill_amount` varchar(64) DEFAULT NULL COMMENT '订单总价',
  `pay_amount` varchar(64) DEFAULT NULL COMMENT '支付的金额',
  `pay_integral` varchar(64) DEFAULT NULL COMMENT '支付的积分',
  `fee_rate` varchar(64) DEFAULT NULL COMMENT '佣金',
  `integral_ratio` varchar(64) DEFAULT NULL COMMENT '积分兑换比例',
  `code` varchar(64) DEFAULT NULL COMMENT '核销码',
  `create_time` varchar(64) DEFAULT NULL COMMENT '下单时间',
  `complete_time` varchar(64) DEFAULT NULL COMMENT '完成时间',
  `delivery_type` int(11) DEFAULT NULL COMMENT '配送方式：1-快递发货,2-上门自提,3-同城配送,4-虚拟发货',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_order_no` (`order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=64075 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='零售小程序订单结算信息表';


-- fooku_prd_20231123.fook_mini_order_sync_log definition

CREATE TABLE `fook_mini_order_sync_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `mini_sn` varchar(64) NOT NULL COMMENT '实物订单号',
  `mini_update_time` datetime DEFAULT NULL COMMENT '实物订单更新时间',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_mini_sn` (`mini_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='实物小程序订单同步日志表';


-- fooku_prd_20231123.fook_module definition

CREATE TABLE `fook_module` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `banner_name` varchar(64) NOT NULL COMMENT '模块名称',
  `banner_style` int(11) DEFAULT NULL COMMENT '模块样式，1-1,2-1*2,3-1*1',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '模块排序',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `valid` tinyint(1) NOT NULL COMMENT '数据删除，0删除，1有效',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建用户id',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '修改用户id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_banner_name` (`banner_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- fooku_prd_20231123.fook_momecoins_task definition

CREATE TABLE `fook_momecoins_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '平台M幣任務表Id',
  `task` varchar(30) NOT NULL COMMENT '任務標題',
  `explain` varchar(255) DEFAULT NULL COMMENT '任務說明',
  `calcu_pattern` int(11) NOT NULL COMMENT '計算模式(1加法運算，2百分比比例)',
  `calcu_value` decimal(8,2) NOT NULL COMMENT '計算值',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `enable` int(11) DEFAULT '1' COMMENT '是否有效 0:无效1:有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)平臺M幣任務';


-- fooku_prd_20231123.fook_mq_local definition

CREATE TABLE `fook_mq_local` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` varchar(100) DEFAULT NULL COMMENT '来源id，可以用于关联某个业务表',
  `resource_type` varchar(100) DEFAULT NULL COMMENT '来源id的类型，用于指明哪种业务表',
  `template_name` varchar(100) NOT NULL COMMENT 'rabbit template spring bean name',
  `try_count` int(11) NOT NULL DEFAULT '0' COMMENT '已尝试次数',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '0-待处理，1-已完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `next_retry` datetime NOT NULL COMMENT '下次尝试时间，注意这个是指在这个时间之后，并且与当前时间差小于一定范围。',
  `max_try_count` int(11) NOT NULL DEFAULT '0' COMMENT '最大尝试次数',
  `mq_config` varchar(1024) DEFAULT NULL COMMENT 'mq的其他发送配置信息',
  `message_body` text COMMENT 'MQ的消息体，过长建议只存此表id，消费时查表使用attachment',
  `attachment` text COMMENT '消费消息时需要的参数变量等等',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `fook_mq_local_resource_id_IDX` (`resource_id`,`resource_type`) USING BTREE,
  KEY `fook_mq_local_status_time_IDX` (`status`,`next_retry`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82589 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='消息队列本地消息表，可用于定时任务的后续重试。';


-- fooku_prd_20231123.fook_multiple_list definition

CREATE TABLE `fook_multiple_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `multiple_id` int(11) DEFAULT NULL COMMENT '核销表id',
  `code` varchar(255) DEFAULT NULL COMMENT '福利核销码',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='批量核銷表';


-- fooku_prd_20231123.fook_multiple_writeoff definition

CREATE TABLE `fook_multiple_writeoff` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `code` varchar(20) DEFAULT NULL COMMENT 'ordercode',
  `type` int(11) DEFAULT NULL COMMENT '商家类型',
  `shopsid` int(11) DEFAULT NULL COMMENT '商家或门店id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `invalid_time` datetime DEFAULT NULL COMMENT '失效时间',
  `status` varchar(255) DEFAULT NULL COMMENT '状态',
  `enable` int(11) DEFAULT NULL COMMENT '有效状态',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='批量核銷關聯表';


-- fooku_prd_20231123.fook_nail_log definition

CREATE TABLE `fook_nail_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_count` int(11) DEFAULT NULL COMMENT '登錄人數',
  `order_count` int(11) DEFAULT NULL COMMENT '下單數',
  `order_code_count` int(11) DEFAULT NULL COMMENT '核銷券碼數',
  `message` varchar(255) DEFAULT NULL COMMENT '推送消息',
  `statistics_time` datetime DEFAULT NULL COMMENT '統計的日期',
  `now_time` datetime DEFAULT NULL COMMENT '執行統計時間',
  `is_send` int(2) DEFAULT '0' COMMENT '是否釘釘推送成功 1ok 0no',
  `nail_token` varchar(255) DEFAULT NULL COMMENT 'token',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '返迴的信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1395 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='推送釘釘的日志';


-- fooku_prd_20231123.fook_notice definition

CREATE TABLE `fook_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `content` varchar(255) DEFAULT NULL COMMENT '通告內容',
  `content_en` varchar(1000) DEFAULT NULL COMMENT '通告內容英文',
  `user_id` int(11) DEFAULT NULL COMMENT '管理員id',
  `enable` tinyint(4) DEFAULT NULL COMMENT '是否有效',
  `sort` tinyint(4) DEFAULT '0' COMMENT '排序默認0 0不參與排序',
  `created_at` datetime DEFAULT NULL COMMENT '修改時間',
  `updated_at` datetime DEFAULT NULL COMMENT '創建時間',
  `start_time` datetime DEFAULT NULL COMMENT '有效開始時間',
  `end_time` datetime DEFAULT NULL COMMENT '有效結束時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- fooku_prd_20231123.fook_notice_mcard_code definition

CREATE TABLE `fook_notice_mcard_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶id',
  `type` tinyint(4) DEFAULT NULL COMMENT '類型（1通知會員卡生成券、2通知會員卡核銷）',
  `order_id` int(11) DEFAULT NULL COMMENT 'order_id',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `code_id` int(11) DEFAULT NULL COMMENT '券碼id',
  `coupon_code` varchar(255) DEFAULT NULL COMMENT '優惠券碼',
  `status` tinyint(4) DEFAULT NULL COMMENT '0失敗，1成功',
  `code` varchar(255) DEFAULT NULL COMMENT '錯誤碼',
  `msg` varchar(255) DEFAULT NULL COMMENT '錯誤信息',
  `content` varchar(2550) DEFAULT NULL COMMENT '發送的數據',
  `reasoncontent` text COMMENT '返回的報文',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='通知會員卡生成券碼表/通知核銷表';


-- fooku_prd_20231123.fook_oa_code definition

CREATE TABLE `fook_oa_code` (
  `id` smallint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `oa_code` varchar(30) DEFAULT '' COMMENT 'OAcode',
  `user_id` int(11) DEFAULT NULL COMMENT '關聯後台users表id',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效(0無效，1有效)',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='OAcode表';


-- fooku_prd_20231123.fook_oa_code_log definition

CREATE TABLE `fook_oa_code_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `oa_code_id` int(11) DEFAULT NULL COMMENT 'oa_code_id',
  `change_oa_code_id` int(11) DEFAULT NULL COMMENT '更改後的oa_code_id',
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶表id',
  `change_user_id` int(11) DEFAULT NULL COMMENT '更改後的用戶id',
  `admin_id` int(11) DEFAULT NULL COMMENT '操作的管理員id',
  `remark` varchar(255) DEFAULT NULL COMMENT '描述備註',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `type` varchar(255) DEFAULT NULL COMMENT '操作類型（business、user、aocode）',
  `phone` varchar(255) DEFAULT NULL COMMENT '手機號碼',
  `email` varchar(255) DEFAULT NULL COMMENT '郵箱',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9480 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='AO用戶調整記錄表';


-- fooku_prd_20231123.fook_order_reconciliation definition

CREATE TABLE `fook_order_reconciliation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) DEFAULT NULL COMMENT '管理員id',
  `start_time` datetime DEFAULT NULL COMMENT '開始時間',
  `end_time` datetime DEFAULT NULL COMMENT '結束時間',
  `status` tinyint(2) DEFAULT '0' COMMENT '1數據對不上、0完全對上',
  `count` int(11) DEFAULT '0' COMMENT '總條數',
  `success_count` int(11) DEFAULT '0' COMMENT '對賬成功條數',
  `fail_count` int(11) DEFAULT '0' COMMENT '對賬失敗條數',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `update_time` datetime DEFAULT NULL COMMENT '修改時間',
  `processing_status` tinyint(2) DEFAULT '-1' COMMENT '處理狀態（2:生成excel上傳oss成功、1:處理完成、0:導入excel數據完成、-1:尚未處理）',
  `url` varchar(255) DEFAULT NULL COMMENT 'excel上傳的路徑',
  `excel_export` varchar(255) DEFAULT NULL COMMENT 'excel生成的路徑',
  `flow_excel_export` varchar(255) DEFAULT NULL COMMENT '支付流水excel生成的路徑',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單對賬週期表';


-- fooku_prd_20231123.fook_order_reconciliation_data definition

CREATE TABLE `fook_order_reconciliation_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reconciliation_id` int(11) DEFAULT NULL COMMENT '訂單對賬統計表id',
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `sellerid` int(11) DEFAULT NULL COMMENT '商家Id',
  `type` int(11) DEFAULT NULL COMMENT '訂單類型（1、福利，2、預約，3、特殊福利）',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `create_time` datetime DEFAULT NULL COMMENT '下單時間',
  `payment_type` int(11) DEFAULT NULL COMMENT '付款方式(0M幣,1支付寶支付,2微信支付,3信用卡支付,4澳門通支付)',
  `payment_transaction` varchar(255) DEFAULT NULL COMMENT '付款流水號(支付機構支付流水號)',
  `status` int(11) DEFAULT NULL COMMENT '訂單狀態（1、未付款，2、已付款,3、失效訂單）',
  `complete_time` datetime DEFAULT NULL COMMENT '訂單完成時間',
  `order_transaction` varchar(100) DEFAULT NULL COMMENT '訂單交易號(支付訂單號)',
  `mpayintegral` int(11) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `score` decimal(8,2) DEFAULT NULL COMMENT 'M幣',
  `platform` int(11) DEFAULT NULL COMMENT '平台(1微信,2Android,3IOS,4PC)',
  `order_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單金額',
  `currency` varchar(10) DEFAULT NULL COMMENT '訂單金額幣種',
  `total_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單最終金额',
  `total_amount_currency` varchar(10) DEFAULT NULL COMMENT '訂單最終金额幣種',
  `transaction_amount` decimal(8,2) DEFAULT NULL COMMENT '支付平台交易金額',
  `transaction_amount_currency` varchar(10) DEFAULT NULL COMMENT '支付平台交易金額幣種',
  `payment_amount` decimal(8,2) DEFAULT NULL COMMENT '客戶實際付款(支付金額)',
  `pay_platform_fees` decimal(8,2) DEFAULT NULL COMMENT '支付平臺手續費',
  `payment_institution_amount` decimal(8,2) DEFAULT NULL COMMENT '支付機構金額',
  `payment_amount_currency` varchar(10) DEFAULT NULL COMMENT '客戶實際付款幣種',
  `payment_time` datetime DEFAULT NULL COMMENT '客戶付款時間',
  `bank_charges` decimal(8,2) DEFAULT NULL COMMENT '銀行費用',
  `bank_settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '銀行結算金額',
  `refundid` int(11) DEFAULT NULL COMMENT '退款ID',
  `ordercode_status` int(11) DEFAULT '0' COMMENT '核銷狀態(0沒核銷，1已核銷，2部分核銷, 3子訂單全核銷)',
  `refund_status` int(11) DEFAULT '0' COMMENT '退款狀態(0 沒退款，1已退款，2部分退款，3子訂單全退款)',
  `is_mpay` varchar(255) DEFAULT NULL COMMENT '是否mpay',
  `pay_tradeno` varchar(100) DEFAULT NULL COMMENT 'MPay支付訂單號',
  `score_tradeno` varchar(100) DEFAULT NULL COMMENT 'mCoin積分支付訂單號',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單id',
  `settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '已結算金額',
  `pending_amount` decimal(8,2) DEFAULT NULL COMMENT '待結算金額',
  `settlement_accounting` decimal(8,2) DEFAULT NULL COMMENT '已核銷金額',
  `pending_accounting` decimal(8,2) DEFAULT NULL COMMENT '待核銷金額',
  `refund_amount` decimal(8,2) DEFAULT NULL COMMENT '退款金額',
  `approval_amount` decimal(8,2) DEFAULT NULL COMMENT '未退款金額(審批中)',
  `commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `total_merch_settle_amount` decimal(8,2) DEFAULT NULL COMMENT '總結算金額',
  `mpayintegral_exchange_amount` decimal(8,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換金額',
  `mpayintegral_fees` decimal(8,2) DEFAULT NULL COMMENT '澳門通mCoin積分手續費',
  `mpayintegral_pay_amount` decimal(8,2) DEFAULT NULL COMMENT '澳門通mCoin支付金額',
  `description` tinyint(2) DEFAULT NULL COMMENT '描述（1：MPay有,mCoin無、2：MPay無,mCoin有、3：一致）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `reconciliation_id_Normal` (`reconciliation_id`) USING BTREE,
  KEY `pay_tradeno_Normal` (`pay_tradeno`) USING BTREE,
  KEY `score_tradeno_Normal` (`score_tradeno`) USING BTREE,
  KEY `order_no_Normal` (`order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48204 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單統計結算交易數據表';


-- fooku_prd_20231123.fook_order_reconciliation_excel definition

CREATE TABLE `fook_order_reconciliation_excel` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `reconciliation_id` int(11) DEFAULT NULL COMMENT 'fook_order_reconciliation表id',
  `a1` int(11) DEFAULT NULL COMMENT '交易序號',
  `a2` varchar(255) DEFAULT NULL COMMENT '未知',
  `a3` varchar(255) DEFAULT NULL COMMENT '交易渠道',
  `a4` varchar(255) DEFAULT NULL COMMENT '交易類型',
  `a5` varchar(255) DEFAULT NULL COMMENT '會員賬號',
  `a6` varchar(255) DEFAULT NULL COMMENT '支付訂單號',
  `a7` decimal(13,2) DEFAULT NULL COMMENT '交易金額',
  `a8` varchar(255) DEFAULT NULL COMMENT '未知',
  `a9` varchar(255) DEFAULT NULL COMMENT '未知',
  `a10` decimal(13,2) DEFAULT NULL COMMENT '商戶優惠金額',
  `a11` decimal(13,2) DEFAULT NULL COMMENT '澳門通優惠金額',
  `a12` decimal(13,2) DEFAULT NULL COMMENT '實際交易金額',
  `a13` decimal(13,2) DEFAULT NULL COMMENT '對沖/調整金額',
  `a14` decimal(13,2) DEFAULT NULL COMMENT '結算金額',
  `a15` datetime DEFAULT NULL COMMENT '交易日期時間',
  `a16` date DEFAULT NULL COMMENT '結算日期',
  `a17` varchar(255) DEFAULT NULL COMMENT '未知',
  `created_time` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `reconciliation_id_NORMAL` (`reconciliation_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36069 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


-- fooku_prd_20231123.fook_order_settlement definition

CREATE TABLE `fook_order_settlement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `total_amount` decimal(13,2) DEFAULT NULL COMMENT '訂單金額',
  `score` decimal(10,2) DEFAULT NULL COMMENT 'M幣',
  `mpayintegral` decimal(13,2) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_cycle` varchar(255) DEFAULT NULL COMMENT '結算週期',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1893 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單統計結算週期表';


-- fooku_prd_20231123.fook_order_settlement_data definition

CREATE TABLE `fook_order_settlement_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `sellerid` int(11) DEFAULT NULL COMMENT '商家Id',
  `type` int(11) DEFAULT NULL COMMENT '訂單類型（1、福利，2、預約，3、特殊福利）',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `create_time` datetime DEFAULT NULL COMMENT '下單時間',
  `payment_type` int(11) DEFAULT NULL COMMENT '付款方式(0M幣,1支付寶支付,2微信支付,3信用卡支付,4澳門通支付)',
  `payment_transaction` varchar(255) DEFAULT NULL COMMENT '付款流水號(支付機構支付流水號)',
  `status` int(11) DEFAULT NULL COMMENT '訂單狀態（1、未付款，2、已付款,3、失效訂單）',
  `complete_time` datetime DEFAULT NULL COMMENT '訂單完成時間',
  `order_transaction` varchar(100) DEFAULT NULL COMMENT '訂單交易號(支付訂單號)',
  `mpayintegral` int(11) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `score` decimal(8,2) DEFAULT NULL COMMENT 'M幣',
  `platform` int(11) DEFAULT NULL COMMENT '平台(1微信,2Android,3IOS,4PC)',
  `order_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單金額',
  `currency` varchar(10) DEFAULT NULL COMMENT '訂單金額幣種',
  `total_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單最終金额',
  `total_amount_currency` varchar(10) DEFAULT NULL COMMENT '訂單最終金额幣種',
  `transaction_amount` decimal(8,2) DEFAULT NULL COMMENT '支付平台交易金額',
  `transaction_amount_currency` varchar(10) DEFAULT NULL COMMENT '支付平台交易金額幣種',
  `payment_amount` decimal(8,2) DEFAULT NULL COMMENT '客戶實際付款',
  `payment_amount_currency` varchar(10) DEFAULT NULL COMMENT '客戶實際付款幣種',
  `payment_time` datetime DEFAULT NULL COMMENT '客戶付款時間',
  `bank_charges` decimal(8,2) DEFAULT NULL COMMENT '銀行費用',
  `bank_settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '銀行結算金額',
  `refundid` int(2) DEFAULT NULL COMMENT '退款ID',
  `ordercode_status` int(2) DEFAULT '0' COMMENT '核銷狀態(0沒核銷，1已核銷，2部分核銷, 3子訂單全核銷)',
  `refund_status` int(2) DEFAULT '0' COMMENT '退款狀態(0 沒退款，1已退款，2部分退款，3子訂單全退款)',
  `is_mpay` varchar(255) DEFAULT NULL COMMENT '是否mpay',
  `pay_tradeno` varchar(100) DEFAULT NULL COMMENT 'MPay支付訂單號',
  `score_tradeno` varchar(100) DEFAULT NULL COMMENT 'mCoin積分支付訂單號',
  `order_settlement_id` int(11) DEFAULT NULL COMMENT '訂單統計結算表id',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單id',
  `settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '已結算金額',
  `pending_amount` decimal(8,2) DEFAULT NULL COMMENT '待結算金額',
  `settlement_accounting` decimal(8,2) DEFAULT NULL COMMENT '已核銷金額',
  `pending_accounting` decimal(8,2) DEFAULT NULL COMMENT '待核銷金額',
  `refund_amount` decimal(8,2) DEFAULT NULL COMMENT '退款金額',
  `approval_amount` decimal(8,2) DEFAULT NULL COMMENT '未退款金額(審批中)',
  `commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `point_ratio` int(11) unsigned DEFAULT '300' COMMENT '福利積分比例',
  `total_merch_settle_amount` decimal(8,2) DEFAULT NULL COMMENT '總結算金額',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `areaid` (`areaid`) USING BTREE,
  KEY `sellerid` (`sellerid`) USING BTREE,
  KEY `order_settlement_id` (`order_settlement_id`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `refundid` (`refundid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6444031 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單統計結算交易數據表';


-- fooku_prd_20231123.fook_order_settlement_data_processs definition

CREATE TABLE `fook_order_settlement_data_processs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `order_settlement_id` int(11) DEFAULT NULL COMMENT '訂單統計結算週期表ID',
  `excel_url` varchar(255) DEFAULT NULL COMMENT 'Excel下载链接',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單統計結算交易數據表(数据量超过2W条时记录，后台定时执行生成报表)';


-- fooku_prd_20231123.fook_pay_cybersource definition

CREATE TABLE `fook_pay_cybersource` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT NULL,
  `merchant_id` varchar(255) DEFAULT NULL,
  `access_key` varchar(255) DEFAULT NULL,
  `transaction_type` varchar(255) DEFAULT NULL,
  `card_type` varchar(255) DEFAULT NULL,
  `card_number` varchar(255) DEFAULT NULL,
  `card_expiry_date` varchar(255) DEFAULT NULL,
  `currency` varchar(255) DEFAULT NULL,
  `token` varchar(255) DEFAULT NULL,
  `auth_time` varchar(255) DEFAULT NULL,
  `auth_trans_ref_no` varchar(255) DEFAULT NULL,
  `bill_trans_ref_no` varchar(255) DEFAULT NULL,
  `reference_number` varchar(255) DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='cybersource记录表(暫時沒使用)';


-- fooku_prd_20231123.fook_pay_log definition

CREATE TABLE `fook_pay_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `uuid` varchar(255) DEFAULT NULL COMMENT '唯一标识',
  `uid` int(11) DEFAULT NULL COMMENT '用户ID',
  `orderid` int(11) DEFAULT NULL COMMENT '订单ID',
  `tradeno` varchar(255) DEFAULT NULL COMMENT '支付平台的支付订单号',
  `out_request_no` varchar(255) DEFAULT NULL COMMENT '退款的标识码',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '支付金额',
  `fee` decimal(10,2) DEFAULT NULL COMMENT '手续费',
  `currency` varchar(10) DEFAULT NULL COMMENT '支付币种',
  `oparemtion` varchar(10) DEFAULT NULL COMMENT '操作类型（支付pay，退款refund）',
  `type` varchar(20) DEFAULT NULL COMMENT '支付类型(wachat,alipay,macau,cybersource)',
  `status` int(11) DEFAULT NULL COMMENT '支付状态',
  `createtime` datetime DEFAULT NULL COMMENT '支付创建时间',
  `reason` varchar(255) DEFAULT NULL COMMENT '返回备注',
  `content` varchar(2500) DEFAULT NULL COMMENT '发送的报文',
  `reasoncontent` text COMMENT '返回的报文',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `paytime` datetime DEFAULT NULL COMMENT '支付时间（支付平台返回的时间）',
  `refund_id` int(11) DEFAULT NULL COMMENT '退款表id',
  `points` int(11) DEFAULT NULL COMMENT '混合支付積分',
  `mpay_coupons_type` int(11) DEFAULT '0' COMMENT 'Mpay優惠券类型0：普通购买福利类型。1领取mpay 优惠券',
  `mpay_coupons_code` varchar(100) DEFAULT NULL COMMENT 'Mpay優惠券碼',
  `mpay_coupons_reasoncontent` text COMMENT 'Mpay優惠券領取返回的內容',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `tradeno` (`tradeno`) USING BTREE,
  KEY `fook_pay_log__index_mansql` (`updatetime`,`oparemtion`,`status`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `idx_type_status_oparemtion_createtime` (`type`,`status`,`oparemtion`,`createtime`) USING BTREE,
  KEY `uuid` (`uuid`) USING BTREE,
  KEY `refund_id` (`refund_id`) USING BTREE,
  KEY `checkcouponspayTask` (`status`,`type`,`oparemtion`,`mpay_coupons_type`,`mpay_coupons_code`,`createtime`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3256407 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)支付平臺返回的流水記錄';


-- fooku_prd_20231123.fook_payment_flow definition

CREATE TABLE `fook_payment_flow` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `reconciliation_id` int(11) DEFAULT NULL COMMENT '訂單對賬統計表id',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單ID',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `tradeno` varchar(255) DEFAULT NULL COMMENT '流水號（積分流水號、錢支付流水號、）',
  `oparemtion` varchar(50) DEFAULT NULL COMMENT '操作（pay、refund）',
  `amount` decimal(8,2) DEFAULT '0.00' COMMENT '金額',
  `score` decimal(8,2) DEFAULT '0.00' COMMENT '積分',
  `fee` decimal(8,2) DEFAULT '0.00' COMMENT '手續費',
  `status` tinyint(4) DEFAULT NULL COMMENT '狀態(1:已支付/已退款)',
  `category` varchar(255) DEFAULT NULL COMMENT '类别（mCoin、mPay）',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `updatetime` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `reconciliation_id` (`reconciliation_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=88274 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='財務流水報表';


-- fooku_prd_20231123.fook_platform_bankcode definition

CREATE TABLE `fook_platform_bankcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '銀行代碼表Id',
  `code` varchar(30) DEFAULT NULL COMMENT '標題',
  `name` varchar(120) DEFAULT NULL COMMENT '名稱',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效(0,無效，1.有效)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='銀行代碼表';


-- fooku_prd_20231123.fook_platform_cbscreditcard definition

CREATE TABLE `fook_platform_cbscreditcard` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'CyberSource用戶信用卡表Id',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `merchantid` varchar(255) DEFAULT NULL COMMENT '商戶號',
  `access_key` varchar(255) DEFAULT NULL COMMENT '訪問Key',
  `transaction_type` varchar(255) DEFAULT NULL COMMENT '交易類型',
  `card_type` varchar(255) DEFAULT NULL COMMENT '卡類型 001 visa',
  `card_number` varchar(255) DEFAULT NULL COMMENT '卡號',
  `card_expiry_date` varchar(255) DEFAULT NULL COMMENT '有效期',
  `currency` varchar(255) DEFAULT NULL COMMENT '貨幣',
  `token` varchar(255) DEFAULT NULL COMMENT 'token',
  `auth_time` varchar(255) DEFAULT NULL COMMENT '授權時間',
  `auth_trans_ref_no` varchar(255) DEFAULT NULL COMMENT '授權號',
  `bill_trans_ref_no` varchar(255) DEFAULT NULL COMMENT '賬單號',
  `reference_number` varchar(255) DEFAULT NULL COMMENT '參考號',
  `transactionid` varchar(255) DEFAULT NULL COMMENT '交易號',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效(0.無效1.有效)',
  `e_commerce_indicator` varchar(10) DEFAULT NULL,
  `enroll_veres_enrolled` varchar(50) DEFAULT NULL,
  `pares_status` varchar(50) DEFAULT NULL,
  `cavv` varchar(50) DEFAULT NULL,
  `eci` varchar(10) DEFAULT NULL,
  `xid` varchar(50) DEFAULT NULL,
  `validate_result` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1035 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用户cybersource的信息';


-- fooku_prd_20231123.fook_platform_intellisense definition

CREATE TABLE `fook_platform_intellisense` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `label` varchar(150) DEFAULT NULL COMMENT '关键字',
  `label_type` int(11) DEFAULT NULL COMMENT '搜索类型',
  `param_key` varchar(30) DEFAULT NULL,
  `param_value` varchar(50) DEFAULT NULL,
  `add_time` timestamp NULL DEFAULT NULL,
  `user_source` int(11) DEFAULT NULL,
  `user_ip` varchar(50) DEFAULT NULL,
  `enable` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=617972 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用户搜索记录';


-- fooku_prd_20231123.fook_platform_order definition

CREATE TABLE `fook_platform_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `sellerid` int(11) DEFAULT NULL COMMENT '商家Id',
  `type` int(11) DEFAULT NULL COMMENT '訂單類型（1、福利，2、預約，3、特殊福利）',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `create_time` datetime DEFAULT NULL COMMENT '下單時間',
  `payment_type` int(11) DEFAULT NULL COMMENT '付款方式(0M幣,1支付寶支付,2微信支付,3信用卡支付,4澳門通支付)',
  `payment_transaction` varchar(255) DEFAULT NULL COMMENT '付款流水號(支付機構支付流水號)',
  `status` int(11) DEFAULT NULL COMMENT '訂單狀態（1、未付款，2、已付款,3、失效訂單，4、退款訂單）',
  `complete_time` datetime DEFAULT NULL COMMENT '訂單完成時間',
  `order_transaction` varchar(100) DEFAULT NULL COMMENT '訂單交易號(支付訂單號)',
  `mpayintegral` int(11) DEFAULT NULL COMMENT 'mpay积分',
  `score` decimal(8,2) DEFAULT NULL COMMENT '積分(M幣)',
  `platform` int(11) DEFAULT NULL COMMENT '平台(1微信,2Android,3IOS,4PC)',
  `order_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單金額',
  `point_ratio` int(11) unsigned DEFAULT '300' COMMENT '福利積分比例',
  `currency` varchar(10) DEFAULT NULL COMMENT '訂單金額幣種',
  `total_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單最終金额',
  `total_amount_currency` varchar(10) DEFAULT NULL COMMENT '訂單最終金额幣種',
  `transaction_amount` decimal(8,2) DEFAULT NULL COMMENT '支付平台交易金額',
  `transaction_amount_currency` varchar(10) DEFAULT NULL COMMENT '支付平台交易金額幣種',
  `payment_amount` decimal(8,2) DEFAULT NULL COMMENT '客戶實際付款',
  `payment_amount_currency` varchar(10) DEFAULT NULL COMMENT '客戶實際付款幣種',
  `payment_time` datetime DEFAULT NULL COMMENT '客戶付款時間',
  `query_mark` int(11) DEFAULT NULL,
  `account_suffix` varchar(24) DEFAULT NULL,
  `expiration_month` int(11) DEFAULT NULL,
  `expiration_year` int(11) DEFAULT NULL,
  `card_type` varchar(50) DEFAULT NULL,
  `credit_cardid` int(11) DEFAULT NULL,
  `reason_code` varchar(100) DEFAULT NULL,
  `process_type` int(11) DEFAULT NULL,
  `bank_charges` decimal(8,2) DEFAULT NULL COMMENT '銀行費用',
  `bank_settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '銀行結算金額',
  `ordercode_id` int(2) DEFAULT NULL COMMENT '關聯的核銷碼ID',
  `refundid` int(2) DEFAULT NULL COMMENT '退款ID',
  `ordercode_status` int(2) DEFAULT '0' COMMENT '核銷狀態(0沒核銷，1已核銷，2部分核銷, 3子訂單全核銷)',
  `refund_status` int(2) DEFAULT '0' COMMENT '退款狀態(0 沒退款，1已退款，2部分退款，3子訂單全退款、4異常單退款)',
  `is_mpay` varchar(255) DEFAULT NULL COMMENT '是否mpay',
  `is_member` tinyint(4) DEFAULT '0' COMMENT '是否member 1是0否 默认0',
  `memberintegral` decimal(8,2) DEFAULT NULL COMMENT '會員卡積分',
  `is_voucher` tinyint(4) DEFAULT '0' COMMENT '是否派券訂單（1是、0否、默认0） ',
  `mpay_coupons_status` int(11) DEFAULT '0' COMMENT 'Mpay優惠券领劵状态 1：已领取 2:未領取',
  `mpay_coupons_code` varchar(100) DEFAULT NULL COMMENT 'Mpay優惠券领劵码',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  `session_id` int(11) DEFAULT NULL COMMENT '抢购场次ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sellerid` (`sellerid`) USING BTREE,
  KEY `ordercode_id` (`ordercode_id`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `refundid` (`refundid`) USING BTREE,
  KEY `idx_sellerid_createtime` (`sellerid`,`create_time`) USING BTREE,
  KEY `order_no` (`order_no`) USING BTREE,
  KEY `payment_time` (`payment_time`) USING BTREE,
  KEY `create_time_idx` (`create_time`) USING BTREE,
  KEY `idx_userid_create_time` (`userid`,`create_time`) USING BTREE,
  KEY `idx_is_member_order_transaction` (`is_member`,`order_transaction`) USING BTREE,
  KEY `idx_order_transaction` (`order_transaction`)
) ENGINE=InnoDB AUTO_INCREMENT=4264768 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)訂單表';


-- fooku_prd_20231123.fook_platform_ordercode definition

CREATE TABLE `fook_platform_ordercode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `code` varchar(50) DEFAULT NULL COMMENT '核銷碼',
  `status` int(11) DEFAULT NULL COMMENT '福利狀態（1未使用，2已使用,3.已失效）',
  `shopid` int(11) DEFAULT NULL COMMENT '核銷店鋪ID',
  `user_time` datetime DEFAULT NULL COMMENT '使用時間',
  `verification_mode` int(11) DEFAULT NULL COMMENT '核銷方式(1微信核銷,2核銷密碼核銷,3APP,4澳门通Pos核銷,5亚洲万里通定时核销,6會員卡核銷,7PC端核銷)',
  `is_comment` int(11) DEFAULT NULL COMMENT '是否已評價(0否、1是)',
  `is_settlement` int(11) DEFAULT '0' COMMENT '是否結算(0否1是)',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_id` int(11) DEFAULT NULL COMMENT '結算記錄Id',
  `merch_settle_amount` decimal(8,2) DEFAULT NULL COMMENT '商戶結算金額',
  `merch_settle_amount_currency` varchar(10) DEFAULT NULL COMMENT '商戶結算金額幣種',
  `refund_status` int(11) DEFAULT NULL COMMENT '退款狀態（1、未退款，2、退款中，3、退款已完成）',
  `apportion_bill_amount` decimal(8,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `apportion_bill_final_amount` decimal(8,2) DEFAULT NULL COMMENT '攤派付款金額',
  `apportion_mpayintegral` int(11) DEFAULT NULL COMMENT '攤派Mpay积分',
  `apportion_momecoins` decimal(8,2) DEFAULT NULL COMMENT '攤派抵扣M幣',
  `apportion_commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT '關聯訂單詳情ID',
  `refundid` int(11) DEFAULT NULL COMMENT '退款ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `apportion_memberintegral` decimal(8,2) DEFAULT NULL COMMENT '攤派會員卡積分',
  `is_voucher` tinyint(4) DEFAULT '0' COMMENT '是否派券（1是、0否、默认0）',
  `preferentialAmount` decimal(8,1) unsigned DEFAULT '0.0' COMMENT '優惠金額，交易核銷券使用',
  `requestOrderNo` varchar(64) DEFAULT NULL COMMENT '請求訂單號【用於重複發送核銷撤銷時校驗】',
  `requestOrderNoStatus` tinyint(4) DEFAULT '0' COMMENT '請求訂單號狀態，0未處理，1核銷完成，2撤銷完成，3核銷中，4撤銷中,5核銷失敗，6撤銷失敗',
  `is_a_open` tinyint(1) DEFAULT '0' COMMENT '是否A+券碼，0否，1是',
  `is_untie` tinyint(4) DEFAULT '0' COMMENT '是否解綁（1是、0否）',
  `is_exported` tinyint(4) DEFAULT '0' COMMENT '導出狀態（0未導出、1已導出）',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  `tracking_no` varchar(64) DEFAULT NULL COMMENT '商家核销订单号',
  `business_redeem_time` datetime DEFAULT NULL COMMENT '商家端核销时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_orderid_status` (`orderid`,`status`) USING BTREE,
  KEY `code` (`code`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `orderinfo_id` (`orderinfo_id`) USING BTREE,
  KEY `shopid` (`shopid`) USING BTREE,
  KEY `idx_status_code` (`status`,`code`) USING BTREE,
  KEY `idx_usertime` (`user_time`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `refundid` (`refundid`) USING BTREE,
  KEY `idx_code_orderinfoid` (`code`,`orderinfo_id`) USING BTREE,
  KEY `requestOrderNo` (`requestOrderNo`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3987667 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)訂單核銷碼記錄表';


-- fooku_prd_20231123.fook_platform_ordercode_log definition

CREATE TABLE `fook_platform_ordercode_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `shopid` int(11) DEFAULT NULL COMMENT '核銷門店id',
  `user_time` datetime DEFAULT NULL COMMENT '核銷時間',
  `code` varchar(50) DEFAULT ' ' COMMENT '核銷碼',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單id',
  `userid` int(11) DEFAULT NULL COMMENT '用户ID',
  `ordercodeid` int(11) DEFAULT NULL COMMENT 'ordercode表id',
  `callback_msg` varchar(1024) DEFAULT NULL COMMENT '核销三方回调消息',
  `external_ref_no` varchar(64) DEFAULT NULL COMMENT '外部系统参照编号',
  `terminal_code` varchar(200) DEFAULT NULL COMMENT '澳门通终端号',
  `branch_code` varchar(200) DEFAULT NULL COMMENT '澳门通门店号',
  `merchant_code` varchar(200) DEFAULT NULL COMMENT '澳门通商户号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_ordercodeid_userid` (`ordercodeid`,`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2987880 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='##用戶核銷日志表##';


-- fooku_prd_20231123.fook_platform_ordercoupon definition

CREATE TABLE `fook_platform_ordercoupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '關聯Id',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `couponid` int(11) DEFAULT NULL COMMENT '活動優惠券兌換記錄Id',
  `status` int(11) DEFAULT NULL COMMENT '兌換碼狀態',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT '關聯子訂單ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='訂單使用優惠券';


-- fooku_prd_20231123.fook_platform_orderinfo definition

CREATE TABLE `fook_platform_orderinfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '訂單詳情Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `prodcutid` int(11) DEFAULT NULL COMMENT '產品Id',
  `product_price` decimal(8,2) DEFAULT NULL COMMENT '產品價格',
  `image_snapshots` varchar(255) DEFAULT NULL COMMENT '產品主圖快照',
  `title_snapshots` varchar(255) DEFAULT NULL COMMENT '產品標題快照',
  `desc_snapshots` varchar(255) DEFAULT NULL COMMENT '產品描述快照',
  `vaild_start_time` datetime DEFAULT NULL COMMENT '有效期開始時間',
  `vaild_end_time` datetime DEFAULT NULL COMMENT '有效期結束時間',
  `details_snapshots` varchar(255) DEFAULT NULL COMMENT '圖文詳情快照',
  `number` int(11) DEFAULT NULL COMMENT '數量',
  `is_weekend` int(2) DEFAULT NULL COMMENT '是否周六日可用（1可用0不可用）',
  `no_useday` varchar(255) DEFAULT NULL COMMENT '不可用日期',
  `type` int(11) DEFAULT NULL COMMENT '產品類型(1、撚手小菜,2、新菜式,3、現金券,4、1蚊福利,5、積分換領,6、M 幣現金券)',
  `img` varchar(255) DEFAULT NULL COMMENT '福利圖片',
  `retail_price` decimal(8,2) DEFAULT NULL COMMENT '門市價',
  `tnc` text COMMENT '細則',
  `is_vacation` int(2) DEFAULT NULL COMMENT '是否假期可用(1可用0不可用)',
  `is_allow_refund` int(2) DEFAULT NULL COMMENT '是否允許退款（1、允許，0、不允許）',
  `fee_rate` decimal(8,2) DEFAULT NULL COMMENT '費率(%)',
  `buy_start_time` datetime DEFAULT NULL,
  `buy_end_time` datetime DEFAULT NULL,
  `valid_mode` int(11) DEFAULT NULL,
  `day_num` int(11) DEFAULT NULL,
  `applicable_shops` varchar(200) DEFAULT NULL,
  `is_hot` int(11) DEFAULT NULL COMMENT '是否熱門搶福 (1是，0否)',
  `momecoins_option` varchar(200) DEFAULT NULL COMMENT 'M幣選項',
  `threshold` int(11) DEFAULT NULL COMMENT '搶福閾值',
  `is_great` int(11) DEFAULT NULL COMMENT '是否搶福 (1是，0否)',
  `is_redeen` int(11) DEFAULT NULL COMMENT '是否兌換代碼福利 (1是，0否)',
  `is_momecoin` int(11) DEFAULT NULL COMMENT '是否M幣 (1是，0否)',
  `ordercode_id` int(11) DEFAULT NULL COMMENT '對應的核銷碼ID',
  `refundid` int(11) DEFAULT NULL COMMENT '對應的退款ID',
  `order_amount` decimal(8,2) unsigned zerofill DEFAULT NULL COMMENT '订单金额',
  `score` int(11) unsigned zerofill DEFAULT NULL COMMENT '使用的M币',
  `mpayintegral` int(11) DEFAULT NULL COMMENT 'may积分',
  `memberintegral` decimal(8,2) DEFAULT NULL COMMENT '會員卡積分',
  `miles_first` varchar(40) DEFAULT NULL COMMENT '會員姓',
  `miles_name` varchar(40) DEFAULT NULL COMMENT '會員名',
  `miles_member` bigint(20) DEFAULT NULL COMMENT '會員卡號',
  `miles_milage` decimal(8,2) DEFAULT NULL COMMENT '兌換里程數',
  `is_voucher` tinyint(4) DEFAULT '0' COMMENT '是否派券訂單（1是、0否、默认0） ',
  `is_settlement` tinyint(4) DEFAULT '1' COMMENT '是否結算(1是，0否)',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uidx_orderid` (`orderid`) USING BTREE,
  KEY `idx_prodcutid_orderid_number` (`prodcutid`,`orderid`,`number`) USING BTREE,
  KEY `idx_orderid_prodcutid` (`orderid`,`prodcutid`) USING BTREE,
  KEY `ordercode_id` (`ordercode_id`) USING BTREE,
  KEY `idx_miles_member_type` (`miles_member`,`type`) USING BTREE,
  KEY `prodcutid` (`prodcutid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4264429 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)訂單詳細信息表';


-- fooku_prd_20231123.fook_platform_orderrefund definition

CREATE TABLE `fook_platform_orderrefund` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '退款訂單Id',
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
  `businessid` int(11) DEFAULT NULL COMMENT '商家Id',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `ordercode_id` varchar(255) DEFAULT NULL COMMENT '訂單核銷碼記錄Ids',
  `refund_orderno` varchar(255) DEFAULT NULL COMMENT '退款訂單號',
  `refund_amount` decimal(8,2) DEFAULT NULL COMMENT '退款金額',
  `mpayintegral` decimal(10,0) DEFAULT NULL COMMENT 'mpay積分',
  `currency` varchar(10) DEFAULT NULL COMMENT '幣種',
  `application_time` datetime DEFAULT NULL COMMENT '申請退款時間',
  `status` int(11) DEFAULT NULL COMMENT '狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款，9轉入人工退款，10人工退款成功）',
  `platform_deal_status` tinyint(1) DEFAULT '0' COMMENT '支付平台處理狀態(0-审核中 1-處理 2-不成功 3-成功)',
  `mpayintegral_status` tinyint(1) DEFAULT NULL COMMENT 'MPay積分處理狀態(0-审核中 1-處理 2-不成功 3-成功)',
  `refund_transacation` varchar(100) DEFAULT NULL COMMENT '退款流水號',
  `refund_time` datetime DEFAULT NULL COMMENT '成功退款時間',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因(後台手動退款原因)',
  `refund_resson_front` varchar(255) DEFAULT NULL COMMENT '前台退款原因',
  `return_route` int(11) DEFAULT NULL COMMENT '退回途徑(0.M幣 1.MPay 2.信用卡 3.微信 4.支付寶)',
  `third_refund_code` varchar(100) DEFAULT NULL COMMENT '第三方退款結果',
  `refund_score` decimal(8,2) NOT NULL COMMENT '退款返還積分金额',
  `actual_refund_amount` decimal(8,2) DEFAULT NULL COMMENT '實際退款金額',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT '關聯子訂單ID',
  `user_remark` varchar(255) DEFAULT ' ' COMMENT '用戶備註',
  `customer_remark` varchar(255) DEFAULT ' ' COMMENT '客服備註',
  `score_id` int(11) DEFAULT NULL COMMENT 'score_log表ID',
  `pay_id` int(11) DEFAULT NULL COMMENT 'pay_log表ID',
  `refund_coupons_status` int(11) DEFAULT NULL COMMENT 'Mpay優惠券退款狀態碼 1：已退  2：退劵失敗',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `refund_time_idx` (`refund_time`) USING BTREE,
  KEY `orderid_idx` (`orderid`) USING BTREE,
  KEY `application_time_idx` (`application_time`) USING BTREE,
  KEY `refund_orderno_idx` (`refund_orderno`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=144355 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='退款表';


-- fooku_prd_20231123.fook_platform_orderrefund_record definition

CREATE TABLE `fook_platform_orderrefund_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '操作記錄Id',
  `refundid` int(11) DEFAULT NULL COMMENT '退款訂單Id',
  `old_status` int(11) DEFAULT NULL COMMENT '舊狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）',
  `new_status` int(11) DEFAULT NULL COMMENT '新狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款）',
  `operation_time` datetime DEFAULT NULL COMMENT '操作時間',
  `operation_type` int(11) DEFAULT NULL COMMENT '操作者身份（1、平台管理員，2、第三方支付平台，3、用戶）',
  `operation_id` int(11) DEFAULT NULL COMMENT '操作管理員Id',
  `remark` varchar(255) DEFAULT NULL COMMENT '備註',
  `out_request_no` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `fook_platform_orderrefund_record_refundid_IDX` (`refundid`) USING BTREE,
  KEY `idx_operation_time` (`operation_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=338898 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='退款历史表';


-- fooku_prd_20231123.fook_platform_suggest definition

CREATE TABLE `fook_platform_suggest` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `label` varchar(150) DEFAULT NULL COMMENT '关键字',
  `label_type` int(11) DEFAULT NULL COMMENT '搜索类型',
  `param_key` varchar(30) DEFAULT NULL COMMENT 'param_key',
  `add_time` timestamp NULL DEFAULT NULL COMMENT '新增時間',
  `user_source` int(11) DEFAULT NULL,
  `user_ip` varchar(50) DEFAULT NULL COMMENT 'ip',
  `enable` int(11) DEFAULT NULL COMMENT '狀態',
  `order` int(2) DEFAULT NULL COMMENT '排序',
  `setup_name` varchar(50) DEFAULT NULL COMMENT '熱詞設置人',
  `popularity` int(11) DEFAULT '0' COMMENT '人氣值',
  `product_count` int(11) DEFAULT '0' COMMENT '熱搜福利數量',
  `stores_count` int(11) DEFAULT '0' COMMENT '熱搜門店數量',
  `browse_count` int(11) DEFAULT '0' COMMENT '瀏覽量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用戶搜索建議';


-- fooku_prd_20231123.fook_platform_unusualorder definition

CREATE TABLE `fook_platform_unusualorder` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `sellerid` int(11) DEFAULT NULL COMMENT '商家Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單id',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `create_time` datetime DEFAULT NULL COMMENT '下單時間',
  `payment_type` int(11) DEFAULT NULL COMMENT '付款方式(0M幣,1支付寶支付,2微信支付,3信用卡支付,4澳門通支付,5會員卡積分支付)',
  `status` int(11) DEFAULT NULL COMMENT '訂單狀態（1、未付款，2、已付款,3、失效訂單，4、退款訂單）',
  `complete_time` datetime DEFAULT NULL COMMENT '訂單完成時間',
  `mpayintegral` decimal(8,2) DEFAULT '0.00' COMMENT 'mpay积分',
  `order_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單金額',
  `currency` varchar(10) DEFAULT NULL COMMENT '訂單金額幣種',
  `total_amount` decimal(8,2) DEFAULT NULL COMMENT '訂單最終金额',
  `payment_amount` decimal(8,2) DEFAULT NULL COMMENT '客戶實際付款',
  `point_ratio` int(11) unsigned DEFAULT '300' COMMENT '福利積分比例',
  `payment_time` datetime DEFAULT NULL COMMENT '客戶付款時間',
  `bank_charges` decimal(8,2) DEFAULT NULL COMMENT '銀行費用',
  `is_mpay` tinyint(4) DEFAULT NULL COMMENT '是否mpay',
  `is_member` tinyint(4) DEFAULT '0' COMMENT '是否member 1是0否 默认0',
  `memberintegral` decimal(8,2) DEFAULT '0.00' COMMENT '會員卡積分',
  `business_name` varchar(255) DEFAULT NULL COMMENT '商戶名稱',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id',
  `product_name` varchar(255) DEFAULT NULL COMMENT '福利名稱',
  `phone` varchar(255) DEFAULT NULL COMMENT '用戶手機號碼',
  `score_tradeno` varchar(255) NOT NULL COMMENT '積分單號',
  `pay_tradeno` varchar(255) NOT NULL COMMENT '支付單號',
  `query_count` int(11) DEFAULT '0' COMMENT '查詢次數',
  `type` int(11) DEFAULT NULL COMMENT '訂單類型（100:單邊賬，200:支付異常訂單，300:退款異常訂單）',
  `pay_uuid` varchar(255) NOT NULL COMMENT 'pay_log uuid參考編號',
  `score_uuid` varchar(255) NOT NULL COMMENT 'score_log uuid參考編號',
  `pay_status` tinyint(4) DEFAULT NULL COMMENT 'pay_log表支付狀態',
  `score_status` tinyint(4) DEFAULT NULL COMMENT 'score_log表支付狀態',
  `refundid` int(11) DEFAULT NULL COMMENT '退款id',
  `refund_code` int(11) DEFAULT NULL COMMENT '退款類型（101:退錢失敗[則積分肯定也沒退]、103:退積分失敗[包含錢成功積分失敗、純積分失敗]）',
  `refund_orderno` varchar(255) DEFAULT NULL COMMENT '退款訂單號',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  `result_status` tinyint(4) DEFAULT '0' COMMENT '結果狀態',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_product` (`product_id`,`product_name`) USING BTREE,
  KEY `index_uuid` (`pay_uuid`,`score_uuid`) USING BTREE,
  KEY `sellerid` (`sellerid`,`business_name`) USING BTREE,
  KEY `create_time` (`create_time`) USING BTREE,
  KEY `index_orderno_tradeno` (`order_no`,`score_tradeno`,`pay_tradeno`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4191 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)異常訂單表';


-- fooku_prd_20231123.fook_platform_user_growth definition

CREATE TABLE `fook_platform_user_growth` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `score` decimal(10,2) DEFAULT NULL COMMENT '積分值變動',
  `growth` int(11) DEFAULT NULL COMMENT '成長值變動',
  `type` int(11) DEFAULT NULL COMMENT '獲得途徑（1、做任務，2、消費抵現，3、退款返還抵現積分，4、商城兌換，5、管理員添加）',
  `task_rule_id` int(11) DEFAULT NULL COMMENT '任務規則Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `evaluationid` int(11) DEFAULT NULL COMMENT '評價記錄Id',
  `mall_order_id` int(11) DEFAULT NULL COMMENT '積分商城訂單Id',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `remarks` varchar(255) DEFAULT NULL COMMENT '积分详情',
  `ordercodeid` int(11) DEFAULT NULL,
  `adminid` int(11) DEFAULT NULL COMMENT '操作的管理員id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13144 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##用戶成長記錄表(0)##';


-- fooku_prd_20231123.fook_platform_usercollection definition

CREATE TABLE `fook_platform_usercollection` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收藏Id',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `type` int(11) DEFAULT NULL COMMENT '收藏類型（1、商家門店，2、優惠券）',
  `collectionid` int(11) DEFAULT NULL COMMENT '收藏信息Id（門店Id或優惠券Id）',
  `create_time` datetime DEFAULT NULL COMMENT '收藏時間',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_collectionid_type_enable` (`collectionid`,`type`,`enable`) USING BTREE,
  KEY `ind_fook_platform_user_01` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=609198 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用户收藏列表';


-- fooku_prd_20231123.fook_platform_userinfo definition

CREATE TABLE `fook_platform_userinfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wechat_userid` int(11) DEFAULT NULL COMMENT '微信用戶Id',
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、香港，2、澳門，3、內地）',
  `registered_phone` varchar(50) DEFAULT NULL COMMENT '註冊手機號碼',
  `password` varchar(255) DEFAULT NULL COMMENT '登錄密碼',
  `nick_name` varchar(255) DEFAULT NULL COMMENT '用戶名',
  `sex` int(1) DEFAULT NULL COMMENT '性別（1、男性，2、女性，0、未知）',
  `avatar` varchar(150) DEFAULT NULL COMMENT '頭像',
  `email` varchar(255) DEFAULT NULL COMMENT '郵箱',
  `enable` int(2) DEFAULT NULL COMMENT '是否凍結(0:凍結，1：正常)',
  `city` varchar(50) DEFAULT NULL COMMENT '常住城市',
  `address` varchar(300) DEFAULT NULL COMMENT '收貨地址',
  `birthday` datetime DEFAULT NULL COMMENT '生日',
  `growth_value` int(11) DEFAULT NULL COMMENT '成長值',
  `score` decimal(10,2) DEFAULT NULL COMMENT '積分',
  `comment_count` int(11) DEFAULT NULL COMMENT '評價數',
  `question` varchar(255) DEFAULT NULL COMMENT '問題1',
  `answer` varchar(255) DEFAULT NULL COMMENT '答案1',
  `question2` varchar(255) DEFAULT NULL COMMENT '問題2',
  `answer2` varchar(255) DEFAULT NULL COMMENT '答案2',
  `last_login_time` varbinary(255) DEFAULT NULL COMMENT '最后登录时间',
  `create_time` varchar(255) DEFAULT NULL COMMENT '創建時間',
  `map` int(11) DEFAULT NULL COMMENT '地圖(1騰訊,2.谷歌)',
  `first_authorized_time` datetime DEFAULT NULL COMMENT '首次授權時間',
  `macaupass_id` int(11) DEFAULT NULL COMMENT 'macaupass id',
  `credit_card_pay` int(2) DEFAULT '0' COMMENT '信用卡支付(0或者空：開啟，1：凍結)',
  `member_id` int(11) DEFAULT NULL COMMENT 'member_id',
  `pay_by_credit_card` int(11) DEFAULT '1' COMMENT '信用卡支付(1：開啟，0：凍結)',
  `bing` tinyint(4) DEFAULT '0' COMMENT '是否授權綁定mpay用戶 0否 1是',
  `new_phone` varchar(50) DEFAULT NULL COMMENT '綁定mpay用戶的手機號碼',
  `bing_time` datetime DEFAULT NULL COMMENT '綁定時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `registered_phone_idx` (`registered_phone`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=690898 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户信息';


-- fooku_prd_20231123.fook_platform_usernotice definition

CREATE TABLE `fook_platform_usernotice` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `type` int(11) DEFAULT NULL COMMENT '通知類型（1預約，2福利，3後台）',
  `title` varchar(500) DEFAULT NULL COMMENT '標題',
  `content` varchar(1000) DEFAULT NULL COMMENT '內容',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `userid` int(11) DEFAULT NULL COMMENT '用戶ID',
  `status` int(11) DEFAULT NULL COMMENT '狀態(0未讀1已讀)',
  `check_time` datetime DEFAULT NULL COMMENT '查看时间',
  `enable` int(11) DEFAULT NULL COMMENT 'Enable(是否有效 0无效1有效)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_platform_userpush definition

CREATE TABLE `fook_platform_userpush` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `type` int(11) DEFAULT NULL COMMENT '推送類型（1預約，2福利，3後台）',
  `noticeid` int(11) DEFAULT NULL COMMENT '通知Id',
  `content` varchar(255) DEFAULT NULL COMMENT '推送內容',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `userid` int(11) DEFAULT NULL COMMENT '推送對象',
  `token` varchar(255) DEFAULT NULL COMMENT '推送码',
  `result` varchar(1000) DEFAULT NULL COMMENT '推送结果',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='用戶推送表（暫時沒有使用）';


-- fooku_prd_20231123.fook_platform_usertoken definition

CREATE TABLE `fook_platform_usertoken` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用戶Token記錄Id',
  `deviceid` varchar(255) DEFAULT NULL COMMENT '設備Id',
  `uid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `ip` varchar(255) DEFAULT NULL COMMENT '請求IP',
  `token` varchar(255) DEFAULT NULL COMMENT 'Token',
  `status` int(11) DEFAULT NULL COMMENT 'Token狀態(0.正常1.用戶密碼修改)',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `invaildtime` datetime DEFAULT NULL COMMENT '失效時間',
  `platform` varchar(255) DEFAULT NULL COMMENT '平台',
  `os` varchar(255) DEFAULT NULL COMMENT '操作系統',
  `pushtoken` varchar(255) DEFAULT NULL COMMENT '推送碼',
  `type` int(11) DEFAULT NULL COMMENT '平台类型1客户端2商户端',
  `version` varchar(30) DEFAULT NULL COMMENT 'APP版本号',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `canceltime` datetime DEFAULT NULL COMMENT '自动任务更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2811 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='用戶Token記錄表';


-- fooku_prd_20231123.fook_product_discount definition

CREATE TABLE `fook_product_discount` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id',
  `integral` int(8) DEFAULT NULL COMMENT '積分',
  `price` decimal(8,2) DEFAULT NULL COMMENT '價錢',
  `status` tinyint(2) DEFAULT '1' COMMENT '狀態 1開啟0關閉',
  `start_time` datetime DEFAULT NULL COMMENT '優惠開始時間',
  `end_time` datetime DEFAULT NULL COMMENT '優惠結束時間',
  `is_mpay` tinyint(2) DEFAULT '1' COMMENT '是否mpay平台開放 1是0否',
  `is_mcoin` tinyint(2) DEFAULT '0' COMMENT '是否mcoin平台開放 1是0否',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='福利優惠';


-- fooku_prd_20231123.fook_product_manual_recommend definition

CREATE TABLE `fook_product_manual_recommend` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `index_shop_type` tinyint(1) DEFAULT NULL,
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `product_sort` int(11) DEFAULT '0' COMMENT '專區福利排序',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='手动推荐排序表';


-- fooku_prd_20231123.fook_product_recommend definition

CREATE TABLE `fook_product_recommend` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `product_id` int(11) DEFAULT NULL COMMENT '優惠券id',
  `status` tinyint(4) DEFAULT '1' COMMENT '狀態1：開啟、0：關閉（默認1）',
  `user_id` int(11) DEFAULT NULL COMMENT '添加優惠券的用戶id',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='小編推薦優惠券表';


-- fooku_prd_20231123.fook_product_snapping_record definition

CREATE TABLE `fook_product_snapping_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `redis_id` varchar(255) DEFAULT NULL COMMENT 'redis_id',
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id',
  `number` int(11) DEFAULT NULL COMMENT '福利数量',
  `status` tinyint(2) DEFAULT NULL COMMENT '状态：1申请抢购、2生效、3失效、4已完成',
  `type` tinyint(4) DEFAULT NULL COMMENT '抢购类型：1按库存、2按随机分配等 3按库存直扣',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `order_id` int(11) DEFAULT NULL COMMENT '订单id',
  `redis_time` datetime DEFAULT NULL COMMENT 'redis同步时间',
  `stock_id` int(11) DEFAULT NULL COMMENT '福利庫存變動記錄表Id',
  `session_id` int(11) DEFAULT NULL COMMENT '抢购场次ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uidx_redis_id` (`redis_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `idx_status_createdat_productid` (`status`,`created_at`,`product_id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE,
  KEY `order_id_idx` (`order_id`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=790252 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='福利限时抢购记录表';


-- fooku_prd_20231123.fook_product_stock_log definition

CREATE TABLE `fook_product_stock_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id',
  `pe` tinyint(1) DEFAULT NULL COMMENT '库存操作类型(1减库存 2加库存)',
  `num` varchar(20) DEFAULT '' COMMENT '加減庫存數',
  `now_stock` varchar(20) DEFAULT '' COMMENT '當前庫存數',
  `stock` varchar(20) DEFAULT '' COMMENT '修改后库存数',
  `ip` varchar(255) DEFAULT '' COMMENT 'ip地址',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14579 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='福利库存修改记录表';


-- fooku_prd_20231123.fook_promotion_category definition

CREATE TABLE `fook_promotion_category` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵ID',
  `category_name` varchar(60) DEFAULT '' COMMENT '分類名稱',
  `category_img` varchar(255) DEFAULT '' COMMENT '分類圖片',
  `index_url` varchar(255) DEFAULT NULL COMMENT '主頁鏈接',
  `sort` int(11) unsigned DEFAULT '0' COMMENT '排序',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='推廣分類表';


-- fooku_prd_20231123.fook_promotion_merchant definition

CREATE TABLE `fook_promotion_merchant` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_name` varchar(100) DEFAULT '' COMMENT '商戶名稱',
  `merchant_url` varchar(255) DEFAULT '' COMMENT '商戶鏈接',
  `merchant_icon` varchar(255) DEFAULT '' COMMENT '商戶圖標',
  `promotion_category_id` int(11) DEFAULT NULL COMMENT '推廣分類表id',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `promotion_category_id` (`promotion_category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=269 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='推廣分類商戶表';


-- fooku_prd_20231123.fook_push_task definition

CREATE TABLE `fook_push_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_type` int(11) DEFAULT NULL COMMENT '模板類型1用戶2商家',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶Id',
  `is_push` int(11) DEFAULT NULL COMMENT '是否推送 0：否 1：是',
  `create_time` datetime DEFAULT NULL COMMENT '添加時間',
  `registration_id` varchar(30) DEFAULT NULL COMMENT '註冊Id',
  `push_template_id` int(11) DEFAULT NULL COMMENT '推送模板表Id',
  `app_config_id` int(11) DEFAULT NULL COMMENT 'App配置表Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='app推送任務';


-- fooku_prd_20231123.fook_push_template definition

CREATE TABLE `fook_push_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '推送模板表Id',
  `type` int(11) DEFAULT NULL COMMENT '模板類型1用戶2商家',
  `title` varchar(255) DEFAULT NULL COMMENT '提示',
  `content` varchar(255) DEFAULT NULL COMMENT '內容',
  `url` varchar(255) DEFAULT NULL COMMENT '鏈接',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理員Id',
  `create_time` datetime DEFAULT NULL COMMENT '添加時間',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效 0:否 1:有',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='推送模板表';


-- fooku_prd_20231123.fook_pwd_reset_record definition

CREATE TABLE `fook_pwd_reset_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '重置密碼記錄表Id',
  `do_enum` tinyint(1) DEFAULT NULL COMMENT '操作者身份標識(1管理員，2商家)',
  `doid` int(11) DEFAULT NULL COMMENT '操作者Id',
  `target_enum` tinyint(1) DEFAULT NULL COMMENT '被操作者身份標識(1管理員，2商家，3門店，4用戶，5門店核銷密碼)',
  `target_id` int(11) DEFAULT NULL COMMENT '被操作者Id',
  `reset_value` varchar(50) DEFAULT ' ' COMMENT '重置密碼值',
  `remark` varchar(500) DEFAULT ' ' COMMENT '備註',
  `ip` varchar(50) DEFAULT ' ' COMMENT 'IP地址',
  `agent` varchar(500) DEFAULT ' ' COMMENT '代理',
  `enable` tinyint(1) DEFAULT NULL COMMENT '是否有效(1有效 0無效)',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `is_all` tinyint(1) DEFAULT NULL COMMENT '是否全部重置(1是、0否)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=173 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='重置密碼記錄表';


-- fooku_prd_20231123.fook_recommend_product definition

CREATE TABLE `fook_recommend_product` (
  `recommend_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `id` int(11) DEFAULT NULL COMMENT '福利id',
  `img` varchar(255) DEFAULT NULL COMMENT '福利圖片',
  `zip_img` varchar(255) DEFAULT NULL COMMENT '壓縮圖片',
  `businessid` int(11) DEFAULT NULL COMMENT '商戶id',
  `title` varchar(255) DEFAULT NULL COMMENT '福利名稱',
  `type` int(11) DEFAULT NULL COMMENT '福利類別',
  `retail_price` decimal(10,2) DEFAULT NULL COMMENT '原價格',
  `price` decimal(10,2) DEFAULT NULL COMMENT '門市價格',
  `point_ratio` int(11) unsigned DEFAULT '300' COMMENT '福利積分比例',
  `shelf_status` tinyint(2) DEFAULT NULL COMMENT '上架下狀態',
  `buy_start_time` datetime DEFAULT NULL COMMENT '可購買開始時間',
  `buy_end_time` datetime DEFAULT NULL COMMENT '可購買結束時間',
  `vaild_mode` tinyint(2) DEFAULT NULL COMMENT '有效期類型1時間段 2有效天數',
  `vaild_start_time` datetime DEFAULT NULL COMMENT '可使用有效開始時間',
  `vaild_end_time` datetime DEFAULT NULL COMMENT '可適用有效結束時間',
  `day_number` int(11) DEFAULT NULL COMMENT '有效天數',
  `snap_up` tinyint(2) DEFAULT NULL COMMENT '是否搶購',
  `s_name` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `i_name` varchar(255) DEFAULT NULL COMMENT '商圈名稱',
  `i_name_en` varchar(255) DEFAULT NULL COMMENT '商圈英文名稱',
  `information_id` int(11) DEFAULT NULL COMMENT '商圈id',
  `stores_type` int(255) DEFAULT NULL COMMENT '門店類別id',
  `stores_type_name` varchar(255) DEFAULT NULL COMMENT '門店類別名稱',
  `t_product_name` varchar(255) DEFAULT NULL COMMENT '福利英文名稱',
  `t_stores_name` varchar(255) DEFAULT NULL COMMENT '門店英文名稱',
  `t_stores_type_name` varchar(255) DEFAULT NULL COMMENT '門店類別英文名稱',
  `created_time` datetime DEFAULT NULL COMMENT '創建時間',
  `complex_name` varchar(255) DEFAULT NULL COMMENT '集合搜索',
  `complex_name_en` varchar(255) DEFAULT NULL COMMENT '集合搜索（英文+中文）',
  `only_point` tinyint(2) DEFAULT '0' COMMENT '是否純積分支付',
  `actual_sales` int(11) DEFAULT '0' COMMENT '實際銷量',
  `sales_weight` double(8,2) DEFAULT '0.00' COMMENT '銷量權重',
  `time_weight` double(8,2) DEFAULT '0.00' COMMENT '有效時間權重',
  `sum_weight` double(8,2) DEFAULT '0.00' COMMENT '總權重',
  `longitude` varchar(255) DEFAULT NULL COMMENT '經度',
  `dimension` varchar(255) DEFAULT NULL COMMENT '緯度',
  `is_hot` int(11) DEFAULT NULL COMMENT '是否熱門',
  `is_great` int(11) DEFAULT NULL COMMENT '是否搶福',
  PRIMARY KEY (`recommend_id`) USING BTREE,
  KEY `normal_type_information_st` (`type`,`information_id`,`stores_type`) USING BTREE,
  KEY `normal_complex_name` (`complex_name`) USING BTREE,
  KEY `normal_complex_name_en` (`complex_name_en`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12231015 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='mCoin推薦表';


-- fooku_prd_20231123.fook_redeemcode definition

CREATE TABLE `fook_redeemcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '兌換碼表Id',
  `redeemcode_product_record_id` int(11) DEFAULT NULL COMMENT '兌換碼批量生產記錄表Id',
  `product_id` int(11) DEFAULT NULL COMMENT '兌換福利Id',
  `code` varchar(50) DEFAULT ' ' COMMENT '兌換碼',
  `code_status` int(11) DEFAULT '0' COMMENT '兌換碼狀態(0未兌換,1已兌換,2兌換中)',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效(1有效 2無效)',
  `created_at` datetime DEFAULT NULL COMMENT '兌換生成日期',
  `updated_at` datetime DEFAULT NULL COMMENT '兌換修改日期',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `redeemcode_product_record_id` (`redeemcode_product_record_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=74724 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='兌換碼表';


-- fooku_prd_20231123.fook_redeemcode_product_record definition

CREATE TABLE `fook_redeemcode_product_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '兌換碼批量生產記錄表Id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利Id',
  `production_count` int(11) DEFAULT NULL COMMENT '生產個數',
  `notes` varchar(255) DEFAULT ' ' COMMENT '備註',
  `ip` varchar(100) DEFAULT ' ' COMMENT 'IP',
  `create_at` datetime DEFAULT NULL COMMENT '創建時間',
  `enable` tinyint(1) DEFAULT NULL COMMENT '是否有效(1有效2無效)',
  `authorid` int(11) DEFAULT NULL COMMENT '作者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1364 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='兌換碼批量生產記錄表';


-- fooku_prd_20231123.fook_redeemcode_use definition

CREATE TABLE `fook_redeemcode_use` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '兌換記錄表Id',
  `product_id` int(11) DEFAULT NULL COMMENT '兌換福利Id',
  `code` varchar(50) DEFAULT ' ' COMMENT '兌換碼',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單Id',
  `pay_status` tinyint(1) DEFAULT '1' COMMENT '支付狀態（1、未付款，2、已付款,3、失效訂單）',
  `ip` varchar(100) DEFAULT ' ' COMMENT 'ip',
  `userinfo_id` int(11) DEFAULT NULL COMMENT '用戶Id',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效(1有效2無效)',
  `redeemcode_id` int(11) DEFAULT NULL COMMENT '兌換碼表id',
  `redeem_time` datetime DEFAULT NULL COMMENT '兌換時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=787 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='兌換記錄表';


-- fooku_prd_20231123.fook_refund_order definition

CREATE TABLE `fook_refund_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `refund_amount` decimal(12,2) DEFAULT NULL COMMENT '訂單金額',
  `score` decimal(12,2) DEFAULT NULL COMMENT 'M幣',
  `mpayintegral` decimal(14,2) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_cycle` varchar(255) DEFAULT NULL COMMENT '結算週期',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1716 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='退款統計結算週期表';


-- fooku_prd_20231123.fook_refund_order_data definition

CREATE TABLE `fook_refund_order_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pay_tradeno` varchar(255) DEFAULT NULL COMMENT 'MPay支付訂單號',
  `score_tradeno` varchar(255) DEFAULT NULL COMMENT 'mCoin積分支付訂單號',
  `order_no` varchar(255) DEFAULT NULL COMMENT '交易訂單號',
  `create_time` datetime DEFAULT NULL COMMENT '下單時間',
  `create_date` date DEFAULT NULL COMMENT '下單日期',
  `order_transaction` varchar(255) DEFAULT NULL COMMENT '支付訂單號',
  `payment_transaction` varchar(255) DEFAULT NULL COMMENT '支付機構流水號',
  `order_amount` decimal(8,2) DEFAULT '0.00' COMMENT '訂單金額',
  `settlement_amount` decimal(8,2) DEFAULT '0.00' COMMENT '已結算金額',
  `pending_amount` decimal(8,2) DEFAULT '0.00' COMMENT '待結算金額',
  `settlement_accounting` decimal(8,2) DEFAULT '0.00' COMMENT '已核銷金額',
  `pending_accounting` decimal(8,2) DEFAULT '0.00' COMMENT '待核銷金額',
  `refund_amount` decimal(8,2) DEFAULT '0.00' COMMENT '已退款金額',
  `approval_amount` decimal(8,2) DEFAULT '0.00' COMMENT '未退款金額(審批中)',
  `commission` decimal(8,2) DEFAULT '0.00' COMMENT '佣金',
  `score` decimal(8,2) DEFAULT '0.00' COMMENT '使用M幣數',
  `total_amount` decimal(8,2) DEFAULT '0.00' COMMENT '實際付款',
  `payment_amount` decimal(8,2) DEFAULT '0.00' COMMENT '支付金額',
  `pay_platform_fees` decimal(8,2) DEFAULT '0.00' COMMENT '支付平臺手續費',
  `payment_institution_amount` decimal(8,2) DEFAULT '0.00' COMMENT '支付機構金額',
  `total_merch_settle_amount` decimal(8,2) DEFAULT '0.00' COMMENT '總結算金額',
  `mpayintegral` decimal(10,2) DEFAULT '0.00' COMMENT '使用澳門通mCoin積分',
  `mpayintegral_exchange_amount` decimal(8,2) DEFAULT '0.00' COMMENT '澳門通mCoin積分兌換金額',
  `mpayintegral_fees` decimal(8,2) DEFAULT '0.00' COMMENT '澳門通mCoin積分手續費',
  `mpayintegral_pay_amount` decimal(8,2) DEFAULT '0.00' COMMENT '澳門通mCoin支付金額',
  `refunded_amount` decimal(8,2) DEFAULT '0.00' COMMENT '退款金額',
  `handling_fee` decimal(8,2) DEFAULT '0.00' COMMENT '手續費',
  `payment_type` int(11) DEFAULT NULL COMMENT '付款方式(0M幣,1支付寶支付,2微信支付,3信用卡支付,4澳門通支付)',
  `business_name` varchar(255) DEFAULT NULL COMMENT '商家名稱',
  `type` int(11) DEFAULT NULL COMMENT '訂單類型（1、福利，2、預約，3、特殊福利）',
  `application_time` datetime DEFAULT NULL COMMENT '申请退款时间',
  `refund_time` datetime DEFAULT NULL COMMENT '退款日期',
  `status` int(11) DEFAULT NULL COMMENT '訂單狀態（1、未付款，2、已付款,3、失效訂單）',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單id',
  `is_mpay` int(11) DEFAULT NULL COMMENT '是否mpay訂單',
  `refund_id` int(11) DEFAULT NULL COMMENT '退款id',
  `refund_status` int(11) DEFAULT NULL COMMENT '退款狀態（1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款',
  `platform_deal_status` int(11) DEFAULT NULL COMMENT '支付平台處理狀態(0-审核中 1-處理 2-不成功 3-成功)',
  `refund_orderno` varchar(255) DEFAULT NULL COMMENT '退款訂單號',
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT 'orderinfo表id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶id',
  `refund_order_id` int(11) DEFAULT NULL COMMENT 'fook_refund_order訂單退款統計表id',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=573612 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='退款統計數據';


-- fooku_prd_20231123.fook_report_business definition

CREATE TABLE `fook_report_business` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `settlement_cycle` varchar(255) DEFAULT NULL COMMENT '结算週期',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=160 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家統計數據的結算期限（只供財務人員的對賬）';


-- fooku_prd_20231123.fook_report_business_data definition

CREATE TABLE `fook_report_business_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商家統計表id',
  `report_business_id` int(11) DEFAULT NULL COMMENT '商家結算表id',
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  `order_amount` decimal(13,2) DEFAULT NULL COMMENT '訂單金額',
  `settlement` decimal(13,2) DEFAULT NULL COMMENT '結算金額',
  `waitsettlement` decimal(13,2) DEFAULT NULL COMMENT '待結算金額',
  `commission` decimal(13,2) DEFAULT NULL COMMENT '佣金',
  `settlement_accounting` decimal(13,2) DEFAULT NULL COMMENT '已核销金額',
  `pending_accounting` decimal(13,2) DEFAULT NULL COMMENT '待核销金額',
  `score` decimal(14,2) DEFAULT NULL COMMENT 'M幣數',
  `mpayintegral` decimal(14,2) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `total_amount` decimal(11,2) DEFAULT NULL COMMENT '訂單最終金額',
  `settlementtime` datetime DEFAULT NULL COMMENT '結算時間',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `report_business_id` (`report_business_id`) USING BTREE,
  KEY `business_id` (`business_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25558 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家統計的結算數據（只供財務人員對賬）';


-- fooku_prd_20231123.fook_report_merchant_settlement definition

CREATE TABLE `fook_report_merchant_settlement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `bank_account` varchar(30) DEFAULT NULL COMMENT '結算銀行賬戶',
  `account_name` varchar(100) DEFAULT NULL COMMENT '結算賬戶姓名',
  `bank` varchar(120) DEFAULT NULL COMMENT '結算銀行',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT NULL COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `pdf_url` varchar(255) DEFAULT '' COMMENT 'pdf OSS鏈接',
  `excel_url` varchar(255) DEFAULT '' COMMENT 'excel OSS鏈接',
  `test_bill_amount` decimal(10,2) DEFAULT NULL COMMENT 'test交易金額',
  `test_settlement_amount` decimal(10,2) DEFAULT NULL COMMENT 'test結算金額數',
  `test_commission` decimal(10,2) DEFAULT NULL COMMENT 'test佣金',
  `test_momecoins` decimal(10,2) DEFAULT NULL COMMENT 'test抵扣M幣',
  `is_a` tinyint(1) DEFAULT '0' COMMENT '是否A+，0否，1是，用於發送結算報表區分',
  `status` int(11) DEFAULT '1' COMMENT '狀態 (0未結算,1已結算)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uidx_rpt_mer_settlement` (`start_time`,`end_time`,`seller_id`,`is_a`) USING BTREE,
  KEY `seller_id` (`seller_id`) USING BTREE,
  KEY `is_a` (`is_a`) USING BTREE,
  KEY `start_time` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=128684 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家結算報表';


-- fooku_prd_20231123.fook_report_merchant_settlement_batch definition

CREATE TABLE `fook_report_merchant_settlement_batch` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `store_id` int(11) DEFAULT NULL COMMENT '门店Id',
  `start_time` datetime NOT NULL COMMENT '結算週期開始',
  `end_time` datetime NOT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT '0.00' COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT '0.00' COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT '0.00' COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT '0.00' COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `is_a` tinyint(1) DEFAULT '0' COMMENT '是否A+，0否，1是，用於發送結算報表區分',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_rpt_mer_settlement_batch` (`start_time`,`end_time`,`seller_id`,`is_a`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1911 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)商家結算-批次表';


-- fooku_prd_20231123.fook_report_merchant_settlement_old231212 definition

CREATE TABLE `fook_report_merchant_settlement_old231212` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `bank_account` varchar(30) DEFAULT NULL COMMENT '結算銀行賬戶',
  `account_name` varchar(100) DEFAULT NULL COMMENT '結算賬戶姓名',
  `bank` varchar(120) DEFAULT NULL COMMENT '結算銀行',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT NULL COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `pdf_url` varchar(255) DEFAULT '' COMMENT 'pdf OSS鏈接',
  `excel_url` varchar(255) DEFAULT '' COMMENT 'excel OSS鏈接',
  `test_bill_amount` decimal(10,2) DEFAULT NULL COMMENT 'test交易金額',
  `test_settlement_amount` decimal(10,2) DEFAULT NULL COMMENT 'test結算金額數',
  `test_commission` decimal(10,2) DEFAULT NULL COMMENT 'test佣金',
  `test_momecoins` decimal(10,2) DEFAULT NULL COMMENT 'test抵扣M幣',
  `is_a` tinyint(1) DEFAULT '0' COMMENT '是否A+，0否，1是，用於發送結算報表區分',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `seller_id` (`seller_id`) USING BTREE,
  KEY `is_a` (`is_a`) USING BTREE,
  KEY `start_time` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=93743 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商家結算報表';


-- fooku_prd_20231123.fook_report_merchant_settlement_optional definition

CREATE TABLE `fook_report_merchant_settlement_optional` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `bank_account` varchar(30) DEFAULT NULL COMMENT '結算銀行賬戶',
  `account_name` varchar(100) DEFAULT NULL COMMENT '結算賬戶姓名',
  `bank` varchar(120) DEFAULT NULL COMMENT '結算銀行',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT NULL COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `pdf_url` varchar(255) DEFAULT '' COMMENT 'pdf OSS鏈接',
  `excel_url` varchar(255) DEFAULT '' COMMENT 'excel OSS鏈接',
  `test_bill_amount` decimal(10,2) DEFAULT NULL COMMENT 'test交易金額',
  `test_settlement_amount` decimal(10,2) DEFAULT NULL COMMENT 'test結算金額數',
  `test_commission` decimal(10,2) DEFAULT NULL COMMENT 'test佣金',
  `test_momecoins` decimal(10,2) DEFAULT NULL COMMENT 'test抵扣M幣',
  `status` int(11) DEFAULT '1' COMMENT '狀態 (0未結算,1已結算)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uidx_rpt_mer_settlement_optional` (`start_time`,`end_time`,`seller_id`) USING BTREE,
  KEY `seller_id` (`seller_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=558752 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家選發結算報表';


-- fooku_prd_20231123.fook_report_merchant_settlement_optional_batch definition

CREATE TABLE `fook_report_merchant_settlement_optional_batch` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `store_id` int(11) DEFAULT NULL COMMENT '门店Id',
  `start_time` datetime NOT NULL COMMENT '結算週期開始',
  `end_time` datetime NOT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT '0.00' COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT '0.00' COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT '0.00' COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT '0.00' COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_rpt_mer_settlement_opt_batch` (`start_time`,`end_time`,`seller_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9312 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家選發結算報表-批次表';


-- fooku_prd_20231123.fook_report_merchant_settlement_optional_processs definition

CREATE TABLE `fook_report_merchant_settlement_optional_processs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `settlementid` int(11) DEFAULT NULL COMMENT '結算ID',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `email` varchar(1000) DEFAULT NULL COMMENT '電子郵箱',
  `upload` int(11) DEFAULT NULL COMMENT '是否上傳',
  `uploadtime` datetime DEFAULT NULL COMMENT '上傳時間',
  `send` int(11) DEFAULT NULL COMMENT '發送郵件時間',
  `sendtime` datetime DEFAULT NULL COMMENT '發送時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `businessid` (`businessid`) USING BTREE,
  KEY `settlementid` (`settlementid`) USING BTREE,
  KEY `createtime_idx` (`createtime`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=558621 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家選填結算處理記錄';


-- fooku_prd_20231123.fook_report_merchant_settlement_optional_processs_old231212 definition

CREATE TABLE `fook_report_merchant_settlement_optional_processs_old231212` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `settlementid` int(11) DEFAULT NULL COMMENT '結算ID',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `email` varchar(255) DEFAULT NULL COMMENT '電子郵箱',
  `upload` int(11) DEFAULT NULL COMMENT '是否上傳',
  `uploadtime` datetime DEFAULT NULL COMMENT '上傳時間',
  `send` int(11) DEFAULT NULL COMMENT '發送郵件時間',
  `sendtime` datetime DEFAULT NULL COMMENT '發送時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `businessid` (`businessid`) USING BTREE,
  KEY `settlementid` (`settlementid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=216772 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家選填結算處理記錄';


-- fooku_prd_20231123.fook_report_merchant_settlement_processs definition

CREATE TABLE `fook_report_merchant_settlement_processs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `settlementid` int(11) DEFAULT NULL COMMENT '結算ID',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `email` varchar(1000) DEFAULT NULL COMMENT '電子郵箱',
  `upload` int(11) DEFAULT NULL COMMENT '是否上傳',
  `uploadtime` datetime DEFAULT NULL COMMENT '上傳時間',
  `send` int(11) DEFAULT NULL COMMENT '發送郵件時間',
  `sendtime` datetime DEFAULT NULL COMMENT '發送時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `createtime_idx` (`createtime`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=122762 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商家結算處理記錄';


-- fooku_prd_20231123.fook_report_miles_internal definition

CREATE TABLE `fook_report_miles_internal` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `mpayintegral_exchange_amount` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換金額',
  `mpayintegral` int(11) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `miles_milage` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '兌換亞洲萬里通里數',
  `success` mediumint(9) DEFAULT NULL COMMENT '兌換成功訂單數',
  `fail` mediumint(9) DEFAULT NULL COMMENT '兌換失敗訂單數',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_cycle` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '結算週期',
  `miles_settlement_amount_usd` decimal(10,4) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(USD)',
  `miles_settlement_amount_hkd` decimal(10,3) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(HKD)',
  `miles_settlement_amount_mop` decimal(10,2) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(MOP)',
  `success_apportion_mpayintegral_sum` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換成功金額',
  `success_mpayintegral_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '兌換成功澳門通mCoin積分',
  `success_miles_milage_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '亞洲萬里通兌換成功里數',
  `success_amount_usd` decimal(10,4) DEFAULT NULL COMMENT '亞洲萬里通里數結算成功金額(USD)',
  `success_amount_hkd` decimal(10,3) DEFAULT NULL COMMENT '亞洲萬里通里數結算成功金額(HKD)',
  `success_amount_mop` decimal(10,2) DEFAULT NULL COMMENT '亞洲萬里通里數結算成功金額(MOP)',
  `fail_apportion_mpayintegral_sum` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換失敗金額',
  `fail_mpayintegral_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '兌換失敗澳門通mCoin積分',
  `fail_miles_milage_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '亞洲萬里通兌換失敗里數',
  `fail_amount_usd` decimal(10,4) DEFAULT NULL COMMENT '亞洲萬里通里數結算失敗金額(USD)',
  `fail_amount_hkd` decimal(10,3) DEFAULT NULL COMMENT '亞洲萬里通里數結算失敗金額(HKD)',
  `fail_amount_mop` decimal(10,2) DEFAULT NULL COMMENT '亞洲萬里通里數結算失敗金額(MOP)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='亞洲萬里通兌換統計表';


-- fooku_prd_20231123.fook_report_miles_internal_detail definition

CREATE TABLE `fook_report_miles_internal_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵ID',
  `miles_internal_id` int(11) DEFAULT NULL COMMENT '亞洲萬里通兌換統計表id',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT '訂單詳細信息表id',
  `mpay_openid` varchar(100) COLLATE utf8_bin DEFAULT '' COMMENT 'MpayOpenID',
  `exchange_date` datetime DEFAULT NULL COMMENT '兌換日期',
  `credit_date` datetime DEFAULT NULL COMMENT 'AsiaMiles到賬日期',
  `miles_member` varchar(30) COLLATE utf8_bin DEFAULT '' COMMENT '會員卡號',
  `miles_first` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '會員姓',
  `miles_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '會員名字',
  `tradeno` varchar(120) COLLATE utf8_bin DEFAULT '' COMMENT 'mCoin積分支付訂單號',
  `order_no` varchar(120) COLLATE utf8_bin DEFAULT '' COMMENT '交易訂單號',
  `payment_type` varchar(30) COLLATE utf8_bin DEFAULT '' COMMENT '付款方式',
  `mpayintegral` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '澳門通mCoin積分',
  `apportion_mpayintegral` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換金額',
  `miles_milage` varchar(30) COLLATE utf8_bin DEFAULT '' COMMENT '亞洲萬里通里數',
  `platform` varchar(30) COLLATE utf8_bin DEFAULT '' COMMENT '交易平台',
  `status` tinyint(1) DEFAULT NULL COMMENT '兌換狀態(0失敗 1成功)',
  `fail_reason` varchar(150) COLLATE utf8_bin DEFAULT '' COMMENT '失敗原因',
  `bill_amount_usd` decimal(10,4) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(USD)',
  `miles_settlement_amount_hkd` decimal(10,3) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(HKD)',
  `miles_settlement_amount_mop` decimal(10,2) DEFAULT NULL COMMENT '亞洲萬里通里數結算參考金額（MOP）',
  `create_time` datetime DEFAULT NULL COMMENT '创建的下单时间',
  `settlement_status` tinyint(4) DEFAULT '0' COMMENT '结算状态 0未结算 1已结算',
  `settlement_time` datetime DEFAULT NULL COMMENT 'fook_report_miles_internal表记录创建时间',
  `writeoff_status` tinyint(4) DEFAULT NULL COMMENT '核销状态 0失败 1成功',
  `writeoff_error_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '核销错误code码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=108378 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='亞洲萬里通兌換統計詳情表';


-- fooku_prd_20231123.fook_report_miles_internal_old231212 definition

CREATE TABLE `fook_report_miles_internal_old231212` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `mpayintegral_exchange_amount` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換金額',
  `mpayintegral` int(11) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `miles_milage` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '兌換亞洲萬里通里數',
  `success` mediumint(9) DEFAULT NULL COMMENT '兌換成功訂單數',
  `fail` mediumint(9) DEFAULT NULL COMMENT '兌換失敗訂單數',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_cycle` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '結算週期',
  `miles_settlement_amount_usd` decimal(10,4) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(USD)',
  `miles_settlement_amount_hkd` decimal(10,3) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(HKD)',
  `miles_settlement_amount_mop` decimal(10,2) DEFAULT NULL COMMENT '亞洲萬里通里數結算金額(MOP)',
  `success_apportion_mpayintegral_sum` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換成功金額',
  `success_mpayintegral_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '兌換成功澳門通mCoin積分',
  `success_miles_milage_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '亞洲萬里通兌換成功里數',
  `success_amount_usd` decimal(10,4) DEFAULT NULL COMMENT '亞洲萬里通里數結算成功金額(USD)',
  `success_amount_hkd` decimal(10,3) DEFAULT NULL COMMENT '亞洲萬里通里數結算成功金額(HKD)',
  `success_amount_mop` decimal(10,2) DEFAULT NULL COMMENT '亞洲萬里通里數結算成功金額(MOP)',
  `fail_apportion_mpayintegral_sum` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分兌換失敗金額',
  `fail_mpayintegral_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '兌換失敗澳門通mCoin積分',
  `fail_miles_milage_sum` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '亞洲萬里通兌換失敗里數',
  `fail_amount_usd` decimal(10,4) DEFAULT NULL COMMENT '亞洲萬里通里數結算失敗金額(USD)',
  `fail_amount_hkd` decimal(10,3) DEFAULT NULL COMMENT '亞洲萬里通里數結算失敗金額(HKD)',
  `fail_amount_mop` decimal(10,2) DEFAULT NULL COMMENT '亞洲萬里通里數結算失敗金額(MOP)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='亞洲萬里通兌換統計表';


-- fooku_prd_20231123.fook_report_miles_settlement_log definition

CREATE TABLE `fook_report_miles_settlement_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storesid` int(11) DEFAULT NULL COMMENT '门店ID',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `backtime` datetime DEFAULT NULL COMMENT '返回时间',
  `code` varchar(50) DEFAULT NULL COMMENT '核銷碼',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT '訂單詳情表ID',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(1发送中 2核对成功 3核对失败,4校驗失敗)',
  `miles_first` varchar(50) DEFAULT NULL COMMENT '會員姓',
  `miles_name` varchar(50) DEFAULT NULL COMMENT '會員名',
  `miles_member` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '會員卡號',
  `miles_milage` decimal(8,2) DEFAULT NULL COMMENT '兌換里程數',
  `docking_log_id` int(11) DEFAULT NULL COMMENT '亞洲萬裡通對接記錄log表id',
  `ordercode_id` int(11) DEFAULT NULL COMMENT 'ordercode表id',
  `back_msg` varchar(255) DEFAULT NULL COMMENT '返回內容',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `businessid` (`businessid`) USING BTREE,
  KEY ```ordercode_id``` (`ordercode_id`) USING BTREE,
  KEY `idx_order_no` (`order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=113708 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='亚洲万里通对接记录表';


-- fooku_prd_20231123.fook_report_momecoin definition

CREATE TABLE `fook_report_momecoin` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `total_count` decimal(10,2) DEFAULT NULL COMMENT '总发行数量',
  `totalsale_count` decimal(10,2) DEFAULT NULL COMMENT '总销量',
  `public_count` decimal(10,2) DEFAULT NULL COMMENT '当天发行数量',
  `sale_count` decimal(10,2) DEFAULT NULL COMMENT '当天销量',
  `report_start_time` datetime DEFAULT NULL COMMENT '统计起始日期',
  `report_end_time` datetime DEFAULT NULL COMMENT '统计结束日期',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1420 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='统计M币的发行和销售数据';


-- fooku_prd_20231123.fook_report_momecoin_product definition

CREATE TABLE `fook_report_momecoin_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `reportid` int(11) DEFAULT NULL COMMENT '统计ID',
  `productid` int(11) DEFAULT NULL COMMENT '统计用户报表ID',
  `score` decimal(10,2) DEFAULT NULL,
  `start_time` datetime DEFAULT NULL COMMENT '统计开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '统计结束时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='M币使用用户详情记录表';


-- fooku_prd_20231123.fook_report_momecoin_user definition

CREATE TABLE `fook_report_momecoin_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `reportid` int(11) DEFAULT NULL COMMENT '统计ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户ID',
  `public_count` decimal(10,2) DEFAULT NULL COMMENT '发行数量',
  `sale_count` decimal(10,2) DEFAULT NULL COMMENT '销量',
  `start_time` datetime DEFAULT NULL COMMENT '统计开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '统计结束时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5541 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='M币统计用户使用表';


-- fooku_prd_20231123.fook_report_momecoin_userreocord definition

CREATE TABLE `fook_report_momecoin_userreocord` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `reportid` int(11) DEFAULT NULL COMMENT '统计ID',
  `reportuserid` int(11) DEFAULT NULL COMMENT '统计用户报表ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户ID',
  `type` int(11) DEFAULT NULL,
  `score` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='M幣用戶使用記錄';


-- fooku_prd_20231123.fook_report_mpaycoin definition

CREATE TABLE `fook_report_mpaycoin` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `settlement_cycle` varchar(255) DEFAULT NULL COMMENT '结算周期',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(13,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(13,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(13,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(13,2) DEFAULT NULL COMMENT '抵扣M幣',
  `mpaycoins` decimal(13,2) DEFAULT NULL COMMENT '抵扣MPay積分',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2075 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='統計MPay內嵌mCoin支付訂單的數據';


-- fooku_prd_20231123.fook_report_mpaycoin_all definition

CREATE TABLE `fook_report_mpaycoin_all` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT NULL COMMENT '訂單總交易金額',
  `bill_score` decimal(10,2) DEFAULT NULL COMMENT 'M幣',
  `mpaycoins` decimal(12,2) DEFAULT NULL COMMENT '澳門通mCoin積分數',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_cycle` varchar(255) DEFAULT NULL COMMENT '结算週期',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1949 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='MPaymCoin所有支付訂單的數據';


-- fooku_prd_20231123.fook_report_mpaycoin_code definition

CREATE TABLE `fook_report_mpaycoin_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `sum_code` decimal(10,0) DEFAULT NULL COMMENT '核銷總計',
  `sum_mpaycoins` decimal(12,2) DEFAULT NULL COMMENT '总消费澳門通mCoin積分',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_cycle` varchar(255) DEFAULT NULL COMMENT '結算週期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2059 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='統計MPay內嵌mCoin支付訂單的數據';


-- fooku_prd_20231123.fook_report_mpaycoin_codelist definition

CREATE TABLE `fook_report_mpaycoin_codelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `ordercodeid` int(11) DEFAULT NULL COMMENT '訂單核銷ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `orderid` int(11) DEFAULT NULL COMMENT '订单id',
  `code` varchar(255) DEFAULT NULL COMMENT '核銷碼',
  `type` int(11) DEFAULT NULL COMMENT '福利類型',
  `writeoff_time` datetime DEFAULT NULL COMMENT '核銷時間',
  `name` varchar(255) DEFAULT NULL COMMENT '福利名稱',
  `mpaycoinid` int(11) DEFAULT NULL COMMENT '關聯id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `mpaycoinid` (`mpaycoinid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8919042 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='澳門通MPay交易訂單核銷子項表';


-- fooku_prd_20231123.fook_report_mpaycoin_ordercode definition

CREATE TABLE `fook_report_mpaycoin_ordercode` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `ordercodeid` int(11) DEFAULT NULL COMMENT '訂單核銷ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `businessname` varchar(255) DEFAULT NULL COMMENT '商家名稱',
  `storename` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `bankaccount` varchar(255) DEFAULT NULL COMMENT '結算銀行帳號',
  `bankname` varchar(255) DEFAULT NULL COMMENT '結算銀行名稱',
  `bank` varchar(255) DEFAULT NULL COMMENT '結算銀行',
  `createtime` datetime DEFAULT NULL COMMENT '下單時間',
  `usetime` datetime DEFAULT NULL COMMENT '使用時間',
  `billamount` decimal(10,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `userpaymentamount` decimal(10,2) DEFAULT NULL COMMENT '攤派支付金額',
  `momecoinsamount` decimal(10,2) DEFAULT NULL COMMENT '攤派m幣',
  `mpaycoins` decimal(10,2) DEFAULT NULL COMMENT '攤派mpay積分',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '傭金',
  `merchantsettleamount` decimal(10,2) DEFAULT NULL COMMENT '商家結算金額',
  `vouchercode` varchar(255) DEFAULT NULL COMMENT '核銷碼',
  `vouchername` varchar(255) DEFAULT NULL COMMENT '核銷商品名稱',
  `ordertransaction` varchar(255) DEFAULT NULL COMMENT '訂單流水號',
  `status` varchar(5) DEFAULT NULL COMMENT '狀態 (0未結算,1結算)',
  `settlementtime` datetime DEFAULT NULL COMMENT '結算時間',
  `mpaycoinid` int(11) DEFAULT NULL COMMENT '结算ID',
  `order_no` varchar(255) DEFAULT NULL COMMENT '交易订单号',
  `order_type` varchar(40) DEFAULT NULL COMMENT '订单类型',
  `pay_type` int(11) DEFAULT NULL COMMENT '付款状态',
  `order_money` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `mpayintegral` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `total_amount` decimal(10,2) DEFAULT NULL COMMENT '實際支付金額',
  `payment_type` int(11) DEFAULT NULL COMMENT '付款方式',
  `payment_amount` decimal(10,2) DEFAULT NULL COMMENT '客戶實際支付金額',
  `platform` varchar(50) DEFAULT NULL COMMENT '平台',
  `payment_time` datetime DEFAULT NULL COMMENT '付款時間',
  `complete_time` datetime DEFAULT NULL COMMENT '訂單完成時間',
  `payment_transaction` varchar(255) DEFAULT NULL COMMENT '支付機構支付流水號',
  `mpaycoinids` int(11) DEFAULT NULL COMMENT 'mpay报表关联id',
  `orderid` int(11) DEFAULT NULL COMMENT '订单id',
  `operationtime` datetime DEFAULT NULL COMMENT '操作时间',
  `refund_amount` decimal(8,2) DEFAULT NULL COMMENT '退款金额',
  `refund_mpayintegral` decimal(8,2) DEFAULT NULL COMMENT '退回积分',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ordercodeid` (`ordercodeid`) USING BTREE,
  KEY `businessid` (`businessid`) USING BTREE,
  KEY `storeid` (`storeid`) USING BTREE,
  KEY `idx_mpaycoinid` (`mpaycoinid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6067384 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='澳門通Mpay交易訂單核銷列表';


-- fooku_prd_20231123.fook_report_mpaycoin_ordercode_all definition

CREATE TABLE `fook_report_mpaycoin_ordercode_all` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mpaycoinid` int(11) DEFAULT NULL COMMENT '结算ID',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `createtime` datetime DEFAULT NULL COMMENT '下單時間',
  `settlementtime` datetime DEFAULT NULL COMMENT '結算時間',
  `order_no` varchar(255) DEFAULT NULL COMMENT '交易订单号',
  `order_type` varchar(40) DEFAULT NULL COMMENT '订单类型',
  `pay_type` int(11) DEFAULT NULL COMMENT '付款状态',
  `order_money` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `mpayintegral` decimal(10,2) DEFAULT NULL COMMENT '澳門通mCoin積分',
  `score` decimal(10,2) DEFAULT NULL COMMENT 'M幣',
  `total_amount` decimal(10,2) DEFAULT NULL COMMENT '實際支付金額',
  `payment_type` tinyint(1) DEFAULT NULL COMMENT '付款方式',
  `payment_amount` decimal(10,2) DEFAULT NULL COMMENT '客戶實際支付金額',
  `platform` varchar(50) DEFAULT NULL COMMENT '平台',
  `payment_time` datetime DEFAULT NULL COMMENT '付款時間',
  `complete_time` datetime DEFAULT NULL COMMENT '訂單完成時間',
  `payment_transaction` varchar(255) DEFAULT NULL COMMENT '支付機構支付流水號',
  `orderid` int(11) DEFAULT NULL COMMENT '订单id',
  `operationtime` datetime DEFAULT NULL COMMENT '支付时间',
  `refund_amount` decimal(8,2) DEFAULT NULL COMMENT '退款金額',
  `refund_mpayintegral` decimal(10,2) DEFAULT NULL COMMENT '退款積分',
  `refund_score` decimal(8,2) DEFAULT NULL COMMENT '退款M幣',
  `is_mpay` tinyint(2) DEFAULT NULL,
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `fook_report_mpaycoin_ordercode_all_mpaycoinid_index` (`mpaycoinid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5921692 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='MPaymCoin所有支付訂單詳情';


-- fooku_prd_20231123.fook_report_order_search_download definition

CREATE TABLE `fook_report_order_search_download` (
  `id` int(8) unsigned NOT NULL AUTO_INCREMENT,
  `paymenttype` tinyint(1) DEFAULT NULL COMMENT '付款方式',
  `starttime` datetime DEFAULT NULL COMMENT '下單開始時間',
  `endtime` datetime DEFAULT NULL COMMENT '下單結束時間',
  `order_no` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '交易訂單號',
  `businessid` int(11) DEFAULT NULL COMMENT '商家id',
  `is_mpay` tinyint(1) DEFAULT NULL COMMENT '交易平台',
  `paylog_tradeno` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT 'MPay支付訂單號',
  `scoreLog_tradeno` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT 'mCoin積分支付訂單號',
  `excel_url` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT 'oss鏈接',
  `status` tinyint(1) DEFAULT '0' COMMENT '狀態(0未執行，1已完成，2執行出錯)',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='訂單統計大文件Excel導出定時生成';


-- fooku_prd_20231123.fook_report_ordercode_internal definition

CREATE TABLE `fook_report_ordercode_internal` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT NULL COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `test_bill_amount` decimal(10,2) DEFAULT NULL COMMENT 'test交易金額',
  `test_settlement_amount` decimal(10,2) DEFAULT NULL COMMENT 'test結算金額數',
  `test_commission` decimal(10,2) DEFAULT NULL COMMENT 'test佣金',
  `test_momecoins` decimal(10,2) DEFAULT NULL COMMENT 'test抵扣M幣',
  `status` int(11) DEFAULT '1' COMMENT '狀態 (0未結算,1已結算)',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uidx_rpt_ordercode_internal` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=144 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='内部统计表';


-- fooku_prd_20231123.fook_report_ordercode_internal_batch definition

CREATE TABLE `fook_report_ordercode_internal_batch` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime NOT NULL COMMENT '結算週期開始',
  `end_time` datetime NOT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT '0.00' COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT '0.00' COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT '0.00' COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT '0.00' COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_rpt_ordercode_internal_batch` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='内部统计表-批次表';


-- fooku_prd_20231123.fook_report_ordercode_process definition

CREATE TABLE `fook_report_ordercode_process` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `internalid` int(11) DEFAULT NULL COMMENT '内部统计表ID',
  `start_id` int(11) DEFAULT NULL COMMENT '查詢開始id',
  `end_id` int(11) DEFAULT NULL COMMENT '查詢結束id',
  `excel_oss_link` varchar(2000) COLLATE utf8_bin DEFAULT NULL COMMENT 'excel上傳oss鏈接',
  `pdf_oss_link` varchar(2000) COLLATE utf8_bin DEFAULT NULL COMMENT 'pdf上傳oss鏈接',
  `if_upload` tinyint(1) DEFAULT '0' COMMENT '是否上傳(0否,1是,2結算週期內沒有數據)',
  `upload_time` datetime DEFAULT NULL COMMENT '上傳oss時間',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `internalid` (`internalid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='內部統計文件隊列處理表';


-- fooku_prd_20231123.fook_report_ordercode_settlement definition

CREATE TABLE `fook_report_ordercode_settlement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `ordercodeid` int(11) DEFAULT NULL COMMENT '訂單核銷ID',
  `businessname` varchar(255) DEFAULT NULL COMMENT '商家名稱',
  `storename` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `bankaccount` varchar(255) DEFAULT NULL COMMENT '結算銀行帳號',
  `bankname` varchar(255) DEFAULT NULL COMMENT '結算銀行名稱',
  `bank` varchar(255) DEFAULT NULL COMMENT '結算銀行',
  `createtime` datetime DEFAULT NULL COMMENT '下單時間',
  `usetime` datetime DEFAULT NULL COMMENT '使用時間',
  `billamount` decimal(10,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `userpaymentamount` decimal(10,2) DEFAULT NULL COMMENT '攤派支付金額',
  `momecoinsamount` decimal(10,2) DEFAULT NULL COMMENT '攤派m幣',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '傭金',
  `merchantsettleamount` decimal(10,2) DEFAULT NULL COMMENT '商家結算金額',
  `vouchercode` varchar(255) DEFAULT NULL COMMENT '核銷碼',
  `vouchername` varchar(255) DEFAULT NULL COMMENT '核銷商品名稱',
  `ordertransaction` varchar(255) DEFAULT NULL COMMENT '訂單流水號',
  `status` varchar(5) DEFAULT NULL COMMENT '狀態 (0未結算,1結算)',
  `settlementtime` datetime DEFAULT NULL COMMENT '結算時間',
  `settlementbusinessid` int(11) DEFAULT NULL COMMENT '關聯的商家結算ID（主要是用于商家結算后便于后期檢查）',
  `internalid` int(11) DEFAULT NULL COMMENT '内部统计ID',
  `mpaysettlement` int(11) DEFAULT NULL COMMENT 'Mpay結算id',
  `mpayintegral` decimal(10,2) DEFAULT NULL COMMENT 'mpay積分',
  `is_mpay` int(10) DEFAULT NULL COMMENT '是否MPAY',
  `is_member` tinyint(4) DEFAULT '0' COMMENT '是否Member 1是 0否',
  `memberintegral` decimal(8,2) DEFAULT NULL COMMENT '會員卡積分',
  `optionalid` int(11) DEFAULT NULL COMMENT '選填報表ID',
  `is_voucher` tinyint(2) DEFAULT '0' COMMENT '是否派券（1是、0否、默認0）',
  `is_settlement` tinyint(4) DEFAULT '1' COMMENT '是否結算(1是，0否)',
  `storeMid` varchar(50) DEFAULT NULL COMMENT '店長ID，交易核銷券使用，用於區分是哪個店長登錄終端機，然後分別對賬',
  `is_a_open` tinyint(1) DEFAULT '0' COMMENT '是否A+訂單：0否，1是',
  `third_party_settlement_price` decimal(10,2) DEFAULT '0.00' COMMENT '第三方渠道結算價格',
  `a_fee_type` varchar(30) DEFAULT NULL COMMENT '收費類型，從收費列表中讀取',
  `a_fee_rate` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '費率，從收費列表讀取',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '折扣金額',
  `third_party_pay` decimal(10,2) DEFAULT '0.00' COMMENT '第三方應付金額',
  `channel_commission` decimal(10,2) DEFAULT '0.00' COMMENT '渠道佣金',
  `retail_price` decimal(10,2) DEFAULT '0.00' COMMENT '福利原價',
  `delivery_type` int(11) DEFAULT NULL COMMENT '配送方式：1-快递发货,2-上门自提,3-同城配送,4-虚拟发货',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  `tracking_no` varchar(64) DEFAULT NULL COMMENT '商家核销订单号',
  `business_redeem_time` datetime DEFAULT NULL COMMENT '商家端核销时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `internalid` (`internalid`) USING BTREE,
  KEY `idx_optionalid` (`optionalid`) USING BTREE,
  KEY `idx_ordercodeid_storeid_businessid` (`ordercodeid`,`storeid`,`businessid`) USING BTREE,
  KEY `settlementbusinessid` (`settlementbusinessid`) USING BTREE,
  KEY `is_mpay` (`is_mpay`) USING BTREE,
  KEY `usetime` (`usetime`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3021022 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單核銷表對應的訂單結算數據表';


-- fooku_prd_20231123.fook_report_profit definition

CREATE TABLE `fook_report_profit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商家結算報表Id',
  `amount` decimal(13,2) DEFAULT NULL COMMENT '交易金額',
  `momecoins` decimal(13,2) DEFAULT NULL COMMENT 'M幣金額(抵扣M幣)',
  `mpayintegral` decimal(13,2) DEFAULT '0.00' COMMENT 'Mpay積分(抵扣積分)',
  `final_amount` decimal(13,2) DEFAULT NULL COMMENT '最後交易金額',
  `wechat_amount` decimal(8,2) DEFAULT NULL COMMENT '微信銀行收款',
  `wechat_fee` decimal(8,2) DEFAULT '0.00' COMMENT '微信手續費',
  `alipay` decimal(8,2) DEFAULT NULL COMMENT '支付寶總收款',
  `alipay_fee` decimal(8,2) DEFAULT '0.00' COMMENT '支付寶手續費',
  `mppay_amount` decimal(13,2) DEFAULT NULL COMMENT '澳門通銀行收款',
  `mppay_fee` decimal(8,2) DEFAULT '0.00' COMMENT '澳門通銀行手續費',
  `cybersource_amount` decimal(8,2) DEFAULT NULL COMMENT 'Cybersource銀行收款',
  `cybersource_fee` decimal(8,2) DEFAULT '0.00' COMMENT 'Cybersource銀行手續費',
  `bank_amount` decimal(8,2) DEFAULT NULL COMMENT '銀行收款',
  `settlement_amount_total` decimal(8,2) DEFAULT NULL COMMENT '需要結算金額數',
  `settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '已核銷結算金額',
  `wait_settlement_amount` decimal(13,2) DEFAULT NULL COMMENT '未核銷結算金額',
  `commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `settlement_commission` decimal(8,2) DEFAULT NULL COMMENT '已結算佣金',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `report_profit_id` int(11) DEFAULT NULL COMMENT '上次利潤報表Id',
  `statistics_report` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_rpt_profit` (`start_time`,`end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=168 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='利潤報表';


-- fooku_prd_20231123.fook_report_settlenedmt_data definition

CREATE TABLE `fook_report_settlenedmt_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL COMMENT '訂單類型（1、福利，2、預約，3、特殊福利）',
  `order_no` varchar(50) DEFAULT NULL COMMENT '訂單號',
  `code` varchar(50) DEFAULT NULL COMMENT '卷碼',
  `retail_price` decimal(8,2) DEFAULT NULL COMMENT '門市價：福利价格',
  `bill_final_amount` decimal(8,2) DEFAULT NULL COMMENT '交易金額:用户支付的金额',
  `point` int(11) DEFAULT NULL COMMENT '積分',
  `point_amount` decimal(8,2) DEFAULT NULL COMMENT '積分金額：经比列换算后的金额',
  `commission` decimal(8,2) DEFAULT NULL COMMENT '佣金金額',
  `jq_amount` decimal(8,2) DEFAULT NULL COMMENT '淨權利金額：商户结算的金额',
  `use_time` datetime DEFAULT NULL COMMENT '券碼核銷的時間',
  `shopid` varchar(150) DEFAULT NULL,
  `shop_name` varchar(100) DEFAULT NULL COMMENT '門店名稱',
  `title_snapshots` varchar(100) DEFAULT NULL COMMENT '核銷券的名稱',
  `trans_type` int(11) DEFAULT NULL COMMENT '交易类型：1：核销 2：撤销',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_no` (`order_no`) USING BTREE,
  KEY `idx_use_time` (`use_time`) USING BTREE,
  KEY `idx_rpt_upload_time` (`upload_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=663343 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='mCoin 核銷數據統計表（每天生成）';


-- fooku_prd_20231123.fook_report_shop_settlenemt definition

CREATE TABLE `fook_report_shop_settlenemt` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '門店結算報表Id',
  `merchant_settlement_id` int(11) DEFAULT NULL COMMENT '商家結算報表Id',
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `shop_id` int(11) DEFAULT NULL COMMENT '門店Id',
  `bank_account` varchar(30) DEFAULT NULL COMMENT '結算銀行賬戶',
  `account_name` varchar(100) DEFAULT NULL COMMENT '結算賬戶姓名',
  `bank` varchar(120) DEFAULT NULL COMMENT '結算銀行',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `amount` decimal(8,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(8,2) DEFAULT NULL COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uidx_rpt_shop_settlenemt` (`start_time`,`end_time`,`merchant_settlement_id`,`seller_id`,`shop_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37464 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='門店結算報表';


-- fooku_prd_20231123.fook_report_shop_settlenemt_optional definition

CREATE TABLE `fook_report_shop_settlenemt_optional` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '門店結算報表Id',
  `optionalid` int(11) DEFAULT NULL COMMENT '商家選發結算報表Id',
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `shop_id` int(11) DEFAULT NULL COMMENT '門店Id',
  `bank_account` varchar(30) DEFAULT NULL COMMENT '結算銀行賬戶',
  `account_name` varchar(100) DEFAULT NULL COMMENT '結算賬戶姓名',
  `bank` varchar(120) DEFAULT NULL COMMENT '結算銀行',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `amount` decimal(8,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(8,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(8,2) DEFAULT NULL COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uidx_rpt_shop_settlenemt_opt` (`start_time`,`end_time`,`optionalid`,`seller_id`,`shop_id`) USING BTREE,
  KEY `shop_id` (`shop_id`) USING BTREE,
  KEY `optionalid` (`optionalid`) USING BTREE,
  KEY `seller_id` (`seller_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=119138 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='門店選發結算報表';


-- fooku_prd_20231123.fook_report_trans_order definition

CREATE TABLE `fook_report_trans_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `request_terminalCode` varchar(64) NOT NULL COMMENT '終端請求號',
  `original_terminalCode` varchar(64) DEFAULT NULL COMMENT '原終端請求號(退或撤时有)',
  `voucherCode` varchar(50) NOT NULL COMMENT '券码',
  `merchantCode` varchar(50) NOT NULL COMMENT '澳門通商戶號',
  `branchCode` varchar(50) NOT NULL COMMENT '澳門通門店號',
  `terminalCode` varchar(100) NOT NULL COMMENT '澳門通终端号',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '券福利类型,10交易核銷券',
  `trans_coupon_type` tinyint(4) NOT NULL COMMENT '券类型，0滿減券，1折扣券',
  `coupon_money` decimal(10,1) unsigned DEFAULT '0.0' COMMENT '券金额【計算后返回的優惠金額】',
  `original_coupon_money` decimal(10,1) unsigned DEFAULT '0.0' COMMENT '原券金额【計算后返回的優惠金額】(退或撤时有)',
  `currency` tinyint(4) DEFAULT '0' COMMENT '券币种,0MOP',
  `created_at` datetime DEFAULT NULL COMMENT '交易時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  `trans_type` tinyint(4) DEFAULT NULL COMMENT '交易类型(核销或撤销或退款),1核銷，0撤銷',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态(默认给成功的即可),1成功，0未發送',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `create_time` (`created_at`) USING BTREE,
  KEY `request_terminalCode` (`request_terminalCode`) USING BTREE,
  KEY `voucherCode` (`voucherCode`) USING BTREE,
  KEY `trans_type` (`trans_type`) USING BTREE,
  KEY `merchantCode` (`merchantCode`,`branchCode`,`terminalCode`) USING BTREE,
  KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=294 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.fook_score_log definition

CREATE TABLE `fook_score_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `uuid` varchar(255) DEFAULT NULL COMMENT '唯一标识',
  `uid` int(11) DEFAULT NULL COMMENT '用户ID',
  `orderid` int(11) DEFAULT NULL COMMENT '订单ID',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `tradeno` varchar(255) DEFAULT NULL COMMENT '支付平台的支付订单号',
  `out_request_no` varchar(255) DEFAULT NULL COMMENT '退款的标识码',
  `score` int(11) DEFAULT NULL COMMENT '支付积分',
  `oparemtion` varchar(10) DEFAULT NULL COMMENT '操作类型（支付pay，退款refund）',
  `type` varchar(20) DEFAULT NULL COMMENT '支付类型(wachat,alipay,macau,cybersource)',
  `status` int(11) DEFAULT NULL COMMENT '支付状态',
  `createtime` datetime DEFAULT NULL COMMENT '支付创建时间',
  `reason` varchar(255) DEFAULT NULL COMMENT '返回备注',
  `content` varchar(2500) DEFAULT NULL COMMENT '发送的报文',
  `reasoncontent` text COMMENT '返回的报文',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `paytime` datetime DEFAULT NULL COMMENT '支付时间（支付平台返回的时间）',
  `refund_id` int(11) DEFAULT NULL COMMENT '退款表id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE,
  KEY `idx_status_oparemtion_type_createtime` (`status`,`oparemtion`,`type`,`createtime`) USING BTREE,
  KEY `tradeno` (`tradeno`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `uuid` (`uuid`) USING BTREE,
  KEY `refund_id` (`refund_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2773660 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='積分支付平臺返回的流水記錄';


-- fooku_prd_20231123.fook_show definition

CREATE TABLE `fook_show` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '专场的id',
  `cn_name` varchar(255) DEFAULT NULL COMMENT '专场的中文名称',
  `en_name` varchar(255) DEFAULT NULL COMMENT '专场的英文名称',
  `banner_img` json NOT NULL COMMENT 'banner图片路径(数组)',
  `href_url` varchar(255) DEFAULT NULL COMMENT '跳转的链接',
  `clause_content` longtext COMMENT '条款内容',
  `zone_name_switch` tinyint(4) DEFAULT '1' COMMENT '控制只有一个专区的时，是否显示专区名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `show_backgroud` varchar(64) DEFAULT NULL COMMENT '背景颜色',
  `show_theme` int(11) DEFAULT NULL COMMENT '主题',
  `banner_title` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'banner的标题',
  `url_type` int(11) DEFAULT '0' COMMENT 'url类型"0": "無","1": "福利","2": "門店","3": "外部鏈接","4": "實物鏈路","5": "專場廣告"',
  `banner_src` varchar(1024) DEFAULT NULL COMMENT 'banner跳转',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='专场表';


-- fooku_prd_20231123.fook_show_snapup_session definition

CREATE TABLE `fook_show_snapup_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '產品Id',
  `start_time` datetime DEFAULT NULL COMMENT '場次開始時段',
  `end_time` datetime DEFAULT NULL COMMENT '場次結束時段',
  `cn_title` varchar(255) DEFAULT NULL COMMENT '場次的中文标题',
  `en_title` varchar(255) DEFAULT NULL COMMENT '場次的英文标题',
  `session_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '場次类型：0-搶購專場;1-H5專場;2-所有',
  `show_id` int(11) DEFAULT NULL COMMENT 'H5专场的id',
  `show_snapup_id` int(11) DEFAULT NULL COMMENT '搶購專場的id',
  `valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效，0否1是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_start_end_time` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='搶購專場場次表';


-- fooku_prd_20231123.fook_show_snapup_session_product definition

CREATE TABLE `fook_show_snapup_session_product` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `session_id` int(11) NOT NULL COMMENT '场次id',
  `product_id` int(11) NOT NULL COMMENT '福利产品id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `product_sort` int(11) DEFAULT '0' COMMENT '專區福利排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_session_id_product_id` (`session_id`,`product_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='搶購專場場次福利关联表';


-- fooku_prd_20231123.fook_show_zone definition

CREATE TABLE `fook_show_zone` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '专区的id',
  `show_id` int(11) NOT NULL COMMENT '专场的id',
  `cn_title` varchar(255) DEFAULT NULL COMMENT '专区的中文标题',
  `en_title` varchar(255) DEFAULT NULL COMMENT '专区的英文标题',
  `valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效，0否1是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `sort` int(11) DEFAULT '0' COMMENT '專區排序',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_show_id` (`show_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=124 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='专场-专区表';


-- fooku_prd_20231123.fook_show_zone_product definition

CREATE TABLE `fook_show_zone_product` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `zone_id` int(11) NOT NULL COMMENT '专区id',
  `product_id` int(11) NOT NULL COMMENT '福利产品id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `sort` int(11) DEFAULT '0' COMMENT '專區福利排序',
  `show_in_zone` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否展示',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_zone_id_product_id` (`zone_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10054 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='专场-专区-福利商品表';


-- fooku_prd_20231123.fook_sms_log definition

CREATE TABLE `fook_sms_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from` varchar(50) DEFAULT NULL COMMENT '從哪個號碼發送出來',
  `to` varchar(50) DEFAULT NULL COMMENT '要發送哪個號碼',
  `location` varchar(10) DEFAULT NULL COMMENT '地區',
  `sms` varchar(255) DEFAULT NULL COMMENT '短信內容',
  `url` varchar(500) DEFAULT NULL COMMENT '發送地址',
  `status` varchar(10) DEFAULT NULL COMMENT '狀態',
  `returnstatus` varchar(10) DEFAULT NULL COMMENT '返回狀態',
  `returnreason` text COMMENT '返回信息',
  `createtime` datetime DEFAULT NULL COMMENT '發送時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1327 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##發送用戶記錄##';


-- fooku_prd_20231123.fook_sms_user definition

CREATE TABLE `fook_sms_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `area` varchar(5) DEFAULT NULL COMMENT '区号',
  `phone` varchar(50) DEFAULT NULL COMMENT '发送号码',
  `code` varchar(50) DEFAULT NULL COMMENT '验证码',
  `status` varchar(5) DEFAULT NULL COMMENT '状态',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1329 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##驗證用戶記錄表##';


-- fooku_prd_20231123.fook_store_qrcode definition

CREATE TABLE `fook_store_qrcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `store_id` int(11) NOT NULL COMMENT '门店id',
  `wechat_url` varchar(255) DEFAULT NULL COMMENT '微信扫码跳转链接',
  `url` varchar(255) DEFAULT NULL COMMENT '其他浏览器跳转链接',
  `wechatswitch` int(1) DEFAULT NULL COMMENT '是否跳转微信',
  `urlswitch` int(1) DEFAULT NULL COMMENT '是否跳转链接',
  `enable` int(1) DEFAULT '1' COMMENT '是否有效 0为无效 1为有效',
  `qrcode_url` varchar(255) DEFAULT '' COMMENT 'qrcode保存路徑',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='門店二維碼表';


-- fooku_prd_20231123.fook_stores definition

CREATE TABLE `fook_stores` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '門店ID',
  `store_number` varchar(150) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店編號',
  `store_type_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '門店類別id',
  `business_information_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '' COMMENT '所屬商圈id',
  `area_id` smallint(4) DEFAULT '1' COMMENT '地區id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶id',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '商家ID',
  `img` text CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT '門店封面',
  `img_zip` text COMMENT '門店封面壓縮圖片',
  `name` varchar(150) NOT NULL DEFAULT '' COMMENT '門店名稱',
  `phone` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店電話',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店地址',
  `longitude` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '經度',
  `dimension` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '維度',
  `google_longitude` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '谷歌經度',
  `google_demension` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '谷歌維度',
  `score` decimal(10,0) DEFAULT NULL COMMENT '評分',
  `score_number` int(11) DEFAULT NULL COMMENT '總評價人數',
  `expense` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '人均消費',
  `expense_number` int(11) DEFAULT NULL COMMENT '人均消費人數',
  `head_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '負責人姓名',
  `head_tel` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '負責人聯繫電話',
  `head_email` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '負責人郵箱',
  `detail` text CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT '簡介',
  `reservation_time` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '訂座時段T&C',
  `charge` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '成功訂座收費',
  `google_index` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '谷歌坐標',
  `scott_index` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '高德坐標',
  `provide_services` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '提供服務 1.WiFi 2.停車場 3.便利店 4.換油服務',
  `business_time` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '營業時間',
  `if_new` tinyint(4) DEFAULT NULL COMMENT '是否新加盟(1是 0否)',
  `verification_pass` varchar(150) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '核銷密碼',
  `login_account` varchar(150) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '登錄賬號',
  `login_pass` varchar(150) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '登錄密碼',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效（0、無效 1、有效，2、凍結  3、已結業）',
  `macau_pass_merchant_number` varchar(200) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT ' ' COMMENT '澳門通商戶號',
  `macau_pass_terminal_number` varchar(200) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT ' ' COMMENT '澳門通終端號',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更改時間',
  `collect_times` int(11) DEFAULT '0' COMMENT '收藏次数',
  `is_selected` tinyint(2) DEFAULT '0',
  `shop_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '會員卡門店id',
  `is_general` tinyint(4) DEFAULT '1' COMMENT '是否會員卡通用門店0：通用 1：特定門店',
  `alone_settlement` tinyint(4) DEFAULT '0' COMMENT '是否單獨結算0：否、1是',
  `settlement_account` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '結算銀行賬戶',
  `settlement_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '結算賬戶姓名',
  `settlement_bank` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '結算銀行名稱',
  `istemporary` tinyint(1) DEFAULT NULL,
  `background_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '背景圖',
  `if_update` tinyint(1) DEFAULT '0' COMMENT '是否已更新(0否 1是)',
  PRIMARY KEY (`id`,`macau_pass_terminal_number`) USING BTREE,
  KEY `stores_business_id_index` (`business_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4187 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商家門店信息表';


-- fooku_prd_20231123.fook_stores_keyword definition

CREATE TABLE `fook_stores_keyword` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` int(11) DEFAULT '0' COMMENT '父級ID',
  `name` varchar(120) DEFAULT '' COMMENT '關鍵字名稱',
  `english_name` varchar(120) DEFAULT ' ' COMMENT '英文名稱',
  `sort` int(11) DEFAULT NULL COMMENT '排序（值越大權重越高）',
  `enable` tinyint(1) DEFAULT '1' COMMENT '1有效 0無效',
  `icon` varchar(255) DEFAULT NULL COMMENT '圖表',
  `if_index_show` tinyint(1) DEFAULT '0' COMMENT '是否首頁顯示1顯示0不顯示',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  `src` varchar(100) DEFAULT 'classify' COMMENT '跳轉的相對路徑',
  `src_type` int(11) DEFAULT NULL COMMENT '链接分类',
  `uuid` int(11) DEFAULT NULL COMMENT '門店/商家id',
  `extend_info` varchar(255)  DEFAULT '' COMMENT '额外参数',

  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)門店關鍵字表';


-- fooku_prd_20231123.fook_stores_keyword_class definition

CREATE TABLE `fook_stores_keyword_class` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '門店關鍵字關聯ID',
  `stores_id` int(11) DEFAULT NULL COMMENT '門店ID',
  `stores_keyword_id` int(11) DEFAULT NULL COMMENT '關鍵字ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_stores_keyword_id_stores_id` (`stores_keyword_id`,`stores_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13618 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)門店關鍵字關聯表';


-- fooku_prd_20231123.fook_stores_show definition

CREATE TABLE `fook_stores_show` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '門店ID',
  `store_number` varchar(150) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店編號',
  `store_type_id` varchar(100) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '門店類別id',
  `store_type_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `business_information_id` varchar(100) COLLATE utf8_unicode_ci DEFAULT '' COMMENT '所屬商圈id',
  `business_information_name` int(11) DEFAULT NULL,
  `business_information_discount` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `area_id` smallint(4) DEFAULT '1' COMMENT '地區id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶id',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '商家ID',
  `img` text COLLATE utf8_unicode_ci COMMENT '門店封面',
  `name` varchar(150) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店名稱',
  `phone` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店電話',
  `address` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店地址',
  `longitude` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '經度',
  `dimension` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '維度',
  `google_longitude` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '谷歌經度',
  `google_demension` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '谷歌維度',
  `score` decimal(10,0) DEFAULT NULL COMMENT '評分',
  `score_number` int(11) DEFAULT NULL COMMENT '總評價人數',
  `expense` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '人均消費',
  `expense_number` int(11) DEFAULT NULL COMMENT '人均消費人數',
  `head_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '負責人姓名',
  `head_tel` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '負責人聯繫電話',
  `head_email` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '負責人郵箱',
  `detail` text COLLATE utf8_unicode_ci COMMENT '簡介',
  `reservation_time` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '訂座時段T&C',
  `charge` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '成功訂座收費',
  `google_index` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '谷歌坐標',
  `scott_index` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '高德坐標',
  `provide_services` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '提供服務 1.WiFi 2.停車場 3.便利店 4.換油服務',
  `business_time` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '營業時間',
  `if_new` tinyint(4) DEFAULT NULL COMMENT '是否新加盟(1是 0否)',
  `verification_pass` varchar(150) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '核銷密碼',
  `login_account` varchar(150) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '登錄賬號',
  `login_pass` varchar(150) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '登錄密碼',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效（0、無效 1、有效，2、凍結  3、已結業）',
  `macau_pass_merchant_number` varchar(200) COLLATE utf8_unicode_ci NOT NULL DEFAULT ' ' COMMENT '澳門通商戶號',
  `macau_pass_terminal_number` varchar(200) COLLATE utf8_unicode_ci NOT NULL DEFAULT ' ' COMMENT '澳門通終端號',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更改時間',
  `collect_times` int(11) DEFAULT '0' COMMENT '收藏次数',
  `address2` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '門店地址',
  `branch` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`,`macau_pass_terminal_number`) USING BTREE,
  KEY `stores_business_id_index` (`business_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=473 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='商家門店信息表';


-- fooku_prd_20231123.fook_stores_translations definition

CREATE TABLE `fook_stores_translations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id自增',
  `stores_id` int(10) unsigned NOT NULL COMMENT '門店id',
  `t_name` varchar(255) DEFAULT NULL COMMENT '門店名',
  `t_address` varchar(255) DEFAULT NULL COMMENT '地址',
  `t_detail` varchar(255) DEFAULT NULL COMMENT '詳情',
  `t_reservation_time` varchar(255) DEFAULT NULL COMMENT '訂座時段T&C',
  `t_expense` varchar(255) DEFAULT NULL COMMENT '人均消費',
  `t_business_time` varchar(255) DEFAULT NULL COMMENT '營業時間',
  `locale` varchar(255) DEFAULT NULL COMMENT '語言',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `fook_stores_translations_stores_id_locale_unique` (`stores_id`,`locale`) USING BTREE,
  KEY `fook_stores_translations_locale_index` (`locale`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1034 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='門店信息對應的翻譯表';


-- fooku_prd_20231123.fook_stores_type definition

CREATE TABLE `fook_stores_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '門店類別ID',
  `pid` int(11) DEFAULT '0' COMMENT '父級ID',
  `name` varchar(120) DEFAULT '' COMMENT '名稱',
  `traditional_name` varchar(120) DEFAULT ' ' COMMENT '繁体名稱',
  `simplified_name` varchar(120) DEFAULT ' ' COMMENT '简体名称',
  `english_name` varchar(120) DEFAULT ' ' COMMENT '英文名稱',
  `sort` int(11) DEFAULT NULL COMMENT '0首頁不顯示，其他首頁顯示并排序',
  `enable` tinyint(1) DEFAULT '1' COMMENT '1有效 0無效',
  `icon` varchar(255) DEFAULT ' ' COMMENT '圖表',
  `type` int(8) DEFAULT NULL COMMENT '類型',
  `if_index_show` tinyint(1) DEFAULT '1' COMMENT '是否首頁顯示 1顯示 0不顯示',
  `if_hot` tinyint(1) DEFAULT '1' COMMENT '是否熱門 1熱門 0不熱門',
  `code` varchar(255) DEFAULT ' ' COMMENT '編碼',
  `distinguish` tinyint(4) DEFAULT '0' COMMENT '区分是0是mcoin模塊 1會員卡模塊',
  `business_id` int(11) DEFAULT NULL COMMENT '商家id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sort` (`sort`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='門店類別表';


-- fooku_prd_20231123.fook_stores_type_class definition

CREATE TABLE `fook_stores_type_class` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '門店類別關聯ID',
  `stores_id` int(11) DEFAULT NULL COMMENT '門店ID',
  `stores_type_id` int(11) DEFAULT NULL COMMENT '類別ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_stores_type_id_stores_id` (`stores_type_id`,`stores_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18011 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='門店類別關聯表';


-- fooku_prd_20231123.fook_stores_type_translations definition

CREATE TABLE `fook_stores_type_translations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `stores_type_id` int(11) DEFAULT NULL COMMENT '門店類別id外鍵',
  `t_name` varchar(255) DEFAULT NULL COMMENT '類別英文名稱',
  `locale` varchar(255) DEFAULT NULL COMMENT '語言',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `fook_stores_type_translations_stores_type_id_locale_unique` (`stores_type_id`,`locale`) USING BTREE,
  KEY `fook_stores_type_translations_locale_index` (`locale`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='門店類別翻譯表';


-- fooku_prd_20231123.fook_stores_vr definition

CREATE TABLE `fook_stores_vr` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主鍵ID',
  `stores_id` int(11) DEFAULT NULL COMMENT '門店id',
  `graphics` varchar(255) DEFAULT ' ' COMMENT '歡迎繪圖',
  `sound` varchar(255) DEFAULT ' ' COMMENT '聲音文件',
  `vr` varchar(1000) DEFAULT NULL COMMENT 'vr視頻',
  `enable` tinyint(1) DEFAULT '1' COMMENT '是否有效(1有效 2 無效)',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `stores_id` (`stores_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='門店VR數據表(H5)';


-- fooku_prd_20231123.fook_system_loginlog definition

CREATE TABLE `fook_system_loginlog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT NULL COMMENT '登錄用戶Id',
  `type` int(11) DEFAULT NULL COMMENT '登錄用戶類型（1后臺管理員，2用戶）',
  `login_time` datetime DEFAULT NULL COMMENT '登錄時間',
  `ip` varchar(50) DEFAULT NULL COMMENT '登錄IP',
  `useragent` varchar(255) DEFAULT NULL COMMENT '登錄瀏覽器用戶代理',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=46186 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##系統登陸日志##';


-- fooku_prd_20231123.fook_temporary_product definition

CREATE TABLE `fook_temporary_product` (
  `temporary_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `id` int(11) DEFAULT NULL COMMENT '福利id',
  `img` varchar(255) DEFAULT NULL COMMENT '福利圖片',
  `zip_img` varchar(255) DEFAULT NULL COMMENT '壓縮圖片',
  `businessid` int(11) DEFAULT NULL COMMENT '商戶id',
  `title` varchar(255) DEFAULT NULL COMMENT '福利名稱',
  `type` int(11) DEFAULT NULL COMMENT '福利類別',
  `retail_price` decimal(10,2) DEFAULT NULL COMMENT '原價格',
  `price` decimal(10,2) DEFAULT NULL COMMENT '門市價格',
  `point_ratio` int(11) unsigned DEFAULT '300' COMMENT '福利積分比例',
  `shelf_status` tinyint(2) DEFAULT NULL COMMENT '上架下狀態',
  `buy_start_time` datetime DEFAULT NULL COMMENT '可購買開始時間',
  `buy_end_time` datetime DEFAULT NULL COMMENT '可購買結束時間',
  `vaild_mode` tinyint(2) DEFAULT NULL COMMENT '有效期類型1時間段 2有效天數',
  `vaild_start_time` datetime DEFAULT NULL COMMENT '可使用有效開始時間',
  `vaild_end_time` datetime DEFAULT NULL COMMENT '可適用有效結束時間',
  `day_number` int(11) DEFAULT NULL COMMENT '有效天數',
  `snap_up` tinyint(2) DEFAULT NULL COMMENT '是否搶購',
  `s_name` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `i_name` varchar(255) DEFAULT NULL COMMENT '商圈名稱',
  `i_name_en` varchar(255) DEFAULT NULL COMMENT '商圈英文名稱',
  `information_id` int(11) DEFAULT NULL COMMENT '商圈id',
  `stores_type` int(255) DEFAULT NULL COMMENT '門店類別id',
  `stores_type_name` varchar(255) DEFAULT NULL COMMENT '門店類別名稱',
  `t_product_name` varchar(255) DEFAULT NULL COMMENT '福利英文名稱',
  `t_stores_name` varchar(255) DEFAULT NULL COMMENT '門店英文名稱',
  `t_stores_type_name` varchar(255) DEFAULT NULL COMMENT '門店類別英文名稱',
  `created_time` datetime DEFAULT NULL COMMENT '創建時間',
  `complex_name` varchar(255) DEFAULT NULL COMMENT '集合搜索',
  `complex_name_en` varchar(255) DEFAULT NULL COMMENT '集合搜索（英文+中文）',
  `only_point` tinyint(2) DEFAULT '0' COMMENT '是否純積分支付',
  `actual_sales` int(11) DEFAULT '0' COMMENT '實際銷量',
  `longitude` varchar(255) DEFAULT NULL COMMENT '經度',
  `dimension` varchar(255) DEFAULT NULL COMMENT '緯度',
  `is_hot` int(11) DEFAULT NULL COMMENT '是否熱門',
  `is_great` int(11) DEFAULT NULL COMMENT '是否搶福',  `business_categories_id` int DEFAULT NULL COMMENT 'category',

  PRIMARY KEY (`temporary_id`) USING BTREE,
  KEY `normal_type_information_st` (`type`,`information_id`,`stores_type`) USING BTREE,
  KEY `normal_complex_name` (`complex_name`) USING BTREE,
  KEY `normal_complex_name_en` (`complex_name_en`) USING BTREE,
  KEY `idx_tmp_product_buy_start_time` (`buy_start_time`) USING BTREE,
  KEY `idx_tmp_product_buy_end_time` (`buy_end_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51674788 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='臨時表福利表';


-- fooku_prd_20231123.fook_trading definition

CREATE TABLE `fook_trading` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `start_time` datetime DEFAULT NULL COMMENT '統計開始時間',
  `end_time` datetime DEFAULT NULL COMMENT '統計結束時間',
  `cycle_type` tinyint(1) DEFAULT NULL COMMENT '發送週期類型(1每日,2每週,3每月)',
  `send_day` int(11) DEFAULT NULL COMMENT '發送幾天的數據',
  `status` tinyint(1) DEFAULT '1' COMMENT '狀態(0無效,1有效)',
  `desc` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '週期描述',
  `file_oss_link` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '鏈接',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=232 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='交易統計週期表';


-- fooku_prd_20231123.fook_trading_data definition

CREATE TABLE `fook_trading_data` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statistics_day` date DEFAULT NULL COMMENT '數據統計日期',
  `user_num` int(11) DEFAULT NULL COMMENT '當天進入用戶數(總)',
  `user_pay_num` int(11) DEFAULT NULL COMMENT '當天進入用戶數(有進行支付的)',
  `use_no_pay_num` int(11) DEFAULT NULL COMMENT '當天進入用戶數(無進行支付的)',
  `business_num` int(11) DEFAULT NULL COMMENT '交易商戶數量',
  `trading_num` int(11) DEFAULT NULL COMMENT '交易筆數',
  `integral_conversion_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額(mcoin積分轉換金額)',
  `payment_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額(客戶實際付款)',
  `payment_sum` decimal(10,2) DEFAULT NULL COMMENT 'mcoin積分轉換金額加客戶實際付款金額',
  `settlement_business_num` int(11) DEFAULT NULL COMMENT '核銷商戶數量',
  `settlement_num` int(11) DEFAULT NULL COMMENT '核銷筆數',
  `settlement_conversion_amount` decimal(10,2) DEFAULT NULL COMMENT '核銷金額(mCoin轉化金額)',
  `settlement_payment_amount` decimal(10,2) DEFAULT NULL COMMENT '核銷金額(MPay支付的金額)',
  `settlement_amount_sum` decimal(10,2) DEFAULT NULL COMMENT '核銷總金額 (核銷mCoin轉化金額加核銷MPay支付的金額)',
  `refund_conversion_amount` decimal(10,2) DEFAULT NULL COMMENT '退款金額(mCoin轉化金額)',
  `refund_payment_amount` decimal(10,2) DEFAULT NULL COMMENT '退款金額(MPay支付的金額)',
  `refund_amount_sum` varchar(255) DEFAULT NULL COMMENT '退款總金額(退款mCoin轉化金額加退款MPay支付的金額)',
  `refund_num` int(11) DEFAULT NULL COMMENT '退款筆數',
  `product_id_hot` int(11) DEFAULT NULL COMMENT '當天銷售商品金額最多的福利',
  `product_title_hot` varchar(255) DEFAULT NULL COMMENT '當天銷售商品金額最多的福利名稱',
  `product_sales_amount` decimal(10,2) DEFAULT NULL COMMENT '熱銷商品銷售金額',
  `product_id_max` int(11) DEFAULT NULL COMMENT '當天銷售商品數量最多的福利',
  `product_title_max` varchar(255) DEFAULT NULL COMMENT '當天銷售商品數量最多的福利名稱',
  `product_num` varchar(255) DEFAULT NULL COMMENT '熱銷商品銷售數量',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更新時間',
  `subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '平台补贴金额',
  `refund_subsidy_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '退款平台补贴金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1331 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='交易統計詳情';


-- fooku_prd_20231123.fook_trading_data_class definition

CREATE TABLE `fook_trading_data_class` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `trading_id` int(11) DEFAULT NULL COMMENT '交易統計週期表',
  `trading_data_id` int(11) DEFAULT NULL COMMENT '交易統計詳情表id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6556 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='交易統計中間表';


-- fooku_prd_20231123.fook_trading_send_email_process definition

CREATE TABLE `fook_trading_send_email_process` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `createtime` datetime DEFAULT NULL COMMENT '創建時間',
  `email_main` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '發送主郵箱',
  `email` varchar(1000) CHARACTER SET utf8 DEFAULT NULL COMMENT '抄送郵箱',
  `send` tinyint(1) DEFAULT NULL COMMENT '發送郵件狀態(0未發送,1已發送,2附件缺失)',
  `sendtime` datetime DEFAULT NULL COMMENT '發送時間',
  `if_upload` tinyint(1) DEFAULT '0' COMMENT '是否上傳(0否,1是)',
  `upload_time` datetime DEFAULT NULL COMMENT '上傳oss時間',
  `trading_id` int(11) DEFAULT NULL COMMENT '交易統計表id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=232 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='mCoin交易統計郵件發送記錄表';


-- fooku_prd_20231123.fook_trading_week definition

CREATE TABLE `fook_trading_week` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主鍵id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id',
  `product_title` varchar(255) DEFAULT NULL COMMENT '福利名稱',
  `pruduct_num` int(11) DEFAULT NULL COMMENT '熱銷商品數量',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '熱銷商品金額(訂單金額)',
  `trading_id` int(11) DEFAULT NULL COMMENT '交易統計週期表id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=79074 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='交易統計熱銷商品周排名';


-- fooku_prd_20231123.fook_user_action_record definition

CREATE TABLE `fook_user_action_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利商品id',
  `type` tinyint(11) DEFAULT NULL COMMENT '用户行为类型，1是确认控酒法提示',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4159 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户行为记录表';


-- fooku_prd_20231123.fook_user_intellisense definition

CREATE TABLE `fook_user_intellisense` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用戶智能感知表Id',
  `userid` int(11) DEFAULT NULL COMMENT '用戶Id',
  `tip` varchar(150) DEFAULT NULL COMMENT '標籤',
  `tip_type` int(11) DEFAULT NULL COMMENT '標籤類型(1跳轉門店列表,2跳轉門店詳情,3跳轉玩樂日誌)',
  `key` varchar(50) DEFAULT NULL COMMENT '參數key',
  `value` varchar(50) DEFAULT NULL COMMENT '參數值',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `source` int(11) DEFAULT NULL COMMENT '用戶來源1微信,2Android,3IOS,4PC)',
  `ip` varchar(50) DEFAULT NULL COMMENT '用戶IP',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2879 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用戶搜索記錄';


-- fooku_prd_20231123.fook_user_log definition

CREATE TABLE `fook_user_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(20) DEFAULT NULL COMMENT '用户ip',
  `type` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `typeid` int(11) DEFAULT NULL COMMENT '操作id',
  `operation` varchar(255) DEFAULT NULL COMMENT '用户操作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `typeid` (`typeid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=503533 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄用戶的日志';


-- fooku_prd_20231123.fook_user_log2 definition

CREATE TABLE `fook_user_log2` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(20) DEFAULT NULL COMMENT '用户ip',
  `type` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `typeid` int(11) DEFAULT NULL COMMENT '操作id',
  `operation` varchar(255) DEFAULT NULL COMMENT '用户操作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `typeid` (`typeid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=599720 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄用戶的日志20200119-V2';


-- fooku_prd_20231123.fook_user_log3 definition

CREATE TABLE `fook_user_log3` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(20) DEFAULT NULL COMMENT '用户ip',
  `type` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `typeid` int(11) DEFAULT NULL COMMENT '操作id',
  `operation` varchar(255) DEFAULT NULL COMMENT '用户操作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `typeid` (`typeid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=608491 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄用戶的日志20200119-V3';


-- fooku_prd_20231123.fook_user_log4 definition

CREATE TABLE `fook_user_log4` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(20) DEFAULT NULL COMMENT '用户ip',
  `type` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `typeid` int(11) DEFAULT NULL COMMENT '操作id',
  `operation` varchar(255) DEFAULT NULL COMMENT '用户操作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `typeid` (`typeid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=675643 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄用戶的日志20200605-V4';


-- fooku_prd_20231123.fook_user_log5 definition

CREATE TABLE `fook_user_log5` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(20) DEFAULT NULL COMMENT '用户ip',
  `type` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `typeid` int(11) DEFAULT NULL COMMENT '操作id',
  `operation` varchar(255) DEFAULT NULL COMMENT '用户操作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `typeid` (`typeid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10455281 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄用戶的日志20200605-V4';


-- fooku_prd_20231123.fook_user_log6 definition

CREATE TABLE `fook_user_log6` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(20) DEFAULT NULL COMMENT '用户ip',
  `type` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `typeid` int(11) DEFAULT NULL COMMENT '操作id',
  `operation` varchar(255) DEFAULT NULL COMMENT '用户操作',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `typeid` (`typeid`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15944343 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄用戶的日志20200605-V4';


-- fooku_prd_20231123.fook_user_momecoins definition

CREATE TABLE `fook_user_momecoins` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用戶賺取M幣記錄表Id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶id',
  `source` varchar(255) DEFAULT NULL COMMENT '賺取來源',
  `task_id` int(11) DEFAULT NULL COMMENT '任務Id',
  `momecoins` decimal(10,2) DEFAULT NULL COMMENT '獲得M幣數',
  `record_type` int(11) DEFAULT NULL COMMENT '主鍵類型(0無,1訂單Id,2評價Id,3門店Id)',
  `record_id` int(11) DEFAULT NULL COMMENT '主鍵Id',
  `ip` varchar(255) DEFAULT NULL COMMENT '用戶IP',
  `sign` varchar(255) DEFAULT NULL COMMENT '簽名',
  `notes` varchar(255) DEFAULT NULL COMMENT '備註',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `title` varchar(255) DEFAULT NULL COMMENT '標題',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5951 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用户M幣使用記錄';


-- fooku_prd_20231123.fook_user_msg definition

CREATE TABLE `fook_user_msg` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用戶消息表Id',
  `type` int(11) DEFAULT NULL COMMENT '消息分類（1預約，2福利，3後台）',
  `message` varchar(1000) DEFAULT NULL COMMENT '消息',
  `userid` int(11) DEFAULT NULL COMMENT '用戶ID',
  `is_read` int(11) DEFAULT NULL COMMENT '是否已讀(1是0否)',
  `url` varchar(500) DEFAULT NULL COMMENT '鏈接',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `read_time` datetime DEFAULT NULL COMMENT '查看时间',
  `enable` int(11) DEFAULT NULL COMMENT 'enable(是否有效 0无效1有效)',
  `user_type` int(11) DEFAULT NULL COMMENT '用戶類型(1.用戶，2商家)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5148010 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)用戶推送信息';


-- fooku_prd_20231123.fook_user_push definition

CREATE TABLE `fook_user_push` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msgid` int(11) DEFAULT NULL COMMENT '關聯用戶消息表id',
  `type` int(11) DEFAULT NULL COMMENT '消息分類（1預約，2福利，3後台）',
  `userid` int(11) DEFAULT NULL COMMENT '用戶ID',
  `message` varchar(1000) DEFAULT NULL COMMENT '通知信息',
  `error` varchar(1000) DEFAULT NULL COMMENT '推送異常信息',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `registratationid` varchar(80) DEFAULT NULL COMMENT '推送註冊id',
  `token` varchar(255) DEFAULT NULL COMMENT '发送的Token',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5144280 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄用戶推送App的日志';


-- fooku_prd_20231123.fook_userinfo_password_log definition

CREATE TABLE `fook_userinfo_password_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) NOT NULL COMMENT '用户ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##登陆记录表##';


-- fooku_prd_20231123.fook_visit_log definition

CREATE TABLE `fook_visit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶id',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=210129 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='記錄MPay用戶登陸的日志';


-- fooku_prd_20231123.fook_voucher_assignment definition

CREATE TABLE `fook_voucher_assignment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶id',
  `product_id` int(11) DEFAULT NULL COMMENT '福利ID',
  `voucher_code` varchar(255) DEFAULT NULL COMMENT '派券號',
  `voucher_status` tinyint(4) DEFAULT '0' COMMENT '派券狀態（0創建任務、1上傳excel、2執行任務中、3執行完成、4失敗）',
  `wait_count` int(11) DEFAULT '0' COMMENT '待發券數量',
  `success_count` int(11) DEFAULT '0' COMMENT '發券成功數量',
  `error_count` int(11) DEFAULT '0' COMMENT '發券失敗數量',
  `follower` varchar(255) DEFAULT NULL COMMENT '跟進人',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  `voucher_at` datetime DEFAULT NULL COMMENT '派券訂單日期',
  `file_at` datetime DEFAULT NULL COMMENT '提交文件日期',
  `excel_url` varchar(1000) DEFAULT NULL COMMENT '上傳文件路徑',
  `msg` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=275 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='創建派券任務表';


-- fooku_prd_20231123.fook_voucher_assignment_data definition

CREATE TABLE `fook_voucher_assignment_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `voucher_id` int(11) DEFAULT NULL COMMENT 'fook_voucher_assignment表ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用戶ID',
  `order_id` int(11) DEFAULT NULL COMMENT '訂單ID',
  `order_no` varchar(255) DEFAULT NULL COMMENT '訂單號',
  `product_id` int(11) DEFAULT NULL COMMENT '福利ID',
  `product_title` varchar(255) DEFAULT NULL COMMENT '優惠券名稱',
  `product_type` tinyint(4) DEFAULT NULL COMMENT '優惠券類型',
  `number` int(11) DEFAULT '1' COMMENT '派券數量',
  `status` tinyint(4) DEFAULT '1' COMMENT '發券狀態（1待執行、2成功、3失敗）',
  `area_code` varchar(255) DEFAULT NULL COMMENT '區號',
  `phone` varchar(255) DEFAULT NULL COMMENT '手機號碼',
  `remark` varchar(255) DEFAULT NULL COMMENT '備註',
  `upload_time` datetime DEFAULT NULL COMMENT '上傳時間',
  `send_time` datetime DEFAULT NULL COMMENT '發券時間（實際就是執行任務時間）',
  `arrive_time` datetime DEFAULT NULL COMMENT '發券到賬時間',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '修改時間',
  `message` varchar(255) DEFAULT NULL COMMENT '失敗錯誤描述',
  `user_status` tinyint(4) DEFAULT '0' COMMENT '用戶是否已經領取查看（0未領取、1已領取）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=564 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='創建派券任務數據表';


-- fooku_prd_20231123.fook_voucher_settlement definition

CREATE TABLE `fook_voucher_settlement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT NULL COMMENT '抵扣M幣',
  `mpayintegral` decimal(10,2) DEFAULT NULL COMMENT '抵扣積分',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_cycle` varchar(255) DEFAULT NULL COMMENT '結算週期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##派券結算統計表##';


-- fooku_prd_20231123.fook_voucher_settlement_data definition

CREATE TABLE `fook_voucher_settlement_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `ordercodeid` int(11) DEFAULT NULL COMMENT '訂單核銷ID',
  `productid` int(11) DEFAULT NULL COMMENT '福利ID',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單ID',
  `businessname` varchar(255) DEFAULT NULL COMMENT '商家名稱',
  `storename` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `businesscode` varchar(255) DEFAULT NULL COMMENT '商家編號',
  `bankaccount` varchar(255) DEFAULT NULL COMMENT '結算銀行帳號',
  `bankname` varchar(255) DEFAULT NULL COMMENT '結算銀行名稱',
  `bank` varchar(255) DEFAULT NULL COMMENT '結算銀行',
  `createtime` datetime DEFAULT NULL COMMENT '下單時間',
  `usetime` datetime DEFAULT NULL COMMENT '使用時間',
  `billamount` decimal(10,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `userpaymentamount` decimal(10,2) DEFAULT NULL COMMENT '攤派支付金額',
  `mpayintegral` decimal(10,2) DEFAULT NULL COMMENT '攤派mpay積分',
  `momecoinsamount` decimal(10,2) DEFAULT NULL COMMENT '攤派m幣',
  `mpayintegral_exchange_amount` decimal(10,2) DEFAULT NULL COMMENT 'mpay積分兌換金額',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '傭金',
  `merchantsettleamount` decimal(10,2) DEFAULT NULL COMMENT '商家結算金額',
  `vouchercode` varchar(255) DEFAULT NULL COMMENT '核銷碼',
  `vouchername` varchar(255) DEFAULT NULL COMMENT '核銷商品名稱',
  `ordertransaction` varchar(255) DEFAULT NULL COMMENT '訂單流水號',
  `settlementtime` datetime DEFAULT NULL COMMENT '結算時間',
  `voucher_id` int(11) DEFAULT NULL COMMENT 'fook_voucher_settlement結算表ID',
  `is_mpay` int(11) DEFAULT NULL COMMENT '是否MPAY',
  `is_member` tinyint(4) DEFAULT '0' COMMENT '是否Member 1是 0否',
  `memberintegral` decimal(8,2) DEFAULT '0.00' COMMENT '攤派會員卡積分',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `normal_voucher_id` (`voucher_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=175 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='對應的派券結算統計數據表';


-- fooku_prd_20231123.fook_wechat_accesstoken definition

CREATE TABLE `fook_wechat_accesstoken` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `wechat_userid` int(11) DEFAULT NULL COMMENT '關聯微信用戶ID',
  `accesstoken` varchar(500) DEFAULT NULL COMMENT 'token',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5808 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##微信授權信息##';


-- fooku_prd_20231123.fook_wechat_jsapiticket definition

CREATE TABLE `fook_wechat_jsapiticket` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `wechat_userid` int(11) DEFAULT NULL COMMENT '關聯微信用戶ID',
  `jsapiticket` varchar(500) DEFAULT NULL COMMENT 'JsapiTicket',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5831 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)微信用戶ticket記錄';


-- fooku_prd_20231123.fook_wechat_sendrecord definition

CREATE TABLE `fook_wechat_sendrecord` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `userid` int(11) DEFAULT NULL COMMENT '平臺用戶ID',
  `openid` varchar(500) DEFAULT NULL COMMENT '用戶微信openid',
  `templateid` int(11) DEFAULT NULL COMMENT '模板Id',
  `message` longtext COMMENT '發送內容',
  `send_time` datetime DEFAULT NULL COMMENT '發送時間',
  `messageid` varchar(50) DEFAULT NULL COMMENT '消息ID',
  `status` varchar(50) DEFAULT NULL COMMENT '發送狀態',
  `callback_status` varchar(50) DEFAULT NULL COMMENT '回調狀態',
  `callback_time` datetime DEFAULT NULL COMMENT '回調時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=943 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)微信推送記錄';


-- fooku_prd_20231123.fook_wechat_setting definition

CREATE TABLE `fook_wechat_setting` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置Id',
  `type` int(11) DEFAULT NULL COMMENT '類型（1、門店分享，2、產品分享，3、評價分享）',
  `img` varchar(255) DEFAULT NULL COMMENT '分享圖片',
  `title` varchar(255) DEFAULT NULL COMMENT '分享標題',
  `description` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL COMMENT '分享鏈接',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##微信设置表##';


-- fooku_prd_20231123.fook_wechat_template definition

CREATE TABLE `fook_wechat_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板Id',
  `type` int(11) DEFAULT NULL COMMENT '模板類型',
  `templateid` varchar(255) DEFAULT NULL COMMENT '微信模板ID',
  `title` varchar(255) DEFAULT NULL COMMENT '模板標題',
  `content` longtext COMMENT '發送文本',
  `json` longtext COMMENT 'Json格式',
  `enable` int(11) DEFAULT NULL COMMENT '是否有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##微信推送模版##';


-- fooku_prd_20231123.fook_wechat_template_log definition

CREATE TABLE `fook_wechat_template_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) DEFAULT NULL COMMENT '微信id',
  `template_id` varchar(100) DEFAULT NULL COMMENT '模板id',
  `data` text COMMENT '发送参数',
  `wechatmsg` text COMMENT '微信返回值',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='推送微信端消息的日志';


-- fooku_prd_20231123.fook_wechat_userinfo definition

CREATE TABLE `fook_wechat_userinfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '微信用戶Id',
  `openid` varchar(255) DEFAULT NULL COMMENT '微信OpenId',
  `nick_name` varchar(255) DEFAULT NULL,
  `sex` tinyint(1) DEFAULT NULL COMMENT '性別（1、男性，2、女性，0、未知）',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `country` varchar(50) DEFAULT NULL COMMENT '國家',
  `avatar` varchar(255) DEFAULT NULL COMMENT '頭像',
  `unionid` varchar(255) DEFAULT NULL COMMENT 'Unionid',
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  `platform_userid` int(11) DEFAULT NULL COMMENT '平台綁定的用戶Id',
  `first_authorized_time` datetime DEFAULT NULL COMMENT '首次授權時間',
  `binding` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `openid` (`openid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=803 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='(*)微信用戶信息';


-- fooku_prd_20231123.mcoin_coupon_inf definition

CREATE TABLE `mcoin_coupon_inf` (
  `COUPON_ID` varchar(21) NOT NULL COMMENT '优惠券id（主鍵）',
  `COUPON_CODE` varchar(32) NOT NULL COMMENT '券碼',
  `STORE_ID` varchar(32) NOT NULL COMMENT '门店id',
  `STORE_NAME` varchar(200) DEFAULT NULL COMMENT '门店名称',
  `STORE_NAME_EN` varchar(200) DEFAULT NULL COMMENT '门店英文名',
  `STORE_ICON_URL` varchar(200) DEFAULT NULL COMMENT '门店icon地址',
  `COUPON_RULE_ID` varchar(32) NOT NULL COMMENT '券規則ID',
  `COUPON_NAME` varchar(200) DEFAULT NULL COMMENT '券名称',
  `COUPON_NAME_EN` varchar(200) DEFAULT NULL COMMENT '券英文名',
  `COUPON_TYPE` varchar(2) DEFAULT NULL COMMENT '券類型 0-其他 1-亞洲萬裡通',
  `ICON_URL` varchar(200) DEFAULT NULL COMMENT '券icon地址',
  `COUPON_DETAIL_URL` varchar(200) DEFAULT NULL COMMENT '券详情页地址',
  `USER_ID` varchar(50) DEFAULT NULL COMMENT '所属用户ID',
  `MOBILE` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `COUPON_TNC` text COMMENT '使用須知',
  `AMOUNT` varchar(15) DEFAULT NULL COMMENT '金额（单位：分）',
  `INTEGRAL` varchar(15) DEFAULT NULL COMMENT '积分',
  `COUPON_STATUS` varchar(2) NOT NULL COMMENT '01-待使用02-已核銷03-已退款04-已過期05-已失效06-退款待審核07-兌換中',
  `COUPON_STATUS_TIME` varchar(19) DEFAULT NULL COMMENT '券狀態變更時間（用於亞洲萬裡通的券特殊顯示）',
  `MCOIN_ORDER_ID` varchar(50) DEFAULT NULL COMMENT '外部訂單號',
  `REMARK` varchar(400) DEFAULT NULL COMMENT '备注',
  `CREATE_TIME` varchar(19) DEFAULT NULL COMMENT '创建时间(单位:年月日时分秒)',
  `MODIFY_TIME` varchar(19) DEFAULT NULL COMMENT '修改时间(单位:年月日时分秒)',
  `VAILD_START_TIME` varchar(19) DEFAULT NULL COMMENT '有效開始時間',
  `VAILD_END_TIME` varchar(19) DEFAULT NULL COMMENT '有效截止時間',
  PRIMARY KEY (`COUPON_ID`) USING BTREE,
  KEY `IX_USER_ID` (`USER_ID`) USING BTREE,
  KEY `IX_COUPON_STATUS` (`COUPON_STATUS`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='mcoin券信息表';


-- fooku_prd_20231123.menus definition

CREATE TABLE `menus` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `name` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '菜單記錄名',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  `roleid` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `menus_name_unique` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##菜單表##';


-- fooku_prd_20231123.migrations definition

CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##Laravel生成數據庫的記錄表##';


-- fooku_prd_20231123.momeplaylog definition

CREATE TABLE `momeplaylog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  `method` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `input` varchar(255) DEFAULT NULL,
  `sql` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7845 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='momeplay游戲關聯表';


-- fooku_prd_20231123.pages definition

CREATE TABLE `pages` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `author_id` int(11) NOT NULL,
  `title` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `excerpt` text COLLATE utf8_unicode_ci,
  `body` text COLLATE utf8_unicode_ci,
  `image` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL,
  `slug` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `meta_description` text COLLATE utf8_unicode_ci,
  `meta_keywords` text COLLATE utf8_unicode_ci,
  `status` enum('ACTIVE','INACTIVE') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'INACTIVE',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `pages_slug_unique` (`slug`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##Laravel的Voyager表##';


-- fooku_prd_20231123.password_resets definition

CREATE TABLE `password_resets` (
  `email` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `token` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##重設密碼表##';


-- fooku_prd_20231123.permissions definition

CREATE TABLE `permissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `key` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '權限key',
  `table_name` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '數據庫表',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  `remark` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '描述',
  `display` tinyint(4) DEFAULT NULL COMMENT '是否顯示',
  `isbusiness` tinyint(4) DEFAULT NULL COMMENT '是否開放商家的權限',
  `isstore` tinyint(4) DEFAULT NULL COMMENT '是否開放門店的權限',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `permissions_key_index` (`key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=914 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##后臺權限表##';


-- fooku_prd_20231123.posts definition

CREATE TABLE `posts` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `author_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `title` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `seo_title` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL,
  `excerpt` text COLLATE utf8_unicode_ci,
  `body` text COLLATE utf8_unicode_ci NOT NULL,
  `image` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL,
  `slug` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `meta_description` text COLLATE utf8_unicode_ci,
  `meta_keywords` text COLLATE utf8_unicode_ci,
  `status` enum('PUBLISHED','DRAFT','PENDING') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'DRAFT',
  `featured` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `posts_slug_unique` (`slug`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##Laravel的Voyager表##';


-- fooku_prd_20231123.request_log definition

CREATE TABLE `request_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `url` varchar(255) DEFAULT NULL,
  `content` text,
  `type` int(11) DEFAULT NULL,
  `createtime` datetime DEFAULT NULL,
  `updatetime` datetime DEFAULT NULL,
  `requestdata` text,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `request_log__index_createtime` (`createtime`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3522684 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='MPay請求日志表2';


-- fooku_prd_20231123.roles definition

CREATE TABLE `roles` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `name` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '權限名',
  `display_name` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '顯示的權限名稱',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `roles_name_unique` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##系統角色表##';


-- fooku_prd_20231123.settings definition

CREATE TABLE `settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `key` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '配置key',
  `display_name` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '顯示的名稱',
  `value` text COLLATE utf8_unicode_ci COMMENT '配置值',
  `details` text COLLATE utf8_unicode_ci COMMENT '描述',
  `type` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '顯示的類型',
  `order` int(11) NOT NULL DEFAULT '1' COMMENT '排列順序',
  `group` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '組別',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `settings_key_unique` (`key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##系统设置表##';


-- fooku_prd_20231123.system_error_log definition

CREATE TABLE `system_error_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `table` varchar(255) DEFAULT NULL COMMENT '表名',
  `uid` varchar(10) DEFAULT NULL COMMENT '用户ID',
  `operationid` varchar(10) DEFAULT NULL COMMENT '操作者ID',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `message` varchar(2000) DEFAULT NULL COMMENT '备注',
  `content` text COMMENT '数据',
  `code` varchar(5) DEFAULT NULL COMMENT '错误码',
  `file` varchar(255) DEFAULT NULL,
  `line` varchar(255) DEFAULT NULL,
  `trace` text,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `system_error_log_createtime_index` (`createtime`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1054472 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##系統出錯記錄表##';


-- fooku_prd_20231123.system_error_log_2023_09_26 definition

CREATE TABLE `system_error_log_2023_09_26` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `table` varchar(255) DEFAULT NULL COMMENT '表名',
  `uid` varchar(10) DEFAULT NULL COMMENT '用户ID',
  `operationid` varchar(10) DEFAULT NULL COMMENT '操作者ID',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `message` varchar(2000) DEFAULT NULL COMMENT '备注',
  `content` text COMMENT '数据',
  `code` varchar(5) DEFAULT NULL COMMENT '错误码',
  `file` varchar(255) DEFAULT NULL,
  `line` varchar(255) DEFAULT NULL,
  `trace` text,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `system_error_log_createtime_index` (`createtime`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11831469 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##系統出錯記錄表##';


-- fooku_prd_20231123.system_operation_log definition

CREATE TABLE `system_operation_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `method` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `input` text,
  `sql` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5520888 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##后臺管理系統的操作記錄表##';


-- fooku_prd_20231123.system_operation_log5 definition

CREATE TABLE `system_operation_log5` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `method` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `input` text,
  `sql` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=829653 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##后臺管理系統的操作記錄表201911202##';


-- fooku_prd_20231123.system_operation_log6 definition

CREATE TABLE `system_operation_log6` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `method` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `input` text,
  `sql` varchar(255) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '創建時間',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2874105 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##后臺管理系統的操作記錄表201911217##';


-- fooku_prd_20231123.temp_20230415_01 definition

CREATE TABLE `temp_20230415_01` (
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.temp_20230415_02 definition

CREATE TABLE `temp_20230415_02` (
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;


-- fooku_prd_20231123.test_business definition

CREATE TABLE `test_business` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) DEFAULT NULL,
  `businesscode` varchar(11) DEFAULT NULL,
  `intercode` varchar(11) DEFAULT NULL,
  `storecode` varchar(11) DEFAULT NULL,
  `posscode` varchar(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  `businessid` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7185 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='澳門通商戶數據';


-- fooku_prd_20231123.test_code definition

CREATE TABLE `test_code` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `presetdate` datetime DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25091 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='油券核銷數據';


-- fooku_prd_20231123.test_log definition

CREATE TABLE `test_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `url` varchar(255) DEFAULT NULL,
  `content` text,
  `type` int(11) DEFAULT NULL,
  `createtime` datetime DEFAULT NULL,
  `updatetime` datetime DEFAULT NULL,
  `requestdata` text,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=420925 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##測試日志表##';


-- fooku_prd_20231123.test_log1 definition

CREATE TABLE `test_log1` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` text,
  `message` varchar(2000) DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `createtime` datetime DEFAULT NULL,
  `state` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `message` (`message`(255)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1004983 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##測試oauth日志表##';


-- fooku_prd_20231123.tmp_business_product_20231220 definition

CREATE TABLE `tmp_business_product_20231220` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '產品Id',
  `areaid` int(11) DEFAULT NULL COMMENT '地區Id（1、澳門，2、泰國）',
  `type` int(11) DEFAULT NULL COMMENT '產品類型（對應fook_business_productcategory表 1、撚手小菜，2、新菜式, 3、現金券,4、1蚊福利,5、積分換領,6、M 幣現金券）',
  `businessid` int(11) DEFAULT NULL COMMENT '商家Id',
  `img` varchar(255) DEFAULT NULL COMMENT '福利圖片',
  `zip_img` varchar(225) DEFAULT NULL COMMENT '壓縮後的福利圖片',
  `imgs` varchar(1000) DEFAULT NULL COMMENT '產品圖片',
  `zip_imgs` varchar(1000) DEFAULT NULL COMMENT '壓縮後的產品圖片',
  `title` varchar(255) DEFAULT NULL COMMENT '產品標題',
  `desc` longtext COMMENT '產品描述',
  `price` decimal(8,2) DEFAULT NULL COMMENT '售出價格',
  `retail_price` decimal(8,2) DEFAULT NULL COMMENT '門市價',
  `point_ratio` int(11) unsigned DEFAULT '300' COMMENT '福利積分比例',
  `stock` int(11) DEFAULT NULL COMMENT '庫存',
  `history_stock` int(11) DEFAULT '0' COMMENT '历史总库存',
  `sales` int(11) unsigned DEFAULT '0' COMMENT '銷量',
  `actual_sales` int(11) unsigned DEFAULT '0' COMMENT '實際銷量',
  `limited_number` int(11) DEFAULT NULL COMMENT '限購數量',
  `is_allow_refund` int(2) DEFAULT NULL COMMENT '是否允許退款（1、允許，0、不允許）',
  `details` longtext COMMENT '圖文詳情',
  `tnc` text COMMENT '細則',
  `score` decimal(8,2) DEFAULT NULL COMMENT '評分',
  `comment_number` int(11) DEFAULT '0' COMMENT '總評價人數',
  `shelf_status` tinyint(1) DEFAULT '1' COMMENT '狀態（1、上架，2、下架）',
  `view_number` int(11) DEFAULT '0' COMMENT '瀏覽量',
  `buy_start_time` datetime DEFAULT NULL COMMENT '可購買開始日期',
  `buy_end_time` datetime DEFAULT NULL COMMENT '可購買結束日期',
  `vaild_mode` int(11) DEFAULT NULL COMMENT '有效期方式(1时间段2购买起多少天内)',
  `vaild_start_time` datetime DEFAULT NULL COMMENT '使用有效期開始日期',
  `vaild_end_time` datetime DEFAULT NULL COMMENT '使用有效期結束日期',
  `day_number` int(11) DEFAULT NULL COMMENT '有效天数',
  `is_weekend` int(11) DEFAULT NULL COMMENT '周六日是否可用（1可用0不可用）',
  `no_useday` varchar(255) DEFAULT NULL COMMENT '不可用日期',
  `use_start_time` datetime DEFAULT NULL COMMENT '可使用开始时间',
  `use_end_time` datetime DEFAULT NULL COMMENT '可使用结束时间',
  `is_vacation` int(11) DEFAULT NULL COMMENT '是否假期可用(1可用0不可用)',
  `fee_rate` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `created_at` datetime DEFAULT NULL COMMENT '創建時間',
  `updated_at` datetime DEFAULT NULL COMMENT '更改時間',
  `enable` int(11) DEFAULT '1' COMMENT '是否有效（1、有效，0、無效  ）',
  `is_hot` int(11) DEFAULT NULL COMMENT '是否熱門（0 否, 1 是）',
  `momecoins_option` varchar(255) DEFAULT '0' COMMENT 'M幣選項',
  `integral_option` varchar(255) DEFAULT '0' COMMENT '積分選項',
  `threshord` int(11) DEFAULT NULL COMMENT '搶福閾值',
  `is_redeem` int(11) DEFAULT '0' COMMENT '是否兑换代码福利（0 否, 1 是）',
  `is_momecoin` int(11) DEFAULT '0' COMMENT '是否M幣福利（0 否, 1 是）',
  `is_great` int(11) DEFAULT NULL COMMENT '是否搶福（0 否, 1 是）',
  `collect_times` int(11) unsigned DEFAULT '0' COMMENT '收藏次數',
  `isexport` tinyint(4) DEFAULT '1' COMMENT 'M幣現金券是否支持人工導入(1.人工導入，2.系統生成)',
  `receive_method` text COMMENT '領取方法',
  `is_mpay_exchange` int(11) DEFAULT NULL COMMENT '是否开启mpay兑换 (1是,0否)',
  `is_mcoin_open` int(2) DEFAULT '1' COMMENT '是否mcoin開放',
  `is_mpay_open` int(2) DEFAULT '1' COMMENT '是否mpay開放',
  `user_limited_number` int(11) DEFAULT NULL COMMENT '用戶限購數量',
  `orderby` tinyint(4) DEFAULT '0',
  `limited_offer` tinyint(2) DEFAULT '0' COMMENT '是否限時優惠',
  `snap_up` tinyint(2) DEFAULT '0' COMMENT '搶購方式 0非搶購 1按庫存 2隨機分配',
  `snap_up_index` tinyint(2) DEFAULT '0' COMMENT '搶購首頁顯示 1顯示 0不顯示',
  `platform_type` tinyint(4) DEFAULT '1' COMMENT '開放平台類別 1mcoin 2會員卡系統',
  `member_integral` decimal(8,2) DEFAULT '0.00' COMMENT '會員卡兌換積分',
  `location` varchar(255) DEFAULT NULL COMMENT '優惠券所在位置',
  `coupon_id` varchar(255) DEFAULT NULL COMMENT '會員卡系統優惠券id',
  `shop_id` varchar(255) DEFAULT NULL COMMENT '會員卡門店id',
  `coupon_type` tinyint(4) DEFAULT '1' COMMENT '0通用券，1特定門店',
  `maximum_points` int(11) DEFAULT NULL COMMENT '最大限制積分',
  `istemporary` tinyint(4) DEFAULT NULL,
  `only_point` tinyint(4) DEFAULT NULL COMMENT '0: 混合支付  1：純積分  2： 純金額',
  `is_settlement` tinyint(4) DEFAULT '1' COMMENT '是否結算(1是，0否)',
  `img_en` varchar(255) DEFAULT NULL COMMENT '圖片（英文）',
  `mpay_coupons_code_id` varchar(100) DEFAULT NULL COMMENT 'Mpay優惠券碼ID',
  `relation_sales` int(11) DEFAULT '0' COMMENT '關聯銷量',
  `product_ids` varchar(100) DEFAULT NULL COMMENT '關聯福利ID',
  `trans_coupon_type` tinyint(4) DEFAULT '0' COMMENT '券類型：0滿減券，1折扣券',
  `meet_money` decimal(11,1) unsigned DEFAULT '0.0' COMMENT '滿足金額',
  `dec_money` decimal(11,1) unsigned DEFAULT '0.0' COMMENT '減免金額',
  `discount` float(4,2) unsigned DEFAULT '0.00' COMMENT '折扣，8折=0.8',
  `max_discount_money` decimal(11,1) unsigned DEFAULT '0.0' COMMENT '最大折扣金額',
  `stock_a` int(11) unsigned DEFAULT '0' COMMENT 'A+庫存',
  `is_a_open` tinyint(4) unsigned DEFAULT '0' COMMENT '是否對A+開放',
  `third_party_settlement_price` decimal(11,2) unsigned DEFAULT '0.00' COMMENT '第三方渠道結算價格',
  `a_fee_type` tinyint(4) unsigned DEFAULT '0' COMMENT '收費類型，從收費列表中讀取',
  `min_point` int(11) unsigned DEFAULT '0' COMMENT '最小限制積分',
  `href_url` varchar(255) DEFAULT NULL COMMENT '跳转链接',
  `goods_id` int(11) DEFAULT NULL COMMENT '零售小程序货物id（商品id）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `businessid` (`businessid`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `trans_coupon_type` (`trans_coupon_type`) USING BTREE,
  KEY `shelf_status` (`shelf_status`) USING BTREE,
  KEY `enable` (`enable`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE,
  KEY `idx_buy_end_time` (`buy_end_time`) USING BTREE,
  KEY `idx_buy_start_time` (`buy_start_time`) USING BTREE,
  KEY `idx_mpay_coupons_code_id` (`mpay_coupons_code_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9660 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='產品信息表';


-- fooku_prd_20231123.tmp_platform_ordercode_20231206 definition

CREATE TABLE `tmp_platform_ordercode_20231206` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `code` varchar(50) DEFAULT NULL COMMENT '核銷碼',
  `status` int(11) DEFAULT NULL COMMENT '福利狀態（1未使用，2已使用,3.已失效）',
  `shopid` int(11) DEFAULT NULL COMMENT '核銷店鋪ID',
  `user_time` datetime DEFAULT NULL COMMENT '使用時間',
  `verification_mode` int(11) DEFAULT NULL COMMENT '核銷方式(1微信核銷,2核銷密碼核銷,3APP,4澳门通Pos核銷,5亚洲万里通定时核销,6會員卡核銷,7PC端核銷)',
  `is_comment` int(11) DEFAULT NULL COMMENT '是否已評價(0否、1是)',
  `is_settlement` int(11) DEFAULT '0' COMMENT '是否結算(0否1是)',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_id` int(11) DEFAULT NULL COMMENT '結算記錄Id',
  `merch_settle_amount` decimal(8,2) DEFAULT NULL COMMENT '商戶結算金額',
  `merch_settle_amount_currency` varchar(10) DEFAULT NULL COMMENT '商戶結算金額幣種',
  `refund_status` int(11) DEFAULT NULL COMMENT '退款狀態（1、未退款，2、退款中，3、退款已完成）',
  `apportion_bill_amount` decimal(8,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `apportion_bill_final_amount` decimal(8,2) DEFAULT NULL COMMENT '攤派付款金額',
  `apportion_mpayintegral` int(11) DEFAULT NULL COMMENT '攤派Mpay积分',
  `apportion_momecoins` decimal(8,2) DEFAULT NULL COMMENT '攤派抵扣M幣',
  `apportion_commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT '關聯訂單詳情ID',
  `refundid` int(11) DEFAULT NULL COMMENT '退款ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `apportion_memberintegral` decimal(8,2) DEFAULT NULL COMMENT '攤派會員卡積分',
  `is_voucher` tinyint(4) DEFAULT '0' COMMENT '是否派券（1是、0否、默认0）',
  `preferentialAmount` decimal(8,1) unsigned DEFAULT '0.0' COMMENT '優惠金額，交易核銷券使用',
  `requestOrderNo` varchar(64) DEFAULT NULL COMMENT '請求訂單號【用於重複發送核銷撤銷時校驗】',
  `requestOrderNoStatus` tinyint(4) DEFAULT '0' COMMENT '請求訂單號狀態，0未處理，1核銷完成，2撤銷完成，3核銷中，4撤銷中,5核銷失敗，6撤銷失敗',
  `is_a_open` tinyint(1) DEFAULT '0' COMMENT '是否A+券碼，0否，1是',
  `is_untie` tinyint(4) DEFAULT '0' COMMENT '是否解綁（1是、0否）',
  `is_exported` tinyint(4) DEFAULT '0' COMMENT '導出狀態（0未導出、1已導出）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_orderid_status` (`orderid`,`status`) USING BTREE,
  KEY `code` (`code`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `orderinfo_id` (`orderinfo_id`) USING BTREE,
  KEY `shopid` (`shopid`) USING BTREE,
  KEY `idx_status_code` (`status`,`code`) USING BTREE,
  KEY `idx_usertime` (`user_time`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `refundid` (`refundid`) USING BTREE,
  KEY `idx_code_orderinfoid` (`code`,`orderinfo_id`) USING BTREE,
  KEY `requestOrderNo` (`requestOrderNo`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3342696 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)訂單核銷碼記錄表';


-- fooku_prd_20231123.tmp_platform_ordercode_220231207 definition

CREATE TABLE `tmp_platform_ordercode_220231207` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '記錄Id',
  `orderid` int(11) DEFAULT NULL COMMENT '訂單Id',
  `code` varchar(50) DEFAULT NULL COMMENT '核銷碼',
  `status` int(11) DEFAULT NULL COMMENT '福利狀態（1未使用，2已使用,3.已失效）',
  `shopid` int(11) DEFAULT NULL COMMENT '核銷店鋪ID',
  `user_time` datetime DEFAULT NULL COMMENT '使用時間',
  `verification_mode` int(11) DEFAULT NULL COMMENT '核銷方式(1微信核銷,2核銷密碼核銷,3APP,4澳门通Pos核銷,5亚洲万里通定时核销,6會員卡核銷,7PC端核銷)',
  `is_comment` int(11) DEFAULT NULL COMMENT '是否已評價(0否、1是)',
  `is_settlement` int(11) DEFAULT '0' COMMENT '是否結算(0否1是)',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `settlement_id` int(11) DEFAULT NULL COMMENT '結算記錄Id',
  `merch_settle_amount` decimal(8,2) DEFAULT NULL COMMENT '商戶結算金額',
  `merch_settle_amount_currency` varchar(10) DEFAULT NULL COMMENT '商戶結算金額幣種',
  `refund_status` int(11) DEFAULT NULL COMMENT '退款狀態（1、未退款，2、退款中，3、退款已完成）',
  `apportion_bill_amount` decimal(8,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `apportion_bill_final_amount` decimal(8,2) DEFAULT NULL COMMENT '攤派付款金額',
  `apportion_mpayintegral` int(11) DEFAULT NULL COMMENT '攤派Mpay积分',
  `apportion_momecoins` decimal(8,2) DEFAULT NULL COMMENT '攤派抵扣M幣',
  `apportion_commission` decimal(8,2) DEFAULT NULL COMMENT '佣金',
  `orderinfo_id` int(11) DEFAULT NULL COMMENT '關聯訂單詳情ID',
  `refundid` int(11) DEFAULT NULL COMMENT '退款ID',
  `userid` int(11) DEFAULT NULL COMMENT '用户id',
  `apportion_memberintegral` decimal(8,2) DEFAULT NULL COMMENT '攤派會員卡積分',
  `is_voucher` tinyint(4) DEFAULT '0' COMMENT '是否派券（1是、0否、默认0）',
  `preferentialAmount` decimal(8,1) unsigned DEFAULT '0.0' COMMENT '優惠金額，交易核銷券使用',
  `requestOrderNo` varchar(64) DEFAULT NULL COMMENT '請求訂單號【用於重複發送核銷撤銷時校驗】',
  `requestOrderNoStatus` tinyint(4) DEFAULT '0' COMMENT '請求訂單號狀態，0未處理，1核銷完成，2撤銷完成，3核銷中，4撤銷中,5核銷失敗，6撤銷失敗',
  `is_a_open` tinyint(1) DEFAULT '0' COMMENT '是否A+券碼，0否，1是',
  `is_untie` tinyint(4) DEFAULT '0' COMMENT '是否解綁（1是、0否）',
  `is_exported` tinyint(4) DEFAULT '0' COMMENT '導出狀態（0未導出、1已導出）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_orderid_status` (`orderid`,`status`) USING BTREE,
  KEY `code` (`code`) USING BTREE,
  KEY `orderid` (`orderid`) USING BTREE,
  KEY `orderinfo_id` (`orderinfo_id`) USING BTREE,
  KEY `shopid` (`shopid`) USING BTREE,
  KEY `idx_status_code` (`status`,`code`) USING BTREE,
  KEY `idx_usertime` (`user_time`) USING BTREE,
  KEY `userid` (`userid`) USING BTREE,
  KEY `refundid` (`refundid`) USING BTREE,
  KEY `idx_code_orderinfoid` (`code`,`orderinfo_id`) USING BTREE,
  KEY `requestOrderNo` (`requestOrderNo`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3342700 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='(*)訂單核銷碼記錄表';


-- fooku_prd_20231123.tmp_report_merchant_settlement_optional_20231206 definition

CREATE TABLE `tmp_report_merchant_settlement_optional_20231206` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL COMMENT '商家Id',
  `bank_account` varchar(30) DEFAULT NULL COMMENT '結算銀行賬戶',
  `account_name` varchar(100) DEFAULT NULL COMMENT '結算賬戶姓名',
  `bank` varchar(120) DEFAULT NULL COMMENT '結算銀行',
  `start_time` datetime DEFAULT NULL COMMENT '結算週期開始',
  `end_time` datetime DEFAULT NULL COMMENT '結算週期結束',
  `bill_amount` decimal(10,2) DEFAULT NULL COMMENT '交易金額',
  `settlement_amount` decimal(10,2) DEFAULT NULL COMMENT '結算金額數',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '佣金',
  `momecoins` decimal(10,2) DEFAULT NULL COMMENT '抵扣M幣',
  `settlement_time` datetime DEFAULT NULL COMMENT '結算時間',
  `pdf_url` varchar(255) DEFAULT '' COMMENT 'pdf OSS鏈接',
  `excel_url` varchar(255) DEFAULT '' COMMENT 'excel OSS鏈接',
  `test_bill_amount` decimal(10,2) DEFAULT NULL COMMENT 'test交易金額',
  `test_settlement_amount` decimal(10,2) DEFAULT NULL COMMENT 'test結算金額數',
  `test_commission` decimal(10,2) DEFAULT NULL COMMENT 'test佣金',
  `test_momecoins` decimal(10,2) DEFAULT NULL COMMENT 'test抵扣M幣',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `seller_id` (`seller_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=206288 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='商家選發結算報表';


-- fooku_prd_20231123.tmp_report_ordercode_settlement_20231206 definition

CREATE TABLE `tmp_report_ordercode_settlement_20231206` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `ordercodeid` int(11) DEFAULT NULL COMMENT '訂單核銷ID',
  `businessname` varchar(255) DEFAULT NULL COMMENT '商家名稱',
  `storename` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `bankaccount` varchar(255) DEFAULT NULL COMMENT '結算銀行帳號',
  `bankname` varchar(255) DEFAULT NULL COMMENT '結算銀行名稱',
  `bank` varchar(255) DEFAULT NULL COMMENT '結算銀行',
  `createtime` datetime DEFAULT NULL COMMENT '下單時間',
  `usetime` datetime DEFAULT NULL COMMENT '使用時間',
  `billamount` decimal(10,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `userpaymentamount` decimal(10,2) DEFAULT NULL COMMENT '攤派支付金額',
  `momecoinsamount` decimal(10,2) DEFAULT NULL COMMENT '攤派m幣',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '傭金',
  `merchantsettleamount` decimal(10,2) DEFAULT NULL COMMENT '商家結算金額',
  `vouchercode` varchar(255) DEFAULT NULL COMMENT '核銷碼',
  `vouchername` varchar(255) DEFAULT NULL COMMENT '核銷商品名稱',
  `ordertransaction` varchar(255) DEFAULT NULL COMMENT '訂單流水號',
  `status` varchar(5) DEFAULT NULL COMMENT '狀態 (0未結算,1結算)',
  `settlementtime` datetime DEFAULT NULL COMMENT '結算時間',
  `settlementbusinessid` int(11) DEFAULT NULL COMMENT '關聯的商家結算ID（主要是用于商家結算后便于后期檢查）',
  `internalid` int(11) DEFAULT NULL COMMENT '内部统计ID',
  `mpaysettlement` int(11) DEFAULT NULL COMMENT 'Mpay結算id',
  `mpayintegral` decimal(10,2) DEFAULT NULL COMMENT 'mpay積分',
  `is_mpay` int(10) DEFAULT NULL COMMENT '是否MPAY',
  `is_member` tinyint(4) DEFAULT '0' COMMENT '是否Member 1是 0否',
  `memberintegral` decimal(8,2) DEFAULT NULL COMMENT '會員卡積分',
  `optionalid` int(11) DEFAULT NULL COMMENT '選填報表ID',
  `is_voucher` tinyint(2) DEFAULT '0' COMMENT '是否派券（1是、0否、默認0）',
  `is_settlement` tinyint(4) DEFAULT '1' COMMENT '是否結算(1是，0否)',
  `storeMid` varchar(50) DEFAULT NULL COMMENT '店長ID，交易核銷券使用，用於區分是哪個店長登錄終端機，然後分別對賬',
  `is_a_open` tinyint(1) DEFAULT '0' COMMENT '是否A+訂單：0否，1是',
  `third_party_settlement_price` decimal(10,2) DEFAULT '0.00' COMMENT '第三方渠道結算價格',
  `a_fee_type` varchar(30) DEFAULT NULL COMMENT '收費類型，從收費列表中讀取',
  `a_fee_rate` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '費率，從收費列表讀取',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '折扣金額',
  `third_party_pay` decimal(10,2) DEFAULT '0.00' COMMENT '第三方應付金額',
  `channel_commission` decimal(10,2) DEFAULT '0.00' COMMENT '渠道佣金',
  `retail_price` decimal(10,2) DEFAULT '0.00' COMMENT '福利原價',
  `delivery_type` int(11) DEFAULT NULL COMMENT '配送方式：1-快递发货,2-上门自提,3-同城配送,4-虚拟发货',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `internalid` (`internalid`) USING BTREE,
  KEY `idx_optionalid` (`optionalid`) USING BTREE,
  KEY `idx_ordercodeid_storeid_businessid` (`ordercodeid`,`storeid`,`businessid`) USING BTREE,
  KEY `settlementbusinessid` (`settlementbusinessid`) USING BTREE,
  KEY `is_mpay` (`is_mpay`) USING BTREE,
  KEY `usetime` (`usetime`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2455181 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單核銷表對應的訂單結算數據表';


-- fooku_prd_20231123.tmp_report_ordercode_settlement_220231207 definition

CREATE TABLE `tmp_report_ordercode_settlement_220231207` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storeid` int(11) DEFAULT NULL COMMENT '門店Id',
  `ordercodeid` int(11) DEFAULT NULL COMMENT '訂單核銷ID',
  `businessname` varchar(255) DEFAULT NULL COMMENT '商家名稱',
  `storename` varchar(255) DEFAULT NULL COMMENT '門店名稱',
  `bankaccount` varchar(255) DEFAULT NULL COMMENT '結算銀行帳號',
  `bankname` varchar(255) DEFAULT NULL COMMENT '結算銀行名稱',
  `bank` varchar(255) DEFAULT NULL COMMENT '結算銀行',
  `createtime` datetime DEFAULT NULL COMMENT '下單時間',
  `usetime` datetime DEFAULT NULL COMMENT '使用時間',
  `billamount` decimal(10,2) DEFAULT NULL COMMENT '攤派訂單金額',
  `userpaymentamount` decimal(10,2) DEFAULT NULL COMMENT '攤派支付金額',
  `momecoinsamount` decimal(10,2) DEFAULT NULL COMMENT '攤派m幣',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '傭金',
  `merchantsettleamount` decimal(10,2) DEFAULT NULL COMMENT '商家結算金額',
  `vouchercode` varchar(255) DEFAULT NULL COMMENT '核銷碼',
  `vouchername` varchar(255) DEFAULT NULL COMMENT '核銷商品名稱',
  `ordertransaction` varchar(255) DEFAULT NULL COMMENT '訂單流水號',
  `status` varchar(5) DEFAULT NULL COMMENT '狀態 (0未結算,1結算)',
  `settlementtime` datetime DEFAULT NULL COMMENT '結算時間',
  `settlementbusinessid` int(11) DEFAULT NULL COMMENT '關聯的商家結算ID（主要是用于商家結算后便于后期檢查）',
  `internalid` int(11) DEFAULT NULL COMMENT '内部统计ID',
  `mpaysettlement` int(11) DEFAULT NULL COMMENT 'Mpay結算id',
  `mpayintegral` decimal(10,2) DEFAULT NULL COMMENT 'mpay積分',
  `is_mpay` int(10) DEFAULT NULL COMMENT '是否MPAY',
  `is_member` tinyint(4) DEFAULT '0' COMMENT '是否Member 1是 0否',
  `memberintegral` decimal(8,2) DEFAULT NULL COMMENT '會員卡積分',
  `optionalid` int(11) DEFAULT NULL COMMENT '選填報表ID',
  `is_voucher` tinyint(2) DEFAULT '0' COMMENT '是否派券（1是、0否、默認0）',
  `is_settlement` tinyint(4) DEFAULT '1' COMMENT '是否結算(1是，0否)',
  `storeMid` varchar(50) DEFAULT NULL COMMENT '店長ID，交易核銷券使用，用於區分是哪個店長登錄終端機，然後分別對賬',
  `is_a_open` tinyint(1) DEFAULT '0' COMMENT '是否A+訂單：0否，1是',
  `third_party_settlement_price` decimal(10,2) DEFAULT '0.00' COMMENT '第三方渠道結算價格',
  `a_fee_type` varchar(30) DEFAULT NULL COMMENT '收費類型，從收費列表中讀取',
  `a_fee_rate` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '費率，從收費列表讀取',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '折扣金額',
  `third_party_pay` decimal(10,2) DEFAULT '0.00' COMMENT '第三方應付金額',
  `channel_commission` decimal(10,2) DEFAULT '0.00' COMMENT '渠道佣金',
  `retail_price` decimal(10,2) DEFAULT '0.00' COMMENT '福利原價',
  `delivery_type` int(11) DEFAULT NULL COMMENT '配送方式：1-快递发货,2-上门自提,3-同城配送,4-虚拟发货',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `internalid` (`internalid`) USING BTREE,
  KEY `idx_optionalid` (`optionalid`) USING BTREE,
  KEY `idx_ordercodeid_storeid_businessid` (`ordercodeid`,`storeid`,`businessid`) USING BTREE,
  KEY `settlementbusinessid` (`settlementbusinessid`) USING BTREE,
  KEY `is_mpay` (`is_mpay`) USING BTREE,
  KEY `usetime` (`usetime`) USING BTREE,
  KEY `is_a_open` (`is_a_open`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2457001 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='訂單核銷表對應的訂單結算數據表';


-- fooku_prd_20231123.tmp_snapping_record_dp_20231010 definition

CREATE TABLE `tmp_snapping_record_dp_20231010` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `redis_id` varchar(255) DEFAULT NULL COMMENT 'redis_id',
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip地址',
  `product_id` int(11) DEFAULT NULL COMMENT '福利id',
  `number` int(11) DEFAULT NULL COMMENT '福利数量',
  `status` tinyint(2) DEFAULT NULL COMMENT '状态：1申请抢购、2生效、3失效、4已完成',
  `type` tinyint(4) DEFAULT NULL COMMENT '抢购类型：1按库存、2按随机分配等 3按库存直扣',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `order_id` int(11) DEFAULT NULL COMMENT '订单id',
  `redis_time` datetime DEFAULT NULL COMMENT 'redis同步时间',
  `stock_id` int(11) DEFAULT NULL COMMENT '福利庫存變動記錄表Id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `redis_id` (`redis_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `idx_status_createdat_productid` (`status`,`created_at`,`product_id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE,
  KEY `order_id_idx` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=665407 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='福利限时抢购记录表';


-- fooku_prd_20231123.translations definition

CREATE TABLE `translations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `column_name` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `foreign_key` int(10) unsigned NOT NULL,
  `locale` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `value` text COLLATE utf8_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `translations_table_name_column_name_foreign_key_locale_unique` (`table_name`,`column_name`,`foreign_key`,`locale`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##Laravel的Voyager翻譯表（暫時沒使用）##';


-- fooku_prd_20231123.user_business definition

CREATE TABLE `user_business` (
  `user_id` int(11) DEFAULT NULL COMMENT '用戶ID',
  `business_id` int(11) DEFAULT NULL COMMENT '商家ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='##后臺管理員關聯多個商家表##';


-- fooku_prd_20231123.categories definition

CREATE TABLE `categories` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int(10) unsigned DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT '1',
  `name` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `slug` varchar(191) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `categories_slug_unique` (`slug`) USING BTREE,
  KEY `categories_parent_id_foreign` (`parent_id`) USING BTREE,
  CONSTRAINT `categories_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##類目##';


-- fooku_prd_20231123.data_rows definition

CREATE TABLE `data_rows` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `data_type_id` int(10) unsigned NOT NULL COMMENT '關聯數據庫表ID',
  `field` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '字段',
  `type` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '控件類型',
  `display_name` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '顯示名稱',
  `required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必填',
  `browse` tinyint(1) NOT NULL DEFAULT '1' COMMENT '瀏覽權限',
  `read` tinyint(1) NOT NULL DEFAULT '1' COMMENT '讀取權限',
  `edit` tinyint(1) NOT NULL DEFAULT '1' COMMENT '更改權限',
  `add` tinyint(1) NOT NULL DEFAULT '1' COMMENT '添加權限',
  `delete` tinyint(1) NOT NULL DEFAULT '1' COMMENT '描述',
  `details` text COLLATE utf8_unicode_ci COMMENT '參數詳情',
  `order` int(11) NOT NULL DEFAULT '1' COMMENT '排列順序',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `data_rows_data_type_id_foreign` (`data_type_id`) USING BTREE,
  CONSTRAINT `data_rows_data_type_id_foreign` FOREIGN KEY (`data_type_id`) REFERENCES `data_types` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2095 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='記錄數據表的字段表';


-- fooku_prd_20231123.menu_items definition

CREATE TABLE `menu_items` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `menu_id` int(10) unsigned DEFAULT NULL COMMENT '菜單主ID',
  `title` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '菜單名',
  `url` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '菜單鏈接',
  `target` varchar(191) COLLATE utf8_unicode_ci NOT NULL DEFAULT '_self' COMMENT '跳轉方式',
  `icon_class` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '圖標',
  `color` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '主顏色',
  `parent_id` int(11) DEFAULT NULL COMMENT '父菜單ID',
  `order` int(11) NOT NULL COMMENT '排列順序',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  `route` varchar(191) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '路由',
  `parameters` text COLLATE utf8_unicode_ci COMMENT '父級',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `menu_items_menu_id_foreign` (`menu_id`) USING BTREE,
  CONSTRAINT `menu_items_menu_id_foreign` FOREIGN KEY (`menu_id`) REFERENCES `menus` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=295 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##菜單子項列表##';


-- fooku_prd_20231123.permission_role definition

CREATE TABLE `permission_role` (
  `permission_id` int(10) unsigned NOT NULL COMMENT '權限ID',
  `role_id` int(10) unsigned NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`permission_id`,`role_id`) USING BTREE,
  KEY `permission_role_role_id_index` (`role_id`) USING BTREE,
  KEY `permission_role_permission_id_index` (`permission_id`) USING BTREE,
  CONSTRAINT `permission_role_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `permission_role_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='##角色權限表##';


-- fooku_prd_20231123.users definition

CREATE TABLE `users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '記錄ID',
  `role_id` int(10) unsigned DEFAULT NULL COMMENT '角色ID',
  `name` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '用戶名',
  `account` varchar(191) COLLATE utf8_unicode_ci DEFAULT ' ' COMMENT '賬號',
  `email` varchar(191) COLLATE utf8_unicode_ci NOT NULL DEFAULT ' ' COMMENT '郵箱',
  `avatar` varchar(191) COLLATE utf8_unicode_ci DEFAULT 'users/default.png' COMMENT '頭像',
  `password` varchar(191) COLLATE utf8_unicode_ci NOT NULL COMMENT '密碼',
  `remember_token` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '驗證token',
  `settings` text COLLATE utf8_unicode_ci COMMENT '參數配置',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '創建時間',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  `businessid` int(11) DEFAULT NULL COMMENT '商家ID',
  `storesid` int(11) DEFAULT NULL COMMENT '門店id(對應fook_stores表id)',
  `type` int(11) DEFAULT NULL COMMENT '用戶類型(1管理員，2商家, 3門店)',
  `real_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '姓名',
  `phone` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '聯繫電話',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `users_email_unique` (`email`) USING BTREE,
  KEY `users_role_id_foreign` (`role_id`) USING BTREE,
  CONSTRAINT `users_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=15215 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='后臺管理員表';


-- fooku_prd_20231123.user_roles definition

CREATE TABLE `user_roles` (
  `user_id` int(10) unsigned NOT NULL COMMENT '用戶ID',
  `role_id` int(10) unsigned NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`) USING BTREE,
  KEY `user_roles_user_id_index` (`user_id`) USING BTREE,
  KEY `user_roles_role_id_index` (`role_id`) USING BTREE,
  CONSTRAINT `user_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `user_roles_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPACT COMMENT='后臺管理員關聯角色表';