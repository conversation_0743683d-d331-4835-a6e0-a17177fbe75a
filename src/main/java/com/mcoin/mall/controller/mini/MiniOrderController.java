package com.mcoin.mall.controller.mini;

import com.mcoin.mall.model.*;
import com.mcoin.mall.service.mini.MiniOrderService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/***
 * @author: laiji<PERSON>e
 * @date 2023-10-20
 */
@RestController
@RequestMapping("/api/mini/order")
public class MiniOrderController {

    @Resource
    private MiniOrderService miniOrderService;

    /**
     * 查询零售小程序的订单列表
     *
     * @return
     */
    @PostMapping(value = "/list")
    public Response<MiniOrderListResponse> getList(@Valid @RequestBody MiniOrderRequest request) {
        MiniOrderListResponse miniOrders = miniOrderService.getMiniOrders(request);
        return Responses.ok(miniOrders);
    }

    /**
     * 查询商城获取订单是否需售后
     */
    @GetMapping(value = "/getOrderIsAfter", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<MiniOrderIsAfterResponse> getOrderIsAfter(@Valid MiniOrderIsAfterRequest request) {
        MiniOrderIsAfterResponse miniOrders = miniOrderService.getOrderIsAfter(request);
        return Responses.ok(miniOrders);
    }
}
