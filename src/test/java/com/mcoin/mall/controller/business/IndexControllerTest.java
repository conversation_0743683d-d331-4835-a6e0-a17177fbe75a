package com.mcoin.mall.controller.business;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.model.BannerResponse;
import com.mcoin.mall.model.HotKeywordsResponse;
import com.mcoin.mall.model.IndexModuleResponse;
import com.mcoin.mall.model.RecommendZoneRequest;
import com.mcoin.mall.model.RecommendZoneResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.ShopIntervalResponse;
import com.mcoin.mall.model.ShopTypeRequest;
import com.mcoin.mall.model.ShopTypeResponse;
import com.mcoin.mall.model.SnapUpNextRequest;
import com.mcoin.mall.model.SnapUpNextResponse;
import com.mcoin.mall.model.StoreKeywordsResponse;
import com.mcoin.mall.model.StoreTypeResponse;
import com.mcoin.mall.model.ZoneTypeRequest;
import com.mcoin.mall.model.ZoneTypeResponse;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 集成测试 IndexController
 * 使用真实服务和数据库，测试首页相关功能
 */
class IndexControllerTest extends BaseUnitTest {

    @Resource
    private IndexController indexController;

    @Resource
    private RedissonClient redissonClient;

    @BeforeEach
    void setUp() {
        when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
    }

    @Test
    void testBanner() {
        // 直接调用控制器方法
        Response<BannerResponse> response = indexController.banner();

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testHotKeywords() {
        // 直接调用控制器方法 - 使用模拟的HttpServletRequest
        HttpServletRequest request = mock(HttpServletRequest.class);
        Response<HotKeywordsResponse> response = indexController.hotKeywords(request);

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testZoneType() {
        // 直接调用控制器方法 - 使用模拟的HttpServletRequest
        ZoneTypeRequest request = new ZoneTypeRequest();

        Response<List<ZoneTypeResponse>> response = indexController.zoneType(request, null);

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testRecommendZone() {
        // 创建请求对象
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(1); // 设置专区ID
        request.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.recommendZone(request, null);

        // 验证响应结构
        assertNotNull(response);
        assertEquals(2, response.getData().stream().filter(r -> r.getProductType() == 12).count());
        assertEquals(3, response.getData().stream().filter(r -> r.getProductType() != 12).count());
    }


    @Test
    void testRecommendZone_Harmony() {
        // 创建请求对象
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(1); // 设置专区ID
        request.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.recommendZone(request, ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response);
        assertEquals(0, response.getData().stream().filter(r -> r.getProductType() == 12).count());
        assertEquals(3, response.getData().stream().filter(r -> r.getProductType() != 12).count());
    }

    /**
     * 测试recommendzone功能 - 带经纬度参数
     * 验证带位置信息的响应包含正确的店铺距离信息
     */
    @Test
    void testRecommendZoneWithLocation() {
        // 创建请求对象
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(1); // 设置专区ID
        request.setType("2");
        request.setLat("22.208801");
        request.setLot("113.550694");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.recommendZone(request, null);

        // 验证响应结构
        assertNotNull(response);
        assertFalse(response.getData().isEmpty(), "Response data should not be empty");

        // 验证响应中包含店铺名称（因为传了经纬度，会获取最近的店铺）
        for (RecommendZoneResponse item : response.getData()) {
            assertNotNull(item.getStoreName(), "Store name should not be null when location provided");
        }
    }

    /**
     * 测试recommendzone功能 - 不同专区ID
     * 验证不同专区ID返回的商品列表不同
     */
    @Test
    void testRecommendZoneWithDifferentZoneId() {
        // 创建第一个请求对象 - 专区ID 1
        RecommendZoneRequest request1 = new RecommendZoneRequest();
        request1.setZonne_id(1);
        request1.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response1 = indexController.recommendZone(request1, null);

        // 验证响应结构
        assertNotNull(response1);
        assertFalse(response1.getData().isEmpty(), "Response data for zone 1 should not be empty");

        // 记录第一个专区的商品ID集合
        List<String> productIdsZone1 = response1.getData().stream()
                .map(RecommendZoneResponse::getId)
                .collect(Collectors.toList());

        // 获取第一个产品的ID用于后续断言
        String firstProductIdZone1 = !productIdsZone1.isEmpty() ? productIdsZone1.get(0) : null;

        // 创建第二个请求对象 - 用不同的专区ID（如果有多个专区数据）
        // 注意：如果测试数据库中没有其他专区，这个测试可能需要调整
        // 这里假设存在ID为2的专区
        RecommendZoneRequest request2 = new RecommendZoneRequest();
        request2.setZonne_id(2);
        request2.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response2 = indexController.recommendZone(request2, null);

        // 验证不同专区返回不同结果
        // 如果专区2存在数据，验证结果与专区1不同
        if (response2 != null && response2.getData() != null && !response2.getData().isEmpty()) {
            List<String> productIdsZone2 = response2.getData().stream()
                    .map(RecommendZoneResponse::getId)
                    .collect(Collectors.toList());

            // 断言：至少有一个产品不同，或者产品数量不同
            assertTrue(
                productIdsZone1.size() != productIdsZone2.size() ||
                !productIdsZone2.containsAll(productIdsZone1),
                "Different zone IDs should return different product lists"
            );
        }
        // 如果专区2不存在数据，这个测试将被跳过
    }

    /**
     * 测试recommendzone功能 - 验证非法ID参数
     * 验证当提供无效的专区ID时返回空列表而不是异常
     */
    @Test
    void testRecommendZoneWithInvalidZoneId() {
        // 创建请求对象 - 使用不存在的专区ID
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(999); // 假设999是一个不存在的专区ID
        request.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.recommendZone(request, null);

        // 验证响应结构 - 应该返回空列表而不是异常
        assertNotNull(response);
        assertNotNull(response.getData(), "Response data should not be null");
        assertTrue(response.getData().isEmpty(), "Response data should be empty for invalid zone ID");
    }

    @Test
    void testThirdPartyRecommendZone() {
        // 创建请求对象
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(1); // 设置专区ID
        request.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.thirdPartyRecommendZone(request, null);

        // 验证响应结构
        assertNotNull(response);
        assertEquals(2, response.getData().stream().filter(r -> r.getProductType() == 12).count());
        assertEquals(3, response.getData().stream().filter(r -> r.getProductType() != 12).count());
    }

    @Test
    void testThirdPartyRecommendZone_Harmony() {
        // 创建请求对象
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(1); // 设置专区ID
        request.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.thirdPartyRecommendZone(request, ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response);
        assertEquals(0, response.getData().stream().filter(r -> r.getProductType() == 12).count());
        assertEquals(3, response.getData().stream().filter(r -> r.getProductType() != 12).count());
    }

    @Test
    void testThirdPartyRecommendZone_IOS() {
        // 创建请求对象
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(1); // 设置专区ID
        request.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.thirdPartyRecommendZone(request, ClientTypeEnum.IOS.toString());

        // 验证响应结构
        assertNotNull(response);
        assertEquals(2, response.getData().stream().filter(r -> r.getProductType() == 12).count());
        assertEquals(3, response.getData().stream().filter(r -> r.getProductType() != 12).count());
    }

    /**
     * 测试thirdPartyRecommendZone功能 - 带经纬度参数
     * 验证第三方接口处理位置信息的能力
     */
    @Test
    void testThirdPartyRecommendZoneWithLocation() {
        // 创建请求对象
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(1); // 设置专区ID
        request.setType("2");
        request.setLat("22.208801");
        request.setLot("113.550694");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.thirdPartyRecommendZone(request, null);

        // 验证响应结构
        assertNotNull(response);
        assertFalse(response.getData().isEmpty(), "Response data should not be empty");

        // 验证响应中包含店铺名称（因为传了经纬度，会获取最近的店铺）
        for (RecommendZoneResponse item : response.getData()) {
            assertNotNull(item.getStoreName(), "Store name should not be null when location provided");
        }
    }

    /**
     * 测试thirdPartyRecommendZone功能 - 不同专区ID
     * 验证第三方接口也能区分不同专区返回不同结果
     */
    @Test
    void testThirdPartyRecommendZoneWithDifferentZoneId() {
        // 创建第一个请求对象 - 专区ID 1
        RecommendZoneRequest request1 = new RecommendZoneRequest();
        request1.setZonne_id(1);
        request1.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response1 = indexController.thirdPartyRecommendZone(request1, null);

        // 验证响应结构
        assertNotNull(response1);
        assertFalse(response1.getData().isEmpty(), "Response data for zone 1 should not be empty");

        // 记录第一个专区的商品ID集合
        List<String> productIdsZone1 = response1.getData().stream()
                .map(RecommendZoneResponse::getId)
                .collect(Collectors.toList());

        // 创建第二个请求对象 - 专区ID 2
        RecommendZoneRequest request2 = new RecommendZoneRequest();
        request2.setZonne_id(2);
        request2.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response2 = indexController.thirdPartyRecommendZone(request2, null);

        // 验证不同专区返回不同结果
        // 如果专区2存在数据，验证结果与专区1不同
        if (response2 != null && response2.getData() != null && !response2.getData().isEmpty()) {
            List<String> productIdsZone2 = response2.getData().stream()
                    .map(RecommendZoneResponse::getId)
                    .collect(Collectors.toList());

            // 断言：至少有一个产品不同，或者产品数量不同
            assertTrue(
                productIdsZone1.size() != productIdsZone2.size() ||
                !productIdsZone2.containsAll(productIdsZone1),
                "Different zone IDs should return different product lists in third party API"
            );
        }
    }

    /**
     * 测试thirdPartyRecommendZone功能 - 验证非法ID参数
     * 验证第三方接口处理错误参数的健壮性
     */
    @Test
    void testThirdPartyRecommendZoneWithInvalidZoneId() {
        // 创建请求对象 - 使用不存在的专区ID
        RecommendZoneRequest request = new RecommendZoneRequest();
        request.setZonne_id(999); // 假设999是一个不存在的专区ID
        request.setType("2");

        // 直接调用控制器方法
        Response<List<RecommendZoneResponse>> response = indexController.thirdPartyRecommendZone(request, null);

        // 验证响应结构 - 应该返回空列表而不是异常
        assertNotNull(response);
        assertNotNull(response.getData(), "Response data should not be null");
        assertTrue(response.getData().isEmpty(), "Response data should be empty for invalid zone ID");
    }

    @Test
    void testShopInterval() {
        // 直接调用控制器方法
        Response<ShopIntervalResponse> response = indexController.shopInterval();

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testShopType() {
        // 创建请求对象
        ShopTypeRequest request = new ShopTypeRequest();
        request.setCount(10);
        request.setPage(1);
        request.setCategory(1);

        // 直接调用控制器方法
        Response<ShopTypeResponse> response = indexController.shopType(request, null);

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testShopType_Harmony() {
        // 创建请求对象
        ShopTypeRequest request = new ShopTypeRequest();
        request.setCount(10);
        request.setPage(1);
        request.setCategory(1);

        // 直接调用控制器方法
        Response<ShopTypeResponse> response = indexController.shopType(request, ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testStoreType() {
        // 直接调用控制器方法
        Response<StoreTypeResponse> response = indexController.storeType();

        // 验证响应结构
        assertNotNull(response);
        assertNotNull(response.getData().getShopInterval());
        assertNotNull(response.getData().getShopInterval().getPointInterval());
        assertEquals(3, response.getData().getShopInterval().getPointInterval().size());
        assertEquals("150",response.getData().getShopInterval().getPointInterval().get(0));
        assertEquals("1200",response.getData().getShopInterval().getPointInterval().get(1));
        assertEquals("150",response.getData().getShopInterval().getPointInterval().get(2));

        assertNotNull(response.getData().getShopInterval().getPriceInterval());
        assertEquals(3, response.getData().getShopInterval().getPriceInterval().size());
        assertEquals("0",response.getData().getShopInterval().getPriceInterval().get(0));
        assertEquals("1000",response.getData().getShopInterval().getPriceInterval().get(1));
        assertEquals("10",response.getData().getShopInterval().getPriceInterval().get(2));
    }

    @Test
    void testStoreType_Internal() {

        // mock数据

        redissonClient.getBucket("index_shop_start_point_interval").set("10");
        redissonClient.getBucket("index_shop_end_point_interval").set("1000");
        redissonClient.getBucket("index_shop_point_interval").set("100");

        redissonClient.getBucket("index_shop_start_price_interval").set("1");
        redissonClient.getBucket("index_shop_end_price_interval").set("500");
        redissonClient.getBucket("index_shop_price_interval").set("20");


        // 直接调用控制器方法
        Response<StoreTypeResponse> response = indexController.storeType();

        // 验证响应结构
        assertNotNull(response);
        assertNotNull(response.getData().getShopInterval());
        assertNotNull(response.getData().getShopInterval());

        assertNotNull(response.getData().getShopInterval().getPointInterval());
        assertEquals(3, response.getData().getShopInterval().getPointInterval().size());
        assertEquals("10",response.getData().getShopInterval().getPointInterval().get(0));
        assertEquals("1000",response.getData().getShopInterval().getPointInterval().get(1));
        assertEquals("100",response.getData().getShopInterval().getPointInterval().get(2));


        assertNotNull(response.getData().getShopInterval().getPriceInterval());
        assertEquals(3, response.getData().getShopInterval().getPriceInterval().size());
        assertEquals("1",response.getData().getShopInterval().getPriceInterval().get(0));
        assertEquals("500",response.getData().getShopInterval().getPriceInterval().get(1));
        assertEquals("20",response.getData().getShopInterval().getPriceInterval().get(2));



        // 清除
        redissonClient.getBucket("index_shop_start_point_interval").delete();
        redissonClient.getBucket("index_shop_end_point_interval").delete();
        redissonClient.getBucket("index_shop_point_interval").delete();

        redissonClient.getBucket("index_shop_start_price_interval").delete();
        redissonClient.getBucket("index_shop_end_price_interval").delete();
        redissonClient.getBucket("index_shop_price_interval").delete();
    }

    @Test
    void testSnapUpNext() {
        // 创建请求对象
        SnapUpNextRequest request = new SnapUpNextRequest();

        // 直接调用控制器方法
        Response<SnapUpNextResponse> response = indexController.snapUpNext(request, null);

        // 验证响应结构
        assertNotNull(response);
        assertEquals(10, response.getData().getSnatchList().size());
    }

    @Test
    void testSnapUpNext_IOS() {
        // 创建请求对象
        SnapUpNextRequest request = new SnapUpNextRequest();

        // 直接调用控制器方法
        Response<SnapUpNextResponse> response = indexController.snapUpNext(request, ClientTypeEnum.IOS.toString());

        // 验证响应结构
        assertNotNull(response);
        assertEquals(10, response.getData().getSnatchList().size());
    }

    @Test
    void testSnapUpNext_Harmony() {
        // 创建请求对象
        SnapUpNextRequest request = new SnapUpNextRequest();

        // 直接调用控制器方法
        Response<SnapUpNextResponse> response = indexController.snapUpNext(request, ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response);
        assertEquals(9, response.getData().getSnatchList().size());
    }

    @Test
    void testIndex() {
        // 直接调用控制器方法
        Response<IndexModuleResponse> response = indexController.index(null);

        // 验证响应结构
        assertNotNull(response);
        assertFalse(response.getData().isHideHarmonyModule());
    }

    @Test
    void testIndex_IOS() {
        // 直接调用控制器方法
        Response<IndexModuleResponse> response = indexController.index(ClientTypeEnum.IOS.toString());

        // 验证响应结构
        assertNotNull(response);
        assertFalse(response.getData().isHideHarmonyModule());
    }

    @Test
    void testIndex_Harmony() {
        // 直接调用控制器方法
        Response<IndexModuleResponse> response = indexController.index(ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response);
        assertTrue(response.getData().isHideHarmonyModule());
    }

    /**
     * 测试著数分类功能 - 非HARMONY客户端
     * 验证返回的著数分类列表包含预期的数据
     */
    @Test
    void testStoreKeywordsWithNonHarmonyClient() {
        // 直接调用控制器方法，使用非HARMONY客户端
        Response<StoreKeywordsResponse> response = indexController.storeKeywords("OTHER");

        // 验证响应结构
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());

        StoreKeywordsResponse data = response.getData();
        assertNotNull(data.getSnatchList());

        // 验证返回的著数分类列表不为空
        List<StoreKeywordsResponse.SnatchListItem> snatchList = data.getSnatchList();
        assertFalse(snatchList.isEmpty(), "Store keywords list should not be empty");

        // 验证返回的著数分类项包含预期的数据
        for (StoreKeywordsResponse.SnatchListItem item : snatchList) {
            assertNotNull(item.getId());
            assertNotNull(item.getName());
            assertNotNull(item.getImg());
            assertNotNull(item.getSort());
        }
    }

    /**
     * 测试著数分类功能 - HARMONY客户端
     * 验证不同客户端类型的响应差异
     */
    @Test
    void testStoreKeywordsWithHarmonyClient() {
        // 直接调用控制器方法，使用HARMONY客户端
        Response<StoreKeywordsResponse> response = indexController.storeKeywords(ClientTypeEnum.HARMONY.toString());

        // 验证响应结构
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());

        StoreKeywordsResponse data = response.getData();
        assertNotNull(data.getSnatchList());

        // 验证返回的著数分类列表不为空
        List<StoreKeywordsResponse.SnatchListItem> snatchList = data.getSnatchList();
        assertFalse(snatchList.isEmpty(), "Store keywords list should not be empty");

        // 验证返回的著数分类项包含预期的数据，并且不包含小程序链接类型(srcType=4)
        for (StoreKeywordsResponse.SnatchListItem item : snatchList) {
            assertNotNull(item.getId());
            assertNotNull(item.getName());
            assertNotNull(item.getImg());
            assertNotNull(item.getSort());

            // 验证HARMONY客户端不返回小程序链接类型
            if (item.getSrcType() != null) {
                assertNotEquals("4", item.getSrcType(), "HARMONY client should not return mini program links (srcType=4)");
            }
        }
    }
}