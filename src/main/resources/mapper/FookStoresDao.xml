<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookStores">
    <id column="id" jdbcType="INTEGER" property="id" />
    <id column="macau_pass_terminal_number" jdbcType="VARCHAR" property="macauPassTerminalNumber" />
    <result column="store_number" jdbcType="VARCHAR" property="storeNumber" />
    <result column="store_type_id" jdbcType="VARCHAR" property="storeTypeId" />
    <result column="business_information_id" jdbcType="VARCHAR" property="businessInformationId" />
    <result column="area_id" jdbcType="SMALLINT" property="areaId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
    <result column="google_longitude" jdbcType="VARCHAR" property="googleLongitude" />
    <result column="google_demension" jdbcType="VARCHAR" property="googleDemension" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="score_number" jdbcType="INTEGER" property="scoreNumber" />
    <result column="expense" jdbcType="VARCHAR" property="expense" />
    <result column="expense_number" jdbcType="INTEGER" property="expenseNumber" />
    <result column="head_name" jdbcType="VARCHAR" property="headName" />
    <result column="head_tel" jdbcType="VARCHAR" property="headTel" />
    <result column="head_email" jdbcType="VARCHAR" property="headEmail" />
    <result column="reservation_time" jdbcType="VARCHAR" property="reservationTime" />
    <result column="charge" jdbcType="VARCHAR" property="charge" />
    <result column="google_index" jdbcType="VARCHAR" property="googleIndex" />
    <result column="scott_index" jdbcType="VARCHAR" property="scottIndex" />
    <result column="provide_services" jdbcType="VARCHAR" property="provideServices" />
    <result column="business_time" jdbcType="VARCHAR" property="businessTime" />
    <result column="if_new" jdbcType="TINYINT" property="ifNew" />
    <result column="verification_pass" jdbcType="VARCHAR" property="verificationPass" />
    <result column="login_account" jdbcType="VARCHAR" property="loginAccount" />
    <result column="login_pass" jdbcType="VARCHAR" property="loginPass" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="macau_pass_merchant_number" jdbcType="VARCHAR" property="macauPassMerchantNumber" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="collect_times" jdbcType="INTEGER" property="collectTimes" />
    <result column="is_selected" jdbcType="TINYINT" property="isSelected" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="is_general" jdbcType="TINYINT" property="isGeneral" />
    <result column="alone_settlement" jdbcType="TINYINT" property="aloneSettlement" />
    <result column="settlement_account" jdbcType="VARCHAR" property="settlementAccount" />
    <result column="settlement_name" jdbcType="VARCHAR" property="settlementName" />
    <result column="settlement_bank" jdbcType="VARCHAR" property="settlementBank" />
    <result column="istemporary" jdbcType="BIT" property="istemporary" />
    <result column="background_img" jdbcType="VARCHAR" property="backgroundImg" />
    <result column="if_update" jdbcType="BIT" property="ifUpdate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.FookStores">
    <result column="img" jdbcType="LONGVARCHAR" property="img" />
    <result column="img_zip" jdbcType="LONGVARCHAR" property="imgZip" />
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
  </resultMap>
  <sql id="Base_Column_List">
    id, macau_pass_terminal_number, store_number, store_type_id, business_information_id, 
    area_id, user_id, business_id, `name`, phone, address, longitude, dimension, google_longitude,
    google_demension, score, score_number, expense, expense_number, head_name, head_tel, 
    head_email, reservation_time, charge, google_index, scott_index, provide_services, 
    business_time, if_new, verification_pass, login_account, login_pass, `enable`, macau_pass_merchant_number,
    created_at, updated_at, collect_times, is_selected, shop_id, is_general, alone_settlement, 
    settlement_account, settlement_name, settlement_bank, istemporary, background_img, 
    if_update
  </sql>
  <sql id="Blob_Column_List">
    img, img_zip, detail
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fook_stores
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fook_stores
    where id = #{id,jdbcType=INTEGER}
      and macau_pass_terminal_number = #{macauPassTerminalNumber,jdbcType=VARCHAR}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookStores">
    insert into fook_stores
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="macauPassTerminalNumber != null">
        macau_pass_terminal_number,
      </if>
      <if test="storeNumber != null">
        store_number,
      </if>
      <if test="storeTypeId != null">
        store_type_id,
      </if>
      <if test="businessInformationId != null">
        business_information_id,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="googleLongitude != null">
        google_longitude,
      </if>
      <if test="googleDemension != null">
        google_demension,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="scoreNumber != null">
        score_number,
      </if>
      <if test="expense != null">
        expense,
      </if>
      <if test="expenseNumber != null">
        expense_number,
      </if>
      <if test="headName != null">
        head_name,
      </if>
      <if test="headTel != null">
        head_tel,
      </if>
      <if test="headEmail != null">
        head_email,
      </if>
      <if test="reservationTime != null">
        reservation_time,
      </if>
      <if test="charge != null">
        charge,
      </if>
      <if test="googleIndex != null">
        google_index,
      </if>
      <if test="scottIndex != null">
        scott_index,
      </if>
      <if test="provideServices != null">
        provide_services,
      </if>
      <if test="businessTime != null">
        business_time,
      </if>
      <if test="ifNew != null">
        if_new,
      </if>
      <if test="verificationPass != null">
        verification_pass,
      </if>
      <if test="loginAccount != null">
        login_account,
      </if>
      <if test="loginPass != null">
        login_pass,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="macauPassMerchantNumber != null">
        macau_pass_merchant_number,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="collectTimes != null">
        collect_times,
      </if>
      <if test="isSelected != null">
        is_selected,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="isGeneral != null">
        is_general,
      </if>
      <if test="aloneSettlement != null">
        alone_settlement,
      </if>
      <if test="settlementAccount != null">
        settlement_account,
      </if>
      <if test="settlementName != null">
        settlement_name,
      </if>
      <if test="settlementBank != null">
        settlement_bank,
      </if>
      <if test="istemporary != null">
        istemporary,
      </if>
      <if test="backgroundImg != null">
        background_img,
      </if>
      <if test="ifUpdate != null">
        if_update,
      </if>
      <if test="img != null">
        img,
      </if>
      <if test="imgZip != null">
        img_zip,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="macauPassTerminalNumber != null">
        #{macauPassTerminalNumber,jdbcType=VARCHAR},
      </if>
      <if test="storeNumber != null">
        #{storeNumber,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeId != null">
        #{storeTypeId,jdbcType=VARCHAR},
      </if>
      <if test="businessInformationId != null">
        #{businessInformationId,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=SMALLINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="googleLongitude != null">
        #{googleLongitude,jdbcType=VARCHAR},
      </if>
      <if test="googleDemension != null">
        #{googleDemension,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="scoreNumber != null">
        #{scoreNumber,jdbcType=INTEGER},
      </if>
      <if test="expense != null">
        #{expense,jdbcType=VARCHAR},
      </if>
      <if test="expenseNumber != null">
        #{expenseNumber,jdbcType=INTEGER},
      </if>
      <if test="headName != null">
        #{headName,jdbcType=VARCHAR},
      </if>
      <if test="headTel != null">
        #{headTel,jdbcType=VARCHAR},
      </if>
      <if test="headEmail != null">
        #{headEmail,jdbcType=VARCHAR},
      </if>
      <if test="reservationTime != null">
        #{reservationTime,jdbcType=VARCHAR},
      </if>
      <if test="charge != null">
        #{charge,jdbcType=VARCHAR},
      </if>
      <if test="googleIndex != null">
        #{googleIndex,jdbcType=VARCHAR},
      </if>
      <if test="scottIndex != null">
        #{scottIndex,jdbcType=VARCHAR},
      </if>
      <if test="provideServices != null">
        #{provideServices,jdbcType=VARCHAR},
      </if>
      <if test="businessTime != null">
        #{businessTime,jdbcType=VARCHAR},
      </if>
      <if test="ifNew != null">
        #{ifNew,jdbcType=TINYINT},
      </if>
      <if test="verificationPass != null">
        #{verificationPass,jdbcType=VARCHAR},
      </if>
      <if test="loginAccount != null">
        #{loginAccount,jdbcType=VARCHAR},
      </if>
      <if test="loginPass != null">
        #{loginPass,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="macauPassMerchantNumber != null">
        #{macauPassMerchantNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="collectTimes != null">
        #{collectTimes,jdbcType=INTEGER},
      </if>
      <if test="isSelected != null">
        #{isSelected,jdbcType=TINYINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="isGeneral != null">
        #{isGeneral,jdbcType=TINYINT},
      </if>
      <if test="aloneSettlement != null">
        #{aloneSettlement,jdbcType=TINYINT},
      </if>
      <if test="settlementAccount != null">
        #{settlementAccount,jdbcType=VARCHAR},
      </if>
      <if test="settlementName != null">
        #{settlementName,jdbcType=VARCHAR},
      </if>
      <if test="settlementBank != null">
        #{settlementBank,jdbcType=VARCHAR},
      </if>
      <if test="istemporary != null">
        #{istemporary,jdbcType=BIT},
      </if>
      <if test="backgroundImg != null">
        #{backgroundImg,jdbcType=VARCHAR},
      </if>
      <if test="ifUpdate != null">
        #{ifUpdate,jdbcType=BIT},
      </if>
      <if test="img != null">
        #{img,jdbcType=LONGVARCHAR},
      </if>
      <if test="imgZip != null">
        #{imgZip,jdbcType=LONGVARCHAR},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookStores">
    update fook_stores
    <set>
      <if test="storeNumber != null">
        store_number = #{storeNumber,jdbcType=VARCHAR},
      </if>
      <if test="storeTypeId != null">
        store_type_id = #{storeTypeId,jdbcType=VARCHAR},
      </if>
      <if test="businessInformationId != null">
        business_information_id = #{businessInformationId,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=SMALLINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="googleLongitude != null">
        google_longitude = #{googleLongitude,jdbcType=VARCHAR},
      </if>
      <if test="googleDemension != null">
        google_demension = #{googleDemension,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="scoreNumber != null">
        score_number = #{scoreNumber,jdbcType=INTEGER},
      </if>
      <if test="expense != null">
        expense = #{expense,jdbcType=VARCHAR},
      </if>
      <if test="expenseNumber != null">
        expense_number = #{expenseNumber,jdbcType=INTEGER},
      </if>
      <if test="headName != null">
        head_name = #{headName,jdbcType=VARCHAR},
      </if>
      <if test="headTel != null">
        head_tel = #{headTel,jdbcType=VARCHAR},
      </if>
      <if test="headEmail != null">
        head_email = #{headEmail,jdbcType=VARCHAR},
      </if>
      <if test="reservationTime != null">
        reservation_time = #{reservationTime,jdbcType=VARCHAR},
      </if>
      <if test="charge != null">
        charge = #{charge,jdbcType=VARCHAR},
      </if>
      <if test="googleIndex != null">
        google_index = #{googleIndex,jdbcType=VARCHAR},
      </if>
      <if test="scottIndex != null">
        scott_index = #{scottIndex,jdbcType=VARCHAR},
      </if>
      <if test="provideServices != null">
        provide_services = #{provideServices,jdbcType=VARCHAR},
      </if>
      <if test="businessTime != null">
        business_time = #{businessTime,jdbcType=VARCHAR},
      </if>
      <if test="ifNew != null">
        if_new = #{ifNew,jdbcType=TINYINT},
      </if>
      <if test="verificationPass != null">
        verification_pass = #{verificationPass,jdbcType=VARCHAR},
      </if>
      <if test="loginAccount != null">
        login_account = #{loginAccount,jdbcType=VARCHAR},
      </if>
      <if test="loginPass != null">
        login_pass = #{loginPass,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="macauPassMerchantNumber != null">
        macau_pass_merchant_number = #{macauPassMerchantNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="collectTimes != null">
        collect_times = #{collectTimes,jdbcType=INTEGER},
      </if>
      <if test="isSelected != null">
        is_selected = #{isSelected,jdbcType=TINYINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="isGeneral != null">
        is_general = #{isGeneral,jdbcType=TINYINT},
      </if>
      <if test="aloneSettlement != null">
        alone_settlement = #{aloneSettlement,jdbcType=TINYINT},
      </if>
      <if test="settlementAccount != null">
        settlement_account = #{settlementAccount,jdbcType=VARCHAR},
      </if>
      <if test="settlementName != null">
        settlement_name = #{settlementName,jdbcType=VARCHAR},
      </if>
      <if test="settlementBank != null">
        settlement_bank = #{settlementBank,jdbcType=VARCHAR},
      </if>
      <if test="istemporary != null">
        istemporary = #{istemporary,jdbcType=BIT},
      </if>
      <if test="backgroundImg != null">
        background_img = #{backgroundImg,jdbcType=VARCHAR},
      </if>
      <if test="ifUpdate != null">
        if_update = #{ifUpdate,jdbcType=BIT},
      </if>
      <if test="img != null">
        img = #{img,jdbcType=LONGVARCHAR},
      </if>
      <if test="imgZip != null">
        img_zip = #{imgZip,jdbcType=LONGVARCHAR},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
      and macau_pass_terminal_number = #{macauPassTerminalNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.FookStores">
    update fook_stores
    set store_number = #{storeNumber,jdbcType=VARCHAR},
      store_type_id = #{storeTypeId,jdbcType=VARCHAR},
      business_information_id = #{businessInformationId,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=SMALLINT},
      user_id = #{userId,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      dimension = #{dimension,jdbcType=VARCHAR},
      google_longitude = #{googleLongitude,jdbcType=VARCHAR},
      google_demension = #{googleDemension,jdbcType=VARCHAR},
      score = #{score,jdbcType=DECIMAL},
      score_number = #{scoreNumber,jdbcType=INTEGER},
      expense = #{expense,jdbcType=VARCHAR},
      expense_number = #{expenseNumber,jdbcType=INTEGER},
      head_name = #{headName,jdbcType=VARCHAR},
      head_tel = #{headTel,jdbcType=VARCHAR},
      head_email = #{headEmail,jdbcType=VARCHAR},
      reservation_time = #{reservationTime,jdbcType=VARCHAR},
      charge = #{charge,jdbcType=VARCHAR},
      google_index = #{googleIndex,jdbcType=VARCHAR},
      scott_index = #{scottIndex,jdbcType=VARCHAR},
      provide_services = #{provideServices,jdbcType=VARCHAR},
      business_time = #{businessTime,jdbcType=VARCHAR},
      if_new = #{ifNew,jdbcType=TINYINT},
      verification_pass = #{verificationPass,jdbcType=VARCHAR},
      login_account = #{loginAccount,jdbcType=VARCHAR},
      login_pass = #{loginPass,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      macau_pass_merchant_number = #{macauPassMerchantNumber,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      collect_times = #{collectTimes,jdbcType=INTEGER},
      is_selected = #{isSelected,jdbcType=TINYINT},
      shop_id = #{shopId,jdbcType=VARCHAR},
      is_general = #{isGeneral,jdbcType=TINYINT},
      alone_settlement = #{aloneSettlement,jdbcType=TINYINT},
      settlement_account = #{settlementAccount,jdbcType=VARCHAR},
      settlement_name = #{settlementName,jdbcType=VARCHAR},
      settlement_bank = #{settlementBank,jdbcType=VARCHAR},
      istemporary = #{istemporary,jdbcType=BIT},
      background_img = #{backgroundImg,jdbcType=VARCHAR},
      if_update = #{ifUpdate,jdbcType=BIT},
      img = #{img,jdbcType=LONGVARCHAR},
      img_zip = #{imgZip,jdbcType=LONGVARCHAR},
      detail = #{detail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
      and macau_pass_terminal_number = #{macauPassTerminalNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookStores">
    update fook_stores
    set store_number = #{storeNumber,jdbcType=VARCHAR},
      store_type_id = #{storeTypeId,jdbcType=VARCHAR},
      business_information_id = #{businessInformationId,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=SMALLINT},
      user_id = #{userId,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      dimension = #{dimension,jdbcType=VARCHAR},
      google_longitude = #{googleLongitude,jdbcType=VARCHAR},
      google_demension = #{googleDemension,jdbcType=VARCHAR},
      score = #{score,jdbcType=DECIMAL},
      score_number = #{scoreNumber,jdbcType=INTEGER},
      expense = #{expense,jdbcType=VARCHAR},
      expense_number = #{expenseNumber,jdbcType=INTEGER},
      head_name = #{headName,jdbcType=VARCHAR},
      head_tel = #{headTel,jdbcType=VARCHAR},
      head_email = #{headEmail,jdbcType=VARCHAR},
      reservation_time = #{reservationTime,jdbcType=VARCHAR},
      charge = #{charge,jdbcType=VARCHAR},
      google_index = #{googleIndex,jdbcType=VARCHAR},
      scott_index = #{scottIndex,jdbcType=VARCHAR},
      provide_services = #{provideServices,jdbcType=VARCHAR},
      business_time = #{businessTime,jdbcType=VARCHAR},
      if_new = #{ifNew,jdbcType=TINYINT},
      verification_pass = #{verificationPass,jdbcType=VARCHAR},
      login_account = #{loginAccount,jdbcType=VARCHAR},
      login_pass = #{loginPass,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      macau_pass_merchant_number = #{macauPassMerchantNumber,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      collect_times = #{collectTimes,jdbcType=INTEGER},
      is_selected = #{isSelected,jdbcType=TINYINT},
      shop_id = #{shopId,jdbcType=VARCHAR},
      is_general = #{isGeneral,jdbcType=TINYINT},
      alone_settlement = #{aloneSettlement,jdbcType=TINYINT},
      settlement_account = #{settlementAccount,jdbcType=VARCHAR},
      settlement_name = #{settlementName,jdbcType=VARCHAR},
      settlement_bank = #{settlementBank,jdbcType=VARCHAR},
      istemporary = #{istemporary,jdbcType=BIT},
      background_img = #{backgroundImg,jdbcType=VARCHAR},
      if_update = #{ifUpdate,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
      and macau_pass_terminal_number = #{macauPassTerminalNumber,jdbcType=VARCHAR}
  </update>
</mapper>