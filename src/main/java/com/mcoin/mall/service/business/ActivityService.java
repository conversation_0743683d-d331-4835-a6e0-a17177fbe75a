package com.mcoin.mall.service.business;

import com.mcoin.mall.model.ActiveMerchantListRequest;
import com.mcoin.mall.model.ActiveMerchantListResponse;
import com.mcoin.mall.model.DaygainListRequest;
import com.mcoin.mall.model.DaygainListResponse;
import com.mcoin.mall.model.Response;

public interface ActivityService {
	DaygainListResponse dayGainList(DaygainListRequest request);
	Response<ActiveMerchantListResponse> merchantList(ActiveMerchantListRequest request);
}