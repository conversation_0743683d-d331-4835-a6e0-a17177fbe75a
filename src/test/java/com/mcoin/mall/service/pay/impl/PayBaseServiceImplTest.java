package com.mcoin.mall.service.pay.impl;

import com.mcoin.mall.util.ConfigUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;

class PayBaseServiceImplTest {

    private MockedStatic<ConfigUtils> mockedStatic;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
        mockedStatic = Mockito.mockStatic(ConfigUtils.class);
    }

    @AfterEach
    void cleanup() {
        if (mockedStatic != null) {
            mockedStatic.close();
        }
    }

    @ParameterizedTest
    @MethodSource("provideSellerIdTestData")
    void testUploadRefundSuccess(int sellerId, boolean expectedResult) {
        // given
        // The real isSellerInBlackList method expects a comma-separated list of seller IDs
        // When the seller should be in blacklist, include it in the list; otherwise provide other IDs
        String blacklistConfig = expectedResult ? "3,1,5" : "3,5,7";
        mockedStatic.when(() -> ConfigUtils.getProperty(eq("task.unupload.uploadRefundSuccess.sellers")))
                   .thenReturn(blacklistConfig);
        
        // when
        boolean result = PayBaseServiceImpl.isSellerInBlackList(sellerId);
        
        // then
        assertEquals(expectedResult, result);
    }

    private static Stream<Arguments> provideSellerIdTestData() {
        return Stream.of(
                Arguments.of(1, true),  // Should be in blacklist
                Arguments.of(2, false)  // Should not be in blacklist
        );
    }
} 