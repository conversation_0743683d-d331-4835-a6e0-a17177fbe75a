package com.mcoin.mall.controller.show;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.SnapUpSessionIndexRequest;
import com.mcoin.mall.model.SnapUpSessionIndexResponse;
import com.mcoin.mall.model.SnapUpSessionInfoRequest;
import com.mcoin.mall.model.SnapUpSessionInfoResponse;
import com.mcoin.mall.model.SnapUpSessionProductRequest;
import com.mcoin.mall.model.SnapUpSessionProductResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

class ShowSnapUpControllerTest extends BaseUnitTest {

    @Autowired
    private ShowSnapUpController showSnapUpController;

    @Test
    void testIndexSnapUp() {
        SnapUpSessionIndexRequest request = new SnapUpSessionIndexRequest();
        request.setNum(6);
        Response<SnapUpSessionIndexResponse> response = showSnapUpController.indexSnapUp(request,null);
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(6, response.getData().getSnatchList().size());
        assertEquals(1, response.getData().getSnatchList().stream().filter(item -> item.getType() == 12).count());
        assertEquals(5, response.getData().getSnatchList().stream().filter(item -> item.getType() != 12).count());
    }

    @Test
    void testIndexSnapUp_harmony() {
        SnapUpSessionIndexRequest request = new SnapUpSessionIndexRequest();
        request.setNum(6);
        Response<SnapUpSessionIndexResponse> response = showSnapUpController.indexSnapUp(request, ClientTypeEnum.HARMONY.toString());
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(5, response.getData().getSnatchList().size());
        assertEquals(0, response.getData().getSnatchList().stream().filter(item -> item.getType() == 12).count());
        assertEquals(5, response.getData().getSnatchList().stream().filter(item -> item.getType() != 12).count());
    }

    @Test
    void testIndexSnapUp_IOS() {
        SnapUpSessionIndexRequest request = new SnapUpSessionIndexRequest();
        request.setNum(6);
        Response<SnapUpSessionIndexResponse> response = showSnapUpController.indexSnapUp(request, ClientTypeEnum.IOS.toString());
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(6, response.getData().getSnatchList().size());
        assertEquals(1, response.getData().getSnatchList().stream().filter(item -> item.getType() == 12).count());
        assertEquals(5, response.getData().getSnatchList().stream().filter(item -> item.getType() != 12).count());
    }

    @Test
    void testInfo() {
        SnapUpSessionInfoRequest request = new SnapUpSessionInfoRequest();
        
        Response<SnapUpSessionInfoResponse> response = showSnapUpController.info(request);
        
        assertNotNull(response);
        assertNotNull(response.getData());
    }

    @Test
    void testProduct() {
        SnapUpSessionProductRequest request = new SnapUpSessionProductRequest();
        request.setSessionId(2);
        request.setProductId(2411);
        request.setPage(1);

        Response<SnapUpSessionProductResponse> response = showSnapUpController.product(request, null);

        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(6, response.getData().getSnatchList().size());
        assertEquals(1, response.getData().getSnatchList().stream().filter(item -> item.getType() == 12).count());
        assertEquals(5, response.getData().getSnatchList().stream().filter(item -> item.getType() != 12).count());
        assertEquals(request.getProductId(), response.getData().getSnatchList().get(0).getId());
    }

    @Test
    void testProduct_Harmony() {
        SnapUpSessionProductRequest request = new SnapUpSessionProductRequest();
        request.setSessionId(2);
        request.setProductId(2411);
        request.setPage(1);

        Response<SnapUpSessionProductResponse> response = showSnapUpController.product(request, ClientTypeEnum.HARMONY.toString());

        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(5, response.getData().getSnatchList().size());
        assertEquals(0, response.getData().getSnatchList().stream().filter(item -> item.getType() == 12).count());
        assertEquals(5, response.getData().getSnatchList().stream().filter(item -> item.getType() != 12).count());
        assertEquals(request.getProductId(), response.getData().getSnatchList().get(0).getId());
    }
} 