package com.mcoin.mall.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

public class DistanceUtilTest {

    @Test
    public void testGetDistanceWithInvalidCoordinates() {
        assertThrows(
                NumberFormatException.class,
                () -> {
                    DistanceUtil.getDistance("invalid", "39.915", "16.405", "39.916");
                },
                "NumberFormatException should be thrown for invalid coordinates");
    }

    @Test
    public void testGetDistanceWithMixedValidAndInvalidCoordinates() {
        double distance = DistanceUtil.getDistance("116.404", "39.915", null, "39.916");
        assertEquals(
                0, distance, 0.001, "Distance should be 0 for mixed valid and invalid coordinates");
    }

    @Test
    public void testGetDistanceWithValidCoordinates() {
        double distance = DistanceUtil.getDistance("116.404", "39.915", "116.404", "39.915");
        assertEquals(0, distance, 0.001, "Distance should be 0 for the same coordinates");
    }

    @Test
    public void testGetDistanceWithDifferentCoordinates() {
        double distance = DistanceUtil.getDistance("116.404", "39.915", "116.405", "39.916");
        assertTrue(
                distance > 0 && distance < 1000,
                "Distance should be greater than 0 and less than 1000");
    }

    @Test
    public void testGetDistanceWithBlankCoordinates() {
        double distance = DistanceUtil.getDistance("", "39.915", "16.405", "39.916");
        assertEquals(0, distance, 0.001, "Distance should be 0 for blank coordinates");
    }

    @Test
    public void testGetDistanceWithNullCoordinates() {
        double distance = DistanceUtil.getDistance(null, "39.915", "16.405", "39.916");
        assertEquals(0, distance, 0.001, "Distance should be 0 for null coordinates");
    }

    @Test
    public void testGetDistanceWithSameCoordinates() {
        // Test case 5: Same coordinates should return 0 km
        double distance = DistanceUtil.getDistance(116.404, 39.915, 16.404, 39.915);
        assertEquals(8002.4, distance, 0.1);
    }

    @Test
    public void testGetDistanceWithNegativeCoordinates() {
        // Test case 6: Negative coordinates should return distance
        double distance = DistanceUtil.getDistance(-116.404, -39.915, -116.405, -39.916);
        assertEquals(0.14, distance, 0.1);
    }

    @Test
    public void testGetDistanceWithZeroCoordinates() {
        // Test case 7: Zero coordinates should return distance
        double distance = DistanceUtil.getDistance(0, 0, 0, 0);
        assertEquals(0.0, distance, 0.1);
    }

    @Test
    public void testGetDistanceWithInvalidCoordinates1() {
        // Test case 8: Invalid coordinates should throw NumberFormatException
        assertThrows(
                NumberFormatException.class,
                () -> {
                    DistanceUtil.getDistance("invalid", "39.915", "16.405", "39.916");
                });
    }

    @Test
    public void testGetDistanceWithValidCoordinates1() {
        // Test case 1: Normal case, distance should be around 1.5 km
        double distance = DistanceUtil.getDistance(116.404, 39.915, 116.405, 39.916);
        assertEquals(0.15, distance, 0.1);

        // Test case 2: Distance should be around 10 km
        distance = DistanceUtil.getDistance(116.404, 39.915, 16.405, 39.915);
        assertEquals(8002.4, distance, 0.1);

        // Test case 3: Distance should be around 1.5 km
        distance = DistanceUtil.getDistance(116.404, 39.915, 116.404, 39.916);
        assertEquals(0.2, distance, 0.1);

        // Test case 4: Distance should be around 10 km
        distance = DistanceUtil.getDistance(116.404, 39.915, 116.404, 39.915);
        assertEquals(0, distance, 0);
    }
}
