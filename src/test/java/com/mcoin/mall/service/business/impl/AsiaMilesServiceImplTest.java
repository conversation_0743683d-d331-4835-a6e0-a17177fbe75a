package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class AsiaMilesServiceImplTest {
    
    @Mock
    FookPlatformOrderDao fookPlatformOrderDao;
    
    @Mock
    FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    
    @Mock
    ContextHolder contextHolder;
    
    @InjectMocks
    AsiaMilesServiceImpl asiaMilesServiceImpl;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetAsiaMilesList() {
        // given
        FookPlatformOrderinfo orderinfo = JsonTestUtils.getObjectFromJson("json/orderinfo.json", "singleOrderInfo", FookPlatformOrderinfo.class);
        FookPlatformOrderinfo orderinfo2 = JsonTestUtils.getObjectFromJson("json/orderinfo.json", "singleOrderInfo", FookPlatformOrderinfo.class);

        // mock
        when(fookPlatformOrderDao.getOrderIdByQuery(anyString(), anyString(), anyInt(), anyInt())).thenReturn(Collections.singletonList(0));
        
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(0);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        
        when(fookPlatformOrderinfoDao.getAsiaMilesInfo(any())).thenReturn(Arrays.asList(orderinfo, orderinfo2));

        // when
        Object result = asiaMilesServiceImpl.getAsiaMilesList();

        // then
        assertNotNull(result);
    }
} 