<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMerchantSettlementBatchDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportMerchantSettlementBatch">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="seller_id" jdbcType="INTEGER" property="sellerId" />
    <result column="store_id" jdbcType="INTEGER" property="storeId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="momecoins" jdbcType="DECIMAL" property="momecoins" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="is_a" jdbcType="BIT" property="isA" />
  </resultMap>
  <sql id="Base_Column_List">
    id, seller_id, store_id, start_time, end_time, bill_amount, settlement_amount, commission, 
    momecoins, settlement_time, is_a
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_merchant_settlement_batch
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_merchant_settlement_batch
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportMerchantSettlementBatch">
    insert into fook_report_merchant_settlement_batch
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="momecoins != null">
        momecoins,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
      <if test="isA != null">
        is_a,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isA != null">
        #{isA,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportMerchantSettlementBatch">
    update fook_report_merchant_settlement_batch
    <set>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        momecoins = #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isA != null">
        is_a = #{isA,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportMerchantSettlementBatch">
    update fook_report_merchant_settlement_batch
    set seller_id = #{sellerId,jdbcType=INTEGER},
      store_id = #{storeId,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      momecoins = #{momecoins,jdbcType=DECIMAL},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      is_a = #{isA,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>