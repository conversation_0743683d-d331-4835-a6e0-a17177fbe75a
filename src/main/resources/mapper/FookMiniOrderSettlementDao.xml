<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMiniOrderSettlementDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMiniOrderSettlement">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="product_price" jdbcType="VARCHAR" property="productPrice" />
    <result column="goods_num" jdbcType="INTEGER" property="goodsNum" />
    <result column="bill_amount" jdbcType="VARCHAR" property="billAmount" />
    <result column="pay_amount" jdbcType="VARCHAR" property="payAmount" />
    <result column="pay_integral" jdbcType="VARCHAR" property="payIntegral" />
    <result column="fee_rate" jdbcType="VARCHAR" property="feeRate" />
    <result column="integral_ratio" jdbcType="VARCHAR" property="integralRatio" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="complete_time" jdbcType="VARCHAR" property="completeTime" />
    <result column="delivery_type" jdbcType="INTEGER" property="deliveryType" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, uuid, order_no, product_id, product_price, goods_num, bill_amount, pay_amount, 
    pay_integral, fee_rate, integral_ratio, code, create_time, complete_time, delivery_type, 
    created_at, subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_mini_order_settlement
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_mini_order_settlement
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMiniOrderSettlement">
    insert into fook_mini_order_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="uuid != null">
        uuid,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productPrice != null">
        product_price,
      </if>
      <if test="goodsNum != null">
        goods_num,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="payIntegral != null">
        pay_integral,
      </if>
      <if test="feeRate != null">
        fee_rate,
      </if>
      <if test="integralRatio != null">
        integral_ratio,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        #{productPrice,jdbcType=VARCHAR},
      </if>
      <if test="goodsNum != null">
        #{goodsNum,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=VARCHAR},
      </if>
      <if test="payIntegral != null">
        #{payIntegral,jdbcType=VARCHAR},
      </if>
      <if test="feeRate != null">
        #{feeRate,jdbcType=VARCHAR},
      </if>
      <if test="integralRatio != null">
        #{integralRatio,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=VARCHAR},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMiniOrderSettlement">
    update fook_mini_order_settlement
    <set>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        product_price = #{productPrice,jdbcType=VARCHAR},
      </if>
      <if test="goodsNum != null">
        goods_num = #{goodsNum,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=VARCHAR},
      </if>
      <if test="payIntegral != null">
        pay_integral = #{payIntegral,jdbcType=VARCHAR},
      </if>
      <if test="feeRate != null">
        fee_rate = #{feeRate,jdbcType=VARCHAR},
      </if>
      <if test="integralRatio != null">
        integral_ratio = #{integralRatio,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=VARCHAR},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMiniOrderSettlement">
    update fook_mini_order_settlement
    set uuid = #{uuid,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      product_price = #{productPrice,jdbcType=VARCHAR},
      goods_num = #{goodsNum,jdbcType=INTEGER},
      bill_amount = #{billAmount,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=VARCHAR},
      pay_integral = #{payIntegral,jdbcType=VARCHAR},
      fee_rate = #{feeRate,jdbcType=VARCHAR},
      integral_ratio = #{integralRatio,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      complete_time = #{completeTime,jdbcType=VARCHAR},
      delivery_type = #{deliveryType,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>