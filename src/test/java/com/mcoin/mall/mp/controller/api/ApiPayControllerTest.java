package com.mcoin.mall.mp.controller.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.bo.PayConfigBo;
import com.mcoin.mall.mp.constant.PayConstant;
import com.mcoin.mall.mp.entity.LsOrder;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.mapper.LsOrderMapper;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.api.MPayNotifyRequest;
import com.mcoin.mall.mp.util.SignUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Transactional
public class ApiPayControllerTest extends BaseUnitTest {

    @Autowired
    private ApiPayController apiPayController;

    @Autowired
    private LsOrderMapper lsOrderMapper;

    @Autowired
    private ObjectMapper objectMapper;

    private String validNotifyJson;
    private MPayNotifyRequest validRequest;
    private PayConfigBo payConfig;

    @BeforeEach
    public void setUp() throws Exception {
        // 准备支付配置
        payConfig = new PayConfigBo();
        payConfig.setPaySignKey("XVPK3RXASMSMVUI9AN4JD7IDXE2ZEF1V");
        
        // 准备测试订单
//        testOrder = new LsOrder();
//        testOrder.setSn("ORDER123456");
//        testOrder.setId(1);
//        testOrder.setMerNo("MERCHANT001");
//        testOrder.setDeleteTime(null);
//        testOrder.setSid(1);
//        testOrder.setOrderStatus(0);
//        testOrder.setPayStatus(0);
//        testOrder.setOrderType(0);
//        testOrder.setOrderTerminal(1);
//        testOrder.setDeliveryType(1);
//        testOrder.setCreateTime(1739870487L);
//        testOrder.setUpdateTime(1739870487L);
//        testOrder.setGoodsPrice(new BigDecimal("100.00"));
//        testOrder.setOrderAmount(new BigDecimal("100.00"));
//        testOrder.setTotalAmount(new BigDecimal("100.00"));
//        testOrder.setTotalNum(1);
//        lsOrderMapper.insert(testOrder);

        // 准备有效的通知请求
        validRequest = new MPayNotifyRequest();
        validRequest.setOutTransId("ORDER123456");
        validRequest.setTransAmount("100.00");
        validRequest.setTransStatus("SUCCESS");
        validRequest.setTransId("TRANS123456");
        validRequest.setTradeNo("TRADE123456");
        validRequest.setNotifyTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        validRequest.setNotifyType("trade_status_sync");
        validRequest.setSignType("RSA2");
        
        // 使用Map生成签名，确保使用下划线格式
        Map<String, String> signMap = new HashMap<>();
        signMap.put("notify_time", validRequest.getNotifyTime());
        signMap.put("notify_type", validRequest.getNotifyType());
        signMap.put("out_trans_id", validRequest.getOutTransId());
        signMap.put("trade_no", validRequest.getTradeNo());
        signMap.put("trans_amount", validRequest.getTransAmount());
        signMap.put("trans_id", validRequest.getTransId());
        signMap.put("trans_status", validRequest.getTransStatus());
        
        String sign = SignUtil.maslSign(payConfig.getPaySignKey(), signMap);
        validRequest.setSign(sign);
        
        // 将对象转换为JSON字符串
        validNotifyJson = objectMapper.writeValueAsString(validRequest);
    }

    @Test
    public void mPayNotify_ShouldSucceed_WhenAllValid() throws IOException {
        // 执行请求
        String result = apiPayController.mPayNotify(validNotifyJson);
        
        // 验证响应
        assertEquals(PayConstant.SUCCESS, result);
        
        // 验证订单状态更新
        LsOrder updatedOrder = lsOrderMapper.selectById(1);
        assertNotNull(updatedOrder);
        assertEquals(1, updatedOrder.getPayStatus());
        assertNotNull(updatedOrder.getPayTime());
    }

    @Test
    public void mPayNotify_ShouldReturnSuccess_WhenOrderNotFound() throws IOException {
        // 准备测试数据 - 使用不存在的订单号
        MPayNotifyRequest request = new MPayNotifyRequest();
        request.setOutTransId("NONEXISTENT_ORDER");
        request.setTransStatus("SUCCESS");
        request.setNotifyTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        request.setNotifyType("trade_status_sync");
        request.setSignType("RSA2");
        
        // 使用Map生成签名，确保使用下划线格式
        Map<String, String> signMap = new HashMap<>();
        signMap.put("notify_time", request.getNotifyTime());
        signMap.put("notify_type", request.getNotifyType());
        signMap.put("out_trans_id", request.getOutTransId());
        signMap.put("trans_status", request.getTransStatus());
        
        String sign = SignUtil.maslSign(payConfig.getPaySignKey(), signMap);
        request.setSign(sign);
        
        String requestJson = objectMapper.writeValueAsString(request);

        // 执行请求
        String result = apiPayController.mPayNotify(requestJson);
        
        // 验证响应
        assertEquals(PayConstant.SUCCESS, result);
    }

    @Test
    public void mPayNotify_ShouldThrowException_WhenInvalidJson() {
        // 准备测试数据
        String invalidJson = "{invalid json}";
        
        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            apiPayController.mPayNotify(invalidJson);
        });
        
        assertEquals(Response.Code.PARAM_ILLEGAL.getCode(), exception.getRespCode().getCode());
        assertEquals("system error", exception.getMessage());
    }

    @Test
    public void mPayNotify_ShouldThrowException_WhenSignatureIsInvalid() throws IOException {
        // 准备测试数据 - 使用无效签名
        MPayNotifyRequest request = new MPayNotifyRequest();
        request.setOutTransId("ORDER123456");
        request.setTransStatus("SUCCESS");
        request.setNotifyTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        request.setNotifyType("trade_status_sync");
        request.setSignType("RSA2");
        request.setSign("invalid_sign");
        String requestJson = objectMapper.writeValueAsString(request);

        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            apiPayController.mPayNotify(requestJson);
        });
        
        assertEquals(Response.Code.PARAM_ILLEGAL.getCode(), exception.getRespCode().getCode());
        assertEquals("验签失败", exception.getMessage());
    }
}