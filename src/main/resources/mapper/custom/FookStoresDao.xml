<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresDao">
    <select id="selectStores" parameterType="map" resultType="com.mcoin.mall.bean.FookStores">
        select a.id,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_name,a.name), a.name) as name,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_address,a.address), a.address) as address,
               a.phone,
               a.longitude,
               a.dimension
        from fook_stores a
        left join fook_stores_translations c on a.id = c.stores_id
        where a.business_id = #{businessId,jdbcType=INTEGER}
        and a.enable = 1
    </select>

    <sql id="searchStoresSql">
        SELECT
        a.id,
        a.macau_pass_terminal_number,
        a.store_number,
        a.store_type_id,
        a.business_information_id,
        a.area_id,
        a.user_id,
        a.business_id,
        a.`name`,
        a.phone,
        a.address,
        a.longitude,
        a.dimension,
        a.google_longitude,
        a.google_demension,
        a.score,
        a.score_number,
        a.expense,
        a.expense_number,
        a.head_name,
        a.head_tel,
        a.head_email,
        a.reservation_time,
        a.charge,
        a.google_index,
        a.scott_index,
        a.provide_services,
        a.business_time,
        a.if_new,
        a.verification_pass,
        a.login_account,
        a.login_pass,
        a.`enable`,
        a.macau_pass_merchant_number,
        a.created_at,
        a.updated_at,
        a.collect_times,
        a.is_selected,
        a.shop_id,
        a.is_general,
        a.alone_settlement,
        a.settlement_account,
        a.settlement_name,
        a.settlement_bank,
        a.istemporary,
        a.background_img,
        a.if_update,
        a.img,
        a.img_zip
        FROM fook_stores a
        LEFT JOIN fook_business_information b on a.business_information_id = b.id
        LEFT JOIN fook_business fb on a.business_id = fb.id
        WHERE
        fb.system_type = 1
        AND a.enable = 1
        <!-- 店铺存在福利 -->
        AND EXISTS (
        SELECT 1 FROM fook_business_product fbp
        left join fook_business_store_product fbsp on fbp.id = fbsp.productid
        WHERE fbsp.storeid = a.id
        AND fbp.is_redeem = 0
        AND fbp.shelf_status = 1
        AND fbp.enable = 1
        AND fbp.buy_end_time <![CDATA[ > ]]> #{currentTime}
        AND fbp.buy_start_time  <![CDATA[ < ]]> #{currentTime}
        AND fbp.platform_type = 1
        AND fbp.snap_up = 0
        <if test="harmonySwitch == true">
            AND (fbp.`type` IS NULL OR fbp.`type` <![CDATA[ <> ]]> 12)
        </if>
        )
        <if test="storeIds != null and storeIds.size() > 0">
            AND a.id in
            <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="businessInformationIds != null and businessInformationIds.size() > 0">
            AND a.business_information_id in
            <foreach collection="businessInformationIds" item="businessInformationId" open="(" separator="," close=")">
                #{businessInformationId}
            </foreach>
        </if>
    </sql>

    <select id="searchStoresCnt" resultType="java.lang.Integer">
        SELECT count(*) FROM (
        <include refid="searchStoresSql" />
        ) a
    </select>

    <select id="searchStores" resultType="com.mcoin.mall.bean.FookStores">
        <include refid="searchStoresSql" />
        order by
        ACOS(SIN((IFNULL(#{lat,jdbcType=VARCHAR},0) * 3.1415) / 180 ) * SIN((a.dimension * 3.1415) / 180 )
        + COS((IFNULL(#{lat,jdbcType=VARCHAR},0) * 3.1415) / 180 ) * COS((a.dimension * 3.1415) / 180 )
        * COS((IFNULL(#{lot,jdbcType=VARCHAR},0) * 3.1415) / 180 - (a.longitude * 3.1415) / 180 ) ) * 6380
        limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>

    <sql id="searchStoresByKeywords_product">
        select fbsp2.storeid from fook_business_product fbp2
        left join fook_business_store_product fbsp2 on fbsp2.productid = fbp2.id
        left join fook_business_product_translations fbpt2 on fbpt2.business_product_id = fbp2.id
        where fbp2.enable = 1
        and fbp2.shelf_status = 1
        <if test="language != 'en'">
            and (
            fbp2.title like concat('%',#{searchTermSimple,jdbcType=VARCHAR},'%')
            or fbp2.title like concat('%',#{searchTermTraditional,jdbcType=VARCHAR},'%')
            or fbp2.title like concat('%',#{searchTermTwTraditional,jdbcType=VARCHAR},'%')
            )
        </if>
        <if test="language == 'en'">
            AND (fbp2.title like concat('%',#{searchTermSimple,jdbcType=VARCHAR},'%')
            or fbp2.title like concat('%',#{searchTermTraditional,jdbcType=VARCHAR},'%')
            or fbp2.title like concat('%',#{searchTermTwTraditional,jdbcType=VARCHAR},'%')
            or fbpt2.t_title like concat('%',#{searchTerm,jdbcType=VARCHAR},'%'))
        </if>
        and fbp2.platform_type = 1
        <if test="harmonySwitch == true">
            AND (fbp2.`type` IS NULL OR fbp2.`type` <![CDATA[ <> ]]> 12)
        </if>
        and fbsp2.storeid in (
        select fs2.id FROM fook_stores fs2
        left JOIN fook_business fb2 on fs2.business_id = fb2.id
        where fs2.enable = 1
        and fb2.system_type = 1)
    </sql>
    <select id="searchStoresByKeywords" resultType="java.lang.Integer">
        <!-- 店铺关键字表 -->
        select stores_id from fook_stores_keyword_class fskc
        where fskc.stores_keyword_id in (
        select id from fook_stores_keyword fsk
        where fsk.enable = 1
        and (fsk.name = #{searchTermSimple,jdbcType=VARCHAR}
        or fsk.name = #{searchTermTraditional,jdbcType=VARCHAR}
        or fsk.name = #{searchTermTwTraditional,jdbcType=VARCHAR}
        or fsk.english_name = #{searchTerm,jdbcType=VARCHAR}))
        <!-- 福利关键字 -->
        UNION
        <include refid="searchStoresByKeywords_product"/>
        and fbp2.buy_start_time <![CDATA[ <= ]]> #{currentTime}
        and fbp2.buy_end_time >= #{currentTime}
        <!-- 福利关键字-关联场次的福利 -->
        UNION
        <include refid="searchStoresByKeywords_product"/>
        AND fbp2.id in (
        select sp.product_id
        from fook_show_snapup_session_product sp
        join fook_show_snapup_session s on sp.session_id = s.id
        where date(s.start_time) >= CURDATE()
        and date(s.start_time) <![CDATA[<=]]> DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        and s.end_time > now()
        and s.valid = 1
        )
        <!-- 店铺主属性关键字 -->
        UNION
        select fs3.id FROM fook_stores fs3
        left JOIN fook_business fb3 on fs3.business_id = fb3.id
        left join fook_stores_translations fst3 on fst3.stores_id = fs3.id
        where fb3.system_type = 1
        and fs3.enable = 1
        <if test="language != 'en'">
            and ( fs3.name like concat('%',#{searchTermSimple,jdbcType=VARCHAR},'%')
                or fs3.name like concat('%',#{searchTermTraditional,jdbcType=VARCHAR},'%')
                or fs3.name like concat('%',#{searchTermTwTraditional,jdbcType=VARCHAR},'%'))
        </if>
        <if test="language == 'en'">
            and (fst3.t_name like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
                or fs3.name like concat('%',#{searchTermSimple,jdbcType=VARCHAR},'%')
                or fs3.name like concat('%',#{searchTermTraditional,jdbcType=VARCHAR},'%')
                or fs3.name like concat('%',#{searchTermTwTraditional,jdbcType=VARCHAR},'%'))
        </if>
    </select>

    <select id="getProductsByType" resultType="java.lang.Integer">
        <!-- 福利分类 -->
        select fbsp2.storeid from fook_business_product fbp2
        left join fook_business_store_product fbsp2 on fbsp2.productid = fbp2.id
        where fbp2.enable = 1
        and fbp2.`type` in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and fbp2.shelf_status = 1
        and fbp2.buy_start_time <![CDATA[ <= ]]> #{currentTime}
        and fbp2.buy_end_time >= #{currentTime}
        and fbp2.platform_type = 1
    </select>

    <select id="getStoresByCategory" resultType="java.lang.Integer">
        <!-- 门店分类 -->
        select stores_id from fook_stores_type_class
        where stores_type_id in
        <foreach collection="categories" item="category" open="(" separator="," close=")">
            #{category}
        </foreach>
    </select>

    <select id="getStore" resultType="com.mcoin.mall.bean.FookStores">
        SELECT
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List" />
        from fook_stores
        where id = #{id,jdbcType=INTEGER}
        and enable = 1
        and exists (
        select * from fook_business fb
        where fook_stores.business_id = fb.id
        and system_type = 1
        )
    </select>

    <select id="getBranchs" resultType="java.lang.Integer">
        select count(*) from fook_stores fs
        where business_id = #{id,jdbcType=INTEGER}
        and enable = 1
    </select>

    <select id="getStores" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List" />
        from fook_stores
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectStoreIdsForSearchValue" resultType="java.lang.Integer">
        select
        id
        from fook_stores a left join fook_stores_translations b on a.id = b. stores_id
        where a.enable = 1
        <choose>
            <when test="lang == 'en'">
                AND b.t_name like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
            </when>
            <otherwise>
                AND a.name like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
            </otherwise>
        </choose>
    </select>
    <select id="getApplyStores" resultType="com.mcoin.mall.bean.FookStores">
        select a.id,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_name,a.name), a.name) as name,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_address,a.address),a.address ) as address,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_detail,a.detail),a.detail ) as detail,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_reservation_time,a.reservation_time),a.reservation_time ) as reservation_time,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_expense,a.expense),a.expense ) as expense,
        if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_business_time,a.business_time),a.business_time ) as business_time,
        a.phone,
        a.longitude,
        a.dimension,
        a.enable,
        a.business_id,
        a.business_information_id
        from fook_stores a
        left join fook_business_store_product b on a.id = b.storeid
        left join fook_stores_translations c on a.id = c.stores_id
        where b.productId = #{productId,jdbcType=INTEGER}
        <if test="isApplyStores == 1">
            and a.enable = 1
        </if>

    </select>
    <select id="getStoresByProductId" resultType="com.mcoin.mall.bean.FookStores">
        select
        <include refid="Base_Column_List"/>
        from fook_stores fs
        where fs.id in (
        select storeid from fook_business_store_product fbsp
        where fbsp.productid = #{productId,jdbcType=INTEGER}
        )
    </select>

    <select id="getStoresByIdsAndEnable" resultType="com.mcoin.mall.bean.FookStores">
        select
        <include refid="Base_Column_List"/>
        from fook_stores
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and enable = 1
    </select>

    <select id="selectByPrimaryKeyWithTranslations" resultType="com.mcoin.mall.bean.FookStores">
        select a.id,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_name,a.name), a.name) as name,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_address,a.address),a.address ) as address,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_detail,a.detail),a.detail ) as detail,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_reservation_time,a.reservation_time),a.reservation_time ) as reservation_time,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_expense,a.expense),a.expense ) as expense,
               if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(c.t_business_time,a.business_time),a.business_time ) as business_time,
               a.phone,
               a.longitude,
               a.dimension,
               a.enable
        from fook_stores a
                 left join fook_stores_translations c on a.id = c.stores_id
        where a.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getProductsByBusinessCategoriesIds" resultType="java.lang.Integer">
        <!-- 福利分类 -->
        select fbsp2.storeid from fook_business_product fbp2
        left join fook_business_store_product fbsp2 on fbsp2.productid = fbp2.id
        where fbp2.enable = 1
        and SUBSTRING_INDEX(fbp2.`category_path`, '-', 1)  in
        <foreach collection="businessCategoriesIds" item="businessCategoriesId" open="(" separator="," close=")">
            #{businessCategoriesId}
        </foreach>
        and fbp2.shelf_status = 1
        and fbp2.buy_start_time <![CDATA[ <= ]]> #{currentTime}
        and fbp2.buy_end_time >= #{currentTime}
        and fbp2.platform_type = 1
    </select>

    <select id="selectStoreDistanceById" resultType="com.mcoin.mall.bo.StoreDistanceBo">
        select s.id storeId, s.name storeName,
               s.longitude, s.dimension,
               s.img_zip imgZip, s.img
        from fook_stores s
        where s.`enable` = 1
          and s.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="filterStoresWithOnlyType12Products" resultType="java.lang.Integer">
        <!-- 过滤掉只有type=12福利的门店 -->
        select distinct fs.id
        from fook_stores fs
        where fs.id in
        <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
        and exists (
            -- 确保门店至少有一个非type=12的福利
            select 1
            from fook_business_product fbp
            join fook_business_store_product fbsp on fbp.id = fbsp.productid
            where fbsp.storeid = fs.id
            and fbp.enable = 1
            and fbp.shelf_status = 1
            and fbp.buy_start_time <![CDATA[ <= ]]> #{currentTime}
            and fbp.buy_end_time >= #{currentTime}
            and fbp.platform_type = 1
            and (fbp.`type` IS NULL OR fbp.`type` <![CDATA[ <> ]]> 12)
        )
    </select>
</mapper>