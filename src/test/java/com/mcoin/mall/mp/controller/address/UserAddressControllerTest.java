package com.mcoin.mall.mp.controller.address;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.address.*;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Transactional
class UserAddressControllerTest extends BaseUnitTest {

    @Autowired
    private UserAddressController userAddressController;

    private static final String TEST_MOBILE = "***********";
    private static final String INVALID_MOBILE = "1234567";
    private static Integer savedAddressId;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
    }

    @Test
    @Order(1)
    void addAddress_ShouldAddAddressSuccessfully() {
        // Arrange
        UserAddressAddRequest request = new UserAddressAddRequest();
        request.setMobile(TEST_MOBILE);
        request.setProvince("广东省");
        request.setCity("深圳市");
        request.setDistrict("南山区");
        request.setAddress("科技园");
        request.setContact("测试用户");
        request.setProvinceId(440000);
        request.setCityId(440300);
        request.setDistrictId(440305);
        request.setIsDefault(1);

        // Act
        Response<String> response = userAddressController.addAddress(request);

        // Assert
        assertNotNull(response);
        assertEquals("success", response.getData());
    }

    @Test
    @Order(2)
    void addAddress_ShouldThrowException_WhenInvalidMobile() {
        // Arrange
        UserAddressAddRequest request = new UserAddressAddRequest();
        request.setMobile(INVALID_MOBILE);
        request.setProvince("广东省");
        request.setCity("深圳市");
        request.setDistrict("南山区");
        request.setAddress("科技园");
        request.setContact("测试用户");
        request.setProvinceId(440000);
        request.setCityId(440300);
        request.setDistrictId(440305);
        request.setIsDefault(1);

        // Act & Assert
        assertThrows(BusinessException.class, () -> userAddressController.addAddress(request));
    }

    @Test
    @Order(3)
    void addAddress_ShouldThrowException_WhenContactNameTooLong() {
        // Arrange
        UserAddressAddRequest request = new UserAddressAddRequest();
        request.setMobile(TEST_MOBILE);
        request.setProvince("广东省");
        request.setCity("深圳市");
        request.setDistrict("南山区");
        request.setAddress("科技园");
        request.setContact("这是一个超过二十个字符的联系人名称测试数据");
        request.setProvinceId(440000);
        request.setCityId(440300);
        request.setDistrictId(440305);
        request.setIsDefault(1);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class,
                () -> userAddressController.addAddress(request));
        assertEquals("聯繫人名稱過長，請調整後重試", exception.getMessage());
    }

    @Test
    @Order(4)
    void queryAddress_ShouldReturnNull_WhenAddressNotFound() {
        // Arrange & Act
        Response<UserAddressResponse> response = userAddressController.queryAddress(999999);

        // Assert
        assertNotNull(response);
        assertNull(response.getData());
    }

    @Test
    @Order(5)
    void queryAddressLists_ShouldReturnAddressList() {
        // Act
        Response<List<UserAddressResponse>> response = userAddressController.queryAddressLists();

        // Assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertFalse(response.getData().isEmpty());

        // Save the first address ID for later tests
        savedAddressId = response.getData().get(0).getId();
    }

    @Test
    @Order(6)
    void queryAddress_ShouldReturnAddressDetail() {
        // Arrange & Act
        Response<UserAddressResponse> response = userAddressController.queryAddress(savedAddressId);

        // Assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(savedAddressId, response.getData().getId());
        assertEquals(TEST_MOBILE, response.getData().getMobile());
    }

    @Test
    @Order(7)
    void editAddress_ShouldUpdateAddressSuccessfully() {
        // Arrange
        UserAddressEditRequest request = new UserAddressEditRequest();
        request.setId(1);
        request.setMobile(TEST_MOBILE);
        request.setIsDefault(0);
        request.setProvince("广东省");
        request.setCity("深圳市");
        request.setDistrict("福田区");
        request.setAddress("车公庙");
        request.setContact("测试用户2");
        request.setProvinceId(440000);
        request.setCityId(440300);
        request.setDistrictId(440304);

        // Act
        Response<String> response = userAddressController.editAddress(request);

        // Assert
        assertNotNull(response);
        assertEquals("success", response.getData());

        // Verify the update
        Response<UserAddressResponse> updatedAddress = userAddressController.queryAddress(savedAddressId);
        assertEquals("测试用户2", updatedAddress.getData().getContact());
        assertEquals("车公庙", updatedAddress.getData().getAddress());
    }

    @Test
    @Order(8)
    void editAddress_ShouldThrowException_WhenInvalidMobile() {
        // Arrange
        UserAddressEditRequest request = new UserAddressEditRequest();
        request.setId(savedAddressId);
        request.setMobile(INVALID_MOBILE);
        request.setProvince("广东省");
        request.setCity("深圳市");
        request.setDistrict("福田区");
        request.setAddress("车公庙");
        request.setContact("测试用户2");
        request.setProvinceId(440000);
        request.setCityId(440300);
        request.setDistrictId(440304);

        // Act & Assert
        assertThrows(BusinessException.class, () -> userAddressController.editAddress(request));
    }

    @Test
    @Order(9)
    void editAddress_ShouldThrowException_WhenContactNameTooLong() {
        // Arrange
        UserAddressEditRequest request = new UserAddressEditRequest();
        request.setId(savedAddressId);
        request.setMobile(TEST_MOBILE);
        request.setProvince("广东省");
        request.setCity("深圳市");
        request.setDistrict("福田区");
        request.setAddress("车公庙");
        request.setContact("这是一个超过二十个字符的联系人名称测试数据");
        request.setProvinceId(440000);
        request.setCityId(440300);
        request.setDistrictId(440304);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class,
                () -> userAddressController.editAddress(request));
        assertEquals("聯繫人名稱過長，請調整後重試", exception.getMessage());
    }

    @Test
    @Order(10)
    void setDefaultAddress_ShouldSetDefaultAddressSuccessfully() {
        // Arrange
        UserAddressDefaultRequest request = new UserAddressDefaultRequest();
        request.setId(savedAddressId);

        // Act
        Response<String> response = userAddressController.setDefaultAddress(request);

        // Assert
        assertNotNull(response);
        assertEquals("success", response.getData());

        // Verify the address is set as default
        Response<UserAddressResponse> defaultAddress = userAddressController.queryAddress(savedAddressId);
        assertEquals(1, defaultAddress.getData().getIsDefault());
    }

    @Test
    @Order(11)
    void deleteAddress_ShouldDeleteAddressSuccessfully() {
        // Arrange
        UserAddressDeleteRequest request = new UserAddressDeleteRequest();
        request.setId(savedAddressId);

        // Act
        Response<String> response = userAddressController.deleteAddress(request);

        // Assert
        assertNotNull(response);
        assertEquals("success", response.getData());

        // Verify the address is deleted
        Response<List<UserAddressResponse>> addresses = userAddressController.queryAddressLists();
        assertTrue(addresses.getData().stream().noneMatch(addr -> addr.getId().equals(savedAddressId)));
    }
}