stages:
  - mbf-sonar-scanner
  


mbf-sonar-scanner:
  stage: mbf-sonar-scanner
  image: sonarsource/sonar-scanner-cli
  variables:
    SONAR_USER_HOME: "$CI_COMMIT_REF_SLUG" 
  cache:
    key: mp-mcoin-mall-target-cache
    paths: 
      - target/
  script:
    - sonar-scanner -Dsonar.sources=. -Dsonar.java.binaries=src/main/java/ -Dsonar.projectKey=sit_c_mcoinGroup_mp-mcoin-mall_AZEgnFgkMEwUumy3Q00P -Dsonar.qualitygate.wait=true  -Dsonar.branch.name=${CI_COMMIT_REF_NAME} -Dsonar.language=java -Dsonar.exclusions=src/test/** -Dsonar.newCode.referenceBranch=${CI_COMMIT_REF_NAME}
  allow_failure: false
  only:
    - merge_request
    - /release_conflict/
    - trigger
  tags:
    - c-runner20.199


