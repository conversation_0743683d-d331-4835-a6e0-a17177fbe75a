<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMpaycoinCodeListDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportMpaycoinCodeList">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="storeid" jdbcType="INTEGER" property="storeid" />
    <result column="ordercodeid" jdbcType="INTEGER" property="ordercodeid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="writeoff_time" jdbcType="TIMESTAMP" property="writeoffTime" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mpaycoinid" jdbcType="INTEGER" property="mpaycoinid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, businessid, storeid, ordercodeid, userid, orderid, code, `type`, writeoff_time, 
    `name`, mpaycoinid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_mpaycoin_codelist
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_mpaycoin_codelist
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinCodeList">
    insert into fook_report_mpaycoin_codelist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="storeid != null">
        storeid,
      </if>
      <if test="ordercodeid != null">
        ordercodeid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="writeoffTime != null">
        writeoff_time,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="mpaycoinid != null">
        mpaycoinid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="writeoffTime != null">
        #{writeoffTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mpaycoinid != null">
        #{mpaycoinid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinCodeList">
    update fook_report_mpaycoin_codelist
    <set>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        storeid = #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="writeoffTime != null">
        writeoff_time = #{writeoffTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mpaycoinid != null">
        mpaycoinid = #{mpaycoinid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportMpaycoinCodeList">
    update fook_report_mpaycoin_codelist
    set businessid = #{businessid,jdbcType=INTEGER},
      storeid = #{storeid,jdbcType=INTEGER},
      ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      code = #{code,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      writeoff_time = #{writeoffTime,jdbcType=TIMESTAMP},
      `name` = #{name,jdbcType=VARCHAR},
      mpaycoinid = #{mpaycoinid,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>