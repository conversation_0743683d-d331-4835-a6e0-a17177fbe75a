package com.mcoin.mall.constant;

/**
 * 是否整單枚舉
 */
public enum IsWholeEnum {

    /**
     * 不是整單
     */
    NOT_WHOLE(0, "不是整單"),
    
    /**
     * 是整單
     */
    WHOLE(1, "是整單");

    private final int code;
    private final String description;

    IsWholeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根據代碼獲取描述
     *
     * @param code 代碼
     * @return 描述
     */
    public static String getDescriptionByCode(int code) {
        for (IsWholeEnum status : IsWholeEnum.values()) {
            if (status.getCode() == code) {
                return status.getDescription();
            }
        }
        return "";
    }
}
