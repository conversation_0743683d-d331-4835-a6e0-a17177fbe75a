package com.mcoin.mall.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

public class AESUtilTest {

    @Test
    public void testEncryptToBase64() {
        String key = "SitZuhHSRgAYblyG";
        String str = "1";
        String expectedBase64 =
                "Tyl/SzWVRHbwKa/eZdMDIw==";

        String result = AESUtil.encryptToBase64(key, str);
        assertEquals(
                expectedBase64,
                result,
                "The encrypted string should match the expected Base64 encoded string.");
    }


    @Test
    public void testEncryptToBase64WithEmptyString() {
        String key = "SitZuhHSRgAYblyG";
        String str = "";
        String expectedBase64 =
                "A8M83K4L+21d2sX7SWLCaA==";

        String result = AESUtil.encryptToBase64(key, str);
        assertEquals(
                expectedBase64,
                result,
                "The encrypted string should match the expected Base64 encoded string.");
    }

    @Test
    public void testEncryptToBase64WithNullKey() {
        String key = null;
        String str = "Hello, World!";

        assertThrows(
                RuntimeException.class,
                () -> {
                    AESUtil.encryptToBase64(key, str);
                },
                "Encryption should throw a RuntimeException when key is null.");
    }

    @Test
    public void testEncryptToBase64WithNullString() {
        String key = "thisisakey123";
        String str = null;

        assertThrows(
                RuntimeException.class,
                () -> {
                    AESUtil.encryptToBase64(key, str);
                },
                "Encryption should throw a RuntimeException when string is null.");
    }
}
