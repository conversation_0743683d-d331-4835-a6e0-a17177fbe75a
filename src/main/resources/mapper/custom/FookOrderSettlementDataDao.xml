<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookOrderSettlementDataDao">

  <select id="queryOrderSettlementDataList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from fook_order_settlement_data
    WHERE create_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP} limit #{offset, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
  </select>

  <select id="sumOrderAmount" resultType="com.mcoin.mall.bo.OrderAmountBo">
    select SUM(order_amount) as totalAmount,
           SUM(if(mpayintegral > 0,0,score)) as totalScore,
           sum(if(mpayintegral is null,0,mpayintegral)) as totalMpayIntegral,
           SUM(total_amount) as payAmount,
           SUM(subsidy_amount) as subsidyAmount
    from fook_order_settlement_data
    where payment_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}

  </select>
</mapper>