INSERT INTO fook_business_store_product (id,productid,storeid,`type`,businessid) VALUES
	 (4887,2877,8,1,4);
INSERT INTO `fook_business_store_product` (`id`, `productid`, `storeid`, `type`, `businessid`) VALUES (1757, 1301, 381, 1, 246);
INSERT INTO `fook_business_store_product` (`id`, `productid`, `storeid`, `type`, `businessid`) VALUES (1758, 1300, 381, 1, 246);
INSERT INTO `fook_business_store_product` (`id`, `productid`, `storeid`, `type`, `businessid`) VALUES (1759, 1302, 381, 1, 246);

INSERT INTO fook_business_store_product (id,productid,storeid,`type`,businessid) VALUES
	 (3276,2207,753,1,519);
INSERT INTO `fook_business_store_product` (`id`, `productid`, `storeid`, `type`, `businessid`) VALUES (3407, 2236, 758, 1, 557);

-- Test data for search functionality
-- Associate temporary products with stores
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES
-- Regular products
(10001, 1, 40, 1, 1),  -- 測試商品1 -> 國記鮮榨果汁 (新口岸店)
(10002, 2, 6, 1, 2),   -- 測試商品2 -> 西洋菜

-- Products with 'test' in the name
(10003, 3, 41, 1, 1),  -- 咖啡測試特惠 -> 國記鮮榨果汁 (皇朝店)
(10004, 4, 6, 1, 2),   -- 餐廳測試套餐 -> 西洋菜
(10005, 5, 8, 1, 4),   -- 甜品測試優惠 -> 聰少甜品

-- Products for HARMONY client testing
(10006, 6, 381, 1, 246), -- 和諧客戶端測試 -> Gold Monkey 金猴子 ( 信譽店 )
(10007, 7, 753, 1, 519), -- 非和諧客戶端測試 -> 8餐厅

-- Products with different types for category filtering
(10008, 8, 758, 1, 557), -- 類型1測試商品 -> Derek coffee (澳门半岛店)
(10009, 9, 758, 1, 558), -- 類型2測試商品 -> Derek coffee (澳门半岛店) [different business]
(10010, 10, 1517, 1, 810); -- 類型3測試商品 -> 劉俊測試門店

INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5240, 3050, 6, 1, 2);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5227, 3043, 40, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5228, 3043, 41, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5229, 3043, 43, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5230, 3043, 44, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5231, 3043, 45, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5232, 3043, 46, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5233, 3043, 1564, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5225, 3041, 1564, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5206, 3000, 40, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5207, 3000, 41, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5208, 3000, 43, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5209, 3000, 44, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5210, 3000, 45, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5153, 2983, 40, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5154, 2983, 41, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5155, 2983, 43, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5156, 2983, 44, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5157, 2983, 45, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5158, 2983, 46, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5159, 2983, 766, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5160, 2983, 770, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5161, 2983, 787, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5162, 2983, 1150, 1, 1);
INSERT INTO fook_business_store_product (id, productid, storeid, type, businessid) VALUES (4826, 2838, 1517, 1, 811);

-- Hot deals test product associations
INSERT INTO fook_business_store_product (id, productid, storeid, `type`, businessid)
VALUES
(9001, 9001, 8, 1, 4),
(9002, 9002, 8, 1, 4),
(9003, 9003, 381, 1, 246),
(9004, 9004, 381, 1, 246),
(9005, 9005, 753, 1, 519);

-- Regular product (not hot deal) association
INSERT INTO fook_business_store_product (id, productid, storeid, `type`, businessid)
VALUES
(9006, 9006, 8, 1, 4);



INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8078, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8077, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8087, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8079, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8080, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8081, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8082, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8083, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8084, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8085, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (8086, 1564, 1, 1);


INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (2445, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (2790, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (3029, 1564, 1, 1);
INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (3031, 1564, 1, 1);


INSERT INTO fook_business_store_product (productid, storeid, type, businessid) VALUES (2411, 84, 1, 55);



INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5045, 2966, 770, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5047, 2966, 1150, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5075, 2973, 770, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5077, 2973, 1150, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5086, 2972, 770, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5088, 2972, 1150, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5097, 2975, 770, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5099, 2975, 1150, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5112, 2982, 770, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5114, 2982, 1150, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5145, 2986, 770, 1, 1);
INSERT INTO fooku.fook_business_store_product (id, productid, storeid, type, businessid) VALUES (5147, 2986, 1150, 1, 1);

