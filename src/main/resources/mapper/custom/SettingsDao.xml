<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.SettingsDao">

  <select id="getValue" resultType="string">
    select `value` from settings where `key` = #{key,jdbcType=VARCHAR}
  </select>

  <select id="getValueDirect" resultType="string">
    select `value` from settings where `key` = #{key,jdbcType=VARCHAR}
  </select>

  <update id="updateByKey" >
    update settings set `value` = #{value,jdbcType=VARCHAR} where `key` = #{key,jdbcType=VARCHAR}
  </update>

</mapper>