package com.mcoin.mall.mp.controller.job;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.job.OrderCloseRequest;
import com.mcoin.mall.mp.model.job.OrderQueryStatusRequest;
import com.mcoin.mall.mp.model.job.OrderSyncRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Transactional
class OrderJobControllerTest extends BaseUnitTest {

    @Autowired
    private OrderJobController orderJobController;

    @Test
    void close_ShouldTriggerOrderClose() {
        // Arrange
        OrderCloseRequest request = new OrderCloseRequest();
        request.setCurrentTime(new Date());

        // Act
        Response<String> response = orderJobController.close(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertEquals("success", response.getData());
    }

    @Test
    void queryStatus_ShouldReturnSuccess() {
        // Arrange
        OrderQueryStatusRequest request = new OrderQueryStatusRequest();
        request.setCurrentTime(new Date());

        // Act
        Response<String> response = orderJobController.queryStatus(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertEquals("success", response.getData());
    }

    @Test
    void sync_ShouldTriggerOrderSync() {
        // Arrange
        OrderSyncRequest request = new OrderSyncRequest();
        request.setCurrentTime(new Date());
        request.setStartTime(new Date());

        // Act
        Response<String> response = orderJobController.sync(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertEquals("success", response.getData());
    }
} 