<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductstockDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessProductstock">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="productid" jdbcType="INTEGER" property="productid" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="operationid" jdbcType="INTEGER" property="operationid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    id, productid, `number`, `type`, operation_type, operationid, userid, orderid, create_time, 
    `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_productstock
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_productstock
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessProductstock">
    insert into fook_business_productstock
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productid != null">
        productid,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="operationid != null">
        operationid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productid != null">
        #{productid,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="operationid != null">
        #{operationid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessProductstock">
    update fook_business_productstock
    <set>
      <if test="productid != null">
        productid = #{productid,jdbcType=INTEGER},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="operationid != null">
        operationid = #{operationid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessProductstock">
    update fook_business_productstock
    set productid = #{productid,jdbcType=INTEGER},
      `number` = #{number,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      operation_type = #{operationType,jdbcType=INTEGER},
      operationid = #{operationid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>