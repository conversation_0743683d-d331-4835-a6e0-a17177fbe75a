package com.mcoin.mall.service.common.impl;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.service.common.AliyunOSSService;
import com.mcoin.mall.util.JodaTimeUtil;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AliyunOSSServiceImpl implements AliyunOSSService {

    @Value("${report.savePath}")
    private String savePath;
    @Value("${report.oss.endpoint}")
    private String ossEndpoint;
    @Value("${report.oss.bucketName}")
    private String ossBucketName;
    @Value("${report.oss.filePath}")
    private String ossFilePath;
    @Value("${report.oss.accessKeyId}")
    private String ossAccessKeyId;
    @Value("${report.oss.accessKeySecret}")
    private String ossAccessKeySecret;

    @Value("${mini.program.oss.filePath}")
    private String miniProgramOssFilePath;

    @Override
    public File downloadFile(String fileName) {
        String objectName = ossFilePath + "/" + fileName;
        String fileDate = JodaTimeUtil.format(new Date(), JodaTimeUtil.YYYYMMDD);
        String savePath = this.savePath + "download/" + fileDate;
        return downloadFile(objectName, savePath, fileName);
    }

    @Override
    public File downloadFile(String objectName, String savePath, String fileName) {

        OSS ossClient = null;
        try {
            // 1. 初始化OOS
            DefaultCredentialProvider credentialsProvider =
                    CredentialsProviderFactory.newDefaultCredentialProvider(ossAccessKeyId, ossAccessKeySecret);
            ossClient = new OSSClientBuilder().build(ossEndpoint, credentialsProvider);
            // 2. 查询OSS中文件是否存在
            if (!ossClient.doesObjectExist(ossBucketName, objectName)) {
                return null;
            }
            // 3. 下载OSS文件
            OSSObject ossObject = ossClient.getObject(ossBucketName, objectName);
            // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
            InputStream content = ossObject.getObjectContent();
            if (content != null) {
                try {
                    // 生成文件
                    File pdir = new File(savePath);
                    if (!pdir.exists()) {
                        pdir.mkdirs();
                    }
                    File file = new File(pdir, fileName);
                    if (file.exists()) {
                        boolean rt = file.delete();
                        log.info("delete downloaded file: {} result: {}", file.getAbsolutePath(), rt);
                    }
                    FileUtil.writeFromStream(content, file);
                    log.info("download file {} to local", file.getAbsolutePath());
                    return file;
                } finally {
                    // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
                    try {
                        content.close();
                    } catch (Exception e){
                        // 忽略
                    }
                }
            }
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                            + "but was rejected with an error response for some reason. Error Message: {} " +
                            "Error Code: {} Request ID: {} Host ID: {}", oe.getErrorMessage(), oe.getErrorCode(),
                    oe.getRequestId(), oe.getHostId());
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "未知异常");
        } catch (Throwable ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network. Error Message: {}", ce.getMessage());
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "未知异常");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    @Override
    public File downloadMiniFile(String fileName) {
        log.info("即将要下载的文件名称：{}",fileName);

        OSS ossClient = null;
        try {
            // 1. 初始化OOS
            DefaultCredentialProvider credentialsProvider =
                    CredentialsProviderFactory.newDefaultCredentialProvider(ossAccessKeyId, ossAccessKeySecret);
            ossClient = new OSSClientBuilder().build(ossEndpoint, credentialsProvider);
            // 2. 查询OSS中文件是否存在
            String objectName = miniProgramOssFilePath + "/" + fileName;
            log.info("即将要下载的全路径文件名称：{}",objectName);
            if (!ossClient.doesObjectExist(ossBucketName, objectName)) {
                return null;
            }
            // 3. 下载OSS文件
            OSSObject ossObject = ossClient.getObject(ossBucketName, objectName);
            // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
            InputStream content = ossObject.getObjectContent();
            if (content != null) {
                try {
                    // 生成文件
                    String fileDate = JodaTimeUtil.format(new Date(), JodaTimeUtil.YYYYMMDD);
                    File pdir = new File(savePath, "download/" + fileDate);
                    if (!pdir.exists()) {
                        pdir.mkdirs();
                    }
                    File file = new File(pdir, fileName);
                    if (file.exists()) {
                        boolean rt = file.delete();
                        log.info("delete downloaded file: {} result: {}", file.getAbsolutePath(), rt);
                    }
                    try (BufferedWriter writer = new BufferedWriter(new FileWriter(file));
                         BufferedReader reader = new BufferedReader(new InputStreamReader(content))){
                        boolean isFirstLine = true;
                        while (true) {
                            String line = reader.readLine();
                            if (line == null) break;
                            if (isFirstLine) {
                                isFirstLine = false;
                            } else {
                                writer.newLine();
                            }
                            writer.write(line);
                        }
                        writer.flush();
                    } catch (IOException e) {
                        throw new BusinessException(Response.Code.UNKNOWN_ERROR, "未知异常");
                    }

                    log.info("download file {} to local", file.getAbsolutePath());
                    return file;
                } finally {
                    // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
                    content.close();
                }
            }
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                            + "but was rejected with an error response for some reason. Error Message: {} " +
                            "Error Code: {} Request ID: {} Host ID: {}", oe.getErrorMessage(), oe.getErrorCode(),
                    oe.getRequestId(), oe.getHostId());
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "未知异常");
        } catch (Throwable ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network. Error Message: {}", ce.getMessage());
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "未知异常");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    @Override
    public void uploadFile(File file, String objectName) {
        OSS ossClient = null;
        try {
            // 1. 初始化OOS
//            EnvironmentVariableCredentialsProvider credentialsProvider =
//                    CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider(ossAccessKeyId, ossAccessKeySecret);
            DefaultCredentialProvider credentialsProvider =
                    CredentialsProviderFactory.newDefaultCredentialProvider(ossAccessKeyId, ossAccessKeySecret);
            ossClient = new OSSClientBuilder().build(ossEndpoint, credentialsProvider);

            // 2. 上传文件
            PutObjectResult putObjectResult = ossClient.putObject(ossBucketName, objectName, file);
            log.info("upload file {} to oss result eTag: {} versionId: {} ", file.getAbsolutePath(),
                    putObjectResult.getETag(), putObjectResult.getVersionId());
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                            + "but was rejected with an error response for some reason. Error Message: {} " +
                            "Error Code: {} Request ID: {} Host ID: {}", oe.getErrorMessage(), oe.getErrorCode(),
                    oe.getRequestId(), oe.getHostId());
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "未知异常");
        } catch (Throwable ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network. Error Message: {}", ce.getMessage());
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "未知异常");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

}
