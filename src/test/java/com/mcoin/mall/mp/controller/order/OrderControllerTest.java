package com.mcoin.mall.mp.controller.order;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.order.*;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Transactional
public class OrderControllerTest extends BaseUnitTest {

    @Autowired
    private OrderController orderController;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
    }

    @Test
    @Order(1)
    void getDeliveryType_ShouldReturnDeliveryTypes() {
        // Arrange
        GetDeliveryTypeRequest request = new GetDeliveryTypeRequest();

        // Act
        Response<GetDeliveryTypeResponse> response = orderController.getDeliveryType(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    @Order(2)
    void placeOrderSettlement_ShouldReturnSettlement_WhenActionIsSettle() {
        // 准备请求数据
        PlaceOrderRequest request = new PlaceOrderRequest();
        request.setAction("settle");
        request.setSource("buy_now");
        request.setDeliveryType(1);
        request.setOrderType(0);
        request.setMcoin(false);

        // Add goods items
        List<PlaceOrderRequest.GoodsItem> goods = new ArrayList<>();
        PlaceOrderRequest.GoodsItem item = new PlaceOrderRequest.GoodsItem();
        item.setItemId(118);
        item.setGoodsNum(1);
        item.setIntegral(300);
        goods.add(item);
        request.setGoods(goods);

        // Set real price
        PlaceOrderRequest.RealPrice realPrice = new PlaceOrderRequest.RealPrice();
        realPrice.setPrice(new BigDecimal("100.00"));
        realPrice.setMcoin(0);
        request.setRealPrice(realPrice);

        // 执行请求
        Response<?> response = orderController.placeOrderSettlement(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    @Order(3)
    void placeOrderSettlement_ShouldCreateOrder_WhenActionIsBuy() {
        // 准备请求数据
        PlaceOrderRequest request = new PlaceOrderRequest();
        request.setAddressId(1);
        request.setAction("buy");
        request.setSource("buy_now");
        request.setDeliveryType(1);
        request.setOrderType(0);
        request.setMcoin(false);

        // Add goods items
        List<PlaceOrderRequest.GoodsItem> goods = new ArrayList<>();
        PlaceOrderRequest.GoodsItem item = new PlaceOrderRequest.GoodsItem();
        item.setItemId(118);
        item.setGoodsNum(1);
        item.setIntegral(300);
        goods.add(item);
        request.setGoods(goods);

        // Set real price
        PlaceOrderRequest.RealPrice realPrice = new PlaceOrderRequest.RealPrice();
        realPrice.setPrice(new BigDecimal("100.00"));
        realPrice.setMcoin(0);
        request.setRealPrice(realPrice);

        // 执行请求
        Response<?> response = orderController.placeOrderSettlement(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    @Order(4)
    void placeOrderSettlement_ShouldThrowException_WhenInvalidAction() {
        // Arrange
        PlaceOrderRequest request = new PlaceOrderRequest();
        request.setAction("invalid");

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class,
                () -> orderController.placeOrderSettlement(request));
        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals("请求参数错误", exception.getMessage());
    }

    @Test
    @Order(6)
    void lists_ShouldReturnOrderList() {
        // 准备请求数据
        OrderListRequest request = new OrderListRequest();
        request.setPage_no(1);
        request.setPage_size(10);
        request.setType("all");

        // 执行请求
        Response<OrderListResponse> response = orderController.lists(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    @Order(7)
    void detail_ShouldReturnOrderDetail() {
        // 准备请求数据
        OrderDetailRequest request = new OrderDetailRequest();
        request.setId(1);

        // 执行请求
        Response<OrderDetailResponse> response = orderController.detail(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    @Order(8)
    void cancel_ShouldCancelOrder() {
        assertThrows(BusinessException.class, () -> {
            // 准备请求数据
            OrderCancelRequest request = new OrderCancelRequest();
            request.setId(1);

            // 执行请求
            Response<OrderCancelResponse> response = orderController.cancel(request);

            // 验证响应
            assertNotNull(response);
            assertEquals(Response.Code.OK.getCode(), response.getCode());
            assertNotNull(response.getData());
        });

    }

    @Test
    @Order(9)
    void orderTraces_ShouldReturnTraces() {
        // 准备请求数据
        OrderTracesRequest request = new OrderTracesRequest();
        request.setId(1);

        // 执行请求
        Response<OrderTracesResponse> response = orderController.orderTraces(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
        
        // 验证订单信息
        OrderTracesResponse data = response.getData();
        assertNotNull(data.getOrder());
    }

    @Test
    @Order(10)
    void orderTraces_ShouldHandleNonExistentOrder() {
        // 准备请求数据 - 使用不存在的订单ID
        OrderTracesRequest request = new OrderTracesRequest();
        request.setId(999999);

        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> orderController.orderTraces(request));
        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals("訂單不存在", exception.getMessage());
    }

    @Test
    @Order(11)
    void orderTraces_ShouldHandleDifferentDeliveryTypes() {
        // 准备请求数据
        OrderTracesRequest request = new OrderTracesRequest();
        request.setId(1);

        // 执行请求
        Response<OrderTracesResponse> response = orderController.orderTraces(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    @Order(12)
    void orderTraces_ShouldHandleDifferentOrderStatus() {
        // 准备请求数据
        OrderTracesRequest request = new OrderTracesRequest();
        request.setId(1);

        // 执行请求
        Response<OrderTracesResponse> response = orderController.orderTraces(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
    }


    @Test
    @Order(12)
    void orderTraces_ShouldReturnTraces_JD() {
        UserInfo userInfo = new UserInfo();
        userInfo.setSid(65);
        userInfo.setUserId(90);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        // 准备请求数据
        OrderTracesRequest request = new OrderTracesRequest();
        request.setId(1114);

        // 执行请求
        Response<OrderTracesResponse> response = orderController.orderTraces(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());

        // 验证订单信息
        OrderTracesResponse data = response.getData();
        assertNotNull(data.getOrder());
        assertFalse(data.getDelivery().getTraces().isEmpty());
    }

    @Test
    @Order(13)
    void orderTraces_ShouldReturnTraces_XIANGSHONGWULIU() {
        UserInfo userInfo = new UserInfo();
        userInfo.setSid(65);
        userInfo.setUserId(90);
        when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        // 准备请求数据
        OrderTracesRequest request = new OrderTracesRequest();
        request.setId(1115);

        // 执行请求
        Response<OrderTracesResponse> response = orderController.orderTraces(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());

        // 验证订单信息
        OrderTracesResponse data = response.getData();
        assertNotNull(data.getOrder());
        assertFalse(data.getDelivery().getTraces().isEmpty());
    }
} 