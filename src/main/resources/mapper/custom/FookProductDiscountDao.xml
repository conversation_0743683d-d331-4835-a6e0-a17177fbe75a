<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookProductDiscountDao">
    <select id="getDiscountByProductIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fook_product_discount fpd
        where fpd.product_id in
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>
</mapper>