package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookBusinessInformation;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessProductTranslations;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.bean.FookStoresTranslations;
import com.mcoin.mall.bean.FookStoresType;
import com.mcoin.mall.bean.FookStoresTypeTranslations;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.dao.FookBusinessInformationDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.dao.FookStoresKeywordDao;
import com.mcoin.mall.dao.FookStoresTranslationsDao;
import com.mcoin.mall.dao.FookStoresTypeDao;
import com.mcoin.mall.dao.FookStoresTypeTranslationsDao;
import com.mcoin.mall.model.StoreDetailCtx;
import com.mcoin.mall.model.StoreDetailRequest;
import com.mcoin.mall.model.StoreDetailResponse;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.service.common.StockService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.MiniProgramDisplayUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.mcoin.mall.constant.SnapUpEnum.NOT_SNAP_UP;
import static com.mcoin.mall.constant.SnapUpEnum.SNAP_UP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyObject;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class StoreServiceImplTestTwo {

    private StoreDetailRequest request;

    @Mock
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;

    @Mock
    private FookStoresDao fookStoresDao;

    @Mock
    private FookStoresKeywordDao fookStoresKeywordDao;

    @InjectMocks
    private StoreServiceImpl storeService;

    private StoreDetailCtx ctx;

    @Mock
    private FookStoresTypeDao fookStoresTypeDao;

    @Mock
    private FookBusinessInformationDao fookBusinessInformationDao;

    @Mock
    private FookBusinessProductDao fookBusinessProductDao;

    @Mock private FookStoresTranslationsDao fookStoresTranslationsDao;

    @Mock
    private FookStoresTypeTranslationsDao fookStoresTypeTranslationsDao;

    @Mock
    private CollectService collectService;

    @Mock
    private StockService stockService;


    private MockedStatic<MiniProgramDisplayUtil> mockedMiniProgramDisplayUtil;
    private MockedStatic<ConfigUtils> mockedConfigUtils;


    @BeforeEach
    public void setUp() {
        ctx = new StoreDetailCtx();
        request = new StoreDetailRequest();
        request.setWelfareId("1");
        request.setPage(1);
        ctx.setStoreDetailRequest(request);
        ctx.setLanguage(McoinMall.LANG_EN);
        mockedMiniProgramDisplayUtil = Mockito.mockStatic(MiniProgramDisplayUtil.class);
        mockedConfigUtils = Mockito.mockStatic(ConfigUtils.class);
    }


    @AfterEach
    public void tearDown() {
        mockedMiniProgramDisplayUtil.close();
        mockedConfigUtils.close();
    }

    @Test
    public void testGetStoreDetail_withValidData() {
        mockedMiniProgramDisplayUtil.when(() -> MiniProgramDisplayUtil.filterMiniprogramProduct(eq(ClientTypeEnum.HARMONY.toString())))
                .thenReturn(Boolean.TRUE);
        mockedConfigUtils.when(() -> ConfigUtils.getProperty(eq("cache.invalidation.original.table.search.switch"), anyString()))
                .thenReturn("true");
        String clientType = "";
        FookStores store = new FookStores();
        store.setId(1);
        store.setName("Test Store");
        store.setAddress("Test Address");
        store.setBusinessTime("Test Business Time");
        store.setDetail("Test Detail");
        store.setLongitude("123.456");
        store.setDimension("100");
        store.setBusinessId(1);
        store.setPhone("1234567890");
        store.setImg("http://test.jpg");
        store.setBackgroundImg("http://background.jpg");
        store.setProvideServices("Test Services");
        store.setBusinessInformationId("1");

        when(fookStoresDao.getStore(1)).thenReturn(store);

        FookStoresTranslations storesTranslations = new FookStoresTranslations();
        storesTranslations.setTName("Translated Name");
        storesTranslations.setTAddress("Translated Address");
        storesTranslations.setTBusinessTime("Translated Business Time");
        storesTranslations.setTDetail("Translated Detail");
        when(fookStoresTranslationsDao.getTranslationsByStoreId(1, McoinMall.LANG_EN))
                .thenReturn(storesTranslations);
        FookStoresType storesType = new FookStoresType();
        storesType.setId(1);
        List<FookStoresType> types = Collections.singletonList(storesType);
        when(fookStoresTypeDao.getStoresTypeByStoreId(1)).thenReturn(types);

        FookStoresTypeTranslations typeTranslations = new FookStoresTypeTranslations();
        typeTranslations.setTName("Translated Type Name");
        typeTranslations.setStoresTypeId(1);
        when(fookStoresTypeTranslationsDao.getTranslationsByStoreTypeIds(
                Collections.singletonList(1), McoinMall.LANG_EN))
                .thenReturn(Collections.singletonList(typeTranslations));

        FookBusinessProduct product = new FookBusinessProduct();
        product.setId(1);
        product.setRetailPrice(new BigDecimal(20));
        product.setPointRatio(300);
        product.setMinPoint(150);
        product.setOnlyPoint(0);
        product.setPrice(new BigDecimal("10"));
        product.setImg("http://example.com/image.png");
        product.setSnapUp(NOT_SNAP_UP.getValue());

        FookBusinessProduct product2 = new FookBusinessProduct();
        product2.setId(2);
        product2.setRetailPrice(new BigDecimal(20));
        product2.setPointRatio(300);
        product2.setMinPoint(150);
        product2.setOnlyPoint(0);
        product2.setPrice(new BigDecimal("10"));
        product2.setImg("http://example.com/image.png");
        product2.setSnapUp(SNAP_UP.getValue());
        product2.setBuyStartTime(JodaTimeUtil.plusDayToDate(new Date(), 1));

        FookBusinessProduct product3 = new FookBusinessProduct();
        product3.setId(3);
        product3.setRetailPrice(new BigDecimal(20));
        product3.setPointRatio(300);
        product3.setMinPoint(150);
        product3.setOnlyPoint(0);
        product3.setPrice(new BigDecimal("10"));
        product3.setImg("http://example.com/image.png");
        product3.setSnapUp(SNAP_UP.getValue());
        product3.setBuyStartTime(JodaTimeUtil.plusDayToDate(new Date(), 1));


        List<FookBusinessProduct> businessProducts = new ArrayList<>(Arrays.asList(product, product3));
        when(fookBusinessProductDao.countBusinessProductByStoreId(eq(1), anyString(), anyObject()))
                .thenReturn(22);
        when(fookBusinessProductDao.getBusinessProductByStoreId(eq(1), anyString(), anyInt(), anyInt(), anyObject()))
                .thenReturn(businessProducts);

        FookBusinessProductTranslations productTranslations = new FookBusinessProductTranslations();
        productTranslations.setBusinessProductId(1);
        productTranslations.setTTitle("Translated Title");
        productTranslations.setTTnc("Translated Tnc");
        productTranslations.setTReceiveMethod("Translated Receive Method");
        when(fookBusinessProductTranslationsDao.getTranslationsByIds(
                anyList(), eq(McoinMall.LANG_EN)))
                .thenReturn(Collections.singletonList(productTranslations));

        FookBusinessInformation fookBusinessInformation = new FookBusinessInformation();
        fookBusinessInformation.setId(1);
        fookBusinessInformation.setName("Test Business Information");
        when(fookBusinessInformationDao.selectByPrimaryKey(1)).thenReturn(fookBusinessInformation);

        when(stockService.isSlodOut(any(FookBusinessProduct.class))).thenReturn(false);

        StoreDetailResponse response = storeService.getStoreDetail(ctx, clientType);

        assertNotNull(response);
        assertEquals("Translated Name", response.getName());
        assertEquals("Translated Address", response.getAddress());
        assertEquals("Translated Business Time", response.getBusinessTime());
        assertEquals("Translated Detail", response.getDetail());
        assertEquals("Translated Type Name", response.getType().get(0).getName());
        assertEquals("Translated Title", response.getBusiness().get(0).getTitle());
        assertEquals("Test Business Information", response.getBusinessinformation().getName());
        assertEquals(150, response.getBusiness().get(0).getPoint());
        assertEquals(new BigDecimal("9.50"), response.getBusiness().get(0).getPreferential());
        assertEquals(2, response.getBusiness().size());
        assertEquals(22, response.getPage().getTotal());
        assertEquals(1, response.getPage().getCurrentPage());
        assertEquals(10, response.getPage().getPerPage());
        assertEquals(3, response.getPage().getLastPage());
        assertFalse(response.getBusiness().get(0).isSoldOut());
    }

}
