package com.mcoin.mall.constant;

/**
 * 訂單核銷碼退款狀態枚舉
 */
public enum OrdercodeRefundStatusEnum {

    /**
     * 未退款
     */
    NO_REFUND(1, "未退款"),
    
    /**
     * 退款中
     */
    REFUNDING(2, "退款中"),
    
    /**
     * 退款已完成
     */
    REFUNDED(3, "退款已完成");

    private final int code;
    private final String description;

    OrdercodeRefundStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根據代碼獲取描述
     *
     * @param code 代碼
     * @return 描述
     */
    public static String getDescriptionByCode(int code) {
        for (OrdercodeRefundStatusEnum status : OrdercodeRefundStatusEnum.values()) {
            if (status.getCode() == code) {
                return status.getDescription();
            }
        }
        return "";
    }
}
