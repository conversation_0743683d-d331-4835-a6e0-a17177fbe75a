<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookVoucherAssignmentDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookVoucherAssignment">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="voucher_code" jdbcType="VARCHAR" property="voucherCode" />
    <result column="voucher_status" jdbcType="TINYINT" property="voucherStatus" />
    <result column="wait_count" jdbcType="INTEGER" property="waitCount" />
    <result column="success_count" jdbcType="INTEGER" property="successCount" />
    <result column="error_count" jdbcType="INTEGER" property="errorCount" />
    <result column="follower" jdbcType="VARCHAR" property="follower" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="voucher_at" jdbcType="TIMESTAMP" property="voucherAt" />
    <result column="file_at" jdbcType="TIMESTAMP" property="fileAt" />
    <result column="excel_url" jdbcType="VARCHAR" property="excelUrl" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, product_id, voucher_code, voucher_status, wait_count, success_count, 
    error_count, follower, created_at, updated_at, voucher_at, file_at, excel_url, msg
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_voucher_assignment
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_voucher_assignment
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookVoucherAssignment">
    insert into fook_voucher_assignment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="voucherCode != null">
        voucher_code,
      </if>
      <if test="voucherStatus != null">
        voucher_status,
      </if>
      <if test="waitCount != null">
        wait_count,
      </if>
      <if test="successCount != null">
        success_count,
      </if>
      <if test="errorCount != null">
        error_count,
      </if>
      <if test="follower != null">
        follower,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="voucherAt != null">
        voucher_at,
      </if>
      <if test="fileAt != null">
        file_at,
      </if>
      <if test="excelUrl != null">
        excel_url,
      </if>
      <if test="msg != null">
        msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="voucherCode != null">
        #{voucherCode,jdbcType=VARCHAR},
      </if>
      <if test="voucherStatus != null">
        #{voucherStatus,jdbcType=TINYINT},
      </if>
      <if test="waitCount != null">
        #{waitCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        #{successCount,jdbcType=INTEGER},
      </if>
      <if test="errorCount != null">
        #{errorCount,jdbcType=INTEGER},
      </if>
      <if test="follower != null">
        #{follower,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherAt != null">
        #{voucherAt,jdbcType=TIMESTAMP},
      </if>
      <if test="fileAt != null">
        #{fileAt,jdbcType=TIMESTAMP},
      </if>
      <if test="excelUrl != null">
        #{excelUrl,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookVoucherAssignment">
    update fook_voucher_assignment
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="voucherCode != null">
        voucher_code = #{voucherCode,jdbcType=VARCHAR},
      </if>
      <if test="voucherStatus != null">
        voucher_status = #{voucherStatus,jdbcType=TINYINT},
      </if>
      <if test="waitCount != null">
        wait_count = #{waitCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        success_count = #{successCount,jdbcType=INTEGER},
      </if>
      <if test="errorCount != null">
        error_count = #{errorCount,jdbcType=INTEGER},
      </if>
      <if test="follower != null">
        follower = #{follower,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherAt != null">
        voucher_at = #{voucherAt,jdbcType=TIMESTAMP},
      </if>
      <if test="fileAt != null">
        file_at = #{fileAt,jdbcType=TIMESTAMP},
      </if>
      <if test="excelUrl != null">
        excel_url = #{excelUrl,jdbcType=VARCHAR},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookVoucherAssignment">
    update fook_voucher_assignment
    set user_id = #{userId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=INTEGER},
      voucher_code = #{voucherCode,jdbcType=VARCHAR},
      voucher_status = #{voucherStatus,jdbcType=TINYINT},
      wait_count = #{waitCount,jdbcType=INTEGER},
      success_count = #{successCount,jdbcType=INTEGER},
      error_count = #{errorCount,jdbcType=INTEGER},
      follower = #{follower,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      voucher_at = #{voucherAt,jdbcType=TIMESTAMP},
      file_at = #{fileAt,jdbcType=TIMESTAMP},
      excel_url = #{excelUrl,jdbcType=VARCHAR},
      msg = #{msg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>