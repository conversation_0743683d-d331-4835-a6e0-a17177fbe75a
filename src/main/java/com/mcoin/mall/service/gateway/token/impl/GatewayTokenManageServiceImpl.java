package com.mcoin.mall.service.gateway.token.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookMacaupassToken;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.model.GatewayAccessTokenResponse;
import com.mcoin.mall.client.model.GatewayAppTicketResponse;
import com.mcoin.mall.client.model.GatewayAppTokenResponse;
import com.mcoin.mall.client.model.MPayUserInfoResponse;
import com.mcoin.mall.dao.FookMacaupassTokenDao;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.service.gateway.token.GatewayTokenManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

import static com.mcoin.mall.util.SentinelUtils.handleBlockException;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
@Service
@Slf4j
public class GatewayTokenManageServiceImpl implements GatewayTokenManageService {
    private final static String fookMacaupassAppToken = "apptoken";//数据库中固定的token类型名
    private final static String fookMacaupassTicket = "ticket";//数据库中固定的ticket类型名


    @Resource
    private MPayClient mPayClient;
    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;
    @Resource
    private FookMacaupassTokenDao fookMacaupassTokenDao;

    private static final String ERRORCODE = "50";

    public GatewayAccessTokenResponse updateUserToken(String code) {
        // 1. 根据 code 获取 MPAY 的 accessToken、refreshToken、openId
        GatewayAccessTokenResponse mPayAccessTokenResponse = new GatewayAccessTokenResponse();
        String accessTokenResp = handleBlockException(() -> mPayClient.accessToken(code), null);
        if (StringUtils.isEmpty(accessTokenResp)) {
            log.error("updateUserToken，网关使用code获取access_token请求失败，触发用户重新登录");
            mPayAccessTokenResponse.setErrcode(ERRORCODE);
            return mPayAccessTokenResponse;
        }
        mPayAccessTokenResponse = JSON.parseObject(accessTokenResp, GatewayAccessTokenResponse.class);
        String accessToken = mPayAccessTokenResponse.getAccessToken();
        String refreshToken = mPayAccessTokenResponse.getRefreshToken();
        String openid = mPayAccessTokenResponse.getOpenid();

        // 2. 查询本地用户(fook_macaupass_user)
        FookMacaupassUser userInfo = fookMacaupassUserDao.selectOne(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getOpenid, openid)
                .last("limit 1")
        );
        // 3. 用户存在则更新accessToken、refreshToken、code、updateTime
        if (userInfo != null) {
            FookMacaupassUser macaupassUser = new FookMacaupassUser();
            macaupassUser.setAccessToken(accessToken);
            macaupassUser.setRefreshToken(refreshToken);
            macaupassUser.setUpdateTime(new Date());
            macaupassUser.setCode(code);
            if(StringUtils.isBlank(userInfo.getCustomid())) {
                log.info("updateUserToken，网关用户信息中custId为空，重新获取 {}",userInfo.getUserId());
                //请求mpay获取用户id
                String userIdResp = handleBlockException(()->mPayClient.getUserId(openid), null);
                log.info("updateUserToken，网关请求mpay获取用户id响应内容：{}", userIdResp);
                if (StringUtils.isEmpty(userIdResp)) {
                    log.error("updateUserToken，网关获取用户id请求失败，触发用户重新登录");
                    mPayAccessTokenResponse.setErrcode(ERRORCODE);
                    return mPayAccessTokenResponse;
                }
                MPayUserInfoResponse mPayUserIdResponse = JSON.parseObject(userIdResp, MPayUserInfoResponse.class);
                String custId = mPayUserIdResponse.getUserId();
                if (StringUtils.isEmpty(custId)) {
                    log.error("updateUserToken，网关获取用户id失败，触发用户重新登录");
                    mPayAccessTokenResponse.setErrcode(ERRORCODE);
                    return mPayAccessTokenResponse;
                }
                macaupassUser.setCustomid(custId);
            }
            fookMacaupassUserDao.update(macaupassUser, new LambdaQueryWrapper<FookMacaupassUser>()
                    .eq(FookMacaupassUser::getId, userInfo.getId()));
        }
        log.info("updateUserToken,response="+mPayAccessTokenResponse);
        return mPayAccessTokenResponse;
    }

    @Override
    public GatewayAppTokenResponse manageAppToken() {
        //查token
        FookMacaupassToken macaupassToken = fookMacaupassTokenDao.selectOne(new LambdaQueryWrapper<FookMacaupassToken>()
                .eq(FookMacaupassToken::getType, fookMacaupassAppToken)
                .last("limit 1")
        );
        // 构造响应
        GatewayAppTokenResponse mPayAppTokenResponse = new GatewayAppTokenResponse();
        mPayAppTokenResponse.setAppToken(macaupassToken.getToken());
        mPayAppTokenResponse.setExpiresIn(Integer.parseInt(macaupassToken.getExpiresIn()));
        log.info("manageAppToken， response" + mPayAppTokenResponse);
        return mPayAppTokenResponse;
    }


    @Override
    public GatewayAppTicketResponse manageAppTicket() {
        //查token
        FookMacaupassToken macaupassToken = fookMacaupassTokenDao.selectOne(new LambdaQueryWrapper<FookMacaupassToken>()
                .eq(FookMacaupassToken::getType, fookMacaupassTicket)
                .last("limit 1")
        );
        // 构造响应
        GatewayAppTicketResponse gatewayAppTicketResponse = new GatewayAppTicketResponse();
        gatewayAppTicketResponse.setTicket(macaupassToken.getToken());
        gatewayAppTicketResponse.setExpiresIn(Integer.parseInt(macaupassToken.getExpiresIn()));
        log.info("manageAppTicket， response" + gatewayAppTicketResponse);
        return gatewayAppTicketResponse;
    }



}
