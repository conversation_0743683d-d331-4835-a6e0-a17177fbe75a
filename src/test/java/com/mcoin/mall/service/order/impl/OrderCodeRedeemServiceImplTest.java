package com.mcoin.mall.service.order.impl;

import com.mcoin.mall.bean.*;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.constant.CouponSynStatus;
import com.mcoin.mall.dao.*;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.SettlementCtx;
import com.mcoin.mall.mq.model.CouponSyncMessage;
import com.mcoin.mall.service.api.OrderApiService;
import com.mcoin.mall.service.chennel.MPayChannelService;
import com.mcoin.mall.service.common.AtomicSeqService;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;

import java.util.Collections;
import java.util.Date;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class OrderCodeRedeemServiceImplTest {
    @Mock
    FookPlatformOrdercodeDao fookPlatformOrdercodeDao;
    @Mock
    FookPlatformOrderDao fookPlatformOrderDao;
    @Mock
    FookStoresDao fookStoresDao;
    @Mock
    FookBusinessProductDao fookBusinessProductDao;
    @Mock
    FookBusinessDao fookBusinessDao;
    @Mock
    FookExternalVcodeDao fookExternalVcodeDao;
    @Mock
    FookReportOrdercodeSettlementDao fookReportOrdercodeSettlementDao;
    @Mock
    FookAFeeTypeDao fookAFeeTypeDao;
    @Mock
    FookPlatformOrdercodeLogDao fookPlatformOrdercodeLogDao;
    @Mock
    FookPlatformOrderinfoDao fookPlatformOrderinfoDao;
    @Mock
    FookBusinessStoreProductDao fookBusinessStoreProductDao;
    @Mock
    MessageSource messageSource;
    @Mock
    ContextHolder contextHolder;
    @Mock
    AtomicSeqService atomicSeqService;
    @Mock
    MPayChannelService mPayChannelService;
    @Mock
    ApplicationContext applicationContext;
    @Mock
    OrderApiService orderApiService;
    @InjectMocks
    OrderCodeRedeemServiceImpl orderCodeRedeemServiceImpl;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testVerifyPass() {
        // given
        when(fookStoresDao.getStores(any())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/stores.json", "singleStores", FookStores.class))
        );

        // when
        Integer result = orderCodeRedeemServiceImpl.verifyPass(Collections.singletonList(0), "Pass123");

        // then
        assertEquals(Integer.valueOf(1), result);
    }

    @Test
    void testSettlement() {
        // given
        when(mPayChannelService.insertCouponSync(anyInt(), anyInt(), any(CouponSyncMessage.Operation.class), 
                any(CouponSynStatus.class), anyString())).thenReturn(1);
        mock();
        
        // when
        SettlementCtx request = newNomalCtx();
        Response.Code result = orderCodeRedeemServiceImpl.settlement(request);
        
        // then
        assertEquals(Response.Code.SUCCESS, result);
    }

    @Test
    void testMpaySettlementWithDetail() {
        // given
        when(mPayChannelService.insertCouponSync(anyInt(), anyInt(), any(CouponSyncMessage.Operation.class), 
                any(CouponSynStatus.class), anyString())).thenReturn(1);
        mock();
        
        // when
        SettlementCtx request = newNomalCtx();
        String result = orderCodeRedeemServiceImpl.mpaySettlementWithDetail(request).getData().getResultcode();
        
        // then
        assertEquals("0000", result);
    }

    @Test
    void testSettlementWithDetail() {
        // given
        when(mPayChannelService.insertCouponSync(anyInt(), anyInt(), any(CouponSyncMessage.Operation.class), 
                any(CouponSynStatus.class), anyString())).thenReturn(1);
        mock();
        
        // when
        SettlementCtx request = newNomalCtx();
        int result = orderCodeRedeemServiceImpl.settlementWithDetail(request).getCode();
        
        // then
        assertEquals(Response.Code.SUCCESS.get(), result);
    }

    @Test
    void testDoSettlementWithNullCode() {
        // given
        mock();
        
        // when
        SettlementCtx request = newNullCodeCtx();
        Response.Code result = orderCodeRedeemServiceImpl.doSettlement(request).getKey();
        
        // then
        assertEquals(Response.Code.ST_NOT_APPLICABLE, result);
    }

    @Test
    void testDoSettlementWithNormalCode() {
        // given
        mock();
        
        // when
        SettlementCtx request = newNomalCtx();
        Response.Code result = orderCodeRedeemServiceImpl.doSettlement(request).getKey();
        
        // then
        assertEquals(Response.Code.SUCCESS, result);
    }

    void mock() {
        when(fookPlatformOrdercodeDao.updateCodeToUsed(anyInt(), anyInt(), anyInt(), any(Date.class), anyString(), 
                any(Date.class))).thenReturn(1);
        when(fookPlatformOrdercodeDao.getOrderCode(anyString())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/fookPlatformOrdercodes.json", 
                        "singleCode", FookPlatformOrdercode.class))
        );
        when(fookPlatformOrdercodeDao.countByOrderIdAndStatus(anyInt(), anyInt())).thenReturn(0);
        when(fookPlatformOrdercodeDao.updateCodeToUnUsed(anyInt())).thenReturn(1);
        when(fookPlatformOrderDao.selectByPrimaryKey(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookPlatformOrders.json", "singleOrder", FookPlatformOrder.class)
        );
        when(fookPlatformOrderDao.updateCompleteTime(anyInt(), any(Date.class), anyInt())).thenReturn(0);
        when(fookStoresDao.getStore(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/stores.json", "singleStores", FookStores.class)
        );
        when(fookBusinessProductDao.selectByPrimaryKey(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/businessProduct.json", "singleBp", FookBusinessProduct.class)
        );
        when(fookBusinessDao.selectByPrimaryKey(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookBusiness.json", "singleBusiness", FookBusiness.class)
        );
        when(fookExternalVcodeDao.updateByCode(anyString(), anyInt(), any(Date.class))).thenReturn(0);
        when(fookReportOrdercodeSettlementDao.updateByPrimaryKeySelective(any(FookReportOrdercodeSettlement.class))).thenReturn(0);
        when(fookReportOrdercodeSettlementDao.getSettlementFromOrderCode(anyInt(), anyBoolean())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookReportOrdercodeSettlement.json", 
                        "singleOrdercodeSettlement", FookReportOrdercodeSettlement.class)
        );
        when(fookAFeeTypeDao.selectByPrimaryKey(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookAFeeType.json", "singleAFeeType", FookAFeeType.class)
        );
        when(fookPlatformOrdercodeLogDao.insertSelective(any(FookPlatformOrdercodeLog.class))).thenReturn(0);
        when(fookPlatformOrderinfoDao.selectByPrimaryKey(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/orderinfo.json", "singleOrderInfo", FookPlatformOrderinfo.class)
        );
        when(fookBusinessStoreProductDao.getStoresIdByProductIds(any())).thenReturn(
                Collections.singletonList(JsonTestUtils.getObjectFromJson("json/fookBusinessStoreProduct.json", 
                        "singleBusinessStoreProduct", FookBusinessStoreProduct.class))
        );
        when(messageSource.getMessage(anyString(), any(Object[].class), any(Locale.class))).thenReturn("getMessageResponse");
        when(mPayChannelService.insertCouponSync(anyInt(), anyInt(), any(CouponSyncMessage.Operation.class), 
                any(CouponSynStatus.class), anyString())).thenReturn(0);
        when(applicationContext.getMessage(anyString(), any(Object[].class), any(Locale.class))).thenReturn("getMessageResponse");
    }

    SettlementCtx newNullCodeCtx() {
        return new SettlementCtx();
    }

    SettlementCtx newNomalCtx() {
        SettlementCtx ctx = new SettlementCtx();
        ctx.setCode("1800029636020735");
        ctx.setStoresId(0);
        ctx.setMode(4);
        ctx.setPassCheck(false);
        ctx.setUserId(-1);
        ctx.setTrackingNo("trackingNo");
        return ctx;
    }
} 