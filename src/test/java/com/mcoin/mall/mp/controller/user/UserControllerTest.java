package com.mcoin.mall.mp.controller.user;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.user.GetUserIntegralResponse;
import com.mcoin.mall.mp.model.user.UserResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@Transactional
class UserControllerTest extends BaseUnitTest {

    @Autowired
    private UserController userController;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
        when(mcoinChannelService.getPoint("1")).thenReturn(1000);
    }

    @Test
    void queryUserInfo_ShouldReturnUserInfo() {
        // Act
        Response<UserResponse> response = userController.queryUserInfo();

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void getIntegral_ShouldReturnUserIntegral() {
        // Act
        Response<GetUserIntegralResponse> response = userController.getIntegral();

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getUserId());
        assertEquals(1000, response.getData().getValue());
    }
} 