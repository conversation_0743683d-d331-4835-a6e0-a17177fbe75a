-- Merged SQL file for RefundListenerTest
-- Contains test data for all tables needed for testing RefundListener

-- =============================================
-- Clean up existing test data
-- =============================================
-- 清理测试数据，避免主键冲突
DELETE FROM `fook_platform_ordercode` WHERE `id` IN (310001, 10001, 10002, 10003);
DELETE FROM `fook_platform_orderinfo` WHERE `id` IN (310001, 10001, 10002, 10003);
DELETE FROM `fook_pay_log` WHERE `id` IN (310001, 10001, 10002, 10003, 10004);
DELETE FROM `fook_platform_order` WHERE `id` IN (310001, 10001, 10002, 10003);
DELETE FROM `fook_platform_orderrefund` WHERE `id` IN (310001, 10001, 10002, 10003);
DELETE FROM `fook_mq_local` WHERE `id` IN (10001, 10002);

-- =============================================
-- fook_mq_local table
-- =============================================
-- 新增本地消息表测试数据 - 退款消息
CREATE TABLE IF NOT EXISTS `fook_mq_local` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `resource_id` varchar(64) DEFAULT NULL COMMENT '来源id，可以用于关联某个业务表',
  `resource_type` varchar(32) DEFAULT NULL COMMENT '来源id的类型，用于指明哪种业务表',
  `template_name` varchar(64) DEFAULT NULL COMMENT 'rabbit template spring bean name',
  `try_count` int(11) DEFAULT '0' COMMENT '已尝试次数',
  `status` int(11) DEFAULT '0' COMMENT '0-待处理，1-已完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `next_retry` datetime DEFAULT NULL,
  `max_try_count` int(11) DEFAULT '3' COMMENT '最大尝试次数',
  `mq_config` text COMMENT 'mq的其他发送配置信息',
  `message_body` text COMMENT 'MQ的消息体，过长建议只存此表id，消费时查表使用attachment',
  `attachment` text COMMENT '消费消息时需要的参数变量等等',
  PRIMARY KEY (`id`),
  KEY `idx_resource` (`resource_id`,`resource_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='本地消息表';

-- 测试用本地消息 - 退款消息
INSERT INTO `fook_mq_local` (
    `id`, `resource_id`, `resource_type`, `template_name`,
    `try_count`, `status`, `create_time`, `update_time`,
    `next_retry`, `max_try_count`, `mq_config`, `message_body`, `attachment`
) VALUES (
    10001, '10001', 'REFUND', 'refundTemplate',
    0, 0, NOW(), NOW(),
    DATE_ADD(NOW(), INTERVAL 5 MINUTE), 3, NULL, 
    '{"refundId":10001,"outRequestNo":"TEST_OUT_REQUEST_NO","refundScene":"APPROVAL_REFUND","mqLocalId":10001}', NULL
);

-- 测试用本地消息 - 已处理完成的退款消息
INSERT INTO `fook_mq_local` (
    `id`, `resource_id`, `resource_type`, `template_name`,
    `try_count`, `status`, `create_time`, `update_time`,
    `next_retry`, `max_try_count`, `mq_config`, `message_body`, `attachment`
) VALUES (
    10002, '10002', 'REFUND', 'refundTemplate',
    1, 1, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 23 HOUR),
    DATE_ADD(NOW(), INTERVAL -1 HOUR), 3, NULL, 
    '{"refundId":10002,"outRequestNo":"TEST_OUT_REQUEST_NO_2","refundScene":"APPROVAL_REFUND","mqLocalId":10002}', NULL
);

-- =============================================
-- fook_platform_orderrefund table
-- =============================================
-- 退款状态(1、提交申請/平台審核中，2、同意退款/第三方審核中，3、不同意退款，4、第三方通過/處理中，5、第三方不通過，6、已到賬，7、到賬失敗，8取消退款)
-- 支付平台處理狀態(0-审核中 1-處理 2-不成功 3-成功)

-- 测试用退款记录 - 正在处理中
INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    310001, 310001, 100.00, '测试退款原因', '前台退款原因',
    '客服备注', 2, 1, 1,
    1, 0.00, 100.00, 0,
    NULL, NOW(), NULL, NULL
);

-- 测试用退款记录 - 已完成
INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    10002, 10002, 200.00, '测试退款原因2', '前台退款原因2',
    '客服备注2', 6, 3, 3,
    1, 0.00, 200.00, 0,
    NOW(), DATE_SUB(NOW(), INTERVAL 1 DAY), 'TEST_REFUND_TRANSACTION', 10002
);

-- 测试用退款记录 - 审核中
INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    10003, 10003, 300.00, '测试退款原因3', '前台退款原因3',
    '客服备注3', 1, 0, 0,
    1, 0.00, 300.00, 0,
    NULL, DATE_SUB(NOW(), INTERVAL 2 DAY), NULL, NULL
);

-- 测试用退款记录 - 用于测试 testOnMessageSuccess 和 testOnMessageTransactionalWithDelay
INSERT INTO `fook_platform_orderrefund` (
    `id`, `orderid`, `refund_amount`, `refund_reason`, `refund_resson_front`,
    `customer_remark`, `status`, `platform_deal_status`, `mpayintegral_status`,
    `return_route`, `refund_score`, `actual_refund_amount`, `mpayintegral`,
    `refund_time`, `application_time`, `refund_transacation`, `pay_id`
) VALUES (
    10001, 10001, 150.00, '测试退款原因4', '前台退款原因4',
    '客服备注4', 2, 1, 1,
    1, 0.00, 150.00, 0,
    NULL, DATE_SUB(NOW(), INTERVAL 1 DAY), NULL, NULL
);

-- =============================================
-- fook_platform_order table
-- =============================================
-- 訂單狀態（1、未付款，2、已付款,3、失效訂單，4、退款訂單）
-- 退款狀態(0 沒退款，1已退款，2部分退款，3子訂單全退款、4異常單退款)

-- 测试用订单 - 已付款，正在退款中
INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    310001, 1, 1, 1, 1, 'TEST_ORDER_NO_310001',
    DATE_SUB(NOW(), INTERVAL 1 DAY), 1, 'TEST_PAYMENT_TRANSACTION_310001', 2,
    NULL, 'TEST_ORDER_TRANSACTION_310001', 0, 0,
    2, 100.00, 0.00, 'MOP',
    100.00, 'MOP', 100.00,
    'MOP', 100.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 23 HOUR), 2
);

-- 测试用订单 - 已退款
INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    10002, 1, 1, 1, 1, 'TEST_ORDER_NO_10002',
    DATE_SUB(NOW(), INTERVAL 2 DAY), 1, 'TEST_PAYMENT_TRANSACTION_10002', 4,
    NOW(), 'TEST_ORDER_TRANSACTION_10002', 0, 0,
    2, 200.00, 0.00, 'MOP',
    200.00, 'MOP', 200.00,
    'MOP', 200.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 47 HOUR), 3
);

-- 测试用订单 - 已付款，未退款
INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    10003, 1, 1, 1, 1, 'TEST_ORDER_NO_10003',
    DATE_SUB(NOW(), INTERVAL 3 DAY), 1, 'TEST_PAYMENT_TRANSACTION_10003', 2,
    NULL, 'TEST_ORDER_TRANSACTION_10003', 0, 0,
    2, 300.00, 0.00, 'MOP',
    300.00, 'MOP', 300.00,
    'MOP', 300.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 71 HOUR), 0
);

-- 测试用订单 - 用于测试 testOnMessageSuccess 和 testOnMessageTransactionalWithDelay
INSERT INTO `fook_platform_order` (
    `id`, `areaid`, `userid`, `sellerid`, `type`, `order_no`,
    `create_time`, `payment_type`, `payment_transaction`, `status`,
    `complete_time`, `order_transaction`, `mpayintegral`, `score`,
    `platform`, `order_amount`, `point_ratio`, `currency`,
    `total_amount`, `total_amount_currency`, `transaction_amount`,
    `transaction_amount_currency`, `payment_amount`, `payment_amount_currency`,
    `payment_time`, `refund_status`
) VALUES (
    10001, 1, 1, 1, 1, 'TEST_ORDER_NO_10001',
    DATE_SUB(NOW(), INTERVAL 1 DAY), 1, 'TEST_PAYMENT_TRANSACTION_10001', 2,
    NULL, 'TEST_ORDER_TRANSACTION_10001', 0, 0,
    2, 150.00, 0.00, 'MOP',
    150.00, 'MOP', 150.00,
    'MOP', 150.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 23 HOUR), 2
);

-- =============================================
-- fook_pay_log table
-- =============================================
-- 支付日志状态 (0-未支付, 1-已支付, 2-退款中, 3-已退款)

-- 测试用支付日志 - 已支付
INSERT INTO `fook_pay_log` (
    `id`, `orderid`, `uid`, `uuid`, `amount`, `currency`,
    `createtime`, `updatetime`, `oparemtion`, `status`,
    `reason`, `reasoncontent`, `type`, `tradeno`, `refund_id`, `content`
) VALUES (
    310001, 310001, 1, 'TEST_UUID_310001', 100.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 23 HOUR), 'pay', 1,
    NULL, NULL, 'macaupay', 'TEST_PAYMENT_TRANSACTION_310001', NULL, '{"test":"payment content"}'
);

-- 测试用支付日志 - 已支付
INSERT INTO `fook_pay_log` (
    `id`, `orderid`, `uid`, `uuid`, `amount`, `currency`,
    `createtime`, `updatetime`, `oparemtion`, `status`,
    `reason`, `reasoncontent`, `type`, `tradeno`, `refund_id`, `content`
) VALUES (
    10002, 10002, 1, 'TEST_UUID_10002', 200.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 47 HOUR), 'pay', 1,
    NULL, NULL, 'macaupay', 'TEST_PAYMENT_TRANSACTION_10002', NULL, '{"test":"payment content"}'
);

-- 测试用退款日志
INSERT INTO `fook_pay_log` (
    `id`, `orderid`, `uid`, `uuid`, `amount`, `currency`,
    `createtime`, `updatetime`, `oparemtion`, `status`,
    `reason`, `reasoncontent`, `type`, `tradeno`, `refund_id`, `content`
) VALUES (
    10003, 10002, 1, 'TEST_OUT_REQUEST_NO', 200.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 1 DAY), NOW(), 'refund', 1,
    'Y', '{"test":"refund content"}', 'macaupay', 'TEST_REFUND_TRANSACTION', 10002, '{"test":"refund request"}'
);

-- 测试用支付日志 - 已支付
INSERT INTO `fook_pay_log` (
    `id`, `orderid`, `uid`, `uuid`, `amount`, `currency`,
    `createtime`, `updatetime`, `oparemtion`, `status`,
    `reason`, `reasoncontent`, `type`, `tradeno`, `refund_id`, `content`
) VALUES (
    10004, 10003, 1, 'TEST_UUID_10003', 300.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 71 HOUR), 'pay', 1,
    NULL, NULL, 'macaupay', 'TEST_PAYMENT_TRANSACTION_10003', NULL, '{"test":"payment content"}'
);

-- 测试用支付日志 - 用于测试 testOnMessageSuccess 和 testOnMessageTransactionalWithDelay
INSERT INTO `fook_pay_log` (
    `id`, `orderid`, `uid`, `uuid`, `amount`, `currency`,
    `createtime`, `updatetime`, `oparemtion`, `status`,
    `reason`, `reasoncontent`, `type`, `tradeno`, `refund_id`, `content`
) VALUES (
    10001, 10001, 1, 'TEST_UUID_10001', 150.00, 'MOP',
    DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 23 HOUR), 'pay', 1,
    NULL, NULL, 'macaupay', 'TEST_PAYMENT_TRANSACTION_10001', NULL, '{"test":"payment content"}'
);

-- =============================================
-- fook_platform_orderinfo table
-- =============================================

-- 测试用订单详情 - 订单310001
INSERT INTO `fook_platform_orderinfo` (
    `id`, `orderid`, `prodcutid`, `product_price`, `image_snapshots`,
    `title_snapshots`, `desc_snapshots`, `vaild_start_time`, `vaild_end_time`,
    `details_snapshots`, `number`, `is_weekend`, `no_useday`, `type`,
    `img`, `retail_price`, `tnc`, `is_vacation`, `is_allow_refund`,
    `fee_rate`, `buy_start_time`, `buy_end_time`, `valid_mode`, `day_num`
) VALUES (
    310001, 310001, 1, 100.00, 'test_image_snapshots.jpg',
    '测试商品1', '测试商品描述1', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY),
    '测试商品详情1', 1, 0, NULL, 1,
    'test_img.jpg', 120.00, '测试使用条款', 0, 1,
    0.00, DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_ADD(NOW(), INTERVAL 7 DAY), 1, 30
);

-- 测试用订单详情 - 订单10002
INSERT INTO `fook_platform_orderinfo` (
    `id`, `orderid`, `prodcutid`, `product_price`, `image_snapshots`,
    `title_snapshots`, `desc_snapshots`, `vaild_start_time`, `vaild_end_time`,
    `details_snapshots`, `number`, `is_weekend`, `no_useday`, `type`,
    `img`, `retail_price`, `tnc`, `is_vacation`, `is_allow_refund`,
    `fee_rate`, `buy_start_time`, `buy_end_time`, `valid_mode`, `day_num`
) VALUES (
    10002, 10002, 2, 200.00, 'test_image_snapshots2.jpg',
    '测试商品2', '测试商品描述2', DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY),
    '测试商品详情2', 1, 0, NULL, 1,
    'test_img2.jpg', 240.00, '测试使用条款', 0, 1,
    0.00, DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_ADD(NOW(), INTERVAL 7 DAY), 1, 30
);

-- 测试用订单详情 - 订单10003
INSERT INTO `fook_platform_orderinfo` (
    `id`, `orderid`, `prodcutid`, `product_price`, `image_snapshots`,
    `title_snapshots`, `desc_snapshots`, `vaild_start_time`, `vaild_end_time`,
    `details_snapshots`, `number`, `is_weekend`, `no_useday`, `type`,
    `img`, `retail_price`, `tnc`, `is_vacation`, `is_allow_refund`,
    `fee_rate`, `buy_start_time`, `buy_end_time`, `valid_mode`, `day_num`
) VALUES (
    10003, 10003, 3, 300.00, 'test_image_snapshots3.jpg',
    '测试商品3', '测试商品描述3', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY),
    '测试商品详情3', 1, 0, NULL, 1,
    'test_img3.jpg', 360.00, '测试使用条款', 0, 1,
    0.00, DATE_SUB(NOW(), INTERVAL 21 DAY), DATE_ADD(NOW(), INTERVAL 7 DAY), 1, 30
);

-- 测试用订单详情 - 用于测试 testOnMessageSuccess 和 testOnMessageTransactionalWithDelay
INSERT INTO `fook_platform_orderinfo` (
    `id`, `orderid`, `prodcutid`, `product_price`, `image_snapshots`,
    `title_snapshots`, `desc_snapshots`, `vaild_start_time`, `vaild_end_time`,
    `details_snapshots`, `number`, `is_weekend`, `no_useday`, `type`,
    `img`, `retail_price`, `tnc`, `is_vacation`, `is_allow_refund`,
    `fee_rate`, `buy_start_time`, `buy_end_time`, `valid_mode`, `day_num`
) VALUES (
    10001, 10001, 4, 150.00, 'test_image_snapshots4.jpg',
    '测试商品4', '测试商品描述4', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 30 DAY),
    '测试商品详情4', 1, 0, NULL, 1,
    'test_img4.jpg', 180.00, '测试使用条款', 0, 1,
    0.00, DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_ADD(NOW(), INTERVAL 7 DAY), 1, 30
);

-- =============================================
-- fook_platform_ordercode table
-- =============================================

-- 测试用订单码 - 订单310001
INSERT INTO `fook_platform_ordercode` (
    `id`, `orderid`, `code`, `status`, `shopid`, `user_time`,
    `verification_mode`, `is_comment`, `is_settlement`, `settlement_time`,
    `settlement_id`, `merch_settle_amount`, `merch_settle_amount_currency`,
    `refund_status`, `apportion_bill_amount`, `apportion_bill_final_amount`,
    `apportion_mpayintegral`, `apportion_momecoins`, `apportion_commission`,
    `orderinfo_id`, `refundid`, `userid`
) VALUES (
    310001, 310001, 'TEST_CODE_310001', 1, NULL, NULL,
    NULL, 0, 0, NULL,
    NULL, 100.00, 'MOP',
    1, 100.00, 100.00,
    0, 0.00, 0.00,
    310001, 310001, 1
);

-- 测试用订单码 - 订单10002
INSERT INTO `fook_platform_ordercode` (
    `id`, `orderid`, `code`, `status`, `shopid`, `user_time`,
    `verification_mode`, `is_comment`, `is_settlement`, `settlement_time`,
    `settlement_id`, `merch_settle_amount`, `merch_settle_amount_currency`,
    `refund_status`, `apportion_bill_amount`, `apportion_bill_final_amount`,
    `apportion_mpayintegral`, `apportion_momecoins`, `apportion_commission`,
    `orderinfo_id`, `refundid`, `userid`
) VALUES (
    10002, 10002, 'TEST_CODE_10002', 1, NULL, NULL,
    NULL, 0, 0, NULL,
    NULL, 200.00, 'MOP',
    2, 200.00, 200.00,
    0, 0.00, 0.00,
    10002, 10002, 1
);

-- 测试用订单码 - 订单10003
INSERT INTO `fook_platform_ordercode` (
    `id`, `orderid`, `code`, `status`, `shopid`, `user_time`,
    `verification_mode`, `is_comment`, `is_settlement`, `settlement_time`,
    `settlement_id`, `merch_settle_amount`, `merch_settle_amount_currency`,
    `refund_status`, `apportion_bill_amount`, `apportion_bill_final_amount`,
    `apportion_mpayintegral`, `apportion_momecoins`, `apportion_commission`,
    `orderinfo_id`, `refundid`, `userid`
) VALUES (
    10003, 10003, 'TEST_CODE_10003', 1, NULL, NULL,
    NULL, 0, 0, NULL,
    NULL, 300.00, 'MOP',
    0, 300.00, 300.00,
    0, 0.00, 0.00,
    10003, NULL, 1
);

-- 测试用订单码 - 用于测试 testOnMessageSuccess 和 testOnMessageTransactionalWithDelay
INSERT INTO `fook_platform_ordercode` (
    `id`, `orderid`, `code`, `status`, `shopid`, `user_time`,
    `verification_mode`, `is_comment`, `is_settlement`, `settlement_time`,
    `settlement_id`, `merch_settle_amount`, `merch_settle_amount_currency`,
    `refund_status`, `apportion_bill_amount`, `apportion_bill_final_amount`,
    `apportion_mpayintegral`, `apportion_momecoins`, `apportion_commission`,
    `orderinfo_id`, `refundid`, `userid`
) VALUES (
    10001, 10001, 'TEST_CODE_10001', 1, NULL, NULL,
    NULL, 0, 0, NULL,
    NULL, 150.00, 'MOP',
    2, 150.00, 150.00,
    0, 0.00, 0.00,
    10001, 10001, 1
);

-- =============================================
-- fook_business_product table - for automaticRefunds test
-- =============================================
DELETE FROM `fook_business_product` WHERE `id` IN (1, 2, 3, 4);

INSERT INTO `fook_business_product` (
    `id`, `areaid`, `type`, `businessid`, `img`, `title`, `price`, 
    `retail_price`, `point_ratio`, `stock`, `history_stock`, `sales`, 
    `actual_sales`, `limited_number`, `is_allow_refund`, `score`, 
    `comment_number`, `shelf_status`, `view_number`, `created_at`, 
    `updated_at`, `enable`, `is_hot`, `isexport`
) VALUES (
    1, 1, 1, 1, 'test_img.jpg', '测试商品1', 100.00,
    120.00, 0, 50, 100, 10,
    10, 5, 1, 5.0,
    10, 1, 100, NOW(),
    NOW(), 1, 0, 0
);

INSERT INTO `fook_business_product` (
    `id`, `areaid`, `type`, `businessid`, `img`, `title`, `price`, 
    `retail_price`, `point_ratio`, `stock`, `history_stock`, `sales`, 
    `actual_sales`, `limited_number`, `is_allow_refund`, `score`, 
    `comment_number`, `shelf_status`, `view_number`, `created_at`, 
    `updated_at`, `enable`, `is_hot`, `isexport`
) VALUES (
    2, 1, 1, 1, 'test_img2.jpg', '测试商品2', 200.00,
    240.00, 0, 30, 80, 20,
    20, 5, 1, 4.5,
    8, 1, 120, NOW(),
    NOW(), 1, 0, 1
);

INSERT INTO `fook_business_product` (
    `id`, `areaid`, `type`, `businessid`, `img`, `title`, `price`, 
    `retail_price`, `point_ratio`, `stock`, `history_stock`, `sales`, 
    `actual_sales`, `limited_number`, `is_allow_refund`, `score`, 
    `comment_number`, `shelf_status`, `view_number`, `created_at`, 
    `updated_at`, `enable`, `is_hot`, `isexport`
) VALUES (
    3, 1, 1, 1, 'test_img3.jpg', '测试商品3', 300.00,
    360.00, 0, 40, 90, 15,
    15, 5, 1, 4.8,
    12, 1, 80, NOW(),
    NOW(), 1, 0, 0
);

INSERT INTO `fook_business_product` (
    `id`, `areaid`, `type`, `businessid`, `img`, `title`, `price`, 
    `retail_price`, `point_ratio`, `stock`, `history_stock`, `sales`, 
    `actual_sales`, `limited_number`, `is_allow_refund`, `score`, 
    `comment_number`, `shelf_status`, `view_number`, `created_at`, 
    `updated_at`, `enable`, `is_hot`, `isexport`
) VALUES (
    4, 1, 1, 1, 'test_img4.jpg', '测试商品4', 150.00,
    180.00, 0, 60, 100, 25,
    25, 5, 1, 4.7,
    15, 1, 150, NOW(),
    NOW(), 1, 0, 0
);

-- =============================================
-- fook_external_vcode table - for automaticRefunds test
-- =============================================
DELETE FROM `fook_external_vcode` WHERE `id` IN (1, 2);

INSERT INTO `fook_external_vcode` (
    `id`, `product_id`, `vcode`, `isascription`, `title`, 
    `enable`, `status`, `created_at`, `update_time`, 
    `is_a_open`, `build_status`, `is_build_order`
) VALUES (
    1, 2, 'TEST_CODE_10002', 1, '测试外部券码1',
    1, 0, NOW(), NOW(),
    0, 2, 1
);

INSERT INTO `fook_external_vcode` (
    `id`, `product_id`, `vcode`, `isascription`, `title`, 
    `enable`, `status`, `created_at`, `update_time`, 
    `is_a_open`, `build_status`, `is_build_order`
) VALUES (
    2, 2, 'TEST_VCODE_2', 0, '测试外部券码2',
    1, 0, NOW(), NOW(),
    0, 0, 0
);
