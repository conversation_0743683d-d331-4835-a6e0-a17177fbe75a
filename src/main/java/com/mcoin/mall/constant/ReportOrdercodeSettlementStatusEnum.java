package com.mcoin.mall.constant;

import lombok.Getter;

/**
 * 订单结算状态枚举
 */
@Getter
public enum ReportOrdercodeSettlementStatusEnum {

    /**
     * 未结算
     */
    UNSETTLED("0", "未结算"),
    
    /**
     * 已结算
     */
    SETTLED("1", "已结算"),
    
    /**
     * 不结算
     */
    NO_SETTLEMENT("2", "不结算"),
    ;

    /**
     * 状态值
     */
    private final String value;

    /**
     * 描述
     */
    private final String desc;

    ReportOrdercodeSettlementStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}