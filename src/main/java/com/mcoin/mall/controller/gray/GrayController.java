package com.mcoin.mall.controller.gray;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.model.GrayRequest;
import com.mcoin.mall.model.GrayResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.service.gray.GrayService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class GrayController {

    @Resource
    private GrayService grayService;

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;

    /**
     * 灰度
     *
     * @param request 灰度请求
     * @return 灰度响应
     */
    @PostMapping(value = "/gray", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<GrayResponse> gray(@Valid @RequestBody GrayRequest request) {
        // 设置客户ID
        request.setCustId(getCustId());
        
        GrayResponse response = grayService.checkGray(request);
        return Responses.ok(response);
    }

    /**
     * 获取客户ID
     *
     * @return 客户ID
     */
    private String getCustId() {
        FookMacaupassUser fookMacaupassUser = fookMacaupassUserDao.getFirstMpayUserByUserId(contextHolder.getAuthUserInfo().getUserId());
        return fookMacaupassUser.getCustomid();
    }
}
