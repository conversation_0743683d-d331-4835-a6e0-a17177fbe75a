<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookExternalVcodeDao">
    <update id="updateByCode" parameterType="com.mcoin.mall.bean.FookExternalVcode">
        update fook_external_vcode
        set
        `status` = #{status,jdbcType=BIT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where vcode = #{code,jdbcType=VARCHAR}
        and isascription = 1
    </update>
    <select id="getUnGenVcode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_external_vcode
        WHERE build_status = 0
        AND is_build_order = 1
        AND created_at BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>
    <update id="updateBuildStatus">
        update fook_external_vcode
        set
        build_status = #{newBuildStatus,jdbcType=INTEGER}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
        <if test="buildStatus != null">
            and build_status = #{buildStatus,jdbcType=INTEGER}
        </if>
    </update>
    <update id="updateStatusToExecuted">
        update fook_external_vcode
        set
        isascription = 1,
        build_status = 2
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>