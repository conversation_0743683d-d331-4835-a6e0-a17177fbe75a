package com.mcoin.mall.service.auth.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookMacaupassToken;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.model.MPayAppTokenResponse;
import com.mcoin.mall.client.model.MPayTicketResponse;
import com.mcoin.mall.dao.FookMacaupassTokenDao;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class AuthCacheServiceImplTest{

    @InjectMocks
    private AuthCacheServiceImpl authCacheService;

    private FookMacaupassToken macaupassToken;

    @Mock
    private MPayClient mPayClient;

    @Mock
    private FookMacaupassTokenDao fookMacaupassTokenDao;

    @BeforeEach
    public void setUp() {
        macaupassToken = new FookMacaupassToken();
        macaupassToken.setType("apptoken");
        macaupassToken.setToken("testToken");
        macaupassToken.setCreateTime(new Date());
        macaupassToken.setExpiresIn("3600");
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithEmptyToken() {
        macaupassToken.setToken("");
    
        when(fookMacaupassTokenDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(macaupassToken);
    
        String appTicket = authCacheService.saveAndGetMacaupassTicketPhp();
    
        assertTrue("".equals(appTicket));
        verify(fookMacaupassTokenDao, times(2)).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithNullToken() {
        macaupassToken.setToken(null);
    
        when(fookMacaupassTokenDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(macaupassToken);
    
        String appTicket = authCacheService.saveAndGetMacaupassTicketPhp();
    
        assertNull(appTicket);
        verify(fookMacaupassTokenDao, times(2)).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithToken() {
        when(fookMacaupassTokenDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(macaupassToken);
    
        String appTicket = authCacheService.saveAndGetMacaupassTicketPhp();
    
        assertNotNull(appTicket);
        assertEquals("testToken", appTicket);
        verify(fookMacaupassTokenDao, times(2)).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithoutToken() throws BlockException {
        when(fookMacaupassTokenDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);
    
        MPayAppTokenResponse appTokenResponse = new MPayAppTokenResponse();
        appTokenResponse.setAppToken("newToken");
        appTokenResponse.setExpiresIn(3600);
        String appTokenResponseJson = JSONObject.toJSONString(appTokenResponse);
    
        MPayTicketResponse ticketResponse = new MPayTicketResponse();
        ticketResponse.setTicket("newTicket");
        ticketResponse.setExpiresIn(3600);
        String ticketResponseJson = JSONObject.toJSONString(ticketResponse);
    
        when(mPayClient.getAppToken()).thenReturn(appTokenResponseJson);
        when(mPayClient.getTicket(anyString())).thenReturn(ticketResponseJson);
    
        String appTicket = authCacheService.saveAndGetMacaupassTicketPhp();
    
        assertNotNull(appTicket);
        assertEquals("newTicket", appTicket);
        verify(fookMacaupassTokenDao, times(4)).selectOne(any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, times(2)).insert(any(FookMacaupassToken.class));
    }

    @Test
    public void testProcessAppTokenWithEmptyToken() {
        String type = "apptoken";
        String apptoken = "";
        String expiresIn = "3600";
    
        authCacheService.processAppToken(type, apptoken, expiresIn);
    
        verify(fookMacaupassTokenDao, never()).update(any(FookMacaupassToken.class), any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, never()).insert(any(FookMacaupassToken.class));
    }

    @Test
    public void testProcessAppTokenWithEmptyExpiresIn() {
        String type = "apptoken";
        String apptoken = "newToken";
        String expiresIn = "";
    
        authCacheService.processAppToken(type, apptoken, expiresIn);
    
        verify(fookMacaupassTokenDao, never()).update(any(FookMacaupassToken.class), any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, never()).insert(any(FookMacaupassToken.class));
    }

    @Test
    public void testProcessAppTokenWithExistingToken() {
        String type = "apptoken";
        String apptoken = "newToken";
        String expiresIn = "600";
    
        when(fookMacaupassTokenDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(macaupassToken);
    
        assertDoesNotThrow(() -> authCacheService.processAppToken(type, apptoken, expiresIn));
    
        verify(fookMacaupassTokenDao, times(1)).selectOne(any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, times(1)).update(any(FookMacaupassToken.class), any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, never()).insert(any(FookMacaupassToken.class));
    }

    @Test
    public void testProcessAppTokenWithNewToken() {
        String type = "apptoken";
        String apptoken = "newToken";
        String expiresIn = "600";
    
        when(fookMacaupassTokenDao.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);
    
        assertDoesNotThrow(() -> authCacheService.processAppToken(type, apptoken, expiresIn));
    
        verify(fookMacaupassTokenDao, times(1)).selectOne(any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, never()).update(any(FookMacaupassToken.class), any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, times(1)).insert(any(FookMacaupassToken.class));
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithInvalidToken() throws Exception {
        // Mock the response from mPayClient
        String appTokenResp = "";
    
        when(mPayClient.getAppToken()).thenReturn(appTokenResp);
    
        String result = authCacheService.saveAndGetMacaupassTicket(true);
    
        assertNull(result);
        verify(mPayClient, times(1)).getAppToken();
        verify(fookMacaupassTokenDao, times(0)).insert(any(FookMacaupassToken.class));
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithInvalidTicket() throws Exception {
        // Mock the response from mPayClient
        String appTokenResp = "{\"expiresIn\": 7200, \"appToken\": \"testAppToken\"}";
        String ticketResp = "";
    
        when(mPayClient.getAppToken()).thenReturn(appTokenResp);
        when(mPayClient.getTicket("testAppToken")).thenReturn(ticketResp);
    
        String result = authCacheService.saveAndGetMacaupassTicket(true);
    
        assertNull(result);
        verify(mPayClient, times(1)).getAppToken();
        verify(mPayClient, times(1)).getTicket("testAppToken");
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithErrorCode() throws Exception {
        // Mock the response from mPayClient
        String appTokenResp = "{\"expiresIn\": 7200, \"appToken\": \"testAppToken\"}";
        String ticketResp = "{\"expiresIn\": 7200, \"ticket\": \"testTicket\", \"errcode\": 98}";
    
        when(mPayClient.getAppToken()).thenReturn(appTokenResp);
        when(mPayClient.getTicket("testAppToken")).thenReturn(ticketResp);
    
        String result = authCacheService.saveAndGetMacaupassTicket(true);
    
        assertNull( result);
        verify(mPayClient, times(2)).getAppToken();
        verify(mPayClient, times(2)).getTicket("testAppToken");
    }

    @Test
    public void testSaveAndGetMacaupassTicketPhpWithEmptyToken1() throws Exception {
        // Mock the response from mPayClient
        String appTokenResp = "{\"expiresIn\": 7200, \"appToken\": \"testAppToken\"}";
        String ticketResp = "{\"expiresIn\": 7200, \"ticket\": \"testTicket\", \"errcode\": 0}";
    
        when(mPayClient.getAppToken()).thenReturn(appTokenResp);
        when(mPayClient.getTicket("testAppToken")).thenReturn(ticketResp);
    
        String result = authCacheService.saveAndGetMacaupassTicket(true);
    
        assertEquals("testTicket", result);
        verify(mPayClient, times(1)).getAppToken();
        verify(mPayClient, times(1)).getTicket("testAppToken");
        verify(fookMacaupassTokenDao, times(2)).selectOne(any(LambdaQueryWrapper.class));
        verify(fookMacaupassTokenDao, times(2)).insert(any(FookMacaupassToken.class));
    }

}