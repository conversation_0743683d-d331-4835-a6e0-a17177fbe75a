package com.mcoin.mall.service.common.impl;

import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.service.common.StockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;

@Service
public class StockServiceImpl implements StockService {

    @Autowired
    private FookBusinessProductDao productDao;

    @Override
    public boolean isSlodOut(FookBusinessProduct product) {
        if (product == null) {
            return true;
        }
        // 锁定的库存不参与售罄的状态判断 defaultIfNull(product.getLockStock(), 0) 
        return defaultIfNull(product.getStock(), 0) <= 0;
    }

    @Override
    public boolean isSlodOut(Integer productId) {
        if (productId == null) {
            return true;
        }
        FookBusinessProduct product = productDao.selectByPrimary<PERSON>ey(productId);
        return isSlodOut(product);
    }
}