package com.mcoin.mall.controller.job;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.service.job.MpayAuthJobService;

@RestController
@RequestMapping("/api/job/auth")
public class MpayAuthJobController {
    @Resource
    private MpayAuthJobService mpayAuthJobService;

    /**
     * 每分鐘檢測一次，若token更新时间已过6000秒（100分钟）则刷新支付appToken
     * PHP(refreshtoken:update)
     * @return
     */
    @PostMapping(value = "/refreshToken")
    public Response<String> refreshToken() {
    	mpayAuthJobService.updRefreshToken();
        return Responses.ok("success");
    }
}
