package com.mcoin.mall.constant;


import lombok.Getter;

/***
 * 福利类型
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date 2023-10-20
 */
@Getter
public enum BusinessProductTypeEnum {

    /**
     * 福利产品类型枚举
     */
    SIGNATURE_PRODUCT(1, "招牌產品"),
    NEW_OFFERS(2, "新優惠"),
    CASH_COUPONS(3, "現金券"),
    ONE_DISCOUNT(4, "1蚊優惠"),
    POINTS_REDEMPTION(5, "積分換領"),
    COIN_CASH_COUPON(6, "mCoin現金券"),
    PAY_FULL_DISCOUNT_COUPON(7, "mPay满减券"),
    PAY_VOUCHER(8, "mPay代金券"),
    PAY_COUPON(9, "Mpay優惠券"),
    TRANSACTION_VERIFICATION_COUPON(10, "交易核銷券"),
    THIRD_PARTY_VOUCHER(11, "第三方兑换码"),
    PHYSICAL_LINK(12, "实物链路"),
    ;

    /**
     * 福利类型id
     */
    private final int typeId;

    /**
     * 福利名称
     */
    private final String desc;

    BusinessProductTypeEnum(int typeId, String desc) {
        this.typeId = typeId;
        this.desc = desc;

    }
}
