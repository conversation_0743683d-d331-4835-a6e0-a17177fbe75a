package com.mcoin.mall.controller.auth;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformUserinfo;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformUserinfoDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.ConfigMPayJSApiRequest;
import com.mcoin.mall.model.CouponLoginRequest;
import com.mcoin.mall.model.CouponLoginResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.TokenManager;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.auth.MPayAuthService;
import com.mcoin.mall.service.chennel.McoinChannelService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

/**
 * 集成测试 MPayAuthController
 * 使用真实服务和数据库，测试认证相关功能
 */
@Slf4j
class MPayAuthControllerTest extends BaseUnitTest {

    @Resource
    private MPayAuthController mPayAuthController;
    
    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;
    
    @Resource
    private FookPlatformUserinfoDao fookPlatformUserinfoDao;
    
    @Resource
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Resource
    private TokenManager tokenManager;
    
    @SpyBean
    private ContextHolder contextHolder;

    @MockBean
    private McoinChannelService mcoinChannelService;
    
    @MockBean
    private MPayAuthService mPayAuthService;
    
    private static final String TEST_CUSTOM_ID = "test_customid_integration";
    private static final String TEST_ORDER_NO = "test_order_no_integration";
    private static final int TEST_USER_ID = 99999;
    
    @BeforeEach
    void setUp() {
        // 清理可能存在的测试数据
        fookMacaupassUserDao.delete(new LambdaQueryWrapper<FookMacaupassUser>()
                .eq(FookMacaupassUser::getCustomid, TEST_CUSTOM_ID));
        fookPlatformOrderDao.delete(new LambdaQueryWrapper<FookPlatformOrder>()
                .eq(FookPlatformOrder::getOrderNo, TEST_ORDER_NO));
        fookPlatformUserinfoDao.delete(new LambdaQueryWrapper<FookPlatformUserinfo>()
                .eq(FookPlatformUserinfo::getId, TEST_USER_ID));
        
        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");
        
        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();
        
        // 准备mPayAuthService响应模拟
        CouponLoginResponse couponLoginResponse = new CouponLoginResponse();
        couponLoginResponse.setToken("test_jwt_token");
        when(mPayAuthService.couponLogin(any(CouponLoginRequest.class))).thenReturn(couponLoginResponse);
        
        // 准备测试数据
        FookPlatformUserinfo userinfo = new FookPlatformUserinfo();
        userinfo.setId(TEST_USER_ID);
        userinfo.setNickName("Test User");
        fookPlatformUserinfoDao.insert(userinfo);
        
        FookMacaupassUser macaupassUser = new FookMacaupassUser();
        macaupassUser.setCustomid(TEST_CUSTOM_ID);
        macaupassUser.setUserId(TEST_USER_ID);
        macaupassUser.setOpenid("test_openid");
        macaupassUser.setAccessToken("test_access_token");
        macaupassUser.setRefreshToken("test_refresh_token");
        macaupassUser.setUpdateTime(new Date());
        macaupassUser.setPoint(100);
        macaupassUser.setStatus(1);
        macaupassUser.setPhone("12345678901"); // 添加电话信息，可能是必需的字段
        macaupassUser.setCode("test_code"); // 添加验证码信息，可能是必需的字段
        fookMacaupassUserDao.insert(macaupassUser);
        
        FookPlatformOrder order = new FookPlatformOrder();
        order.setOrderNo(TEST_ORDER_NO);
        order.setUserid(TEST_USER_ID);
        // 设置其他必需的订单字段
        order.setStatus(1);
        fookPlatformOrderDao.insert(order);
    }

    @Test
    void testCouponLogin() {
        // 创建请求对象
        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid(TEST_CUSTOM_ID);
        request.setOrder_no(TEST_ORDER_NO);

        // 直接调用控制器方法
        Response<?> response = mPayAuthController.couponLogin(request);
        
        // 验证响应结构
        assertNotNull(response);
        assertEquals(200, response.getCode());
    }

    @Test
    void testGetUserInfo() {
        try {
            // 确保已经设置了安全上下文
            assertNotNull(SecurityContextHolder.getContext().getAuthentication());
            assertNotNull(contextHolder.getAuthUserInfo());
            
            // 直接调用控制器方法
            Response<?> response = mPayAuthController.getUserInfo();
            
            // 验证响应结构
            assertNotNull(response);
        } catch (Exception e) {
            // 打印详细异常堆栈，帮助诊断问题
            log.error("Exception in testGetUserInfo: " + e.getMessage(), e);
            throw e;
        }
    }

    @Test
    void testConfigJSApi() {
        // 创建请求对象
        ConfigMPayJSApiRequest request = new ConfigMPayJSApiRequest();
        request.setUrl("https://test.example.com");

        // 直接调用控制器方法
        Response<?> response = mPayAuthController.configJSApi(request);

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testRefreshToken() {
        try {
            // 确保已经设置了安全上下文
            assertNotNull(SecurityContextHolder.getContext().getAuthentication());
            assertNotNull(contextHolder.getAuthUserInfo());
            
            // 直接调用控制器方法
            Response<?> response = mPayAuthController.refreshToken();
            
            // 验证响应结构
            assertNotNull(response);
            assertEquals(200, response.getCode());
        } catch (Exception e) {
            // 打印详细异常堆栈，帮助诊断问题
            log.error("Exception in testRefreshToken: " + e.getMessage(), e);
            throw e;
        }
    }
    
    // 异常情况测试
    @Test
    void testCouponLoginWithInvalidUser() {
        // 创建请求对象
        CouponLoginRequest request = new CouponLoginRequest();
        request.setCustomid("non_existent_user");
        request.setOrder_no(TEST_ORDER_NO);

        // 模拟抛出业务异常
        when(mPayAuthService.couponLogin(any(CouponLoginRequest.class)))
            .thenThrow(new BusinessException(Response.Code.BAD_REQUEST, "用户不存在"));

        // 验证抛出预期的业务异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> mPayAuthController.couponLogin(request));
        assertEquals("用户不存在", exception.getMessage());
    }
} 