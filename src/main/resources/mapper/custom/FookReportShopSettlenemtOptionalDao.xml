<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportShopSettlenemtOptionalDao">

  <insert id="replaceInto" parameterType="com.mcoin.mall.bean.FookReportShopSettlenemtOptional">
    replace into fook_report_shop_settlenemt_optional
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="optionalid != null">
        optionalid,
      </if>
      <if test="sellerId != null">
        seller_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="bank != null">
        bank,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="settlementAmount != null">
        settlement_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="momecoins != null">
        momecoins,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="optionalid != null">
        #{optionalid,jdbcType=INTEGER},
      </if>
      <if test="sellerId != null">
        #{sellerId,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=INTEGER},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmount != null">
        #{settlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="momecoins != null">
        #{momecoins,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>