package com.mcoin.mall.service.order.impl;

import org.mockito.junit.jupiter.MockitoExtension;
import com.mcoin.mall.mapping.OrderEnvMapping;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import com.mcoin.mall.bean.FookOrderEnv;
import com.mcoin.mall.dao.FookOrderEnvDao;
import org.mockito.InjectMocks;
import com.mcoin.mall.model.OrderEnvInfo;
import org.junit.jupiter.api.Test;
import com.mcoin.mall.mq.model.OrderEnvMessage;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
public class OrderEnvServiceImplTest{

    private FookOrderEnv fookOrderEnv;

    @Mock
    private OrderEnvMapping orderEnvMapping;

    @Mock
    private FookOrderEnvDao fookOrderEnvDao;

    @InjectMocks
    private OrderEnvServiceImpl orderEnvService;

    @Test
    public void testSave_OrderEnv_01() {
        OrderEnvMessage message = new OrderEnvMessage();
        message.setOrderId(12345);

        OrderEnvInfo orderEnvInfo = new OrderEnvInfo();
        orderEnvInfo.setSource("source_value");
        message.setOrderEnvInfo(orderEnvInfo);

        fookOrderEnv = new FookOrderEnv();
        fookOrderEnv.setOrderId(12345);
        fookOrderEnv.setSource("source_value");

        Mockito.when(orderEnvMapping.toOrderEnv(any(OrderEnvInfo.class))).thenReturn(fookOrderEnv);
        Mockito.when(fookOrderEnvDao.insertSelective(any(FookOrderEnv.class))).thenReturn(1);
        int result = orderEnvService.saveOrderEnv(message);
        assertEquals(1, result);
        assertEquals("source", fookOrderEnv.getChannel());
    
        // Verify that the correct methods were called
        Mockito.verify(orderEnvMapping).toOrderEnv(any(OrderEnvInfo.class));
        Mockito.verify(fookOrderEnvDao).insertSelective(any(FookOrderEnv.class));
    }


    @Test
    public void testSave_OrderEnv_02() {
        OrderEnvMessage message = new OrderEnvMessage();
        message.setOrderId(12345);

        OrderEnvInfo orderEnvInfo = new OrderEnvInfo();
        orderEnvInfo.setSource("source");
        message.setOrderEnvInfo(orderEnvInfo);

        fookOrderEnv = new FookOrderEnv();
        fookOrderEnv.setOrderId(12345);
        fookOrderEnv.setSource("source");

        Mockito.when(orderEnvMapping.toOrderEnv(any(OrderEnvInfo.class))).thenReturn(fookOrderEnv);
        Mockito.when(fookOrderEnvDao.insertSelective(any(FookOrderEnv.class))).thenReturn(1);
        int result = orderEnvService.saveOrderEnv(message);
        assertEquals(1, result);
        assertEquals("source", fookOrderEnv.getChannel());

        // Verify that the correct methods were called
        Mockito.verify(orderEnvMapping).toOrderEnv(any(OrderEnvInfo.class));
        Mockito.verify(fookOrderEnvDao).insertSelective(any(FookOrderEnv.class));
    }


    @Test
    public void testSave_OrderEnv_03() {
        OrderEnvMessage message = new OrderEnvMessage();
        message.setOrderId(12345);

        OrderEnvInfo orderEnvInfo = new OrderEnvInfo();
        orderEnvInfo.setSource("source_1😊_2");
        message.setOrderEnvInfo(orderEnvInfo);

        fookOrderEnv = new FookOrderEnv();
        fookOrderEnv.setOrderId(12345);
        fookOrderEnv.setSource("source_1😊_2");

        Mockito.when(orderEnvMapping.toOrderEnv(any(OrderEnvInfo.class))).thenReturn(fookOrderEnv);
        Mockito.when(fookOrderEnvDao.insertSelective(any(FookOrderEnv.class))).thenReturn(1);
        int result = orderEnvService.saveOrderEnv(message);
        assertEquals(1, result);
        assertEquals("source", fookOrderEnv.getChannel());

        // Verify that the correct methods were called
        Mockito.verify(orderEnvMapping).toOrderEnv(any(OrderEnvInfo.class));
        Mockito.verify(fookOrderEnvDao).insertSelective(any(FookOrderEnv.class));
    }
}