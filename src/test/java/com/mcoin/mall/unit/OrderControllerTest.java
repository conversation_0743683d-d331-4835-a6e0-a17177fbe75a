package com.mcoin.mall.unit;

import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.controller.order.OrderController;
import com.mcoin.mall.model.OrderDetailRequest;
import com.mcoin.mall.model.OrderDetailResponse;
import com.mcoin.mall.model.RefundCodeRequest;
import com.mcoin.mall.model.RefundCodeResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.order.OrderCodeRedeemService;
import com.mcoin.mall.service.order.OrderService;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.ArrayList;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderControllerTest extends ControllerMock {

    @Mock
    private OrderService orderService;

    @InjectMocks
    private OrderController orderController;

    @Override
    @BeforeEach
    public void setup() {
        super.setup();
    }

    @Test
    void testOrderDetail() {
        // given
        when(orderService.getOrderDetail(any())).thenReturn(new OrderDetailResponse());
        
        // when
        OrderDetailRequest req = JsonTestUtils.getObjectFromJson("json/request/orderDetailRequest.json", "singleObj", OrderDetailRequest.class);
        Response<OrderDetailResponse> detail = orderController.orderDetail(req);
        
        // then
        assertEquals(1, detail.getStatus());
    }

    @Test
    void testOrderRefundCode() {
        // given
        RefundCodeResponse refundResponse = new RefundCodeResponse();
        refundResponse.setStores(new ArrayList<>());
        when(orderService.getRefundCode(any())).thenReturn(refundResponse);
        
        // when
        RefundCodeRequest req = new RefundCodeRequest();
        req.setCodeId("3950896");
        Response<RefundCodeResponse> detail = orderController.getRefundCode(req);
        
        // then
        assertEquals(1, detail.getStatus());
        assertEquals(0, detail.getData().getStores().size());
    }
    
    private UserInfo createUserInfo(int userId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        return userInfo;
    }
}