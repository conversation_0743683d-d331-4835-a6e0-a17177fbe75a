package com.mcoin.mall.util;

import net.lingala.zip4j.ZipFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.nio.file.Files;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import static org.junit.jupiter.api.Assertions.*;

public class ZipUtilsTest{

    private File file1;

    private File file2;

    private File tempDir;

    private File zipFile;

    @AfterEach
    void tearDown() {
        // Clean up temp files
        if (zipFile.exists()) {
            zipFile.delete();
        }
        if (file1.exists()) {
            file1.delete();
        }
        if (file2.exists()) {
            file2.delete();
        }
        if (tempDir.exists()) {
            tempDir.delete();
        }
    }

    @Test
    void testCompress() throws IOException {
        List<File> srcFiles = Arrays.asList(file1, file2);
        String password = "password";
    
        ZipUtils.compress(zipFile, srcFiles, password);
    
        assertTrue(zipFile.exists(), "Zip file should exist after compression");
        assertTrue(zipFile.length() > 0, "Zip file should not be empty after compression");
    
        // Verify the contents of the zip file
        try (ZipFile zip = new ZipFile(zipFile, password.toCharArray())) {
            assertEquals(2, zip.getFileHeaders().size(), "Zip file should contain 2 entries");
        }
    }

    @BeforeEach
    void setUp() throws IOException {
        tempDir = Files.createTempDirectory("testZipUtils").toFile();
        zipFile = new File(tempDir, "test.zip");
        file1 = new File(tempDir, "file1.txt");
        file2 = new File(tempDir, "file2.txt");
    
        // Create some test files
        Files.write(file1.toPath(), "This is file 1".getBytes());
        Files.write(file2.toPath(), "This is file 2".getBytes());
    }

}