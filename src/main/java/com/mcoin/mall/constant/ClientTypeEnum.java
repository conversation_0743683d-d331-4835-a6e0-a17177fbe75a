package com.mcoin.mall.constant;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
public enum ClientTypeEnum {
    // 鸿蒙设备
    HARMONY,
    // iOS设备
    IOS,
    // 安卓设备
    ANDROID,
    // 未知设备
    UNKNOWN;

    /**
     * 根据字符串返回枚举类型
     *
     * @param clientType 字符串类型
     * @return 对应的枚举对象，如果没有匹配，则返回 UNKNOWN
     */
    public static ClientTypeEnum fromString(String clientType) {
        if (clientType == null) {
            return UNKNOWN;
        }
        switch (clientType.toUpperCase()) {
            case "HARMONY":
                return HARMONY;
            case "IOS":
                return IOS;
            case "ANDROID":
                return ANDROID;
            default:
                return UNKNOWN;
        }
    }
}
