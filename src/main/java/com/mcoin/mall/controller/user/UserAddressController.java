package com.mcoin.mall.controller.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.client.MiniUserAddressClient;
import com.mcoin.mall.client.MpMcoinMallManagementClient;
import com.mcoin.mall.client.model.MiniOrdersHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressDetailHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressEditHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressLocationHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressResponse;
import com.mcoin.mall.client.model.common.MiniProgramCommonResponse;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.mapping.MiniMapping;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.UserAddressAddRequest;
import com.mcoin.mall.model.UserAddressListResponse;
import com.mcoin.mall.model.UserAddressLocationRequest;
import com.mcoin.mall.model.UserAddressRequest;
import com.mcoin.mall.model.UserAddressResponse;
import com.mcoin.mall.model.UserAddressSetRequest;
import com.mcoin.mall.service.amap.AmapService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.mcoin.mall.util.SentinelUtils.handleBlockException;

@RestController
@Slf4j
public class UserAddressController {

    @Resource
    private AmapService amapService;

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private MiniUserAddressClient miniUserAddressClient;

    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;

    @Resource
    private MpMcoinMallManagementClient mpMcoinMallManagementClient;

    @Resource
    private MessageSource messageSource;

    @GetMapping(value = "/api/mini/userAddress/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<UserAddressResponse> detail(@Valid UserAddressRequest request) {
        MiniUserAddressDetailHttpRequest req = new MiniUserAddressDetailHttpRequest();
        req.setId(request.getId());
        req.setCustId(getCustId());
        req.setSignature(SignUtil.phpSign(ConfigUtils.getProperty("macaupass.coupon.secret"), req));
        log.info("userAddress detail param:{}", JSON.toJSONString(req));
        String detail;
        if (Boolean.parseBoolean(ConfigUtils.getProperty("address.switch", Boolean.TRUE.toString()))) {
           detail = handleBlockException(()->mpMcoinMallManagementClient.detail(req), null);
        } else {
           detail = handleBlockException(()->miniUserAddressClient.detail(req), null);
        }
        log.info("userAddress detail result:{}", detail);
        if (StringUtils.isBlank(detail)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        MiniProgramCommonResponse<MiniUserAddressResponse> response = JSON.parseObject(detail, new TypeReference<MiniProgramCommonResponse<MiniUserAddressResponse>>() {
        });
        return Responses.ok(MiniMapping.INSTANCE.toUserAddressResponse(response.getData()));
    }

    @PostMapping(value = "/api/mini/userAddress/add", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Object> add(@Valid @RequestBody UserAddressAddRequest request) {
        Locale locale = contextHolder.getLocale();
        MiniUserAddressEditHttpRequest req = MiniMapping.INSTANCE.toUserEditReq(request);
        req.setCustId(getCustId());
        req.setSignature(SignUtil.phpSign(ConfigUtils.getProperty("macaupass.coupon.secret"), req));
        log.info("userAddress add param:{}", JSON.toJSONString(req));
        String detail;
        if (Boolean.parseBoolean(ConfigUtils.getProperty("address.switch", Boolean.TRUE.toString()))) {
            detail =  handleBlockException(()->mpMcoinMallManagementClient.add(req), null);
        } else {
            detail = handleBlockException(()->miniUserAddressClient.add(req), null);
        }
        log.info("userAddress add result:{}", detail);
        if (StringUtils.isBlank(detail)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        MiniProgramCommonResponse<Object> response = JSON.parseObject(detail, new TypeReference<MiniProgramCommonResponse<Object>>() {
        });
        if (10001 == response.getCode()) {
            String message = messageSource.getMessage("message.address.limit", null, locale);
            throw new BusinessException(Response.Code.UA_ADD_LIMIT, message);
        } else if (10002 == response.getCode()) {
            String message = messageSource.getMessage("message.address.phone_verification", null, locale);
            throw new BusinessException(Response.Code.UA_PHONE, message);
        } else if (1 != response.getCode()) {
            throw new BusinessException(Response.Code.BAD_REQUEST, response.getMsg());
        }
        return Responses.ok("success");
    }

    @PostMapping(value = "/api/mini/userAddress/edit", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Object> edit(@Valid @RequestBody UserAddressAddRequest request) {
        Locale locale = contextHolder.getLocale();
        MiniUserAddressEditHttpRequest req = MiniMapping.INSTANCE.toUserEditReq(request);
        req.setCustId(getCustId());
        req.setSignature(SignUtil.phpSign(ConfigUtils.getProperty("macaupass.coupon.secret"), req));
        log.info("userAddress edit param:{}", JSON.toJSONString(req));
        String detail;
        if (Boolean.parseBoolean(ConfigUtils.getProperty("address.switch", Boolean.TRUE.toString()))) {
            detail =  handleBlockException(()->mpMcoinMallManagementClient.edit(req), null);
        } else {
            detail = handleBlockException(()->miniUserAddressClient.edit(req), null);
        }
        log.info("userAddress edit result:{}", detail);
        if (StringUtils.isBlank(detail)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        MiniProgramCommonResponse<Object> response = JSON.parseObject(detail, new TypeReference<MiniProgramCommonResponse<Object>>() {
        });
        if (10002 == response.getCode()) {
            String message = messageSource.getMessage("message.address.phone_verification", null, locale);
            throw new BusinessException(Response.Code.UA_PHONE, message);
        } else if (1 != response.getCode()) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, response.getMsg());
        }
        return Responses.ok("success");
    }

    @GetMapping(value = "/api/mini/userAddress/lists", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<UserAddressListResponse> list() {
        MiniOrdersHttpRequest req = new MiniOrdersHttpRequest();
        req.setCustId(getCustId());
        req.setSignature(SignUtil.phpSign(ConfigUtils.getProperty("macaupass.coupon.secret"), req));
        log.info("userAddress lists param:{}", JSON.toJSONString(req));

        String detail;
        if (Boolean.parseBoolean(ConfigUtils.getProperty("address.switch", Boolean.TRUE.toString()))) {
            detail =  handleBlockException(()->mpMcoinMallManagementClient.lists(req), null);
        } else {
            detail = handleBlockException(()->miniUserAddressClient.lists(req), null);
        }
        log.info("userAddress lists result:{}", detail);
        if (StringUtils.isBlank(detail)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        MiniProgramCommonResponse<List<MiniUserAddressResponse>> response = JSON.parseObject(detail, new TypeReference<MiniProgramCommonResponse<List<MiniUserAddressResponse>>>() {
        });
        if (1 != response.getCode()) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, response.getMsg());
        }
        UserAddressListResponse res = new UserAddressListResponse();
        res.setSnatchList(MiniMapping.INSTANCE.toUserAddressResponses(response.getData()));
        return Responses.ok(res);
    }

    @PostMapping(value = "/api/mini/userAddress/setDefault", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Object> setDefault(@Valid @RequestBody UserAddressSetRequest request) {
        MiniUserAddressDetailHttpRequest req = new MiniUserAddressDetailHttpRequest();
        req.setId(request.getId());
        req.setCustId(getCustId());
        req.setSignature(SignUtil.phpSign(ConfigUtils.getProperty("macaupass.coupon.secret"), req));
        log.info("userAddress setDefault param:{}", JSON.toJSONString(req));

        String detail;
        if (Boolean.parseBoolean(ConfigUtils.getProperty("address.switch", Boolean.TRUE.toString()))) {
            detail =  handleBlockException(()->mpMcoinMallManagementClient.setDefault(req), null);
        } else {
            detail = handleBlockException(()->miniUserAddressClient.setDefault(req), null);
        }
        log.info("userAddress setDefault result:{}", detail);
        if (StringUtils.isBlank(detail)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        MiniProgramCommonResponse<Object> response = JSON.parseObject(detail, new TypeReference<MiniProgramCommonResponse<Object>>() {
        });
        if (1 != response.getCode()) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, response.getMsg());
        }
        return Responses.ok("success");
    }

    @PostMapping(value = "/api/mini/userAddress/del", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Object> del(@Valid @RequestBody UserAddressSetRequest request) {
        MiniUserAddressDetailHttpRequest req = new MiniUserAddressDetailHttpRequest();
        req.setId(request.getId());
        req.setCustId(getCustId());
        req.setSignature(SignUtil.phpSign(ConfigUtils.getProperty("macaupass.coupon.secret"), req));
        log.info("userAddress del param:{}", JSON.toJSONString(req));
        String detail;
        if (Boolean.parseBoolean(ConfigUtils.getProperty("address.switch", Boolean.TRUE.toString()))) {
            detail = handleBlockException(()->mpMcoinMallManagementClient.del(req), null);
        } else {
            detail = handleBlockException(()->miniUserAddressClient.del(req), null);
        }
        log.info("userAddress del result:{}", detail);
        if (StringUtils.isBlank(detail)) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        MiniProgramCommonResponse<Object> response = JSON.parseObject(detail, new TypeReference<MiniProgramCommonResponse<Object>>() {
        });
        if (1 != response.getCode()) {
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, response.getMsg());
        }
        return Responses.ok("success");
    }

    @GetMapping(value = "/api/mini/index/geocoderCoordinate", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Map<String, Object>> geocoderCoordinate(UserAddressLocationRequest request) {
        if (Boolean.parseBoolean(ConfigUtils.getProperty("address.switch", Boolean.TRUE.toString()))) {
            Map<String, Object> stringObjectMap = amapService.geocoderCoordinate(request.getLocation());
            return Responses.ok(stringObjectMap);
        } else {
            MiniUserAddressLocationHttpRequest req = new MiniUserAddressLocationHttpRequest();
            req.setLocation(request.getLocation());
            req.setCustId(getCustId());
            req.setSignature(SignUtil.phpSign(ConfigUtils.getProperty("macaupass.coupon.secret"), req));
            log.info("userAddress geocoderCoordinate param:{}", JSON.toJSONString(req));
            String detail = handleBlockException(()->miniUserAddressClient.geocoderCoordinate(req), null);
            log.info("userAddress geocoderCoordinate result:{}", detail);
            if (StringUtils.isBlank(detail)) {
                throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
            }
            MiniProgramCommonResponse<Map<String, Object>> response = JSON.parseObject(detail, new TypeReference<MiniProgramCommonResponse<Map<String, Object>>>() {
            });
            if (1 != response.getCode()) {
                throw new BusinessException(Response.Code.BAD_REQUEST, response.getMsg());
            }
            return Responses.ok(response.getData());
        }
    }


    private String getCustId() {
        FookMacaupassUser fookMacaupassUser = fookMacaupassUserDao.getFirstMpayUserByUserId(contextHolder.getAuthUserInfo().getUserId());
        return fookMacaupassUser.getCustomid();
    }


}
