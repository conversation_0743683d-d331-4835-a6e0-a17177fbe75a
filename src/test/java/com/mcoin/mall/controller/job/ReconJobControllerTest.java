package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.ReconCheckPayRequest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 ReconJobController
 * 使用真实服务和数据库，测试对账任务相关功能
 */
class ReconJobControllerTest extends BaseUnitTest {

    @Resource
    private ReconJobController reconJobController;

    @Test
    void testCheckPay() {
        // 创建请求对象
        ReconCheckPayRequest request = new ReconCheckPayRequest();
        
        // 直接调用控制器方法
        Response<String> response = reconJobController.checkPay(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 