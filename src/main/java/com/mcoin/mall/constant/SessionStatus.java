package com.mcoin.mall.constant;

import java.util.Date;

public enum SessionStatus {
    SNATCHING(0, "抢购中"),
    SOON(1, "即将开抢"),
    ENDED(2, "已结束");

    private final int code;
    private final String description;

    SessionStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取对应的枚举类型，如果没有匹配项，则返回null
    public static SessionStatus fromCode(int code) {
        for (SessionStatus status : SessionStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 抢购状态: 0-抢购中；1-即将开抢；2-已结束
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer from(Date now, Date startTime, Date endTime){
        if (startTime == null || endTime == null){
            return null;
        }
        if(now.getTime() >= startTime.getTime() && now.getTime() < endTime.getTime()){
            return SessionStatus.SNATCHING.getCode();// 抢购中
        } else if(now.getTime() < startTime.getTime()){
            return SessionStatus.SOON.getCode();// 即将开抢
        } else {
            return SessionStatus.ENDED.getCode();//已结束
        }
    }
}
