package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.SettlementInternalJobRequest;
import com.mcoin.mall.model.job.SettlementInternalReportOrderCodeJobRequest;
import com.mcoin.mall.model.job.SettlementJobRequest;
import com.mcoin.mall.model.job.SettlementOptionalJobRequest;
import com.mcoin.mall.model.job.SettlementOptionalReportSendJobRequest;
import com.mcoin.mall.model.job.SettlementOptionalReportUploadJobRequest;
import com.mcoin.mall.model.job.SettlementReportSendJobRequest;
import com.mcoin.mall.model.job.SettlementReportUploadJobRequest;
import com.mcoin.mall.service.job.SettlementInternalJobService;
import com.mcoin.mall.service.job.SettlementInternalReportOrderCodeJobService;
import com.mcoin.mall.service.job.SettlementJobService;
import com.mcoin.mall.service.job.SettlementOptionalJobService;
import com.mcoin.mall.service.job.SettlementOptionalReportSendJobService;
import com.mcoin.mall.service.job.SettlementOptionalReportUploadJobService;
import com.mcoin.mall.service.job.SettlementReportSendJobService;
import com.mcoin.mall.service.job.SettlementReportUploadJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/settlement")
@Slf4j
public class SettlementJobController {

    @Resource
    private SettlementJobService settlementJobService;
    @Resource
    private SettlementReportUploadJobService settlementReportUploadJobService;
    @Resource
    private SettlementReportSendJobService settlementReportSendJobService;
    @Resource
    private SettlementInternalJobService settlementInternalJobService;
    @Resource
    private SettlementOptionalJobService settlementOptionalJobService;
    @Resource
    private SettlementOptionalReportUploadJobService settlementOptionalReportUploadJobService;
    @Resource
    private SettlementOptionalReportSendJobService settlementOptionalReportSendJobService;
    @Resource
    private SettlementInternalReportOrderCodeJobService settlementInternalReportOrderCodeJobService;

    /**
     * 半个月结算
     * 每月的1號和16號零點結算商家的數據
     *
     * @return
     */
    @PostMapping(value = "/halfMonth")
    public Response<String> halfMonth(@Valid SettlementJobRequest request) {
        settlementJobService.triggerHalfMonthSettlement(request);
        return Responses.ok("success");
    }

    /**
     * 上传结算报表
     *
     * @return
     */
    @PostMapping(value = "/reportUpload")
    public Response<String> reportUpload(@Valid SettlementReportUploadJobRequest request) {
        settlementReportUploadJobService.triggerUpload(request);
        return Responses.ok("success");
    }

    /**
     * 发送结算报表
     *
     * @return
     */
    @PostMapping(value = "/reportSend")
    public Response<String> reportSend(@Valid SettlementReportSendJobRequest request) {
        settlementReportSendJobService.triggerSend(request);
        return Responses.ok("success");
    }

    /**
     * 内部结算
     *
     * @return
     */
    @PostMapping(value = "/internal")
    public Response<String> internal(@Valid SettlementInternalJobRequest request) {
        settlementInternalJobService.triggerSettlement(request);
        return Responses.ok("success");
    }

    /**
     * 可选结算
     * 每天生成商家的選填結算日報表
     *
     * @return
     */
    @PostMapping(value = "/optional")
    public Response<String> optional(@Valid SettlementOptionalJobRequest request) {
        settlementOptionalJobService.triggerSettlement(request);
        return Responses.ok("success");
    }

    /**
     * 内部结算（每天生产核销数据）
     *
     * @return
     */
    @PostMapping(value = "/internal/reportOrderCode")
    public Response<String> internalReportOrderCode(@Valid SettlementInternalReportOrderCodeJobRequest request) {
        settlementInternalReportOrderCodeJobService.doSettlement(request);
        return Responses.ok("success");
    }

    /**
     * 上传可选结算报表
     *
     * @return
     */
    @PostMapping(value = "/optional/reportUpload")
    public Response<String> optionalReportUpload(@Valid SettlementOptionalReportUploadJobRequest request) {
        settlementOptionalReportUploadJobService.triggerUpload(request);
        return Responses.ok("success");
    }

    /**
     * 发送可选结算报表
     *
     * @return
     */
    @PostMapping(value = "/optional/reportSend")
    public Response<String> optionalReportSend(@Valid SettlementOptionalReportSendJobRequest request) {
        settlementOptionalReportSendJobService.triggerSend(request);
        return Responses.ok("success");
    }

    /**
     * Mpay澳門通訂單結算報表
     * 日结，月结数据生成
     * 1. 插入fook_report_mpaycoin
     * 2. 插入fook_report_mpaycoin_ordercode
     * 从PHP 代码settlement:mpay迁移
     */
    @PostMapping(value = "/sumMcoinOrderInMpay")
    public Response<String> sumMcoinOrderInMpay(@Valid SettlementJobRequest request) {
        settlementJobService.sumMcoinOrderInMpay(request);
        return Responses.ok("success");
    }

    /**
     * Mpay澳門通訂單結算報表
     * 1. 插入fook_report_mpaycoin
     * 2. 插入fook_report_mpaycoin_ordercode
     * 从PHP 代码 settlement:yearmpay迁移
     */
    @PostMapping(value = "/sumYearMcoinOrderInMpay")
    public Response<String> sumYearMcoinOrderInMpay(@Valid SettlementJobRequest request) {
        settlementJobService.sumYearMcoinOrderInMpay(request);
        return Responses.ok("success");
    }

    /**
     * 澳門通優惠券核銷報表
     * 1. 插入fook_report_mpaycoin_code
     * 2. 插入fook_report_mpaycoin_codelist
     * 3. 更新fook_report_mpaycoin_code的sumCode为fook_report_mpaycoin_codelist的数量
     * 从PHP 代码settlement:mpaycode迁移
     */
    @PostMapping(value = "/sumSubmitedMcoinCodeInMpay")
    public Response<String> sumSubmitedMcoinCodeInMpay(@Valid SettlementJobRequest request) {
        settlementJobService.sumSubmitedMcoinCodeInMpay(request);
        return Responses.ok("success");
    }

    /**
     * 商家結算報表sumBusinessData
     * 1. 插入fook_report_business
     * 2. 插入fook_report_business_data
     * 下半上半月结报表
     * 从PHP 代码settlement:reportbusiness迁移
     */
        @PostMapping(value = "/sumBusinessData")
    public Response<String> sumBusinessData(@Valid SettlementJobRequest request) {
        settlementJobService.sumBusinessData(request);
        return Responses.ok("success");
    }


    /**
     * 订单统计結算報表
     * 1. 插入fook_order_settlement
     * 2. 插入fook_order_settlement_data
     * 从PHP 代码settlement:reportorder迁移
     */
    @PostMapping(value = "/sumOrderSettlementData")
    public Response<String> sumOrderSettlementData(@Valid SettlementJobRequest request) {
        settlementJobService.sumOrderSettlementData(request);
        return Responses.ok("success");
    }

    /**
     * 澳門通mCoin訂單報表
     * 1.插入fook_report_mpaycoin_all
     * 2.插入支付的fook_report_mpaycoin_ordercode_all
     * 3.插入退款的fook_report_mpaycoin_ordercode_all
     *
     * settlement:mpayall
     */
    @PostMapping(value = "/reportAllOrderSettlement")
    public Response<String> sumAllOrderSettlement(@Valid SettlementJobRequest request) {
        settlementJobService.sumAllOrderSettlement(request);
        return Responses.ok("success");
    }

    /**
     * 派券數據統計報表
     * 1.插入fook_voucher_settlement
     * 2.插入fook_voucher_settlement_data
     * settlement:voucher
     *
     */
    @PostMapping(value = "/settlementVoucher")
    public Response<String> settlementVoucher(@Valid SettlementJobRequest request) {
        settlementJobService.settlementVoucher(request);
        return Responses.ok("success");
    }
}
