<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookProductSnappingRecordDao">
  <select id="sumSnappingRecordNumber" resultType="int">
    select sum(`number`)
    from fook_product_snapping_record
    where product_id = #{productId,jdbcType=INTEGER}
    and user_id= #{userId,jdbcType=INTEGER}
    and `status` in(1,2)
    and order_id is not null
    <choose>
      <when test="sessionId != null">
        and session_id = #{sessionId,jdbcType=INTEGER}
      </when>
      <otherwise>
        and session_id is null
      </otherwise>
    </choose>
  </select>
</mapper>