package com.mcoin.mall.constant;


import lombok.Getter;

/***
 * 兑换码生成方式枚举
 * @author: la<PERSON><PERSON><PERSON>
 * @date 2023-10-20
 */
@Getter
public enum BusinessProductIsExportEnum {

    /**
     * 兑换码生成方式枚举
     */
    MANUAL_IMPORT(1, "人工导入"),
    SYSTEM_GENERATED(2, "系统生成"),
    ;

    /**
     * 码生成方式
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    BusinessProductIsExportEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;

    }

}
