package com.mcoin.mall.unit;

import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.controller.user.UserController;
import com.mcoin.mall.model.CollectionListRequest;
import com.mcoin.mall.model.CollectionListResponse;
import com.mcoin.mall.model.CollectionRequest;
import com.mcoin.mall.model.CollectionResponse;
import com.mcoin.mall.model.CouponsRequest;
import com.mcoin.mall.model.CouponsResponse;
import com.mcoin.mall.model.MyCouponCtx;
import com.mcoin.mall.model.MyCouponRequest;
import com.mcoin.mall.model.MyCouponResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.user.UserService;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class UserControllerTest extends ControllerMock {

    @Mock
    private ContextHolder contextHolder;
    
    @Mock
    private UserService userService;

    @InjectMocks
    private UserController userController;

    @Override
    @BeforeEach
    public void setup() {
        super.setup();
        
        // Setup common mocks with lenient() to avoid UnnecessaryStubbingException
        UserInfo userInfo = createUserInfo(36721);
        lenient().when(contextHolder.getAuthUserInfo()).thenReturn(userInfo);
        lenient().when(contextHolder.getLocale()).thenReturn(Locale.ENGLISH);
        
        // Mock collection responses
        CollectionResponse collectionResponse = new CollectionResponse();
        lenient().when(userService.saveCollection(any(), anyInt())).thenReturn(collectionResponse);
        
        // Mock collection list responses
        CollectionListResponse collectionsResponse = new CollectionListResponse();
        CollectionListResponse.SnatchListItem item = new CollectionListResponse.SnatchListItem();
        item.setId(2877);
        collectionsResponse.setSnatchList(Collections.singletonList(item));
        lenient().when(userService.getCollectionList(any())).thenReturn(collectionsResponse);
        
        // Mock coupons responses
        CouponsResponse couponsResponse = new CouponsResponse();
        CouponsResponse.SnatchGroup couponItem = new CouponsResponse.SnatchGroup();
        couponItem.setGroupName("無適用門店");
        couponsResponse.setSnatchList(Collections.singletonList(couponItem));
        lenient().when(userService.getCoupons(any())).thenReturn(couponsResponse);
        
        // Mock my coupon responses
        MyCouponResponse myCouponResponse = new MyCouponResponse();
        MyCouponResponse.SnatchListItem myCouponItem = new MyCouponResponse.SnatchListItem();
        myCouponItem.setStoreName("無適用門店");
        myCouponResponse.setSnatchList(Collections.singletonList(myCouponItem));
        lenient().when(userService.myCoupon(any(MyCouponCtx.class))).thenReturn(myCouponResponse);
    }

    @Test
    void testCollection() {
        // when
        CollectionRequest req = JsonTestUtils.getObjectFromJson("json/request/collectionRequest.json", "singleObj", CollectionRequest.class);
        Response<CollectionResponse> detail = userController.collection(req);
        
        // then
        assertEquals(1, detail.getStatus());
    }

    @Test
    void testCollections() {
        // when
        CollectionListRequest req = JsonTestUtils.getObjectFromJson("json/request/collectionListRequest.json", "singleObj", CollectionListRequest.class);
        Response<CollectionListResponse> detail = userController.collections(req);
        
        // then
        assertEquals(1, detail.getStatus());
        assertEquals(2877, detail.getData().getSnatchList().get(0).getId());
    }

    @Test
    void testCoupons() {
        // when
        CouponsRequest req = JsonTestUtils.getObjectFromJson("json/request/couponsRequest.json", "singleObj", CouponsRequest.class);
        Response<CouponsResponse> detail = userController.coupons(req);
        
        // then
        assertEquals(1, detail.getStatus());
        assertEquals("無適用門店", detail.getData().getSnatchList().get(0).getGroupName());
    }

    @Test
    void testMyCoupon() {
        // when
        MyCouponRequest req = new MyCouponRequest();
        req.setStatus(1);
        Response<MyCouponResponse> detail = userController.getCoupon(req);
        
        // then
        assertEquals(1, detail.getStatus());
        assertEquals("無適用門店", detail.getData().getSnatchList().get(0).getStoreName());
    }

    @Test
    void testCouponsWithStatus3() {
        // Setup specific mock for this test
        CouponsResponse response = new CouponsResponse();
        CouponsResponse.SnatchGroup item1 = new CouponsResponse.SnatchGroup();
        item1.setGroupName("無適用門店");
        CouponsResponse.SnatchGroup item2 = new CouponsResponse.SnatchGroup();
        item2.setGroupName("Another store");
        response.setSnatchList(Arrays.asList(item1, item2));
        lenient().when(userService.getCoupons(any())).thenReturn(response);
        
        // when
        CouponsRequest req = JsonTestUtils.getObjectFromJson("json/request/couponsRequest.json", "singleObj", CouponsRequest.class);
        req.setStatus(3);
        Response<CouponsResponse> detail = userController.coupons(req);
        
        // then
        assertEquals(1, detail.getStatus());
        assertEquals("無適用門店", detail.getData().getSnatchList().get(0).getGroupName());
        assertEquals(true, detail.getData().getSnatchList().size() > 1);
    }
    
    private UserInfo createUserInfo(int userId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        return userInfo;
    }
} 