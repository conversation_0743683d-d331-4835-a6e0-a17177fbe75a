package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.MiniOrderSettlementRequest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.MiniOrderSyncRequest;
import com.mcoin.mall.model.job.SyncProductListRequest;
import com.mcoin.mall.mq.model.MiniOrderSyncMessage;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.amqp.core.Message;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * 集成测试 MiniProgramJobController
 * 使用真实服务和数据库，测试小程序相关任务功能
 */
class MiniProgramJobControllerTest extends BaseUnitTest {

    @Resource
    private MiniProgramJobController miniProgramJobController;



    @BeforeEach
    public void setUp() {
        // Mock locale
        Mockito.when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        Mockito.when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
        doNothing().when(miniOrderSyncTemplateDelay).convertAndSend(any(Message.class));
        doNothing().when(miniOrderSyncTemplate).convertAndSend(any(Message.class));

    }

    @Test
    void testSettlement() {
        // 创建请求对象
        MiniOrderSettlementRequest request = new MiniOrderSettlementRequest();
        
        // 直接调用控制器方法
        Response<String> response = miniProgramJobController.settlement(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testSyncProducts() {
        // 创建请求对象
        SyncProductListRequest request = new SyncProductListRequest();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endDateTime = now.minusDays(-1).withHour(23).withMinute(59).withSecond(59);
        LocalDateTime startDateTime = endDateTime.minusMonths(-1);
        Date startDate = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
        request.setEndTime(endDate);
        request.setStartTime(startDate);

        // 直接调用控制器方法
        Response<String> response = miniProgramJobController.syncProducts(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testSyncOrder() {
        // 创建请求对象
        MiniOrderSyncRequest request = new MiniOrderSyncRequest();
        MiniOrderSyncMessage message = new MiniOrderSyncMessage();
        message.setDelayTime(10);

        request.setMessage(message);
        // 直接调用控制器方法
        Response<String> response = miniProgramJobController.syncOrder(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 