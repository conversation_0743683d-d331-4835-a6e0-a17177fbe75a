<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrderrefundDao">
    <select id="queryByStatusAndPlatformStatusInRefundTime"  resultType="com.mcoin.mall.bo.MpaycoinRefundDetailBo">
        SELECT
            o.userid,
            o.sellerid,
            o.create_time AS createTime,
            o.order_no AS orderNo,
            o.payment_transaction AS paymentTransaction,
            o.type,
            o.mpayintegral,
            o.total_amount AS totalAmount,
            r.refund_amount AS refundAmount,
            r.mpayintegral AS refundMpayintegral,
            o.payment_type AS paymentType,
            o.payment_amount AS paymentAmount,
            o.subsidy_amount AS subsidyAmount,
            o.id,
            o.payment_time AS paymentTime,
            o.order_amount AS orderAmount,
            r.refund_resson_front as refundRessonFront,
            r.user_remark as userRemark,
            r.customer_remark as customerRemark
        FROM fook_platform_orderrefund AS r
                 INNER JOIN fook_platform_order  AS o ON r.orderid = o.id
        WHERE r.refund_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          AND  r.status = #{status,jdbcType=INTEGER}
          AND r.platform_deal_status = #{platformStatus,jdbcType=INTEGER}
          AND o.is_mpay = 1
    </select>


    <select id="queryOrderWithRefundInfo" resultType="com.mcoin.mall.bo.RefundOrderBo">
        select
            o.userid,
            o.sellerid,
            o.create_time AS createTime,
            o.order_no AS orderNo,
            o.payment_transaction AS paymentTransaction,
            o.type as orderType,
            o.mpayintegral as mpayintegral,
            o.score as score,
            o.total_amount as totalAmount,
            ore.refund_amount as  refundAmount,
            ore.mpayintegral  as  refundMpayintegral,
            o.payment_type as paymentType,
            o.payment_amount as paymentAmount,
            o.id as id,
            o.payment_time as paymentTime,
            o.is_mpay as isMpay,
            o.order_amount as orderAmount,
            o.complete_time as completeTime,
            ore.refund_resson_front as refundRessonFront,
            ore.user_remark as userRemark,
            ore.customer_remark as customerRemark,
            ore.subsidy_amount AS subsidyAmount
        from fook_platform_orderrefund ore
                 left join fook_platform_order o on o.id = ore.orderid
        where ore.refund_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          and ore.`status` in (6,10) and ore.platform_deal_status = 3
    </select>

    <select id="sumOrderAmount" resultType="com.mcoin.mall.bo.OrderAmountBo">
        SELECT
            sum(
                    IF
                        ( ore.mpayintegral IS NULL, 0, ore.mpayintegral )) AS mpayintegral,
            sum( ore.refund_amount ) AS refundAmount

        FROM
            fook_platform_orderrefund ore
                INNER JOIN fook_platform_ordercode orc ON ore.orderid = orc.orderid
                INNER JOIN fook_platform_order o ON ore.orderid = o.id
        WHERE
            refund_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          AND ore.`status` IN ( 6, 10 )
          AND platform_deal_status = 3
          AND o.id is not null
        GROUP BY
            orc.id
        HAVING
            COUNT( orc.id ) > 1
    </select>
</mapper>