<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMacaupassUserLoginLog2Dao">

  <select id="countDistinctUserId" resultType="int">
        select count(distinct user_id)
        from fook_macaupass_user_login_log2
        where create_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
  </select>

</mapper>