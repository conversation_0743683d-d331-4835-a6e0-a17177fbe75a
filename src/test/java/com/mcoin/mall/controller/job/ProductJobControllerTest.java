package com.mcoin.mall.controller.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookShowSnapupSession;
import com.mcoin.mall.client.model.MiniGoodsUpdateHttpRequest;
import com.mcoin.mall.client.model.MiniOrdersHttpRequest;
import com.mcoin.mall.client.model.mp.management.MpMcoinMallResponse;
import com.mcoin.mall.constant.ShelfStatus;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookShowSnapupSessionDao;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.CheckSnappingStockRequest;
import com.mcoin.mall.security.UserInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 集成测试 ProductJobController
 * 使用真实服务和数据库，测试产品任务相关功能
 */
class ProductJobControllerTest extends BaseUnitTest {

    @Resource
    private ProductJobController productJobController;

    @Resource
    private FookShowSnapupSessionDao fookShowSnapupSessionDao;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    @BeforeEach
    void setUp() {
        when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
        MpMcoinMallResponse response = new MpMcoinMallResponse();
        response.setCode(MpMcoinMallResponse.Code.OK.getCode());
        Mockito.when(managementClient.updateProductStatus(any(MiniGoodsUpdateHttpRequest.class)))
                .thenReturn(response);
    }

    @Test
    void testCheckSnappingStock() {
        // 创建请求对象
        CheckSnappingStockRequest request = new CheckSnappingStockRequest();
        
        // 直接调用控制器方法
        Response<String> response = productJobController.checkSnappingStock(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
    
    @Test
    void testBatchOfflineProducts() {

        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, -10);
        Date twoMonthBefore = calendar.getTime();
        fookBusinessProductDao.update(
                null,
                new LambdaUpdateWrapper<FookBusinessProduct>()
                        .set(FookBusinessProduct::getBuyEndTime, twoMonthBefore)
                        .in(FookBusinessProduct::getId, Arrays.asList(2409
                                ,2410
                                ,2835)));



        // 直接调用控制器方法
        Response<String> response = productJobController.batchOfflineProducts();


        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testBatchOfflineProductsWithSession() {

        FookShowSnapupSession fookShowSnapupSession = fookShowSnapupSessionDao.selectOne(new LambdaQueryWrapper<FookShowSnapupSession>().eq(FookShowSnapupSession::getId, 180));
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.HOUR_OF_DAY, 1);
        Date oneHourLater = calendar.getTime();
        calendar.add(Calendar.HOUR_OF_DAY, -2);
        Date oneHourBefore = calendar.getTime();
        fookShowSnapupSession.setStartTime(oneHourBefore);
        fookShowSnapupSession.setEndTime(oneHourLater);
        fookShowSnapupSessionDao.updateById(fookShowSnapupSession);

        // 直接调用控制器方法
        Response<String> response = productJobController.batchOfflineProducts();

        // 验证响应结构
        assertNotNull(response);
    }
} 