package com.mcoin.mall.controller.show;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.SnapUpSessionIndexRequest;
import com.mcoin.mall.model.SnapUpSessionIndexResponse;
import com.mcoin.mall.model.SnapUpSessionInfoRequest;
import com.mcoin.mall.model.SnapUpSessionInfoResponse;
import com.mcoin.mall.model.SnapUpSessionProductRequest;
import com.mcoin.mall.model.SnapUpSessionProductResponse;
import com.mcoin.mall.service.show.ShowSnapUpService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/show/snapup")
public class ShowSnapUpController {
    @Resource
    private ShowSnapUpService showSnapUpService;

    @GetMapping("/indexsnapup")
    @ResponseBody
    @ApiOperation(value = "获取抢购首页数据", notes = "获取抢购活动的首页展示数据")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<SnapUpSessionIndexResponse> indexSnapUp(@Valid SnapUpSessionIndexRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        return Responses.ok(showSnapUpService.getIndexSnapUpProduct(request, clientType));
    }

    @GetMapping("/info")
    @ResponseBody
    public Response<SnapUpSessionInfoResponse> info(@Valid SnapUpSessionInfoRequest request) {
        return Responses.ok(showSnapUpService.getSnapUpSessionInfo(request));
    }

    @GetMapping("/product")
    @ResponseBody
    @ApiOperation(value = "获取抢购商品详情", notes = "获取抢购活动的商品详情数据")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<SnapUpSessionProductResponse> product(@Valid SnapUpSessionProductRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        return Responses.ok(showSnapUpService.getSnapUpSessionProduct(request, clientType));
    }
}
