<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrderinfoDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformOrderinfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="prodcutid" jdbcType="INTEGER" property="prodcutid" />
    <result column="product_price" jdbcType="DECIMAL" property="productPrice" />
    <result column="image_snapshots" jdbcType="VARCHAR" property="imageSnapshots" />
    <result column="title_snapshots" jdbcType="VARCHAR" property="titleSnapshots" />
    <result column="desc_snapshots" jdbcType="VARCHAR" property="descSnapshots" />
    <result column="vaild_start_time" jdbcType="TIMESTAMP" property="vaildStartTime" />
    <result column="vaild_end_time" jdbcType="TIMESTAMP" property="vaildEndTime" />
    <result column="details_snapshots" jdbcType="VARCHAR" property="detailsSnapshots" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="is_weekend" jdbcType="INTEGER" property="isWeekend" />
    <result column="no_useday" jdbcType="VARCHAR" property="noUseday" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
    <result column="is_vacation" jdbcType="INTEGER" property="isVacation" />
    <result column="is_allow_refund" jdbcType="INTEGER" property="isAllowRefund" />
    <result column="fee_rate" jdbcType="DECIMAL" property="feeRate" />
    <result column="buy_start_time" jdbcType="TIMESTAMP" property="buyStartTime" />
    <result column="buy_end_time" jdbcType="TIMESTAMP" property="buyEndTime" />
    <result column="valid_mode" jdbcType="INTEGER" property="validMode" />
    <result column="day_num" jdbcType="INTEGER" property="dayNum" />
    <result column="applicable_shops" jdbcType="VARCHAR" property="applicableShops" />
    <result column="is_hot" jdbcType="INTEGER" property="isHot" />
    <result column="momecoins_option" jdbcType="VARCHAR" property="momecoinsOption" />
    <result column="threshold" jdbcType="INTEGER" property="threshold" />
    <result column="is_great" jdbcType="INTEGER" property="isGreat" />
    <result column="is_redeen" jdbcType="INTEGER" property="isRedeen" />
    <result column="is_momecoin" jdbcType="INTEGER" property="isMomecoin" />
    <result column="ordercode_id" jdbcType="INTEGER" property="ordercodeId" />
    <result column="refundid" jdbcType="INTEGER" property="refundid" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="mpayintegral" jdbcType="INTEGER" property="mpayintegral" />
    <result column="memberintegral" jdbcType="DECIMAL" property="memberintegral" />
    <result column="miles_first" jdbcType="VARCHAR" property="milesFirst" />
    <result column="miles_name" jdbcType="VARCHAR" property="milesName" />
    <result column="miles_member" jdbcType="BIGINT" property="milesMember" />
    <result column="miles_milage" jdbcType="DECIMAL" property="milesMilage" />
    <result column="is_voucher" jdbcType="TINYINT" property="isVoucher" />
    <result column="is_settlement" jdbcType="TINYINT" property="isSettlement" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.FookPlatformOrderinfo">
    <result column="tnc" jdbcType="LONGVARCHAR" property="tnc" />
  </resultMap>
  <sql id="Base_Column_List">
    id, orderid, prodcutid, product_price, image_snapshots, title_snapshots, desc_snapshots, 
    vaild_start_time, vaild_end_time, details_snapshots, `number`, is_weekend, no_useday, 
    `type`, img, retail_price, is_vacation, is_allow_refund, fee_rate, buy_start_time, 
    buy_end_time, valid_mode, day_num, applicable_shops, is_hot, momecoins_option, threshold, 
    is_great, is_redeen, is_momecoin, ordercode_id, refundid, order_amount, score, mpayintegral, 
    memberintegral, miles_first, miles_name, miles_member, miles_milage, is_voucher, 
    is_settlement, subsidy_amount
  </sql>
  <sql id="Blob_Column_List">
    tnc
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fook_platform_orderinfo
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_orderinfo
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformOrderinfo">
    insert into fook_platform_orderinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="prodcutid != null">
        prodcutid,
      </if>
      <if test="productPrice != null">
        product_price,
      </if>
      <if test="imageSnapshots != null">
        image_snapshots,
      </if>
      <if test="titleSnapshots != null">
        title_snapshots,
      </if>
      <if test="descSnapshots != null">
        desc_snapshots,
      </if>
      <if test="vaildStartTime != null">
        vaild_start_time,
      </if>
      <if test="vaildEndTime != null">
        vaild_end_time,
      </if>
      <if test="detailsSnapshots != null">
        details_snapshots,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="isWeekend != null">
        is_weekend,
      </if>
      <if test="noUseday != null">
        no_useday,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="img != null">
        img,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="isVacation != null">
        is_vacation,
      </if>
      <if test="isAllowRefund != null">
        is_allow_refund,
      </if>
      <if test="feeRate != null">
        fee_rate,
      </if>
      <if test="buyStartTime != null">
        buy_start_time,
      </if>
      <if test="buyEndTime != null">
        buy_end_time,
      </if>
      <if test="validMode != null">
        valid_mode,
      </if>
      <if test="dayNum != null">
        day_num,
      </if>
      <if test="applicableShops != null">
        applicable_shops,
      </if>
      <if test="isHot != null">
        is_hot,
      </if>
      <if test="momecoinsOption != null">
        momecoins_option,
      </if>
      <if test="threshold != null">
        threshold,
      </if>
      <if test="isGreat != null">
        is_great,
      </if>
      <if test="isRedeen != null">
        is_redeen,
      </if>
      <if test="isMomecoin != null">
        is_momecoin,
      </if>
      <if test="ordercodeId != null">
        ordercode_id,
      </if>
      <if test="refundid != null">
        refundid,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="memberintegral != null">
        memberintegral,
      </if>
      <if test="milesFirst != null">
        miles_first,
      </if>
      <if test="milesName != null">
        miles_name,
      </if>
      <if test="milesMember != null">
        miles_member,
      </if>
      <if test="milesMilage != null">
        miles_milage,
      </if>
      <if test="isVoucher != null">
        is_voucher,
      </if>
      <if test="isSettlement != null">
        is_settlement,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
      <if test="tnc != null">
        tnc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="prodcutid != null">
        #{prodcutid,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="imageSnapshots != null">
        #{imageSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="titleSnapshots != null">
        #{titleSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="descSnapshots != null">
        #{descSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="vaildStartTime != null">
        #{vaildStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildEndTime != null">
        #{vaildEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailsSnapshots != null">
        #{detailsSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="isWeekend != null">
        #{isWeekend,jdbcType=INTEGER},
      </if>
      <if test="noUseday != null">
        #{noUseday,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="img != null">
        #{img,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="isVacation != null">
        #{isVacation,jdbcType=INTEGER},
      </if>
      <if test="isAllowRefund != null">
        #{isAllowRefund,jdbcType=INTEGER},
      </if>
      <if test="feeRate != null">
        #{feeRate,jdbcType=DECIMAL},
      </if>
      <if test="buyStartTime != null">
        #{buyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyEndTime != null">
        #{buyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validMode != null">
        #{validMode,jdbcType=INTEGER},
      </if>
      <if test="dayNum != null">
        #{dayNum,jdbcType=INTEGER},
      </if>
      <if test="applicableShops != null">
        #{applicableShops,jdbcType=VARCHAR},
      </if>
      <if test="isHot != null">
        #{isHot,jdbcType=INTEGER},
      </if>
      <if test="momecoinsOption != null">
        #{momecoinsOption,jdbcType=VARCHAR},
      </if>
      <if test="threshold != null">
        #{threshold,jdbcType=INTEGER},
      </if>
      <if test="isGreat != null">
        #{isGreat,jdbcType=INTEGER},
      </if>
      <if test="isRedeen != null">
        #{isRedeen,jdbcType=INTEGER},
      </if>
      <if test="isMomecoin != null">
        #{isMomecoin,jdbcType=INTEGER},
      </if>
      <if test="ordercodeId != null">
        #{ordercodeId,jdbcType=INTEGER},
      </if>
      <if test="refundid != null">
        #{refundid,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        #{score,jdbcType=INTEGER},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="memberintegral != null">
        #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="milesFirst != null">
        #{milesFirst,jdbcType=VARCHAR},
      </if>
      <if test="milesName != null">
        #{milesName,jdbcType=VARCHAR},
      </if>
      <if test="milesMember != null">
        #{milesMember,jdbcType=BIGINT},
      </if>
      <if test="milesMilage != null">
        #{milesMilage,jdbcType=DECIMAL},
      </if>
      <if test="isVoucher != null">
        #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="isSettlement != null">
        #{isSettlement,jdbcType=TINYINT},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="tnc != null">
        #{tnc,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformOrderinfo">
    update fook_platform_orderinfo
    <set>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="prodcutid != null">
        prodcutid = #{prodcutid,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        product_price = #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="imageSnapshots != null">
        image_snapshots = #{imageSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="titleSnapshots != null">
        title_snapshots = #{titleSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="descSnapshots != null">
        desc_snapshots = #{descSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="vaildStartTime != null">
        vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildEndTime != null">
        vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailsSnapshots != null">
        details_snapshots = #{detailsSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="isWeekend != null">
        is_weekend = #{isWeekend,jdbcType=INTEGER},
      </if>
      <if test="noUseday != null">
        no_useday = #{noUseday,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="img != null">
        img = #{img,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="isVacation != null">
        is_vacation = #{isVacation,jdbcType=INTEGER},
      </if>
      <if test="isAllowRefund != null">
        is_allow_refund = #{isAllowRefund,jdbcType=INTEGER},
      </if>
      <if test="feeRate != null">
        fee_rate = #{feeRate,jdbcType=DECIMAL},
      </if>
      <if test="buyStartTime != null">
        buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyEndTime != null">
        buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validMode != null">
        valid_mode = #{validMode,jdbcType=INTEGER},
      </if>
      <if test="dayNum != null">
        day_num = #{dayNum,jdbcType=INTEGER},
      </if>
      <if test="applicableShops != null">
        applicable_shops = #{applicableShops,jdbcType=VARCHAR},
      </if>
      <if test="isHot != null">
        is_hot = #{isHot,jdbcType=INTEGER},
      </if>
      <if test="momecoinsOption != null">
        momecoins_option = #{momecoinsOption,jdbcType=VARCHAR},
      </if>
      <if test="threshold != null">
        threshold = #{threshold,jdbcType=INTEGER},
      </if>
      <if test="isGreat != null">
        is_great = #{isGreat,jdbcType=INTEGER},
      </if>
      <if test="isRedeen != null">
        is_redeen = #{isRedeen,jdbcType=INTEGER},
      </if>
      <if test="isMomecoin != null">
        is_momecoin = #{isMomecoin,jdbcType=INTEGER},
      </if>
      <if test="ordercodeId != null">
        ordercode_id = #{ordercodeId,jdbcType=INTEGER},
      </if>
      <if test="refundid != null">
        refundid = #{refundid,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="memberintegral != null">
        memberintegral = #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="milesFirst != null">
        miles_first = #{milesFirst,jdbcType=VARCHAR},
      </if>
      <if test="milesName != null">
        miles_name = #{milesName,jdbcType=VARCHAR},
      </if>
      <if test="milesMember != null">
        miles_member = #{milesMember,jdbcType=BIGINT},
      </if>
      <if test="milesMilage != null">
        miles_milage = #{milesMilage,jdbcType=DECIMAL},
      </if>
      <if test="isVoucher != null">
        is_voucher = #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="isSettlement != null">
        is_settlement = #{isSettlement,jdbcType=TINYINT},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="tnc != null">
        tnc = #{tnc,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.FookPlatformOrderinfo">
    update fook_platform_orderinfo
    set orderid = #{orderid,jdbcType=INTEGER},
      prodcutid = #{prodcutid,jdbcType=INTEGER},
      product_price = #{productPrice,jdbcType=DECIMAL},
      image_snapshots = #{imageSnapshots,jdbcType=VARCHAR},
      title_snapshots = #{titleSnapshots,jdbcType=VARCHAR},
      desc_snapshots = #{descSnapshots,jdbcType=VARCHAR},
      vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      details_snapshots = #{detailsSnapshots,jdbcType=VARCHAR},
      `number` = #{number,jdbcType=INTEGER},
      is_weekend = #{isWeekend,jdbcType=INTEGER},
      no_useday = #{noUseday,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      img = #{img,jdbcType=VARCHAR},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      is_vacation = #{isVacation,jdbcType=INTEGER},
      is_allow_refund = #{isAllowRefund,jdbcType=INTEGER},
      fee_rate = #{feeRate,jdbcType=DECIMAL},
      buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      valid_mode = #{validMode,jdbcType=INTEGER},
      day_num = #{dayNum,jdbcType=INTEGER},
      applicable_shops = #{applicableShops,jdbcType=VARCHAR},
      is_hot = #{isHot,jdbcType=INTEGER},
      momecoins_option = #{momecoinsOption,jdbcType=VARCHAR},
      threshold = #{threshold,jdbcType=INTEGER},
      is_great = #{isGreat,jdbcType=INTEGER},
      is_redeen = #{isRedeen,jdbcType=INTEGER},
      is_momecoin = #{isMomecoin,jdbcType=INTEGER},
      ordercode_id = #{ordercodeId,jdbcType=INTEGER},
      refundid = #{refundid,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      score = #{score,jdbcType=INTEGER},
      mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      memberintegral = #{memberintegral,jdbcType=DECIMAL},
      miles_first = #{milesFirst,jdbcType=VARCHAR},
      miles_name = #{milesName,jdbcType=VARCHAR},
      miles_member = #{milesMember,jdbcType=BIGINT},
      miles_milage = #{milesMilage,jdbcType=DECIMAL},
      is_voucher = #{isVoucher,jdbcType=TINYINT},
      is_settlement = #{isSettlement,jdbcType=TINYINT},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      tnc = #{tnc,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformOrderinfo">
    update fook_platform_orderinfo
    set orderid = #{orderid,jdbcType=INTEGER},
      prodcutid = #{prodcutid,jdbcType=INTEGER},
      product_price = #{productPrice,jdbcType=DECIMAL},
      image_snapshots = #{imageSnapshots,jdbcType=VARCHAR},
      title_snapshots = #{titleSnapshots,jdbcType=VARCHAR},
      desc_snapshots = #{descSnapshots,jdbcType=VARCHAR},
      vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      details_snapshots = #{detailsSnapshots,jdbcType=VARCHAR},
      `number` = #{number,jdbcType=INTEGER},
      is_weekend = #{isWeekend,jdbcType=INTEGER},
      no_useday = #{noUseday,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      img = #{img,jdbcType=VARCHAR},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      is_vacation = #{isVacation,jdbcType=INTEGER},
      is_allow_refund = #{isAllowRefund,jdbcType=INTEGER},
      fee_rate = #{feeRate,jdbcType=DECIMAL},
      buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      valid_mode = #{validMode,jdbcType=INTEGER},
      day_num = #{dayNum,jdbcType=INTEGER},
      applicable_shops = #{applicableShops,jdbcType=VARCHAR},
      is_hot = #{isHot,jdbcType=INTEGER},
      momecoins_option = #{momecoinsOption,jdbcType=VARCHAR},
      threshold = #{threshold,jdbcType=INTEGER},
      is_great = #{isGreat,jdbcType=INTEGER},
      is_redeen = #{isRedeen,jdbcType=INTEGER},
      is_momecoin = #{isMomecoin,jdbcType=INTEGER},
      ordercode_id = #{ordercodeId,jdbcType=INTEGER},
      refundid = #{refundid,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      score = #{score,jdbcType=INTEGER},
      mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      memberintegral = #{memberintegral,jdbcType=DECIMAL},
      miles_first = #{milesFirst,jdbcType=VARCHAR},
      miles_name = #{milesName,jdbcType=VARCHAR},
      miles_member = #{milesMember,jdbcType=BIGINT},
      miles_milage = #{milesMilage,jdbcType=DECIMAL},
      is_voucher = #{isVoucher,jdbcType=TINYINT},
      is_settlement = #{isSettlement,jdbcType=TINYINT},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>