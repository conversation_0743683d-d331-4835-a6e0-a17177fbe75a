package com.mcoin.mall.service.business.impl;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.mcoin.mall.constant.SnapUpEnum.NOT_SNAP_UP;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookBusinessInformation;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookBusinessProductTranslations;
import com.mcoin.mall.bean.FookStores;
import com.mcoin.mall.bean.FookStoresTranslations;
import com.mcoin.mall.bean.FookStoresType;
import com.mcoin.mall.bean.FookStoresTypeTranslations;
import com.mcoin.mall.bean.StoresKeywordWithStoreId;
import com.mcoin.mall.bo.ProductsCollectBo;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookBusinessInformationDao;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.dao.FookStoresKeywordDao;
import com.mcoin.mall.dao.FookStoresTranslationsDao;
import com.mcoin.mall.dao.FookStoresTypeDao;
import com.mcoin.mall.dao.FookStoresTypeTranslationsDao;
import com.mcoin.mall.mapping.FookStoreKeywordsMapping;
import com.mcoin.mall.mapping.FookStoreMapping;
import com.mcoin.mall.model.ApplyStoresRequest;
import com.mcoin.mall.model.ApplyStoresResponse;
import com.mcoin.mall.model.StoreDetailCtx;
import com.mcoin.mall.model.StoreDetailRequest;
import com.mcoin.mall.model.StoreDetailResponse;
import com.mcoin.mall.model.StoresRequest;
import com.mcoin.mall.model.StoresResponse;
import com.mcoin.mall.service.business.StoreService;
import com.mcoin.mall.service.common.CollectService;
import com.mcoin.mall.service.common.StockService;
import com.mcoin.mall.util.BusinessProductUtil;
import com.mcoin.mall.util.DistanceUtil;
import com.mcoin.mall.util.HtmlUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.MiniProgramDisplayUtil;
import com.mcoin.mall.util.OssUtil;
import com.mcoin.mall.util.PageUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StoreServiceImpl implements StoreService {
    @Resource
    private FookStoresDao fookStoresDao;

    @Resource
    private FookStoresTypeDao fookStoresTypeDao;

    @Resource
    private FookStoresTranslationsDao fookStoresTranslationsDao;

    @Resource
    private FookStoresTypeTranslationsDao fookStoresTypeTranslationsDao;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    @Resource
    private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;

    @Resource
    private FookBusinessInformationDao fookBusinessInformationDao;

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private FookStoresKeywordDao fookStoresKeywordDao;
    @Resource
    private CollectService collectService;
    @Resource
    private StockService stockService;

    @Override
    public StoresResponse getStores(StoresRequest storesRequest) {
        StoresResponse storesResponse = new StoresResponse();
        Integer businessId = StringUtils.isEmpty(storesRequest.BusinessId) ? 1 : Integer.parseInt(storesRequest.BusinessId);
        Locale locale = contextHolder.getLocale();
        String lang = locale.getLanguage();
        List<FookStores> storeList = fookStoresDao.selectStores(businessId, lang);
        if (CollectionUtils.isEmpty(storeList)) {
            return storesResponse;
        }

        for (FookStores store : storeList) {
            double distanceNum = DistanceUtil.getDistance(storesRequest.getLot(), storesRequest.getLat(), store.getLongitude(), store.getDimension());
            String distance = DistanceUtil.getDistanceStr(distanceNum);
            store.setDistanceNum(distanceNum);
            store.setDistance(distance);
        }
        //根據距離順序正序排序
        storeList = storeList.stream().sorted(Comparator.comparing(FookStores::getDistanceNum)).collect(Collectors.toList());
        List<StoresResponse.SnatchListItem> itemList = new ArrayList<>();
        for (FookStores store : storeList) {
            StoresResponse.SnatchListItem item = new StoresResponse.SnatchListItem();
            item.setId(store.getId());
            item.setName(store.getName());
            item.setPhone(store.getPhone());
            item.setAddress(store.getAddress());
            item.setDistance(store.getDistance());
            itemList.add(item);
        }
        storesResponse.setSnatchList(itemList);
        return storesResponse;
    }

    @Override
    public StoreDetailResponse getStoreDetail(StoreDetailCtx ctx, String clientType) {
        StoreDetailResponse result = new StoreDetailResponse();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date currentTime = new Date();
        String currentTimeStr = dateFormat.format(currentTime);
        StoreDetailRequest request = ctx.getStoreDetailRequest();
        String language = ctx.getLanguage();
        int storeId = Integer.parseInt(request.getWelfareId());
        //门店详情
        FookStores store = this.fookStoresDao.getStore(storeId);
        if (null != store) {
            String name = store.getName();
            String address = store.getAddress();
            String businessTime = store.getBusinessTime();
            String detail = store.getDetail();

            //再查store翻译
            if (McoinMall.LANG_EN.equals(language)) {
                FookStoresTranslations storesTranslations = fookStoresTranslationsDao.getTranslationsByStoreId(storeId, language);
                if (storesTranslations != null) {
                    name = StringUtils.defaultIfBlank(storesTranslations.getTName(), name);
                    address = StringUtils.defaultIfBlank(storesTranslations.getTAddress(), address);
                    businessTime = StringUtils.defaultIfBlank(storesTranslations.getTBusinessTime(), businessTime);
                    detail = StringUtils.defaultIfBlank(storesTranslations.getTDetail(), detail);
                }
            }

            //门店关键字
            List<StoresKeywordWithStoreId> keywords = this.fookStoresKeywordDao.getStoreKeyWordsByStoreIds(Lists.newArrayList(storeId));
            if (McoinMall.LANG_EN.equals(language)) {
                keywords.forEach(k -> k.setName(StringUtils.defaultIfBlank(k.getEnglishName(), k.getName())));
            }
            result.setKeyword(FookStoreKeywordsMapping.INSTANCE.toKeywordVo(keywords));

            //门店类别
            List<FookStoresType> types = this.fookStoresTypeDao.getStoresTypeByStoreId(storeId);
            if (CollectionUtils.isNotEmpty(types) && "en".equals(language)) {
                List<Integer> typeIds = types.stream().map(t -> t.getId()).collect(Collectors.toList());
                //再查翻译表
                List<FookStoresTypeTranslations> typeTranslations = fookStoresTypeTranslationsDao.getTranslationsByStoreTypeIds(typeIds, language);
                if (CollectionUtils.isNotEmpty(typeTranslations)) {
                    types.stream().forEach(t -> {
                        FookStoresTypeTranslations typeTranslations1 = typeTranslations.stream().filter(t1 -> t1.getStoresTypeId().equals(t.getId())).findFirst().orElse(null);
                        if (null != typeTranslations1) {
                            t.setName(StringUtils.defaultIfBlank(typeTranslations1.getTName(), t.getName()));
                        }
                    });
                }

            }
            //福利
            Integer page = request.getPage();
            Integer offset = null;
            if (page != null) {
                offset = PageUtil.getOffset(page, McoinMall.DEFAULT_PAGE_SIZE);
            }
            Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);
            List<FookBusinessProduct> businessProducts = this.fookBusinessProductDao.
                    getBusinessProductByStoreId(storeId, currentTimeStr, McoinMall.DEFAULT_PAGE_SIZE, offset,harmonySwitch);
            List<StoreDetailResponse.BusinessItem> business = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(businessProducts)) {
                List<Integer> ids = businessProducts.stream().map(FookBusinessProduct::getId).collect(Collectors.toList());
                if (McoinMall.LANG_EN.equals(language)) {
                    //再查翻译表
                    List<FookBusinessProductTranslations> fookBusinessProductTranslations = this.fookBusinessProductTranslationsDao.getTranslationsByIds(ids, language);
                    if (CollectionUtils.isNotEmpty(fookBusinessProductTranslations)) {
                        businessProducts.stream().forEach(t -> {
                            FookBusinessProductTranslations fookBusinessProductTranslations1 = fookBusinessProductTranslations.stream().filter(t1 -> t1.getBusinessProductId().equals(t.getId())).findFirst().orElse(null);
                            if (null != fookBusinessProductTranslations1) {
                                t.setTitle(StringUtils.defaultIfBlank(fookBusinessProductTranslations1.getTTitle(), t.getTitle()));
                                String tnc = StringUtils.defaultIfBlank(fookBusinessProductTranslations1.getTTnc(), t.getTnc());
                                t.setTnc(HtmlUtil.replaceNewLine(tnc));
                                t.setReceiveMethod(StringUtils.defaultIfBlank(fookBusinessProductTranslations1.getTReceiveMethod(), t.getReceiveMethod()));
                            }
                        });
                    }
                }
                List<Integer> productIds = businessProducts.stream().map(FookBusinessProduct::getId).collect(Collectors.toList());
                // 处理收藏
                ProductsCollectBo collectBo = collectService.getProductsCollect(productIds);
                businessProducts.forEach(b -> {
                    StoreDetailResponse.BusinessItem item = new StoreDetailResponse.BusinessItem();
                    item.setTitle(b.getTitle());
                    item.setId(b.getId());
                    String img = StringUtils.isNotBlank(b.getZipImg()) ? b.getZipImg() : b.getImg();
                    item.setImg(OssUtil.getProductImg(img));
                    if (null != b.getRetailPrice()) {
                        item.setRetailPrice(b.getRetailPrice().toPlainString());
                    }
                    int pointRatio = b.getPointRatio();
                    int minPoint = defaultIfNull(b.getMinPoint(), 0);
                    int point = (minPoint > 0 && b.getOnlyPoint() == 0) ? minPoint : pointRatio / 2;
                    BigDecimal preferential = b.getPrice().subtract(BigDecimal.valueOf(point).divide(BigDecimal.valueOf(pointRatio), 2, RoundingMode.HALF_UP));
                    //如果是純積分支付
                    if (1 == b.getOnlyPoint()) {
                        point = b.getPrice().multiply(new BigDecimal(pointRatio)).intValue();
                        preferential = new BigDecimal(0);
                    } else if (2 == b.getOnlyPoint()) {
                        point = 0;
                        preferential = b.getPrice();
                    }
                    item.setPoint(point);
                    item.setPreferential(preferential);
                    item.setPayType(b.getOnlyPoint());
                    item.setType(b.getType());
                    item.setHrefUrl(BusinessProductUtil.fillHrefUrlBy(b.getId(), b.getType(), b.getHrefUrl(), b.getGoodsId()));
                    item.setSnapUp(defaultIfNull(b.getSnapUp(), NOT_SNAP_UP.getValue()));
                    // 处理收藏
                    collectService.setCollectNumber(collectBo, b.getId(), item, false);
                    // 处理售罄
                    item.setSoldOut(stockService.isSlodOut(b));
                    business.add(item);
                });
            }
            //商圈
            if (StringUtils.isNotBlank(store.getBusinessInformationId())) {
                FookBusinessInformation fookBusinessInformation = fookBusinessInformationDao.selectByPrimaryKey(Integer.parseInt(store.getBusinessInformationId()));
                StoreDetailResponse.Businessinformation businessinformation = new StoreDetailResponse.Businessinformation();
                if (null != fookBusinessInformation) {
                    businessinformation.setId(fookBusinessInformation.getId());
                    businessinformation.setName(fookBusinessInformation.getName());
                    result.setBusinessinformation(businessinformation);
                }
            }

            //距离
            String lot = request.getLot();
            String lat = request.getLat();
            String longitude = store.getLongitude();
            String dimension = store.getDimension();
            double distanceKm = DistanceUtil.getDistance(lot, lat, longitude, dimension);
            String distance =DistanceUtil.getDistanceStr(distanceKm);

            //商家店铺数
            Integer branch = this.fookStoresDao.getBranchs(store.getBusinessId());


            result.setId(store.getId());
            String img = FookStoreMapping.INSTANCE.extractImg(store.getImg());
            result.setImg(img);
            String backgroundImg = store.getBackgroundImg();
            if (StringUtils.isBlank(backgroundImg)) {
                backgroundImg = img;
            }
            result.setName(name);
            result.setPhone(store.getPhone());
            result.setDetail(HtmlUtil.replaceNewLine(detail));
            result.setBusinessTime(businessTime);
            result.setBusinessId(store.getBusinessId());
            result.setAddress(address);
            result.setType(types);
            result.setBusiness(business);
            //商家店铺数
            result.setBranch(branch);
            result.setProvideServices(store.getProvideServices());
            result.setDistance(distance);
            result.setBackgroundImg(OssUtil.initOssImage(backgroundImg));
            // 返回分頁信息
            if (page != null) {
                Integer productCount = this.fookBusinessProductDao.countBusinessProductByStoreId(storeId, currentTimeStr,harmonySwitch);
                result.setPage(PageUtil.createPageV2(page, productCount));
            }
        }

        return result;
    }

    @Override
    public ApplyStoresResponse getApplyStores(ApplyStoresRequest request) {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        ApplyStoresResponse applyStoresResponse = new ApplyStoresResponse();
        List<ApplyStoresResponse.SnatchListItem> itemList = new ArrayList<>();
        List<FookStores> storeList = fookStoresDao.getApplyStores(lang, Integer.parseInt(request.getProductId()), 1);
        if (CollectionUtils.isNotEmpty(storeList)) {
            for (FookStores store : storeList) {
                double distanceNum = DistanceUtil.getDistance(request.getLot(), request.getLat(), store.getLongitude(), store.getDimension());
                String distance = DistanceUtil.getDistanceStr(distanceNum);
                store.setDistanceNum(distanceNum);
                store.setDistance(distance);
            }
            //對門店直接排序
            storeList = storeList.stream().sorted(Comparator.comparing(FookStores::getDistanceNum)).collect(Collectors.toList());
            for (FookStores store : storeList) {
                ApplyStoresResponse.SnatchListItem item = new ApplyStoresResponse.SnatchListItem();
                item.setId(store.getId());
                item.setName(store.getName());
                item.setAddress(store.getAddress());
                item.setPhone(store.getPhone());
                item.setDistance(store.getDistance());
                itemList.add(item);
            }
        }
        applyStoresResponse.setSnatchList(itemList);
        return applyStoresResponse;
    }


}
