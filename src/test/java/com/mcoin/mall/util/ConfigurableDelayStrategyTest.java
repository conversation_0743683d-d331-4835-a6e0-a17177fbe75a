package com.mcoin.mall.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

/**
 * Test for {@link ConfigurableDelayStrategy}
 */
class ConfigurableDelayStrategyTest {

    private static final int MINUTE = 60000;
    private static final int HOUR = 3600000;
    private static final int DAY = 86400000;
    private static final int TEST_BASE_DELAY = 5000; // 5 seconds

    @Test
    void testGetDelayWithNegativeRetryCount() {
        // Test retry count < 1
        assertEquals(0, ConfigurableDelayStrategy.getDelay(-1, TEST_BASE_DELAY));
        assertEquals(0, ConfigurableDelayStrategy.getDelay(0, TEST_BASE_DELAY));
    }

    @Test
    void testGetDelayWithRetryCountOne() {
        // Test retry count = 1 (should return baseDelay)
        assertEquals(TEST_BASE_DELAY, ConfigurableDelayStrategy.getDelay(1, TEST_BASE_DELAY));
        
        // Test with different base delays
        assertEquals(1000, ConfigurableDelayStrategy.getDelay(1, 1000));
        assertEquals(10000, ConfigurableDelayStrategy.getDelay(1, 10000));
    }

    @Test
    void testGetDelayWithRetryCountTwo() {
        // Test retry count = 2 (should return MINUTE)
        assertEquals(MINUTE, ConfigurableDelayStrategy.getDelay(2, TEST_BASE_DELAY));
    }

    @Test
    void testGetDelayWithRetryCountThree() {
        // Test retry count = 3 (should return HOUR)
        assertEquals(HOUR, ConfigurableDelayStrategy.getDelay(3, TEST_BASE_DELAY));
    }

    @Test
    void testGetDelayWithRetryCountGreaterThanThree() {
        // Test retry count > 3 (should return days * DAY)
        assertEquals(1 * DAY, ConfigurableDelayStrategy.getDelay(4, TEST_BASE_DELAY)); // 4-3 = 1 day
        assertEquals(2 * DAY, ConfigurableDelayStrategy.getDelay(5, TEST_BASE_DELAY)); // 5-3 = 2 days
        assertEquals(3 * DAY, ConfigurableDelayStrategy.getDelay(6, TEST_BASE_DELAY)); // 6-3 = 3 days
        assertEquals(7 * DAY, ConfigurableDelayStrategy.getDelay(10, TEST_BASE_DELAY)); // (10-3) capped at 7 days
        assertEquals(7 * DAY, ConfigurableDelayStrategy.getDelay(20, TEST_BASE_DELAY)); // (20-3) capped at 7 days
    }
} 