package com.mcoin.mall.util;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class HttpBuildQueryTest{

    @Test
    public void testHttpBuildQueryWithMultipleKeyValuePairs() {
        Map<String, Object> params = new HashMap<>();
        params.put("key1", "value1");
        params.put("key2", "value2");
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertTrue(result.contains("key1=value1"));
        assertTrue(result.contains("key2=value2"));
    }

    @Test
    public void testHttpBuildQueryWithNullValue() {
        Map<String, Object> params = new HashMap<>();
        params.put("key1", "value1");
        params.put("key2", null);
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertTrue(result.contains("key1=value1"));
        assertFalse(result.contains("key2=null"));
    }

    @Test
    public void testHttpBuildQueryWithEmptyValue() {
        Map<String, Object> params = new HashMap<>();
        params.put("key1", "");
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertEquals("key1=", result);
    }

    @Test
    public void testHttpBuildQueryWithSpecialCharacters() {
        Map<String, Object> params = new HashMap<>();
        params.put("key1", "value@# ");
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertEquals("key1=value@# ", result);
    }

    @Test
    public void testHttpBuildQueryWithNestedMap() {
        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nestedKey", "nestedValue");
        Map<String, Object> params = new HashMap<>();
        params.put("key1", nestedMap);
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertEquals("key1[nestedkey]=nestedValue", result);
    }

    @Test
    public void testHttpBuildQueryWithMixedContent() {
        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nestedKey", "nestedValue");
        Map<String, Object> params = new HashMap<>();
        params.put("key1", "value1");
        params.put("key2", nestedMap);
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertTrue(result.contains("key1=value1"));
        assertTrue(result.contains("key2[nestedkey]=nestedValue"));
    }

    @Test
    public void testHttpBuildQueryWithEmptyMap() {
        Map<String, Object> params = new HashMap<>();
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertEquals("", result);
    }

    @Test
    public void testHttpBuildQueryWithSingleKeyValuePair() {
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");
        String result = HttpBuildQuery.httpBuildQuery(params);
        assertEquals("key=value", result);
    }

}