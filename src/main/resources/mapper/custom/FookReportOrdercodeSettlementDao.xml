<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportOrdercodeSettlementDao">
    <select id="getSettlementFromOrderCode" resultMap="BaseResultMap">
        SELECT
        b.id AS businessid,
        b.name AS businessname,
        b.js_account AS bankaccount,
        b.js_name AS bankname,
        b.js_bank AS bank,
        a.create_time AS createtime,
        c.id AS ordercodeid,
        c.user_time AS usetime,
        c.apportion_bill_amount AS billamount,
        c.apportion_bill_final_amount AS userpaymentamount,
        c.apportion_mpayintegral AS mpayintegral,
        c.apportion_momecoins AS momecoinsamount,
        c.apportion_commission AS commission,
        c.merch_settle_amount AS merchantsettleamount,
        c.code AS voucherCode,
        c.shopid AS storeid,
        s.name AS storename,
        i.title_snapshots AS vouchername,
        a.order_transaction AS ordertransaction,
        a.is_mpay AS is_mpay,
        a.is_member AS is_member,
        c.apportion_memberintegral as memberintegral,
        i.is_settlement,
        i.is_voucher,
        c.is_a_open,
        i.retail_price,
        bp.third_party_settlement_price,
        bp.a_fee_type,
        c.tracking_no,
        c.business_redeem_time
        FROM fook_platform_ordercode AS c
        LEFT JOIN fook_platform_order AS a ON a.id = c.orderid
        LEFT JOIN fook_platform_orderinfo AS i ON i.orderid = a.id
        LEFT JOIN fook_stores AS s ON s.id = c.shopid
        LEFT JOIN fook_business AS b ON b.id = s.business_id
        LEFT JOIN fook_business_product AS bp ON bp.id = i.prodcutid
        WHERE c.id = #{id,jdbcType=INTEGER}
        AND a.type = '1'
        AND a.status = '2'
        AND c.refund_status = '1'
        <if test ="!isWithDraw">
            AND c.status = '2'
        </if>
        ORDER BY b.id ASC, c.user_time ASC
    </select>
    
    <select id="selectUnsettlementBusinessIds" resultType="integer">
        <!-- 获取结算时间还没有结算的商家列表 -->
        select id as businessid from fook_business
        where status = '1'
        <![CDATA[
        and created_at < #{endTime,jdbcType=TIMESTAMP}
          ]]>

        union

        <!-- 處理商家凍結的結算 -->
        select distinct businessid
        from fook_report_ordercode_settlement
        where usetime >= #{startTime,jdbcType=TIMESTAMP}
          <![CDATA[
          and usetime <= #{endTime,jdbcType=TIMESTAMP}
          ]]>
        and status = '0'
    </select>

    <sql id="Unsettlement_Where">
        where usetime >= #{startTime,jdbcType=TIMESTAMP}
          <![CDATA[
        and usetime <= #{endTime,jdbcType=TIMESTAMP}
        ]]>
        and businessid = #{businessId,jdbcType=INTEGER}
        and is_settlement = '1'
        and is_a_open = #{isAlipayPlus,jdbcType=INTEGER}
        and status = '0'
    </sql>

    <select id="selectUnsettlementOrdercode" resultType="com.mcoin.mall.bean.FookReportOrdercodeSettlement">
        select <include refid="Base_Column_List"/>
        from fook_report_ordercode_settlement
        <include refid="Unsettlement_Where"/>
        limit #{limit,jdbcType=INTEGER}
    </select>

    <update id="batchUpdateSettlementStatus">
        update fook_report_ordercode_settlement
        set status = #{status,jdbcType=INTEGER},
            settlementtime = #{settlementTime,jdbcType=TIMESTAMP},
            settlementbusinessid = #{settlementId,jdbcType=INTEGER}
        where status = '0'
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateSettlementOptionalId">
        update fook_report_ordercode_settlement
        set
        optionalid = #{optionalId,jdbcType=INTEGER}
        where optionalid is null
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="sumRequireSettlementAmountBo" resultType="com.mcoin.mall.bo.OrderCodeSettlementAmountBo">
        select SUM(merchantsettleamount) as totalAmount,
               SUM(commission) as totalCommission
        from fook_report_ordercode_settlement
        where settlementtime between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and status = 1
    </select>

    <select id="selectUnsettlementIntenalOrdercode" resultType="com.mcoin.mall.bean.FookReportOrdercodeSettlement">
        select <include refid="Base_Column_List"/>
        from fook_report_ordercode_settlement
        where usetime >= #{startTime,jdbcType=TIMESTAMP}
        <![CDATA[
        and usetime <= #{endTime,jdbcType=TIMESTAMP}
        ]]>
        and internalid is null
        limit #{limit,jdbcType=INTEGER}
    </select>

    <update id="batchUpdateSettlementInternalId" >
        update fook_report_ordercode_settlement
        set internalid = #{internalId,jdbcType=INTEGER}
        where internalid is null
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectUnsettlementOrdercodeForOptional" resultType="com.mcoin.mall.bean.FookReportOrdercodeSettlement">
        select <include refid="Base_Column_List"/>
        from fook_report_ordercode_settlement
        where usetime >= #{startTime,jdbcType=TIMESTAMP}
        <![CDATA[
        and usetime <= #{endTime,jdbcType=TIMESTAMP}
        ]]>
        and businessid = #{businessId,jdbcType=INTEGER}
        and optionalid is null
        <if test="optionalType != 4">
            and is_settlement = '1'
        </if>
        limit #{limit,jdbcType=INTEGER}
    </select>

    <select id="sumSettlementAmountBoByOptionalId" resultType="com.mcoin.mall.bo.SettlementAmountBo">
        select SUM(billamount) as totalAmount,
               SUM(if(is_settlement=1, merchantsettleamount, 0)) as totalSettlement,
               SUM(if(is_settlement=1, commission, 0)) as totalCommission
        from fook_report_ordercode_settlement
        where optionalid = #{optionalId,jdbcType=INTEGER}
    </select>

    <select id="sumSubmitedMcoinCodeOrderAmount" resultType="com.mcoin.mall.bo.OrderAmountBo">
        select SUM(mpayintegral) as totalMpayIntegral
        from fook_report_ordercode_settlement
        where usetime between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          and is_mpay = 1
    </select>

    <select id="querySubmitedDataBetweenTime" resultType="com.mcoin.mall.bo.MpayOrderCodeSettlementBo">
        SELECT
            ocs.businessid,
            ocs.storeid,
            ocs.ordercodeid,
            ocs.usetime,
            oc.userid,
            oc.orderid,
            oc.code,
            oi.type,
            oi.title_snapshots as titleSnapshots
        FROM
            fook_report_ordercode_settlement ocs
                LEFT JOIN
            fook_platform_ordercode oc ON ocs.ordercodeid = oc.id
                LEFT JOIN
            fook_platform_orderinfo oi ON oc.orderinfo_id = oi.id
        WHERE
            ocs.usetime between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          AND ocs.is_mpay = 1 limit #{offset, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER};
    </select>

    <select id="countOrderCodeSettlementData" resultType="Integer">
        SELECT
            count(ocs.id)
        FROM
            fook_report_ordercode_settlement ocs
        WHERE
            usetime between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          AND ocs.is_mpay = 1
    </select>
</mapper>