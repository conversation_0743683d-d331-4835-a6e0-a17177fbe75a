# Nacos\u5E2E\u52A9\u6587\u6863: https://nacos.io/zh-cn/docs/concepts.html
# Nacos\u8BA4\u8BC1\u4FE1\u606F
spring.cloud.nacos.config.username=@spring.cloud.nacos.discovery.username@
spring.cloud.nacos.config.password=@spring.cloud.nacos.discovery.password@
spring.cloud.nacos.config.contextPath=/nacos
# \u8BBE\u7F6E\u914D\u7F6E\u4E2D\u5FC3\u670D\u52A1\u7AEF\u5730\u5740
spring.cloud.nacos.config.server-addr=@spring.cloud.nacos.discovery.server-addr@
# Nacos \u914D\u7F6E\u4E2D\u5FC3\u7684namespace\u3002\u9700\u8981\u6CE8\u610F\uFF0C\u5982\u679C\u4F7F\u7528 public \u7684 namcespace \uFF0C\u8BF7\u4E0D\u8981\u586B\u5199\u8FD9\u4E2A\u503C\uFF0C\u76F4\u63A5\u7559\u7A7A\u5373\u53EF
spring.cloud.nacos.config.namespace=@spring.profiles.active@
spring.cloud.nacos.config.group=MCOIN_GROUP
spring.cloud.nacos.config.file-extension=properties


# è®¾ç½®Sentinel Nacosæ°æ®æºéç½®
# 1ãæµæ§éç½®
spring.cloud.sentinel.datasource.flow.nacos.server-addr=${spring.cloud.nacos.discovery.server-addr}
spring.cloud.sentinel.datasource.flow.nacos.namespace=${spring.cloud.nacos.discovery.namespace}
spring.cloud.sentinel.datasource.flow.nacos.username=${spring.cloud.nacos.discovery.username}
spring.cloud.sentinel.datasource.flow.nacos.password=${spring.cloud.nacos.discovery.password}
spring.cloud.sentinel.datasource.flow.nacos.data-id=${spring.application.name}-flow-rules
spring.cloud.sentinel.datasource.flow.nacos.group-id=B_SENTINEL
spring.cloud.sentinel.datasource.flow.nacos.data-type=json
spring.cloud.sentinel.datasource.flow.nacos.rule-type=FLOW
# 2ãéçº§éç½®
spring.cloud.sentinel.datasource.degrade.nacos.server-addr=${spring.cloud.nacos.discovery.server-addr}
spring.cloud.sentinel.datasource.degrade.nacos.namespace=${spring.cloud.nacos.discovery.namespace}
spring.cloud.sentinel.datasource.degrade.nacos.username=${spring.cloud.nacos.discovery.username}
spring.cloud.sentinel.datasource.degrade.nacos.password=${spring.cloud.nacos.discovery.password}
spring.cloud.sentinel.datasource.degrade.nacos.dataId=${spring.application.name}-degrade-rules
spring.cloud.sentinel.datasource.degrade.nacos.groupId=B_SENTINEL
spring.cloud.sentinel.datasource.degrade.nacos.data-type=json
spring.cloud.sentinel.datasource.degrade.nacos.rule-type=DEGRADE
# 3ãç­ç¹åæ°éç½®
spring.cloud.sentinel.datasource.param-flow.nacos.server-addr=${spring.cloud.nacos.discovery.server-addr}
spring.cloud.sentinel.datasource.param-flow.nacos.namespace=${spring.cloud.nacos.discovery.namespace}
spring.cloud.sentinel.datasource.param-flow.nacos.dataId=${spring.application.name}-param-flow-rules
spring.cloud.sentinel.datasource.param-flow.nacos.username=${spring.cloud.nacos.discovery.username}
spring.cloud.sentinel.datasource.param-flow.nacos.password=${spring.cloud.nacos.discovery.password}
spring.cloud.sentinel.datasource.param-flow.nacos.groupId=B_SENTINEL
spring.cloud.sentinel.datasource.param-flow.nacos.data-type=json
spring.cloud.sentinel.datasource.param-flow.nacos.rule-type=PARAM_FLOW
# 4ãç³»ç»ä¿æ¤éç½®
spring.cloud.sentinel.datasource.system.nacos.server-addr=${spring.cloud.nacos.discovery.server-addr}
spring.cloud.sentinel.datasource.system.nacos.namespace=${spring.cloud.nacos.discovery.namespace}
spring.cloud.sentinel.datasource.system.nacos.username=${spring.cloud.nacos.discovery.username}
spring.cloud.sentinel.datasource.system.nacos.password=${spring.cloud.nacos.discovery.password}
spring.cloud.sentinel.datasource.system.nacos.dataId=${spring.application.name}-system-rules
spring.cloud.sentinel.datasource.system.nacos.groupId=B_SENTINEL
spring.cloud.sentinel.datasource.system.nacos.data-type=json
spring.cloud.sentinel.datasource.system.nacos.rule-type=SYSTEM
# 5ãè®¿é®æ§å¶éç½®
spring.cloud.sentinel.datasource.authority.nacos.server-addr=${spring.cloud.nacos.discovery.server-addr}
spring.cloud.sentinel.datasource.authority.nacos.namespace=${spring.cloud.nacos.discovery.namespace}
spring.cloud.sentinel.datasource.authority.nacos.username=${spring.cloud.nacos.discovery.username}
spring.cloud.sentinel.datasource.authority.nacos.password=${spring.cloud.nacos.discovery.password}
spring.cloud.sentinel.datasource.authority.nacos.dataId=${spring.application.name}-authority-rules
spring.cloud.sentinel.datasource.authority.nacos.groupId=B_SENTINEL
spring.cloud.sentinel.datasource.authority.nacos.data-type=json
spring.cloud.sentinel.datasource.authority.nacos.rule-type=AUTHORITY
# 6ãéç¾¤æµæ§éç½®
spring.cloud.sentinel.cluster-flow-control.nacos.serverAddr=${spring.cloud.nacos.discovery.server-addr}
spring.cloud.sentinel.cluster-flow-control.nacos.namespace=${spring.cloud.nacos.discovery.namespace}
spring.cloud.sentinel.cluster-flow-control.nacos.username=${spring.cloud.nacos.discovery.username}
spring.cloud.sentinel.cluster-flow-control.nacos.password=${spring.cloud.nacos.discovery.password}
spring.cloud.sentinel.cluster-flow-control.nacos.groupId=B_SENTINEL
spring.cloud.sentinel.cluster-flow-control.nacos.flowDataIdPostfix=-flow-rules
spring.cloud.sentinel.cluster-flow-control.nacos.paramFlowDataIdPostfix=-param-flow-rules
spring.cloud.sentinel.cluster-flow-control.nacos.clusterMapDataIdPostfix=-cluster-map
spring.cloud.sentinel.cluster-flow-control.nacos.clusterClientConfigDataIdPostfix=-cluster-client-config