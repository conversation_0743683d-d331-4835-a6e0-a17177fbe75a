package com.mcoin.mall.controller.business;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.AsiaMilesFromRequest;
import com.mcoin.mall.model.AsiaMilesUserInfoListResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

/**
 * 集成测试 AsiaMilesController
 * 使用真实服务和数据库，测试亚洲万里通相关功能
 */
@Slf4j
class AsiaMilesControllerTest extends BaseUnitTest {

    @Resource
    private AsiaMilesController asiaMilesController;

    @BeforeEach
    void setUp() {
        when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
    }

    @Test
    void testGetUserInfo() {
        // 直接调用控制器方法
        Response<AsiaMilesUserInfoListResponse> response = asiaMilesController.getUserInfo();
        
        // 验证响应结构
        assertNotNull(response);
        assertEquals(1, response.getData().getSnatchList().size());
    }

    @Test
    void testJudgeAsiaMiles() {
        // 创建请求对象
        AsiaMilesFromRequest request = new AsiaMilesFromRequest();
        request.setMiles_member("1001281251");
        request.setMiles_member_repeat("1001281251");
        request.setMiles_name_family("Smith");
        request.setMiles_name_given("John");
        
        // 直接调用控制器方法 - 注意：此方法预期会抛出业务异常，这是正常的测试行为

        Response<Object> response = asiaMilesController.judgeAsiaMiles(request);
        assertNotNull(response);
    }
} 