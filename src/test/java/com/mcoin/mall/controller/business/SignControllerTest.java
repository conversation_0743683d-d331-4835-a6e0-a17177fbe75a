package com.mcoin.mall.controller.business;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.SignConfigResponse;
import com.mcoin.mall.model.SignRequest;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

/**
 * 集成测试 SignController
 * 使用真实服务和数据库，测试签到配置功能
 */
class SignControllerTest extends BaseUnitTest {

    @Resource
    private SignController signController;

    @BeforeEach
    void setUp() {
        when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
    }

    @Test
    void testSignConfig() {
        // 创建请求对象
        SignRequest request = new SignRequest();
        request.setShowImg(1);
        
        // 直接调用控制器方法
        Response<SignConfigResponse> response = signController.signConfig(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 