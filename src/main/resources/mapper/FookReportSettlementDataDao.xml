<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportSettlementDataDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportSettlementData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
    <result column="bill_final_amount" jdbcType="DECIMAL" property="billFinalAmount" />
    <result column="point" jdbcType="INTEGER" property="point" />
    <result column="point_amount" jdbcType="DECIMAL" property="pointAmount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="jq_amount" jdbcType="DECIMAL" property="jqAmount" />
    <result column="use_time" jdbcType="TIMESTAMP" property="useTime" />
    <result column="shopid" jdbcType="VARCHAR" property="shopid" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="title_snapshots" jdbcType="VARCHAR" property="titleSnapshots" />
    <result column="trans_type" jdbcType="INTEGER" property="transType" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, order_no, code, retail_price, bill_final_amount, point, point_amount, 
    commission, jq_amount, use_time, shopid, shop_name, title_snapshots, trans_type, 
    upload_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_settlenedmt_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_settlenedmt_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportSettlementData">
    insert into fook_report_settlenedmt_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="billFinalAmount != null">
        bill_final_amount,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="pointAmount != null">
        point_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="jqAmount != null">
        jq_amount,
      </if>
      <if test="useTime != null">
        use_time,
      </if>
      <if test="shopid != null">
        shopid,
      </if>
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="titleSnapshots != null">
        title_snapshots,
      </if>
      <if test="transType != null">
        trans_type,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="billFinalAmount != null">
        #{billFinalAmount,jdbcType=DECIMAL},
      </if>
      <if test="point != null">
        #{point,jdbcType=INTEGER},
      </if>
      <if test="pointAmount != null">
        #{pointAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="jqAmount != null">
        #{jqAmount,jdbcType=DECIMAL},
      </if>
      <if test="useTime != null">
        #{useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopid != null">
        #{shopid,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="titleSnapshots != null">
        #{titleSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="transType != null">
        #{transType,jdbcType=INTEGER},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportSettlementData">
    update fook_report_settlenedmt_data
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="billFinalAmount != null">
        bill_final_amount = #{billFinalAmount,jdbcType=DECIMAL},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=INTEGER},
      </if>
      <if test="pointAmount != null">
        point_amount = #{pointAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="jqAmount != null">
        jq_amount = #{jqAmount,jdbcType=DECIMAL},
      </if>
      <if test="useTime != null">
        use_time = #{useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopid != null">
        shopid = #{shopid,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="titleSnapshots != null">
        title_snapshots = #{titleSnapshots,jdbcType=VARCHAR},
      </if>
      <if test="transType != null">
        trans_type = #{transType,jdbcType=INTEGER},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportSettlementData">
    update fook_report_settlenedmt_data
    set `type` = #{type,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      bill_final_amount = #{billFinalAmount,jdbcType=DECIMAL},
      point = #{point,jdbcType=INTEGER},
      point_amount = #{pointAmount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      jq_amount = #{jqAmount,jdbcType=DECIMAL},
      use_time = #{useTime,jdbcType=TIMESTAMP},
      shopid = #{shopid,jdbcType=VARCHAR},
      shop_name = #{shopName,jdbcType=VARCHAR},
      title_snapshots = #{titleSnapshots,jdbcType=VARCHAR},
      trans_type = #{transType,jdbcType=INTEGER},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>