package com.mcoin.mall.util;

import org.junit.jupiter.api.Test;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static org.junit.jupiter.api.Assertions.*;

public class MoneyUtilTest{

    @Test
    public void testDivide_ZeroDivisor() {
        BigDecimal amount1 = new BigDecimal("100");
        BigDecimal amount2 = new BigDecimal("0");
        assertThrows(ArithmeticException.class, () -> MoneyUtil.divide(amount1, amount2), "Divide by zero should throw ArithmeticException");
    }

    @Test
    public void testDivide_LargeNumbers() {
        BigDecimal amount1 = new BigDecimal("1234567890.12");
        BigDecimal amount2 = new BigDecimal("9876543210.98");
        BigDecimal expected = new BigDecimal("1234567890.12").divide(new BigDecimal("9876543210.98"), 2, RoundingMode.HALF_UP);
        BigDecimal result = MoneyUtil.divide(amount1, amount2);
        assertEquals(expected, result, "Division of large numbers should be close to 1.01");
    }

    @Test
    public void testDivide_NormalCase() {
        BigDecimal amount1 = new BigDecimal("100");
        BigDecimal amount2 = new BigDecimal("5");
        BigDecimal expected = new BigDecimal("20.00");
        BigDecimal result = MoneyUtil.divide(amount1, amount2);
        assertEquals(expected, result, "Division should be 20");
    }

    @Test
    public void testDivide_DivideByZero() {
        BigDecimal amount1 = new BigDecimal("100");
        BigDecimal amount2 = new BigDecimal("0");
        assertThrows(ArithmeticException.class, () -> MoneyUtil.divide(amount1, amount2), "Divide by zero should throw ArithmeticException");
    }

    @Test
    public void testDivide_NegativeNumbers() {
        BigDecimal amount1 = new BigDecimal("-100");
        BigDecimal amount2 = new BigDecimal("5");
        BigDecimal expected = new BigDecimal("-20.00");
        BigDecimal result = MoneyUtil.divide(amount1, amount2);
        assertEquals(expected, result, "Division of negative numbers should be -20");
    }

    @Test
    public void testDivide_PositiveAndNegativeNumbers() {
        BigDecimal amount1 = new BigDecimal("100");
        BigDecimal amount2 = new BigDecimal("-5");
        BigDecimal expected = new BigDecimal("-20.00");
        BigDecimal result = MoneyUtil.divide(amount1, amount2);
        assertEquals(expected, result, "Division of positive and negative numbers should be -20");
    }

    @Test
    public void testDivide_ZeroDividend() {
        BigDecimal amount1 = new BigDecimal("0");
        BigDecimal amount2 = new BigDecimal("5");
        BigDecimal expected = new BigDecimal("0.00");
        BigDecimal result = MoneyUtil.divide(amount1, amount2);
        assertEquals(expected, result, "Division of zero dividend should be 0");
    }
    @Test
    public void testFormat_PositiveNumber() {
        BigDecimal amount = new BigDecimal("123.45");
        String expected = "123.45";
        String actual = MoneyUtil.format(amount);
        assertEquals(expected, actual, "Formatted amount should match the expected value");
    }

    @Test
    public void testFormat_NegativeNumber() {
        BigDecimal amount = new BigDecimal("-678.90");
        String expected = "-678.90";
        String actual = MoneyUtil.format(amount);
        assertEquals(expected, actual, "Formatted amount should match the expected value");
    }

    @Test
    public void testFormat_Zero() {
        BigDecimal amount = new BigDecimal("0");
        String expected = "0.00";
        String actual = MoneyUtil.format(amount);
        assertEquals(expected, actual, "Formatted amount should match the expected value");
    }

    @Test
    public void testFormat_Integer() {
        BigDecimal amount = new BigDecimal("12345");
        String expected = "12345.00";
        String actual = MoneyUtil.format(amount);
        assertEquals(expected, actual, "Formatted amount should match the expected value");
    }

    @Test
    public void testFormat_DecimalWithNoFraction() {
        BigDecimal amount = new BigDecimal("12345.00");
        String expected = "12345.00";
        String actual = MoneyUtil.format(amount);
        assertEquals(expected, actual, "Formatted amount should match the expected value");
    }
}