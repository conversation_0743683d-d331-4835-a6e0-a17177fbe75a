<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresTypeDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookStoresType">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="traditional_name" jdbcType="VARCHAR" property="traditionalName" />
    <result column="simplified_name" jdbcType="VARCHAR" property="simplifiedName" />
    <result column="english_name" jdbcType="VARCHAR" property="englishName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="if_index_show" jdbcType="BIT" property="ifIndexShow" />
    <result column="if_hot" jdbcType="BIT" property="ifHot" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="distinguish" jdbcType="TINYINT" property="distinguish" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pid, `name`, traditional_name, simplified_name, english_name, sort, `enable`,
    icon, `type`, if_index_show, if_hot, code, distinguish, business_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_stores_type
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_stores_type
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookStoresType">
    insert into fook_stores_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="traditionalName != null">
        traditional_name,
      </if>
      <if test="simplifiedName != null">
        simplified_name,
      </if>
      <if test="englishName != null">
        english_name,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="ifIndexShow != null">
        if_index_show,
      </if>
      <if test="ifHot != null">
        if_hot,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="distinguish != null">
        distinguish,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="traditionalName != null">
        #{traditionalName,jdbcType=VARCHAR},
      </if>
      <if test="simplifiedName != null">
        #{simplifiedName,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="ifIndexShow != null">
        #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="ifHot != null">
        #{ifHot,jdbcType=BIT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="distinguish != null">
        #{distinguish,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookStoresType">
    update fook_stores_type
    <set>
      <if test="pid != null">
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="traditionalName != null">
        traditional_name = #{traditionalName,jdbcType=VARCHAR},
      </if>
      <if test="simplifiedName != null">
        simplified_name = #{simplifiedName,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        english_name = #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="ifIndexShow != null">
        if_index_show = #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="ifHot != null">
        if_hot = #{ifHot,jdbcType=BIT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="distinguish != null">
        distinguish = #{distinguish,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookStoresType">
    update fook_stores_type
    set pid = #{pid,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      traditional_name = #{traditionalName,jdbcType=VARCHAR},
      simplified_name = #{simplifiedName,jdbcType=VARCHAR},
      english_name = #{englishName,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      `enable` = #{enable,jdbcType=BIT},
      icon = #{icon,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      if_index_show = #{ifIndexShow,jdbcType=BIT},
      if_hot = #{ifHot,jdbcType=BIT},
      code = #{code,jdbcType=VARCHAR},
      distinguish = #{distinguish,jdbcType=TINYINT},
      business_id = #{businessId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>