package com.mcoin.mall.service.job.utils;

import static org.junit.jupiter.api.Assertions.*;

import com.mcoin.mall.util.JodaTimeUtil;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;

public class JodaTimeUtilTest {

    @Test
    public void testParse() {
        String dateStr = "2023-10-01 12:30:00";
        Date date = JodaTimeUtil.parse(dateStr);
        assertNotNull(date);
        assertEquals("20231001", JodaTimeUtil.format(date, JodaTimeUtil.YYYYMMDD));
    }

    @Test
    public void testParseWithPattern() {
        String dateStr = "2023-10-01 12:30:00";
        Date date = JodaTimeUtil.parse(dateStr, JodaTimeUtil.DEFAULT_PATTERN);
        assertNotNull(date);
        assertEquals("2023-10-01 12:30:00", JodaTimeUtil.format(date));
    }

    @Test
    public void testGetDate() {
        String date = JodaTimeUtil.getDate();
        assertNotNull(date);
        assertEquals(8, date.length()); // yyyyMMdd
    }

    @Test
    public void testGetDate2() {
        String date = JodaTimeUtil.getDate2();
        assertNotNull(date);
        assertEquals(10, date.length()); // yyyy-MM-dd
    }

    @Test
    public void testGetTime() {
        String time = JodaTimeUtil.getTime();
        assertNotNull(time);
        assertEquals(6, time.length()); // HHmmss
    }

    @Test
    public void testGetStandardTime() {
        String standardTime = JodaTimeUtil.getStandardTime();
        assertNotNull(standardTime);
        assertEquals(19, standardTime.length()); // yyyy-MM-dd HH:mm:ss
    }

    @Test
    public void testFormat() {
        Date date = new Date();
        String formattedDate = JodaTimeUtil.format(date);
        assertNotNull(formattedDate);
    }

    @Test
    public void testFormatWithPattern() {
        Date date = new Date();
        String formattedDate = JodaTimeUtil.format(date, JodaTimeUtil.YYYYMMDD);
        assertNotNull(formattedDate);
        assertEquals(8, formattedDate.length()); // yyyyMMdd
    }

    @Test
    public void testIfGrownUp() throws ParseException {
        String birthday = "2001-10-01 12:30:00"; // 18岁
        boolean result = JodaTimeUtil.ifGrownUp(birthday, JodaTimeUtil.DEFAULT_PATTERN);
        assertTrue(result);
    }

    @Test
    public void testGetDelayTime() {
        String delayTime = JodaTimeUtil.getDelayTime("90m");
        assertNotNull(delayTime);
    }

    @Test
    public void testGetDelayLimitTime() {
        String delayLimitTime = JodaTimeUtil.getDelayLimitTime("90m", 1);
        assertNotNull(delayLimitTime);
    }

    @Test
    public void testFormatByString() {
        String formatted = JodaTimeUtil.formatByString("2023-10-01", "yyyy-MM-dd", "yyyyMMdd");
        assertEquals("20231001", formatted);
    }

    @Test
    public void testValidDate() {
        String validDate = "20231001";
        String invalidDate = "20231301"; // 不存在的日期
        boolean isValid = JodaTimeUtil.validDate(validDate, JodaTimeUtil.YYYYMMDD);
        boolean isInvalid = JodaTimeUtil.validDate(invalidDate, JodaTimeUtil.YYYYMMDD);
        assertTrue(isValid);
        assertFalse(isInvalid);
    }

    @Test
    public void testIsTimeout() throws ParseException {
        String sourceDay = "2023-10-01 12:00:00";
        String desDay = "2023-10-01 12:30:00";
        boolean result = JodaTimeUtil.isTimeout(sourceDay, desDay, JodaTimeUtil.DEFAULT_PATTERN, Calendar.MINUTE, 30);
        assertFalse(result);
    }

    @Test
    public void testGetDateYear() {
        String year = JodaTimeUtil.getDateYear();
        assertNotNull(year);
        assertEquals(4, year.length()); // yyyy
    }

    @Test
    public void testGetDateMonth() {
        String month = JodaTimeUtil.getDateMonth();
        assertNotNull(month);
        assertEquals(2, month.length()); // MM
    }

    @Test
    public void testGetDateDay() {
        String day = JodaTimeUtil.getDateDay();
        assertNotNull(day);
        assertEquals(2, day.length()); // dd
    }

    @Test
    public void testGetNow() {
        String now = JodaTimeUtil.getNow();
        assertNotNull(now);
        assertEquals(8, now.length()); // yyyyMMdd
    }

    @Test
    public void testGetDateTime() {
        String dateTime = JodaTimeUtil.getDateTime();
        assertNotNull(dateTime);
        assertEquals(19, dateTime.length()); // yyyy-MM-dd HH:mm:ss
    }

    @Test
    public void testGetAnotherDate() throws ParseException {
        String nowDate = "20231001"; // yyyyMMdd
        String newDate = JodaTimeUtil.getAnotherDate(nowDate, 5);
        assertNotNull(newDate);
        assertEquals("20231006", newDate); // 5天后
    }

    @Test
    public void testGetAnotherDateWithDate() {
        Date nowDate = new Date();
        Date newDate = JodaTimeUtil.getAnotherDate(nowDate, 5);
        assertNotNull(newDate);
    }

    @Test
    public void testGetAnotherWeek() throws ParseException {
        String nowDate = "20231001"; // yyyyMMdd
        String newDate = JodaTimeUtil.getAnotherWeek(nowDate, 1);
        assertNotNull(newDate);
    }

    @Test
    public void testGetAnotherMonth() throws ParseException {
        String nowDate = "20231001"; // yyyyMMdd
        String newDate = JodaTimeUtil.getAnotherMonth(nowDate, 1);
        assertNotNull(newDate);
    }

    @Test
    public void testGetsCurrentDate() {
        int year = JodaTimeUtil.getsCurrentDate("YEAR");
        int month = JodaTimeUtil.getsCurrentDate("MONTH");
        int date = JodaTimeUtil.getsCurrentDate("DATE");
        int dayOfWeek = JodaTimeUtil.getsCurrentDate("DAY_OF_WEEK");

        assertTrue(year > 0);
        assertTrue(month > 0 && month <= 12);
        assertTrue(date > 0 && date <= 31);
        assertTrue(dayOfWeek >= 0 && dayOfWeek <= 6);
    }

    @Test
    public void testGetWeek() {
        String weekDate = JodaTimeUtil.getWeek(1);
        assertNotNull(weekDate);
    }

    @Test
    public void testGetTimestamp() throws ParseException {
        String timeStr = "2023-10-01 12:30:00";
        Long timestamp = JodaTimeUtil.getTimestamp(timeStr, JodaTimeUtil.DEFAULT_PATTERN);
        assertNotNull(timestamp);
        assertTrue(timestamp > 0);
    }

    @Test
    public void testGetConverDate() throws ParseException {
        String nowDate = "2023-10-01";
        String newDate = JodaTimeUtil.getConverDate(nowDate, "yyyy-MM-dd", Calendar.DAY_OF_MONTH, 5);
        assertNotNull(newDate);
    }

    @Test
    public void testFormatSeconds() {
        String formatted = JodaTimeUtil.formatSeconds(3661);
        assertEquals("1小时1分1秒", formatted);
    }

    @Test
    public void testGetDistanceOfTwoDate() {
        Date before = new Date();
        Date after = JodaTimeUtil.plusSecondToDate(before, 120); // 2分钟后
        double distance = JodaTimeUtil.getDistanceOfTwoDate(before, after, "s");
        assertEquals(120.0, distance);
    }

    @Test
    public void testGetAnotherMinute() {
        Date nowDate = new Date();
        Date newDate = JodaTimeUtil.getAnotherMinute(nowDate, 5);
        assertNotNull(newDate);
    }

    @Test
    public void testGetRemainSecondsOneDay() {
        int seconds = JodaTimeUtil.getRemainSecondsOneDay(new Date());
        assertTrue(seconds >= 0);
    }

    @Test
    public void testFormat4Mcoin() {
        String formatted = JodaTimeUtil.format4Mcoin(new Date(), "en");
        assertNotNull(formatted);
    }

    @Test
    public void testGetCurrentDateInSpecificFormat() {
        String formatted = JodaTimeUtil.getCurrentDateInSpecificFormat(new Date());
        assertNotNull(formatted);
    }

    @Test
    public void testGetDayNumberSuffix() {
        String suffix = JodaTimeUtil.getDayNumberSuffix(1);
        assertEquals("st", suffix);
    }

    @Test
    public void testPlusHours() {
        String newTime = JodaTimeUtil.plusHours(1);
        assertNotNull(newTime);
    }

    @Test
    public void testPlusHoursWithDate() {
        String newTime = JodaTimeUtil.plusHours(new Date(), 1);
        assertNotNull(newTime);
    }

    @Test
    public void testPlusMinute() {
        String newTime = JodaTimeUtil.plusMinute(new Date(), 1);
        assertNotNull(newTime);
    }

    @Test
    public void testPlusSecond() {
        String newTime = JodaTimeUtil.plusSecond(new Date(), 1);
        assertNotNull(newTime);
    }

    @Test
    public void testPlusDay() {
        String newDate = JodaTimeUtil.plusDay(new Date(), 1);
        assertNotNull(newDate);
    }

    @Test
    public void testPlusDayYYYYMMDD() {
        String newDate = JodaTimeUtil.plusDayYYYYMMDD(new Date(), 1);
        assertNotNull(newDate);
    }

    @Test
    public void testPlusMonthToDate() {
        Date newDate = JodaTimeUtil.plusMonthToDate(new Date(), 1);
        assertNotNull(newDate);
    }

    @Test
    public void testGetDateWithoutTime() {
        Date dateWithoutTime = JodaTimeUtil.getDateWithoutTime(new Date());
        assertNotNull(dateWithoutTime);
    }

    @Test
    public void testAddDateHours() {
        String newDate = JodaTimeUtil.addDateHours(1, JodaTimeUtil.DEFAULT_PATTERN);
        assertNotNull(newDate);
    }

    @Test
    public void testAddDateSeconds() {
        Date newDate = JodaTimeUtil.addDateSeconds(60);
        assertNotNull(newDate);
    }

    @Test
    public void testIsAfter() {
        String beginTime = "2023-10-01 12:00:00";
        String endTime = "2023-10-01 12:30:00";
        boolean result = JodaTimeUtil.isAfter(beginTime, endTime, JodaTimeUtil.DEFAULT_PATTERN);
        assertFalse(result);
    }

    @Test
    public void testGetMonthFirstDay() {
        Date firstDay = JodaTimeUtil.getMonthFirstDay();
        assertNotNull(firstDay);
    }

    @Test
    public void testGetNextMonthFirstDay() {
        Date nextFirstDay = JodaTimeUtil.getNextMonthFirstDay();
        assertNotNull(nextFirstDay);
    }

    @Test
    public void testToLocalDate() {
        LocalDate localDate = JodaTimeUtil.toLocalDate(new Date());
        assertNotNull(localDate);
    }

    @Test
    public void testToDate() {
        LocalDate localDate = LocalDate.now();
        Date date = JodaTimeUtil.toDate(localDate);
        assertNotNull(date);
    }

    @Test
    public void testGetSecondsWithoutSecondsLastDigit() {
        int seconds = JodaTimeUtil.getSecondsWithoutSecondsLastDigit(new Date());
        assertTrue(seconds >= 0);
    }
}
