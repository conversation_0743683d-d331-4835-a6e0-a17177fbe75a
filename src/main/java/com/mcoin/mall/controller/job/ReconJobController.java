package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.ReconCheckPayRequest;
import com.mcoin.mall.service.job.ReconJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/recon")
@Slf4j
public class ReconJobController {
    @Resource
    private ReconJobService reconJobService;

    /**
     * 支付对账 (
     * PHP:pay:check CheckPayTask
     * PHP:couponspay:check CheckCouponsPayTask
     * )
     *
     * @return
     */
    @PostMapping(value = "/checkPay")
    public Response<String> checkPay(@Valid ReconCheckPayRequest request) {
        reconJobService.triggerCheckPayMessages(request);
        return Responses.ok("success");
    }

}
