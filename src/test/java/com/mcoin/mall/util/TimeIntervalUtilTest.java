package com.mcoin.mall.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class TimeIntervalUtilTest {

    private static final int SECONDS_TO_MILLIS = 1000;
    private static final int MINUTES_TO_SECONDS = 60;
    private static final int HOURS_TO_MINUTES = 60;
    private static final int DAY_TO_HOURS = 24;
    private static final int HOUR_TO_MILLIS = HOURS_TO_MINUTES * MINUTES_TO_SECONDS * SECONDS_TO_MILLIS;
    private static final int MINUTE_TO_MILLIS = MINUTES_TO_SECONDS * SECONDS_TO_MILLIS;
    private static final int DAY_TO_MILLIS = DAY_TO_HOURS * HOUR_TO_MILLIS;

    @Test
    void parseIntervalToMillis_nullInput() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis(null));
    }

    @Test
    void parseIntervalToMillis_emptyInput() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis(""));
    }

    @Test
    void parseIntervalToMillis_seconds() {
        assertEquals(5 * SECONDS_TO_MILLIS, TimeIntervalUtil.parseIntervalToMillis("5s"));
    }

    @Test
    void parseIntervalToMillis_minutes() {
        assertEquals(10 * MINUTE_TO_MILLIS, TimeIntervalUtil.parseIntervalToMillis("10m"));
    }

    @Test
    void parseIntervalToMillis_hours() {
        assertEquals(2 * HOUR_TO_MILLIS, TimeIntervalUtil.parseIntervalToMillis("2h"));
    }

    @Test
    void parseIntervalToMillis_days() {
        assertEquals(3 * DAY_TO_MILLIS, TimeIntervalUtil.parseIntervalToMillis("3d"));
    }

    @Test
    void parseIntervalToMillis_milliseconds() {
        assertEquals(500, TimeIntervalUtil.parseIntervalToMillis("500"));
    }

    @Test
    void parseIntervalToMillis_invalidInput_suffixOnly() {
        // Assuming System.err.println for invalid format, the method returns 0
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("m"));
    }

    @Test
    void parseIntervalToMillis_invalidInput_noNumber() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("abc"));
    }

    @Test
    void parseIntervalToMillis_invalidInput_wrongSuffix() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("5x"));
    }
    
    @Test
    void parseIntervalToMillis_zeroSeconds() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("0s"));
    }

    @Test
    void parseIntervalToMillis_zeroMinutes() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("0m"));
    }

    @Test
    void parseIntervalToMillis_zeroHours() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("0h"));
    }

    @Test
    void parseIntervalToMillis_zeroDays() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("0d"));
    }

    @Test
    void parseIntervalToMillis_zeroMilliseconds() {
        assertEquals(0, TimeIntervalUtil.parseIntervalToMillis("0"));
    }

    @Test
    void parseIntervalToMillis_largeNumberOfSeconds() {
        assertEquals(100000 * SECONDS_TO_MILLIS, TimeIntervalUtil.parseIntervalToMillis("100000s"));
    }

    @Test
    void parseIntervalToMillis_largeNumberOfMinutes() {
        assertEquals(10000 * MINUTE_TO_MILLIS, TimeIntervalUtil.parseIntervalToMillis("10000m"));
    }
} 