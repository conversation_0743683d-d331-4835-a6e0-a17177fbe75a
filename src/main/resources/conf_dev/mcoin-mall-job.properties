
# å³å
job.order.close.back.minutes=-15
job.order.close.max.minutes=120
# ç¶ææ¥è¯¢
job.order.query.status.back.minutes=-2
job.order.query.status.max.minutes=30

# æª¢æ¥å·²æ¶è³¼åº«å­
job.check.snapping.stock.back.minutes=-15
job.check.snapping.stock.max.minutes=120

# å¯¹è´¦æ£æ¥æ¯ä»åè¾¹è´¦
job.recon.check.pay.back.minutes=35
job.recon.check.pay.max.minutes=60

# æ£æ¥æªä¸ä¼ ç»ç®æ¥è¡¨
job.settlement.report.upload.back.minutes=-120
job.settlement.report.upload.max.minutes=4320

# æ£æ¥æªåéç»ç®æ¥è¡¨
job.settlement.report.send.back.minutes=0
job.settlement.report.send.max.minutes=4320

# æ£æ¥æªä¸ä¼ å¯éç»ç®æ¥è¡¨
job.settlement.optional.report.upload.back.minutes=-120
job.settlement.optional.report.upload.max.minutes=4320

# æ£æ¥æªåéå¯éç»ç®æ¥è¡¨
job.settlement.optional.report.send.back.minutes=0
job.settlement.optional.report.send.max.minutes=4320

# åéé®ä»¶å¼å§/ç»ææ¶é´
job.email.send.start=9:0
job.email.send.end=18:0

# åæ­¥å¸ç¢¼ä¿¡æ¯å°å¸å¹³å°
job.syn.voucher.code.to.coupon.back.minutes=-10

# æª¢æ¸¬ä¸æ¬¡å¸ç¢¼æ¯å¦æéè¤
job.order.code.check.repeat.back.minutes=-10

