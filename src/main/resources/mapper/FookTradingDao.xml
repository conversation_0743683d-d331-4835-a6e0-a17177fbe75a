<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookTrading">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="cycle_type" jdbcType="BIT" property="cycleType" />
    <result column="send_day" jdbcType="INTEGER" property="sendDay" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="file_oss_link" jdbcType="VARCHAR" property="fileOssLink" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, start_time, end_time, cycle_type, send_day, `status`, `desc`, file_oss_link, 
    created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_trading
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_trading
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookTrading">
    insert into fook_trading
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="cycleType != null">
        cycle_type,
      </if>
      <if test="sendDay != null">
        send_day,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="fileOssLink != null">
        file_oss_link,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleType != null">
        #{cycleType,jdbcType=BIT},
      </if>
      <if test="sendDay != null">
        #{sendDay,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="fileOssLink != null">
        #{fileOssLink,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookTrading">
    update fook_trading
    <set>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleType != null">
        cycle_type = #{cycleType,jdbcType=BIT},
      </if>
      <if test="sendDay != null">
        send_day = #{sendDay,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="fileOssLink != null">
        file_oss_link = #{fileOssLink,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookTrading">
    update fook_trading
    set start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      cycle_type = #{cycleType,jdbcType=BIT},
      send_day = #{sendDay,jdbcType=INTEGER},
      `status` = #{status,jdbcType=BIT},
      `desc` = #{desc,jdbcType=VARCHAR},
      file_oss_link = #{fileOssLink,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>