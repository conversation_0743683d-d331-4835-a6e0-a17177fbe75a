package com.mcoin.mall.service.management.impl;

import com.alibaba.fastjson.JSON;
import com.mcoin.mall.bean.FookShowSnapupSession;
import com.mcoin.mall.client.MpMcoinMallManagementClient;
import com.mcoin.mall.client.model.mp.management.ActivityTimeToGoodsBuyTimeRequest;
import com.mcoin.mall.client.model.mp.management.DeleteActivityInfoRequest;
import com.mcoin.mall.client.model.mp.management.MpMcoinMallResponse;
import com.mcoin.mall.client.model.mp.management.SyncActivityInfoRequest;
import com.mcoin.mall.constant.SessionStatus;
import com.mcoin.mall.constant.YesNo;
import com.mcoin.mall.dao.FookShowSnapupSessionDao;
import com.mcoin.mall.dao.FookShowSnapupSessionProductDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.management.SyncSessionInfoRequest;
import com.mcoin.mall.mq.model.SessionTimeToProductBuyTimeMessage;
import com.mcoin.mall.service.management.SnapUpManagementService;
import com.mcoin.mall.util.JodaTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;

@Service
@Slf4j
public class SnapUpManagementServiceImpl implements SnapUpManagementService {
    @Resource
    private FookShowSnapupSessionDao fookShowSnapupSessionDao;
    @Resource
    private FookShowSnapupSessionProductDao fookShowSnapupSessionProductDao;
    @Resource
    private RabbitTemplate sessionTimeToProductBuyTimeTemplateDelay;
    @Resource
    private RabbitTemplate sessionTimeToProductBuyTimeTemplate;
    @Resource
    private MpMcoinMallManagementClient mpMcoinMallManagementClient;

    @Override
    public void syncSessionInfo(SyncSessionInfoRequest request) {
        if (!"DELETE_SESSION".equals(request.getAction())) {
            FookShowSnapupSession session = fookShowSnapupSessionDao.selectByPrimaryKey(request.getSessionId());
            if (session == null){
                throw new BusinessException(Response.Code.BAD_REQUEST, "活動不存在");
            }
            List<String> uuidsBySessionId = fookShowSnapupSessionProductDao.selectMpUUIDsBySessionId(request.getSessionId());
            // 同步场次信息到小程序
            SyncActivityInfoRequest syncActivityInfoRequest = new SyncActivityInfoRequest();
            syncActivityInfoRequest.setSessionId(request.getSessionId());
            syncActivityInfoRequest.setName(String.format("%s秒殺", JodaTimeUtil.format(session.getStartTime(), "yyyy-MM-dd HH:mm")));
            syncActivityInfoRequest.setStartTime(session.getStartTime());
            syncActivityInfoRequest.setEndTime(session.getEndTime());
            syncActivityInfoRequest.setAction(request.getAction());
            Date now = new Date();
            Integer status = SessionStatus.from(now, session.getStartTime(), session.getEndTime());
            // 活动状态[1=未开始，2=进行中, 3=已结束]
            if (session.getValid() == YesNo.NO.getValue()) {
                status = SessionStatus.ENDED.getCode();
            }
            // 场次状态：0-抢购中；1-即将开抢；2-已结束
            switch (status) {
                case 0:
                    syncActivityInfoRequest.setStatus(2);
                    break;
                case 1:
                    syncActivityInfoRequest.setStatus(1);
                    break;
                case 2:
                    syncActivityInfoRequest.setStatus(3);
                    break;
                default:
                    throw new BusinessException(Response.Code.BAD_REQUEST, "活动状态错误");
            }
            syncActivityInfoRequest.setUuids(uuidsBySessionId);
            MpMcoinMallResponse<Object> response = mpMcoinMallManagementClient.syncActivityInfo(syncActivityInfoRequest);
            if (response.getCode() != MpMcoinMallResponse.Code.OK.getCode()) {
                throw new BusinessException(Response.Code.BAD_REQUEST, defaultIfNull(response.getMsg(),
                        "同步場次資訊到小程序失敗"));
            }

            if (session.getValid() == YesNo.YES.getValue()) {
                // 发送购买时间同步延时消息
                SessionTimeToProductBuyTimeMessage message = new SessionTimeToProductBuyTimeMessage();
                message.setSessionId(request.getSessionId());
                Message msg = new Message(JSON.toJSONBytes(message));
                msg.getMessageProperties().setMessageId(String.valueOf(request.getSessionId()));
                Integer status2 = SessionStatus.from(now, session.getStartTime(), session.getEndTime());
                if (status2 == SessionStatus.SOON.getCode()) {
                    long delay = session.getStartTime().getTime() - now.getTime();
                    msg.getMessageProperties().setDelay((int)delay);
                    sessionTimeToProductBuyTimeTemplateDelay.convertAndSend(msg);
                    log.info("发送购买时间同步延时（{}ms）消息：{}", msg.getMessageProperties().getDelay(), new String(msg.getBody()));
                } else if (status2 == SessionStatus.SNATCHING.getCode()){
                    sessionTimeToProductBuyTimeTemplate.convertAndSend(msg);
                    log.info("发送购买时间同步延时（0ms）消息：{}", new String(msg.getBody()));
                } else {
                    log.info("场次已经结束，不用更新时间：{}", session);
                }
            }
        } else {
            DeleteActivityInfoRequest deleteActivityInfoRequest = new DeleteActivityInfoRequest();
            deleteActivityInfoRequest.setSessionId(request.getSessionId());
            MpMcoinMallResponse<Object> response = mpMcoinMallManagementClient.deleteActivityInfo(deleteActivityInfoRequest);
            if (response.getCode() != MpMcoinMallResponse.Code.OK.getCode()) {
                throw new BusinessException(Response.Code.BAD_REQUEST, defaultIfNull(response.getMsg(),
                        "刪除小程式的場次信息失敗"));
            }
        }
    }

    @Override
    public void updSessionTimeToProductBuyTime(SessionTimeToProductBuyTimeMessage message) {
        FookShowSnapupSession session = fookShowSnapupSessionDao.selectByPrimaryKey(message.getSessionId());
        if (session == null){
            log.info("活動不存在：{}", message);
            return;
        }
        if (session.getValid() == YesNo.NO.getValue()) {
            log.info("活动尚未启用：{}", session);
            return;
        }
        Date now = new Date();
        Integer status = SessionStatus.from(now, session.getStartTime(), session.getEndTime());
        if (status == SessionStatus.SNATCHING.getCode()) {
            // 检查场次的时间和关联商品的时间是否一致
            int notEqualBuyTime = fookShowSnapupSessionProductDao.countSessionTimeNotEqualBuyTime(message.getSessionId());
            log.info("场次时间和关联商品时间不一致：{}", notEqualBuyTime);
            if (notEqualBuyTime > 0) {
                int updated = fookShowSnapupSessionProductDao.updateSessionProductBuyTime(session.getId(),
                        session.getStartTime(), session.getEndTime());
                log.info("更新{}条商品购买时间", updated);

                MpMcoinMallResponse<Object> response = mpMcoinMallManagementClient.activityTimeToGoodsBuyTime(new ActivityTimeToGoodsBuyTimeRequest()
                        .setSessionId(message.getSessionId()));
                if (response.getCode() != MpMcoinMallResponse.Code.OK.getCode()) {
                    throw new BusinessException(Response.Code.BAD_REQUEST, defaultIfNull(response.getMsg(),
                            "设置商品购买时间失败"));
                }
            }
        } else {
            log.info("场次不在抢购状态：{}", session);
        }
    }
}
