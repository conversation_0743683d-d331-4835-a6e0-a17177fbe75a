<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessProduct">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areaid" jdbcType="INTEGER" property="areaid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="zip_img" jdbcType="VARCHAR" property="zipImg" />
    <result column="zip_imgs" jdbcType="VARCHAR" property="zipImgs" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
    <result column="point_ratio" jdbcType="INTEGER" property="pointRatio" />
    <result column="stock" jdbcType="INTEGER" property="stock" />
    <result column="history_stock" jdbcType="INTEGER" property="historyStock" />
    <result column="sales" jdbcType="INTEGER" property="sales" />
    <result column="actual_sales" jdbcType="INTEGER" property="actualSales" />
    <result column="limited_number" jdbcType="INTEGER" property="limitedNumber" />
    <result column="is_allow_refund" jdbcType="INTEGER" property="isAllowRefund" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="comment_number" jdbcType="INTEGER" property="commentNumber" />
    <result column="shelf_status" jdbcType="BIT" property="shelfStatus" />
    <result column="view_number" jdbcType="INTEGER" property="viewNumber" />
    <result column="buy_start_time" jdbcType="TIMESTAMP" property="buyStartTime" />
    <result column="buy_end_time" jdbcType="TIMESTAMP" property="buyEndTime" />
    <result column="vaild_mode" jdbcType="INTEGER" property="vaildMode" />
    <result column="vaild_start_time" jdbcType="TIMESTAMP" property="vaildStartTime" />
    <result column="vaild_end_time" jdbcType="TIMESTAMP" property="vaildEndTime" />
    <result column="day_number" jdbcType="INTEGER" property="dayNumber" />
    <result column="is_weekend" jdbcType="INTEGER" property="isWeekend" />
    <result column="no_useday" jdbcType="VARCHAR" property="noUseday" />
    <result column="use_start_time" jdbcType="TIMESTAMP" property="useStartTime" />
    <result column="use_end_time" jdbcType="TIMESTAMP" property="useEndTime" />
    <result column="is_vacation" jdbcType="INTEGER" property="isVacation" />
    <result column="fee_rate" jdbcType="DECIMAL" property="feeRate" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="is_hot" jdbcType="INTEGER" property="isHot" />
    <result column="momecoins_option" jdbcType="VARCHAR" property="momecoinsOption" />
    <result column="integral_option" jdbcType="VARCHAR" property="integralOption" />
    <result column="threshord" jdbcType="INTEGER" property="threshord" />
    <result column="is_redeem" jdbcType="INTEGER" property="isRedeem" />
    <result column="is_momecoin" jdbcType="INTEGER" property="isMomecoin" />
    <result column="is_great" jdbcType="INTEGER" property="isGreat" />
    <result column="collect_times" jdbcType="INTEGER" property="collectTimes" />
    <result column="isexport" jdbcType="TINYINT" property="isexport" />
    <result column="is_mpay_exchange" jdbcType="INTEGER" property="isMpayExchange" />
    <result column="is_mcoin_open" jdbcType="INTEGER" property="isMcoinOpen" />
    <result column="is_mpay_open" jdbcType="INTEGER" property="isMpayOpen" />
    <result column="user_limited_number" jdbcType="INTEGER" property="userLimitedNumber" />
    <result column="orderby" jdbcType="TINYINT" property="orderby" />
    <result column="limited_offer" jdbcType="TINYINT" property="limitedOffer" />
    <result column="snap_up" jdbcType="TINYINT" property="snapUp" />
    <result column="snap_up_index" jdbcType="TINYINT" property="snapUpIndex" />
    <result column="platform_type" jdbcType="TINYINT" property="platformType" />
    <result column="member_integral" jdbcType="DECIMAL" property="memberIntegral" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="coupon_type" jdbcType="TINYINT" property="couponType" />
    <result column="maximum_points" jdbcType="INTEGER" property="maximumPoints" />
    <result column="istemporary" jdbcType="TINYINT" property="istemporary" />
    <result column="only_point" jdbcType="TINYINT" property="onlyPoint" />
    <result column="is_settlement" jdbcType="TINYINT" property="isSettlement" />
    <result column="img_en" jdbcType="VARCHAR" property="imgEn" />
    <result column="mpay_coupons_code_id" jdbcType="VARCHAR" property="mpayCouponsCodeId" />
    <result column="relation_sales" jdbcType="INTEGER" property="relationSales" />
    <result column="product_ids" jdbcType="VARCHAR" property="productIds" />
    <result column="trans_coupon_type" jdbcType="TINYINT" property="transCouponType" />
    <result column="meet_money" jdbcType="DECIMAL" property="meetMoney" />
    <result column="dec_money" jdbcType="DECIMAL" property="decMoney" />
    <result column="discount" jdbcType="REAL" property="discount" />
    <result column="max_discount_money" jdbcType="DECIMAL" property="maxDiscountMoney" />
    <result column="stock_a" jdbcType="INTEGER" property="stockA" />
    <result column="is_a_open" jdbcType="TINYINT" property="isAOpen" />
    <result column="third_party_settlement_price" jdbcType="DECIMAL" property="thirdPartySettlementPrice" />
    <result column="a_fee_type" jdbcType="TINYINT" property="aFeeType" />
    <result column="min_point" jdbcType="INTEGER" property="minPoint" />
    <result column="href_url" jdbcType="VARCHAR" property="hrefUrl" />
    <result column="goods_id" jdbcType="INTEGER" property="goodsId" />
    <result column="category_path" jdbcType="VARCHAR" property="categoryPath" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
    <result column="seckill_img" jdbcType="VARCHAR" property="seckillImg" />
    <result column="lock_stock" jdbcType="INTEGER" property="lockStock" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.FookBusinessProduct">
    <result column="imgs" jdbcType="LONGVARCHAR" property="imgs" />
    <result column="desc" jdbcType="LONGVARCHAR" property="desc" />
    <result column="details" jdbcType="LONGVARCHAR" property="details" />
    <result column="tnc" jdbcType="LONGVARCHAR" property="tnc" />
    <result column="receive_method" jdbcType="LONGVARCHAR" property="receiveMethod" />
  </resultMap>
  <sql id="Base_Column_List">
    id, areaid, `type`, businessid, img, zip_img, zip_imgs, title, price, retail_price, 
    point_ratio, stock, history_stock, sales, actual_sales, limited_number, is_allow_refund, 
    score, comment_number, shelf_status, view_number, buy_start_time, buy_end_time, vaild_mode, 
    vaild_start_time, vaild_end_time, day_number, is_weekend, no_useday, use_start_time, 
    use_end_time, is_vacation, fee_rate, created_at, updated_at, `enable`, is_hot, momecoins_option, 
    integral_option, threshord, is_redeem, is_momecoin, is_great, collect_times, isexport, 
    is_mpay_exchange, is_mcoin_open, is_mpay_open, user_limited_number, orderby, limited_offer, 
    snap_up, snap_up_index, platform_type, member_integral, `location`, coupon_id, shop_id, 
    coupon_type, maximum_points, istemporary, only_point, is_settlement, img_en, mpay_coupons_code_id, 
    relation_sales, product_ids, trans_coupon_type, meet_money, dec_money, discount, 
    max_discount_money, stock_a, is_a_open, third_party_settlement_price, a_fee_type, 
    min_point, href_url, goods_id, category_path, subsidy_amount, seckill_img, lock_stock
  </sql>
  <sql id="Blob_Column_List">
    imgs, `desc`, details, tnc, receive_method
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fook_business_product
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_product
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessProduct">
    insert into fook_business_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areaid != null">
        areaid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="img != null">
        img,
      </if>
      <if test="zipImg != null">
        zip_img,
      </if>
      <if test="zipImgs != null">
        zip_imgs,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="pointRatio != null">
        point_ratio,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="historyStock != null">
        history_stock,
      </if>
      <if test="sales != null">
        sales,
      </if>
      <if test="actualSales != null">
        actual_sales,
      </if>
      <if test="limitedNumber != null">
        limited_number,
      </if>
      <if test="isAllowRefund != null">
        is_allow_refund,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="commentNumber != null">
        comment_number,
      </if>
      <if test="shelfStatus != null">
        shelf_status,
      </if>
      <if test="viewNumber != null">
        view_number,
      </if>
      <if test="buyStartTime != null">
        buy_start_time,
      </if>
      <if test="buyEndTime != null">
        buy_end_time,
      </if>
      <if test="vaildMode != null">
        vaild_mode,
      </if>
      <if test="vaildStartTime != null">
        vaild_start_time,
      </if>
      <if test="vaildEndTime != null">
        vaild_end_time,
      </if>
      <if test="dayNumber != null">
        day_number,
      </if>
      <if test="isWeekend != null">
        is_weekend,
      </if>
      <if test="noUseday != null">
        no_useday,
      </if>
      <if test="useStartTime != null">
        use_start_time,
      </if>
      <if test="useEndTime != null">
        use_end_time,
      </if>
      <if test="isVacation != null">
        is_vacation,
      </if>
      <if test="feeRate != null">
        fee_rate,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="isHot != null">
        is_hot,
      </if>
      <if test="momecoinsOption != null">
        momecoins_option,
      </if>
      <if test="integralOption != null">
        integral_option,
      </if>
      <if test="threshord != null">
        threshord,
      </if>
      <if test="isRedeem != null">
        is_redeem,
      </if>
      <if test="isMomecoin != null">
        is_momecoin,
      </if>
      <if test="isGreat != null">
        is_great,
      </if>
      <if test="collectTimes != null">
        collect_times,
      </if>
      <if test="isexport != null">
        isexport,
      </if>
      <if test="isMpayExchange != null">
        is_mpay_exchange,
      </if>
      <if test="isMcoinOpen != null">
        is_mcoin_open,
      </if>
      <if test="isMpayOpen != null">
        is_mpay_open,
      </if>
      <if test="userLimitedNumber != null">
        user_limited_number,
      </if>
      <if test="orderby != null">
        orderby,
      </if>
      <if test="limitedOffer != null">
        limited_offer,
      </if>
      <if test="snapUp != null">
        snap_up,
      </if>
      <if test="snapUpIndex != null">
        snap_up_index,
      </if>
      <if test="platformType != null">
        platform_type,
      </if>
      <if test="memberIntegral != null">
        member_integral,
      </if>
      <if test="location != null">
        `location`,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="couponType != null">
        coupon_type,
      </if>
      <if test="maximumPoints != null">
        maximum_points,
      </if>
      <if test="istemporary != null">
        istemporary,
      </if>
      <if test="onlyPoint != null">
        only_point,
      </if>
      <if test="isSettlement != null">
        is_settlement,
      </if>
      <if test="imgEn != null">
        img_en,
      </if>
      <if test="mpayCouponsCodeId != null">
        mpay_coupons_code_id,
      </if>
      <if test="relationSales != null">
        relation_sales,
      </if>
      <if test="productIds != null">
        product_ids,
      </if>
      <if test="transCouponType != null">
        trans_coupon_type,
      </if>
      <if test="meetMoney != null">
        meet_money,
      </if>
      <if test="decMoney != null">
        dec_money,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="maxDiscountMoney != null">
        max_discount_money,
      </if>
      <if test="stockA != null">
        stock_a,
      </if>
      <if test="isAOpen != null">
        is_a_open,
      </if>
      <if test="thirdPartySettlementPrice != null">
        third_party_settlement_price,
      </if>
      <if test="aFeeType != null">
        a_fee_type,
      </if>
      <if test="minPoint != null">
        min_point,
      </if>
      <if test="hrefUrl != null">
        href_url,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="categoryPath != null">
        category_path,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
      <if test="seckillImg != null">
        seckill_img,
      </if>
      <if test="lockStock != null">
        lock_stock,
      </if>
      <if test="imgs != null">
        imgs,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="details != null">
        details,
      </if>
      <if test="tnc != null">
        tnc,
      </if>
      <if test="receiveMethod != null">
        receive_method,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areaid != null">
        #{areaid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="img != null">
        #{img,jdbcType=VARCHAR},
      </if>
      <if test="zipImg != null">
        #{zipImg,jdbcType=VARCHAR},
      </if>
      <if test="zipImgs != null">
        #{zipImgs,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=INTEGER},
      </if>
      <if test="historyStock != null">
        #{historyStock,jdbcType=INTEGER},
      </if>
      <if test="sales != null">
        #{sales,jdbcType=INTEGER},
      </if>
      <if test="actualSales != null">
        #{actualSales,jdbcType=INTEGER},
      </if>
      <if test="limitedNumber != null">
        #{limitedNumber,jdbcType=INTEGER},
      </if>
      <if test="isAllowRefund != null">
        #{isAllowRefund,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="commentNumber != null">
        #{commentNumber,jdbcType=INTEGER},
      </if>
      <if test="shelfStatus != null">
        #{shelfStatus,jdbcType=BIT},
      </if>
      <if test="viewNumber != null">
        #{viewNumber,jdbcType=INTEGER},
      </if>
      <if test="buyStartTime != null">
        #{buyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyEndTime != null">
        #{buyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildMode != null">
        #{vaildMode,jdbcType=INTEGER},
      </if>
      <if test="vaildStartTime != null">
        #{vaildStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildEndTime != null">
        #{vaildEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dayNumber != null">
        #{dayNumber,jdbcType=INTEGER},
      </if>
      <if test="isWeekend != null">
        #{isWeekend,jdbcType=INTEGER},
      </if>
      <if test="noUseday != null">
        #{noUseday,jdbcType=VARCHAR},
      </if>
      <if test="useStartTime != null">
        #{useStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useEndTime != null">
        #{useEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isVacation != null">
        #{isVacation,jdbcType=INTEGER},
      </if>
      <if test="feeRate != null">
        #{feeRate,jdbcType=DECIMAL},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
      <if test="isHot != null">
        #{isHot,jdbcType=INTEGER},
      </if>
      <if test="momecoinsOption != null">
        #{momecoinsOption,jdbcType=VARCHAR},
      </if>
      <if test="integralOption != null">
        #{integralOption,jdbcType=VARCHAR},
      </if>
      <if test="threshord != null">
        #{threshord,jdbcType=INTEGER},
      </if>
      <if test="isRedeem != null">
        #{isRedeem,jdbcType=INTEGER},
      </if>
      <if test="isMomecoin != null">
        #{isMomecoin,jdbcType=INTEGER},
      </if>
      <if test="isGreat != null">
        #{isGreat,jdbcType=INTEGER},
      </if>
      <if test="collectTimes != null">
        #{collectTimes,jdbcType=INTEGER},
      </if>
      <if test="isexport != null">
        #{isexport,jdbcType=TINYINT},
      </if>
      <if test="isMpayExchange != null">
        #{isMpayExchange,jdbcType=INTEGER},
      </if>
      <if test="isMcoinOpen != null">
        #{isMcoinOpen,jdbcType=INTEGER},
      </if>
      <if test="isMpayOpen != null">
        #{isMpayOpen,jdbcType=INTEGER},
      </if>
      <if test="userLimitedNumber != null">
        #{userLimitedNumber,jdbcType=INTEGER},
      </if>
      <if test="orderby != null">
        #{orderby,jdbcType=TINYINT},
      </if>
      <if test="limitedOffer != null">
        #{limitedOffer,jdbcType=TINYINT},
      </if>
      <if test="snapUp != null">
        #{snapUp,jdbcType=TINYINT},
      </if>
      <if test="snapUpIndex != null">
        #{snapUpIndex,jdbcType=TINYINT},
      </if>
      <if test="platformType != null">
        #{platformType,jdbcType=TINYINT},
      </if>
      <if test="memberIntegral != null">
        #{memberIntegral,jdbcType=DECIMAL},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null">
        #{couponType,jdbcType=TINYINT},
      </if>
      <if test="maximumPoints != null">
        #{maximumPoints,jdbcType=INTEGER},
      </if>
      <if test="istemporary != null">
        #{istemporary,jdbcType=TINYINT},
      </if>
      <if test="onlyPoint != null">
        #{onlyPoint,jdbcType=TINYINT},
      </if>
      <if test="isSettlement != null">
        #{isSettlement,jdbcType=TINYINT},
      </if>
      <if test="imgEn != null">
        #{imgEn,jdbcType=VARCHAR},
      </if>
      <if test="mpayCouponsCodeId != null">
        #{mpayCouponsCodeId,jdbcType=VARCHAR},
      </if>
      <if test="relationSales != null">
        #{relationSales,jdbcType=INTEGER},
      </if>
      <if test="productIds != null">
        #{productIds,jdbcType=VARCHAR},
      </if>
      <if test="transCouponType != null">
        #{transCouponType,jdbcType=TINYINT},
      </if>
      <if test="meetMoney != null">
        #{meetMoney,jdbcType=DECIMAL},
      </if>
      <if test="decMoney != null">
        #{decMoney,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=REAL},
      </if>
      <if test="maxDiscountMoney != null">
        #{maxDiscountMoney,jdbcType=DECIMAL},
      </if>
      <if test="stockA != null">
        #{stockA,jdbcType=INTEGER},
      </if>
      <if test="isAOpen != null">
        #{isAOpen,jdbcType=TINYINT},
      </if>
      <if test="thirdPartySettlementPrice != null">
        #{thirdPartySettlementPrice,jdbcType=DECIMAL},
      </if>
      <if test="aFeeType != null">
        #{aFeeType,jdbcType=TINYINT},
      </if>
      <if test="minPoint != null">
        #{minPoint,jdbcType=INTEGER},
      </if>
      <if test="hrefUrl != null">
        #{hrefUrl,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="categoryPath != null">
        #{categoryPath,jdbcType=VARCHAR},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="seckillImg != null">
        #{seckillImg,jdbcType=VARCHAR},
      </if>
      <if test="lockStock != null">
        #{lockStock,jdbcType=INTEGER},
      </if>
      <if test="imgs != null">
        #{imgs,jdbcType=LONGVARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=LONGVARCHAR},
      </if>
      <if test="details != null">
        #{details,jdbcType=LONGVARCHAR},
      </if>
      <if test="tnc != null">
        #{tnc,jdbcType=LONGVARCHAR},
      </if>
      <if test="receiveMethod != null">
        #{receiveMethod,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessProduct">
    update fook_business_product
    <set>
      <if test="areaid != null">
        areaid = #{areaid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="img != null">
        img = #{img,jdbcType=VARCHAR},
      </if>
      <if test="zipImg != null">
        zip_img = #{zipImg,jdbcType=VARCHAR},
      </if>
      <if test="zipImgs != null">
        zip_imgs = #{zipImgs,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        point_ratio = #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=INTEGER},
      </if>
      <if test="historyStock != null">
        history_stock = #{historyStock,jdbcType=INTEGER},
      </if>
      <if test="sales != null">
        sales = #{sales,jdbcType=INTEGER},
      </if>
      <if test="actualSales != null">
        actual_sales = #{actualSales,jdbcType=INTEGER},
      </if>
      <if test="limitedNumber != null">
        limited_number = #{limitedNumber,jdbcType=INTEGER},
      </if>
      <if test="isAllowRefund != null">
        is_allow_refund = #{isAllowRefund,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="commentNumber != null">
        comment_number = #{commentNumber,jdbcType=INTEGER},
      </if>
      <if test="shelfStatus != null">
        shelf_status = #{shelfStatus,jdbcType=BIT},
      </if>
      <if test="viewNumber != null">
        view_number = #{viewNumber,jdbcType=INTEGER},
      </if>
      <if test="buyStartTime != null">
        buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyEndTime != null">
        buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildMode != null">
        vaild_mode = #{vaildMode,jdbcType=INTEGER},
      </if>
      <if test="vaildStartTime != null">
        vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vaildEndTime != null">
        vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dayNumber != null">
        day_number = #{dayNumber,jdbcType=INTEGER},
      </if>
      <if test="isWeekend != null">
        is_weekend = #{isWeekend,jdbcType=INTEGER},
      </if>
      <if test="noUseday != null">
        no_useday = #{noUseday,jdbcType=VARCHAR},
      </if>
      <if test="useStartTime != null">
        use_start_time = #{useStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useEndTime != null">
        use_end_time = #{useEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isVacation != null">
        is_vacation = #{isVacation,jdbcType=INTEGER},
      </if>
      <if test="feeRate != null">
        fee_rate = #{feeRate,jdbcType=DECIMAL},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=INTEGER},
      </if>
      <if test="isHot != null">
        is_hot = #{isHot,jdbcType=INTEGER},
      </if>
      <if test="momecoinsOption != null">
        momecoins_option = #{momecoinsOption,jdbcType=VARCHAR},
      </if>
      <if test="integralOption != null">
        integral_option = #{integralOption,jdbcType=VARCHAR},
      </if>
      <if test="threshord != null">
        threshord = #{threshord,jdbcType=INTEGER},
      </if>
      <if test="isRedeem != null">
        is_redeem = #{isRedeem,jdbcType=INTEGER},
      </if>
      <if test="isMomecoin != null">
        is_momecoin = #{isMomecoin,jdbcType=INTEGER},
      </if>
      <if test="isGreat != null">
        is_great = #{isGreat,jdbcType=INTEGER},
      </if>
      <if test="collectTimes != null">
        collect_times = #{collectTimes,jdbcType=INTEGER},
      </if>
      <if test="isexport != null">
        isexport = #{isexport,jdbcType=TINYINT},
      </if>
      <if test="isMpayExchange != null">
        is_mpay_exchange = #{isMpayExchange,jdbcType=INTEGER},
      </if>
      <if test="isMcoinOpen != null">
        is_mcoin_open = #{isMcoinOpen,jdbcType=INTEGER},
      </if>
      <if test="isMpayOpen != null">
        is_mpay_open = #{isMpayOpen,jdbcType=INTEGER},
      </if>
      <if test="userLimitedNumber != null">
        user_limited_number = #{userLimitedNumber,jdbcType=INTEGER},
      </if>
      <if test="orderby != null">
        orderby = #{orderby,jdbcType=TINYINT},
      </if>
      <if test="limitedOffer != null">
        limited_offer = #{limitedOffer,jdbcType=TINYINT},
      </if>
      <if test="snapUp != null">
        snap_up = #{snapUp,jdbcType=TINYINT},
      </if>
      <if test="snapUpIndex != null">
        snap_up_index = #{snapUpIndex,jdbcType=TINYINT},
      </if>
      <if test="platformType != null">
        platform_type = #{platformType,jdbcType=TINYINT},
      </if>
      <if test="memberIntegral != null">
        member_integral = #{memberIntegral,jdbcType=DECIMAL},
      </if>
      <if test="location != null">
        `location` = #{location,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null">
        coupon_type = #{couponType,jdbcType=TINYINT},
      </if>
      <if test="maximumPoints != null">
        maximum_points = #{maximumPoints,jdbcType=INTEGER},
      </if>
      <if test="istemporary != null">
        istemporary = #{istemporary,jdbcType=TINYINT},
      </if>
      <if test="onlyPoint != null">
        only_point = #{onlyPoint,jdbcType=TINYINT},
      </if>
      <if test="isSettlement != null">
        is_settlement = #{isSettlement,jdbcType=TINYINT},
      </if>
      <if test="imgEn != null">
        img_en = #{imgEn,jdbcType=VARCHAR},
      </if>
      <if test="mpayCouponsCodeId != null">
        mpay_coupons_code_id = #{mpayCouponsCodeId,jdbcType=VARCHAR},
      </if>
      <if test="relationSales != null">
        relation_sales = #{relationSales,jdbcType=INTEGER},
      </if>
      <if test="productIds != null">
        product_ids = #{productIds,jdbcType=VARCHAR},
      </if>
      <if test="transCouponType != null">
        trans_coupon_type = #{transCouponType,jdbcType=TINYINT},
      </if>
      <if test="meetMoney != null">
        meet_money = #{meetMoney,jdbcType=DECIMAL},
      </if>
      <if test="decMoney != null">
        dec_money = #{decMoney,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=REAL},
      </if>
      <if test="maxDiscountMoney != null">
        max_discount_money = #{maxDiscountMoney,jdbcType=DECIMAL},
      </if>
      <if test="stockA != null">
        stock_a = #{stockA,jdbcType=INTEGER},
      </if>
      <if test="isAOpen != null">
        is_a_open = #{isAOpen,jdbcType=TINYINT},
      </if>
      <if test="thirdPartySettlementPrice != null">
        third_party_settlement_price = #{thirdPartySettlementPrice,jdbcType=DECIMAL},
      </if>
      <if test="aFeeType != null">
        a_fee_type = #{aFeeType,jdbcType=TINYINT},
      </if>
      <if test="minPoint != null">
        min_point = #{minPoint,jdbcType=INTEGER},
      </if>
      <if test="hrefUrl != null">
        href_url = #{hrefUrl,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="categoryPath != null">
        category_path = #{categoryPath,jdbcType=VARCHAR},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="seckillImg != null">
        seckill_img = #{seckillImg,jdbcType=VARCHAR},
      </if>
      <if test="lockStock != null">
        lock_stock = #{lockStock,jdbcType=INTEGER},
      </if>
      <if test="imgs != null">
        imgs = #{imgs,jdbcType=LONGVARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=LONGVARCHAR},
      </if>
      <if test="details != null">
        details = #{details,jdbcType=LONGVARCHAR},
      </if>
      <if test="tnc != null">
        tnc = #{tnc,jdbcType=LONGVARCHAR},
      </if>
      <if test="receiveMethod != null">
        receive_method = #{receiveMethod,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.FookBusinessProduct">
    update fook_business_product
    set areaid = #{areaid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      businessid = #{businessid,jdbcType=INTEGER},
      img = #{img,jdbcType=VARCHAR},
      zip_img = #{zipImg,jdbcType=VARCHAR},
      zip_imgs = #{zipImgs,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      point_ratio = #{pointRatio,jdbcType=INTEGER},
      stock = #{stock,jdbcType=INTEGER},
      history_stock = #{historyStock,jdbcType=INTEGER},
      sales = #{sales,jdbcType=INTEGER},
      actual_sales = #{actualSales,jdbcType=INTEGER},
      limited_number = #{limitedNumber,jdbcType=INTEGER},
      is_allow_refund = #{isAllowRefund,jdbcType=INTEGER},
      score = #{score,jdbcType=DECIMAL},
      comment_number = #{commentNumber,jdbcType=INTEGER},
      shelf_status = #{shelfStatus,jdbcType=BIT},
      view_number = #{viewNumber,jdbcType=INTEGER},
      buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      vaild_mode = #{vaildMode,jdbcType=INTEGER},
      vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      day_number = #{dayNumber,jdbcType=INTEGER},
      is_weekend = #{isWeekend,jdbcType=INTEGER},
      no_useday = #{noUseday,jdbcType=VARCHAR},
      use_start_time = #{useStartTime,jdbcType=TIMESTAMP},
      use_end_time = #{useEndTime,jdbcType=TIMESTAMP},
      is_vacation = #{isVacation,jdbcType=INTEGER},
      fee_rate = #{feeRate,jdbcType=DECIMAL},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=INTEGER},
      is_hot = #{isHot,jdbcType=INTEGER},
      momecoins_option = #{momecoinsOption,jdbcType=VARCHAR},
      integral_option = #{integralOption,jdbcType=VARCHAR},
      threshord = #{threshord,jdbcType=INTEGER},
      is_redeem = #{isRedeem,jdbcType=INTEGER},
      is_momecoin = #{isMomecoin,jdbcType=INTEGER},
      is_great = #{isGreat,jdbcType=INTEGER},
      collect_times = #{collectTimes,jdbcType=INTEGER},
      isexport = #{isexport,jdbcType=TINYINT},
      is_mpay_exchange = #{isMpayExchange,jdbcType=INTEGER},
      is_mcoin_open = #{isMcoinOpen,jdbcType=INTEGER},
      is_mpay_open = #{isMpayOpen,jdbcType=INTEGER},
      user_limited_number = #{userLimitedNumber,jdbcType=INTEGER},
      orderby = #{orderby,jdbcType=TINYINT},
      limited_offer = #{limitedOffer,jdbcType=TINYINT},
      snap_up = #{snapUp,jdbcType=TINYINT},
      snap_up_index = #{snapUpIndex,jdbcType=TINYINT},
      platform_type = #{platformType,jdbcType=TINYINT},
      member_integral = #{memberIntegral,jdbcType=DECIMAL},
      `location` = #{location,jdbcType=VARCHAR},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=VARCHAR},
      coupon_type = #{couponType,jdbcType=TINYINT},
      maximum_points = #{maximumPoints,jdbcType=INTEGER},
      istemporary = #{istemporary,jdbcType=TINYINT},
      only_point = #{onlyPoint,jdbcType=TINYINT},
      is_settlement = #{isSettlement,jdbcType=TINYINT},
      img_en = #{imgEn,jdbcType=VARCHAR},
      mpay_coupons_code_id = #{mpayCouponsCodeId,jdbcType=VARCHAR},
      relation_sales = #{relationSales,jdbcType=INTEGER},
      product_ids = #{productIds,jdbcType=VARCHAR},
      trans_coupon_type = #{transCouponType,jdbcType=TINYINT},
      meet_money = #{meetMoney,jdbcType=DECIMAL},
      dec_money = #{decMoney,jdbcType=DECIMAL},
      discount = #{discount,jdbcType=REAL},
      max_discount_money = #{maxDiscountMoney,jdbcType=DECIMAL},
      stock_a = #{stockA,jdbcType=INTEGER},
      is_a_open = #{isAOpen,jdbcType=TINYINT},
      third_party_settlement_price = #{thirdPartySettlementPrice,jdbcType=DECIMAL},
      a_fee_type = #{aFeeType,jdbcType=TINYINT},
      min_point = #{minPoint,jdbcType=INTEGER},
      href_url = #{hrefUrl,jdbcType=VARCHAR},
      goods_id = #{goodsId,jdbcType=INTEGER},
      category_path = #{categoryPath,jdbcType=VARCHAR},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      seckill_img = #{seckillImg,jdbcType=VARCHAR},
      lock_stock = #{lockStock,jdbcType=INTEGER},
      imgs = #{imgs,jdbcType=LONGVARCHAR},
      `desc` = #{desc,jdbcType=LONGVARCHAR},
      details = #{details,jdbcType=LONGVARCHAR},
      tnc = #{tnc,jdbcType=LONGVARCHAR},
      receive_method = #{receiveMethod,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessProduct">
    update fook_business_product
    set areaid = #{areaid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      businessid = #{businessid,jdbcType=INTEGER},
      img = #{img,jdbcType=VARCHAR},
      zip_img = #{zipImg,jdbcType=VARCHAR},
      zip_imgs = #{zipImgs,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      point_ratio = #{pointRatio,jdbcType=INTEGER},
      stock = #{stock,jdbcType=INTEGER},
      history_stock = #{historyStock,jdbcType=INTEGER},
      sales = #{sales,jdbcType=INTEGER},
      actual_sales = #{actualSales,jdbcType=INTEGER},
      limited_number = #{limitedNumber,jdbcType=INTEGER},
      is_allow_refund = #{isAllowRefund,jdbcType=INTEGER},
      score = #{score,jdbcType=DECIMAL},
      comment_number = #{commentNumber,jdbcType=INTEGER},
      shelf_status = #{shelfStatus,jdbcType=BIT},
      view_number = #{viewNumber,jdbcType=INTEGER},
      buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
      buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
      vaild_mode = #{vaildMode,jdbcType=INTEGER},
      vaild_start_time = #{vaildStartTime,jdbcType=TIMESTAMP},
      vaild_end_time = #{vaildEndTime,jdbcType=TIMESTAMP},
      day_number = #{dayNumber,jdbcType=INTEGER},
      is_weekend = #{isWeekend,jdbcType=INTEGER},
      no_useday = #{noUseday,jdbcType=VARCHAR},
      use_start_time = #{useStartTime,jdbcType=TIMESTAMP},
      use_end_time = #{useEndTime,jdbcType=TIMESTAMP},
      is_vacation = #{isVacation,jdbcType=INTEGER},
      fee_rate = #{feeRate,jdbcType=DECIMAL},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=INTEGER},
      is_hot = #{isHot,jdbcType=INTEGER},
      momecoins_option = #{momecoinsOption,jdbcType=VARCHAR},
      integral_option = #{integralOption,jdbcType=VARCHAR},
      threshord = #{threshord,jdbcType=INTEGER},
      is_redeem = #{isRedeem,jdbcType=INTEGER},
      is_momecoin = #{isMomecoin,jdbcType=INTEGER},
      is_great = #{isGreat,jdbcType=INTEGER},
      collect_times = #{collectTimes,jdbcType=INTEGER},
      isexport = #{isexport,jdbcType=TINYINT},
      is_mpay_exchange = #{isMpayExchange,jdbcType=INTEGER},
      is_mcoin_open = #{isMcoinOpen,jdbcType=INTEGER},
      is_mpay_open = #{isMpayOpen,jdbcType=INTEGER},
      user_limited_number = #{userLimitedNumber,jdbcType=INTEGER},
      orderby = #{orderby,jdbcType=TINYINT},
      limited_offer = #{limitedOffer,jdbcType=TINYINT},
      snap_up = #{snapUp,jdbcType=TINYINT},
      snap_up_index = #{snapUpIndex,jdbcType=TINYINT},
      platform_type = #{platformType,jdbcType=TINYINT},
      member_integral = #{memberIntegral,jdbcType=DECIMAL},
      `location` = #{location,jdbcType=VARCHAR},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=VARCHAR},
      coupon_type = #{couponType,jdbcType=TINYINT},
      maximum_points = #{maximumPoints,jdbcType=INTEGER},
      istemporary = #{istemporary,jdbcType=TINYINT},
      only_point = #{onlyPoint,jdbcType=TINYINT},
      is_settlement = #{isSettlement,jdbcType=TINYINT},
      img_en = #{imgEn,jdbcType=VARCHAR},
      mpay_coupons_code_id = #{mpayCouponsCodeId,jdbcType=VARCHAR},
      relation_sales = #{relationSales,jdbcType=INTEGER},
      product_ids = #{productIds,jdbcType=VARCHAR},
      trans_coupon_type = #{transCouponType,jdbcType=TINYINT},
      meet_money = #{meetMoney,jdbcType=DECIMAL},
      dec_money = #{decMoney,jdbcType=DECIMAL},
      discount = #{discount,jdbcType=REAL},
      max_discount_money = #{maxDiscountMoney,jdbcType=DECIMAL},
      stock_a = #{stockA,jdbcType=INTEGER},
      is_a_open = #{isAOpen,jdbcType=TINYINT},
      third_party_settlement_price = #{thirdPartySettlementPrice,jdbcType=DECIMAL},
      a_fee_type = #{aFeeType,jdbcType=TINYINT},
      min_point = #{minPoint,jdbcType=INTEGER},
      href_url = #{hrefUrl,jdbcType=VARCHAR},
      goods_id = #{goodsId,jdbcType=INTEGER},
      category_path = #{categoryPath,jdbcType=VARCHAR},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      seckill_img = #{seckillImg,jdbcType=VARCHAR},
      lock_stock = #{lockStock,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>