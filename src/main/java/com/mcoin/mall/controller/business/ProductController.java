package com.mcoin.mall.controller.business;

import com.mcoin.mall.model.*;
import com.mcoin.mall.service.business.BusinessService;
import com.mcoin.mall.service.business.HotDealsService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
public class ProductController {
    @Resource
    private HotDealsService hotDealsService;
    @Resource
    private BusinessService businessService;
    /**
     * 熱門優惠
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 热门优惠响应
     */
    @GetMapping(value = "/api/business/hotdeals", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取热门优惠", notes = "获取平台热门优惠商品列表")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<HotDealsResponse> hotDeals(HotDealsRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        HotDealsResponse response = hotDealsService.hotDeals(request, clientType);
        return Responses.ok(response);
    }
    /**
     * 分享福利
     * @return
     */
    @GetMapping(value = "/api/business/shareProduct", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ShareProductResponse> shareProduct(@Valid ShareProductRequest request) {
        ShareProductResponse response = businessService.getShareProduct(request.getId());
        return Responses.ok(response);
    }
}
