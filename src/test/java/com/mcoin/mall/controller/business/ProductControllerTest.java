package com.mcoin.mall.controller.business;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.model.*;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.util.AESUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doReturn;

/**
 * 集成测试 ProductController
 * 使用真实服务和数据库，测试产品相关功能
 */
class ProductControllerTest extends BaseUnitTest {

    @Resource
    private ProductController productController;

    @SpyBean
    private ContextHolder contextHolder;

    private static final int TEST_USER_ID = 99999;

    @BeforeEach
    public void setUp() {
        // 模拟Locale
        doReturn(Locale.TRADITIONAL_CHINESE).when(contextHolder).getLocale();

        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");

        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();
    }

    /**
     * 测试热门优惠功能 - HARMONY客户端
     * 验证返回的热门优惠列表包含预期的数据
     */
    @Test
    void testHotDealsWithHarmonyClient() {
        // 创建请求对象
        HotDealsRequest request = new HotDealsRequest();
        request.setIsRefresh(false);

        // 直接调用控制器方法，使用HARMONY客户端
        Response<HotDealsResponse> response = productController.hotDeals(request, "HARMONY");

        // 验证响应结构
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());

        HotDealsResponse data = response.getData();
        assertNotNull(data.getSnatchList());

        // 验证返回的热门优惠列表不为空
        List<HotDealsResponse.SnatchListItem> snatchList = data.getSnatchList();
        assertFalse(snatchList.isEmpty(), "Hot deals list should not be empty");

        // 验证返回的热门优惠项包含预期的数据
        boolean foundTestProduct = false;
        for (HotDealsResponse.SnatchListItem item : snatchList) {
            if (item.getTitle().contains("Hot Deal Test Product")) {
                foundTestProduct = true;
                assertNotNull(item.getId());
                assertNotNull(item.getImg());
                assertNotNull(item.getTitle());
                assertNotNull(item.getPrice());
                assertNotNull(item.getRetailPrice());
                break;
            }
        }

        assertTrue(foundTestProduct, "Should find at least one test hot deal product");
    }

    /**
     * 测试热门优惠功能 - 非HARMONY客户端
     * 验证不同客户端类型的响应差异
     */
    @Test
    void testHotDealsWithNonHarmonyClient() {
        // 创建请求对象
        HotDealsRequest request = new HotDealsRequest();
        request.setIsRefresh(false);

        // 直接调用控制器方法，使用非HARMONY客户端
        Response<HotDealsResponse> response = productController.hotDeals(request, "OTHER");

        // 验证响应结构
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());

        HotDealsResponse data = response.getData();
        assertNotNull(data.getSnatchList());

        // 验证返回的热门优惠列表不为空
        List<HotDealsResponse.SnatchListItem> snatchList = data.getSnatchList();
        assertFalse(snatchList.isEmpty(), "Hot deals list should not be empty");
    }

    /**
     * 测试热门优惠刷新功能
     * 验证刷新参数对结果的影响
     */
    @Test
    void testHotDealsWithRefresh() {
        // 创建请求对象，设置刷新为true
        HotDealsRequest request = new HotDealsRequest();
        request.setIsRefresh(true);

        // 直接调用控制器方法
        Response<HotDealsResponse> response = productController.hotDeals(request, "HARMONY");

        // 验证响应结构
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());

        HotDealsResponse data = response.getData();
        assertNotNull(data.getSnatchList());

        // 验证返回的热门优惠列表不为空
        List<HotDealsResponse.SnatchListItem> snatchList = data.getSnatchList();
        assertFalse(snatchList.isEmpty(), "Hot deals list should not be empty");
    }

    @Test
    void testShareProduct() {
        // 创建请求对象
        ShareProductRequest request = new ShareProductRequest();
        request.setId(AESUtil.encryptToHex("UNYmcFBSz30Sibfk","2236"));

        // 直接调用控制器方法
        Response<ShareProductResponse> response = productController.shareProduct(request);

        // 验证响应结构
        assertNotNull(response);
    }
}