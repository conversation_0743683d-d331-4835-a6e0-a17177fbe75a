<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMacaupassUserLoginLog2Dao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMacaupassUserLoginLog2">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="openid" jdbcType="VARCHAR" property="openid" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken" />
    <result column="point" jdbcType="INTEGER" property="point" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, openid, phone, access_token, refresh_token, point, ip, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_macaupass_user_login_log2
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_macaupass_user_login_log2
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMacaupassUserLoginLog2">
    insert into fook_macaupass_user_login_log2
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="openid != null">
        openid,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="accessToken != null">
        access_token,
      </if>
      <if test="refreshToken != null">
        refresh_token,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMacaupassUserLoginLog2">
    update fook_macaupass_user_login_log2
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        refresh_token = #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMacaupassUserLoginLog2">
    update fook_macaupass_user_login_log2
    set user_id = #{userId,jdbcType=INTEGER},
      openid = #{openid,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      access_token = #{accessToken,jdbcType=VARCHAR},
      refresh_token = #{refreshToken,jdbcType=VARCHAR},
      point = #{point,jdbcType=INTEGER},
      ip = #{ip,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>