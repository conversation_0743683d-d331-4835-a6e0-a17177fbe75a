<#setting number_format="0.00">
<html>
<head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<style>
  @page {
    size: a3 landscape;
  }
  body,
  html {
    width: 100%;
    height: 100%;
    line-height: 120%;
    font-weight: 450;
  }
  body {
    margin: 0;
    padding: 0;
    font-size: 12px;
  }

  table {
    table-layout: fixed;
    /*word-break: break-all;*/
    border-collapse: collapse;
    word-wrap: break-word;
  }

  body {
    font-family: Source Han <PERSON>, m<PERSON>, Deja<PERSON><PERSON> Sans, sans-serif;
  }

  table tr {
    margin: 0;
  }

  .noneBg td {
    border: none;
  }

  table td {
    border-width: 1px;
    border-style: solid;
    border-color: #000;
    background: none;
    margin: 0;

  }

  .title {
    text-align: center;
    font-size: 18px;
    padding-bottom: 10px;
  }

  .page-break {
    /* page-break-after: always; */
        page-break-inside: avoid;
    }

</style>
</head>
<body>
<div style="padding-left: 20px;padding-top: 5px;">
  <img style="width: 250px;" src="https://mcoin-prd.oss-cn-hongkong.aliyuncs.com/image/mCoinPdf.png" alt="">
</div>
<div class="title">${code} ${businessName} 結算報表 </div>
<table style="width: 100%;font-size: 14px;">
  <tr class="noneBg">
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
    <td style="width: 500px;text-align: right;">&nbsp; </td>
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
  </tr>
  <tr class="noneBg">
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
    <td style="width: 500px;text-align: right;">&nbsp; </td>
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
  </tr>
    <tr class="noneBg" style="line-height: 160%;">
      <td style="width: 100px;text-align: right;">
        <u>商店名稱:</u>
      </td>
      <td style="text-align: left">
        ${businessName}
      </td>
      <td style="width: 500px;text-align: right;">&nbsp; </td>
      <td style="width: 100px;text-align: right;">
        <u>編印日期:</u>
      </td>
      <td style="text-align: left">
        ${currentDate?string("yyyy-MM-dd")}
      </td>
    </tr>
    <tr class="noneBg" style="line-height: 160%;">
      <td style="width: 100px;text-align: right;">
        <u>報表主送郵箱:</u>
      </td>
      <td style="text-align: left">
        ${headEmail}
      </td>
      <td style="width: 500px;text-align: right;">&nbsp; </td>
      <td style="width: 100px;text-align: right;">
        <u>結算時間:</u>
      </td>
      <td style="text-align: left">
        ${startTime?string("yyyy-MM-dd HH:mm:ss")} - ${endTime?string("yyyy-MM-dd HH:mm:ss")}
      </td>
    </tr>

  <tr class="noneBg">
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
    <td style="width: 500px;text-align: right;">&nbsp; </td>
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
  </tr>
  <tr class="noneBg">
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
    <td style="width: 500px;text-align: right;">&nbsp; </td>
    <td style="width: 100px;text-align: right;">
      &nbsp;
    </td>
    <td style="text-align: left">
      &nbsp;
    </td>
  </tr>

</table>

<table style="width: 100%;">
  <tr style="text-align: center; border-right:1px solid #000;">
    <td>
      <div>
        S/N
      </div>
    </td>
    <td>
      <div>
        訂單類型
      </div>
    </td>
    <td>
      <div>
        訂單號
      </div>
    </td>
    <td>
      <div>
        小程序單號
      </div>
    </td>
    <td>
      <div>
        核銷碼/提貨碼
      </div>
    </td>
    <td>
      <div>
        交易金額
      </div>
    </td>
    <td>
      <div>
        淨權利金額
      </div>
    </td>
    <td>
      <div>
        核銷時間
      </div>
    </td>
    <td>
      <div>
        核銷門店
      </div>
    </td>
    <td>
      <div>
        福利名稱
      </div>
    </td>
    <td>
      <div>
        配送方式
      </div>
    </td>
  </tr>
  <#list dataList as data>
  <tr style="text-align: center; border-right:1px solid #000;">
    <td width="2%">
      <div>
        ${ ((pageCountStart + data?index) + 1)?string["0.##"] }
      </div>
    </td>
    <td width="5%">
      <div>
        <!-- 訂單類型（1、福利，2、預約，3、特殊福利，4、實物鏈路） -->
        <#if data.type == 1>
          福利
        <#elseif data.type == 2>
          預約
        <#elseif data.type == 3>
          特殊福利
        <#elseif data.type == 4>
          實物鏈路
        <#else>
          --
        </#if>
      </div>
    </td>
    <td width="15%">
      <div>
        ${(data.orderNo)!"--"}
      </div>
    </td>
    <td width="15%">
      <div>
        ${(data.orderTransaction)!"--"}
      </div>
    </td>
    <td>
      <div style="word-wrap: break-word;">${data.voucherCode}</div>
    </td>
    <td>
      <div>
        ${(data.billAmount)!""}
      </div>
    </td>
    <td>
      <div>
        ${(data.merchantSettleAmount)!""}
      </div>
    </td>
    <td>
      <div>
        ${data.useTime?string("yyyy-MM-dd HH:mm:ss")}
      </div>
    </td>
    <td width="12%">
      <div>
        ${(data.storeName)!"--"}
      </div>
    </td>
    <td width="15%">
      <div>
        ${data.voucherName}
      </div>
    </td>
    <td width="15%">
      <div>
        <#if data.deliveryType == 1>
          物流
        <#elseif data.deliveryType == 2>
          自提
        <#elseif data.deliveryType == 3>
          同城
        <#elseif data.deliveryType == 4>
          虛擬
        <#else>
          --
        </#if>
      </div>
    </td>
  </tr>
  </#list>

  <#if isLastPage == 1 >
  <tr style="text-align: center; border-right:1px solid #000;" class="noneBg">
    <td>
      <div>
      </div>
    </td>
    <td>
      <div>
      </div>
    </td>
    <td>
      <div>
      </div>
    </td>
    <td>
      <div>
        總金額:
      </div>
    </td>
    <td style="border-bottom: 1px solid #000">
      <div>
        MOP ${billAmountSum}
      </div>
    </td>
    <td style="border-bottom: 1px solid #000">
      <div>
         MOP ${merchantSettleAmountSum}
      </div>
    </td>
    <td>
      <div>
      </div>
    </td>
    <td>
      <div>
      </div>
    </td>
    <td>
      <div>
      </div>
    </td>
  </tr>
  <#else>
    --
  </#if>
</table>
<div class="page-break"></div>
<div style="margin-top: 20px;">
  備註：
  <div>1. 佣金根據合約簽署的百分比計算；</div>
  <div>2. 結算金額以小數後2位作計算，由於四捨五入的影響，小計項目加總與合計結果，在特殊情況下存在誤差，以最終合計結果為準；</div>
  <div>3. 如對結算金額有任何疑問，請在收到當期結單後的10個工作日內向我司提出疑問。</div>
</div>
</body>

</html>
