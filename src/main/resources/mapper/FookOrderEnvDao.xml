<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookOrderEnvDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookOrderEnv">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="device_model" jdbcType="VARCHAR" property="deviceModel" />
    <result column="device_brand" jdbcType="VARCHAR" property="deviceBrand" />
    <result column="terminal_type" jdbcType="VARCHAR" property="terminalType" />
    <result column="mac" jdbcType="VARCHAR" property="mac" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="imsi" jdbcType="VARCHAR" property="imsi" />
    <result column="screen_size" jdbcType="VARCHAR" property="screenSize" />
    <result column="os" jdbcType="VARCHAR" property="os" />
    <result column="os_version" jdbcType="VARCHAR" property="osVersion" />
    <result column="app" jdbcType="VARCHAR" property="app" />
    <result column="app_version" jdbcType="VARCHAR" property="appVersion" />
    <result column="sdk_version" jdbcType="VARCHAR" property="sdkVersion" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="browser" jdbcType="VARCHAR" property="browser" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="network_type" jdbcType="VARCHAR" property="networkType" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="latlng_alg" jdbcType="VARCHAR" property="latlngAlg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, device_model, device_brand, terminal_type, mac, imei, imsi, screen_size, 
    os, os_version, app, app_version, sdk_version, `language`, browser, ip, lng, lat, 
    network_type, gmt_create, gmt_modified, creator, modifier, channel, `source`, ext_info, 
    latlng_alg
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_order_env
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_order_env
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookOrderEnv">
    insert into fook_order_env
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="deviceModel != null">
        device_model,
      </if>
      <if test="deviceBrand != null">
        device_brand,
      </if>
      <if test="terminalType != null">
        terminal_type,
      </if>
      <if test="mac != null">
        mac,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="imsi != null">
        imsi,
      </if>
      <if test="screenSize != null">
        screen_size,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="osVersion != null">
        os_version,
      </if>
      <if test="app != null">
        app,
      </if>
      <if test="appVersion != null">
        app_version,
      </if>
      <if test="sdkVersion != null">
        sdk_version,
      </if>
      <if test="language != null">
        `language`,
      </if>
      <if test="browser != null">
        browser,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="networkType != null">
        network_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="latlngAlg != null">
        latlng_alg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="deviceModel != null">
        #{deviceModel,jdbcType=VARCHAR},
      </if>
      <if test="deviceBrand != null">
        #{deviceBrand,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="mac != null">
        #{mac,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="imsi != null">
        #{imsi,jdbcType=VARCHAR},
      </if>
      <if test="screenSize != null">
        #{screenSize,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        #{os,jdbcType=VARCHAR},
      </if>
      <if test="osVersion != null">
        #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        #{app,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null">
        #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="sdkVersion != null">
        #{sdkVersion,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="browser != null">
        #{browser,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
      <if test="networkType != null">
        #{networkType,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="latlngAlg != null">
        #{latlngAlg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookOrderEnv">
    update fook_order_env
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="deviceModel != null">
        device_model = #{deviceModel,jdbcType=VARCHAR},
      </if>
      <if test="deviceBrand != null">
        device_brand = #{deviceBrand,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        terminal_type = #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="mac != null">
        mac = #{mac,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        imei = #{imei,jdbcType=VARCHAR},
      </if>
      <if test="imsi != null">
        imsi = #{imsi,jdbcType=VARCHAR},
      </if>
      <if test="screenSize != null">
        screen_size = #{screenSize,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        os = #{os,jdbcType=VARCHAR},
      </if>
      <if test="osVersion != null">
        os_version = #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        app = #{app,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null">
        app_version = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="sdkVersion != null">
        sdk_version = #{sdkVersion,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        `language` = #{language,jdbcType=VARCHAR},
      </if>
      <if test="browser != null">
        browser = #{browser,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
      <if test="networkType != null">
        network_type = #{networkType,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="latlngAlg != null">
        latlng_alg = #{latlngAlg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>