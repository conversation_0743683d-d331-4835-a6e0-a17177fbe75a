package com.mcoin.mall.controller.gray;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import javax.annotation.Resource;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.model.GrayRequest;
import com.mcoin.mall.model.GrayResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;

import ch.vorburger.exec.ManagedProcessException;
import lombok.extern.slf4j.Slf4j;

/**
 * 集成测试 GrayController
 * 测试灰度接口功能
 */
@Slf4j
public class GrayControllerTest extends BaseUnitTest {

    @Resource
    private GrayController grayController;

    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;


    @BeforeEach
    void setUp() throws ManagedProcessException {
        // 加载测试数据
        try {
            log.info("Loading test data for GrayControllerTest");
            // 准备测试用户数据
            prepareMacaupassUserTestData();
            log.info("Test data loaded successfully");
        } catch (Exception e) {
            log.error("Failed to load test data: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 准备澳门通用户测试数据
     */
    private void prepareMacaupassUserTestData() {
        // 创建测试用户数据
        FookMacaupassUser testUser = new FookMacaupassUser();
        testUser.setUserId(123);
        testUser.setCustomid("test_cust_456");
        testUser.setOpenid("test_openid_789");
        
        // 检查是否已存在，如果不存在则插入
        FookMacaupassUser existingUser = fookMacaupassUserDao.getFirstMpayUserByUserId(123);
        if (existingUser == null) {
            fookMacaupassUserDao.insert(testUser);
            log.info("插入测试用户数据: userId={}, custId={}", testUser.getUserId(), testUser.getCustomid());
        } else {
            log.info("测试用户数据已存在: userId={}, custId={}", existingUser.getUserId(), existingUser.getCustomid());
        }
    }

    /**
     * 测试灰度接口 - 成功场景
     * 验证灰度请求被正确处理，custId正确设置，返回预期结果
     */
    @Test
    @DisplayName("测试灰度接口 - 成功场景")
    void testGraySuccess() throws BlockException {
        // 准备测试数据
        String testFeatureKey = "new_feature_001";
        Integer testUserId = 123;
        String expectedCustId = "test_cust_456";

        // 模拟用户认证信息
        when(contextHolder.getAuthUserInfo().getUserId()).thenReturn(testUserId);

        // 模拟PointProdClient返回的响应
        GrayResponse mockGrayResponse = new GrayResponse();
        mockGrayResponse.setGray(true);
        Response<GrayResponse> mockResponse = Responses.ok(mockGrayResponse);
        when(pointProdClient.gray(any(GrayRequest.class))).thenReturn(mockResponse);

        // 创建请求
        GrayRequest request = new GrayRequest();
        request.setFeatureKey(testFeatureKey);

        // 调用被测试方法
        Response<GrayResponse> result = grayController.gray(request);

        // 验证结果
        assertNotNull(result, "响应不应为空");
        assertNotNull(result.getData(), "响应数据不应为空");
        assertEquals(expectedCustId, request.getCustId(), "custId应被正确设置");
        assertEquals(true, result.getData().getGray(), "应返回灰度命中结果");
    }

    /**
     * 测试灰度接口 - PointProdClient抛出BlockException场景
     * 验证当远程服务熔断时，使用默认响应
     */
    @Test
    @DisplayName("测试灰度接口 - PointProdClient熔断场景")
    void testGrayWithBlockException() throws BlockException {
        // 准备测试数据
        String testFeatureKey = "new_feature_002";
        Integer testUserId = 123;
        String expectedCustId = "test_cust_456";

        // 模拟用户认证信息
        when(contextHolder.getAuthUserInfo().getUserId()).thenReturn(testUserId);

        // 模拟PointProdClient抛出BlockException（使用具体实现类）
        when(pointProdClient.gray(any(GrayRequest.class))).thenThrow(new FlowException("熔断测试"));

        // 创建请求
        GrayRequest request = new GrayRequest();
        request.setFeatureKey(testFeatureKey);

        // 调用被测试方法
        Response<GrayResponse> result = grayController.gray(request);

        // 验证结果 - 应该返回默认值（false）
        assertNotNull(result, "响应不应为空");
        assertNotNull(result.getData(), "响应数据不应为空");
        assertEquals(expectedCustId, request.getCustId(), "custId应被正确设置");
        assertFalse(result.getData().getGray(), "熔断时应返回默认值false");
    }

    /**
     * 测试灰度接口 - PointProdClient返回空值场景
     * 验证当远程服务返回空值时，使用默认响应
     */
    @Test
    @DisplayName("测试灰度接口 - PointProdClient返回空值场景")
    void testGrayWithNullResponse() throws BlockException {
        // 准备测试数据
        String testFeatureKey = "new_feature_003";
        Integer testUserId = 123;
        String expectedCustId = "test_cust_456";

        // 模拟用户认证信息
        when(contextHolder.getAuthUserInfo().getUserId()).thenReturn(testUserId);

        // 模拟PointProdClient返回null
        when(pointProdClient.gray(any(GrayRequest.class))).thenReturn(null);

        // 创建请求
        GrayRequest request = new GrayRequest();
        request.setFeatureKey(testFeatureKey);

        // 调用被测试方法
        Response<GrayResponse> result = grayController.gray(request);

        // 验证结果 - 应该返回默认值（false）
        assertNotNull(result, "响应不应为空");
        assertNotNull(result.getData(), "响应数据不应为空");
        assertEquals(expectedCustId, request.getCustId(), "custId应被正确设置");
        assertFalse(result.getData().getGray(), "空响应时应返回默认值false");
    }

    /**
     * 测试灰度接口 - 验证custId设置功能
     * 专门测试custId的获取和设置逻辑
     */
    @Test
    @DisplayName("测试custId设置功能")
    void testCustIdSetting() throws BlockException {
        // 准备测试数据
        String testFeatureKey = "feature_custid_test";
        Integer testUserId = 123;
        String expectedCustId = "test_cust_456";

        // 模拟用户认证信息
        when(contextHolder.getAuthUserInfo().getUserId()).thenReturn(testUserId);

        // 模拟PointProdClient返回默认响应
        GrayResponse defaultResponse = new GrayResponse();
        defaultResponse.setGray(false);
        when(pointProdClient.gray(any(GrayRequest.class))).thenReturn(Responses.ok(defaultResponse));

        // 创建请求（初始时custId为空）
        GrayRequest request = new GrayRequest();
        request.setFeatureKey(testFeatureKey);
        // 确认初始时custId为空
        assertEquals(null, request.getCustId(), "初始custId应为空");

        // 调用被测试方法
        Response<GrayResponse> result = grayController.gray(request);

        // 验证custId被正确设置
        assertEquals(expectedCustId, request.getCustId(), "custId应被正确设置为从数据库查询的值");
        assertNotNull(result, "响应不应为空");
    }

    /**
     * 测试灰度接口 - 验证数据库查询功能
     * 测试真实的数据库查询操作
     */
    @Test
    @DisplayName("测试数据库查询功能")
    void testDatabaseQuery() {
        // 准备测试数据
        Integer testUserId = 123;

        // 直接调用DAO进行查询
        FookMacaupassUser user = fookMacaupassUserDao.getFirstMpayUserByUserId(testUserId);

        // 验证查询结果
        assertNotNull(user, "应该能查询到测试用户");
        assertEquals(testUserId, user.getUserId(), "用户ID应该匹配");
        assertEquals("test_cust_456", user.getCustomid(), "客户ID应该匹配");
    }

    /**
     * 测试灰度接口 - 直接调用场景
     * 验证直接调用controller方法的完整流程
     */
    @Test
    @DisplayName("测试灰度接口 - 直接调用场景")
    void testGrayDirectCall() throws BlockException {
        // 准备测试数据
        String testFeatureKey = "feature_direct_call";
        Integer testUserId = 123;
        String expectedCustId = "test_cust_456";

        // 模拟用户认证信息
        when(contextHolder.getAuthUserInfo().getUserId()).thenReturn(testUserId);

        // 模拟PointProdClient返回的响应
        GrayResponse mockGrayResponse = new GrayResponse();
        mockGrayResponse.setGray(false);
        Response<GrayResponse> mockResponse = Responses.ok(mockGrayResponse);
        when(pointProdClient.gray(any(GrayRequest.class))).thenReturn(mockResponse);

        // 创建请求
        GrayRequest request = new GrayRequest();
        request.setFeatureKey(testFeatureKey);

        // 直接调用controller方法
        Response<GrayResponse> result = grayController.gray(request);

        // 验证结果
        assertNotNull(result, "响应不应为空");
        assertNotNull(result.getData(), "响应数据不应为空");
        assertEquals(expectedCustId, request.getCustId(), "custId应被正确设置");
        assertEquals(false, result.getData().getGray(), "应返回预期的灰度结果");
        assertEquals(200, result.getCode(), "响应码应为200");
    }
} 