package com.mcoin.mall.mp.controller.selffechshop;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.entity.LsSelffetchShop;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.selffechshop.SelffetchShopResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolationException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Transactional
class SelffetchShopControllerTest extends BaseUnitTest {

    @Autowired
    private SelffetchShopController selffetchShopController;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
    }


    @Test
    void queryAddressLists_ShouldReturnShopList_WhenParametersAreValid() {
        // Arrange
        String latitude = "39.98119";
        String longitude = "116.30739";
        Integer pageNo = 1;
        Integer pageSize = 10;

        LsSelffetchShop mockShop1 = new LsSelffetchShop()
                .setId(1L)
                .setName("Shop 1")
                .setLongitude("116.30739")
                .setLatitude("39.98119");

        LsSelffetchShop mockShop2 = new LsSelffetchShop()
                .setId(2L)
                .setName("Shop 2")
                .setLongitude("116.30800")
                .setLatitude("39.98200");

        List<LsSelffetchShop> mockShopList = Arrays.asList(mockShop1, mockShop2);

        when(lsSelffetchShopMapper.queryListOrderByDistance(
                anyString(), anyString(), anyInt(), anyInt(), anyInt()))
                .thenReturn(mockShopList);

        // Act
        Response<SelffetchShopResponse> response = selffetchShopController.queryAddressLists(
            latitude, longitude, pageNo, pageSize
        );

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void queryAddressLists_ShouldThrowException_WhenLatitudeIsBlank() {
        // Arrange
        String latitude = "";
        String longitude = "116.30739";
        Integer pageNo = 1;
        Integer pageSize = 10;

        // Act & Assert
        assertThrows(ConstraintViolationException.class, () -> {
            selffetchShopController.queryAddressLists(latitude, longitude, pageNo, pageSize);
        });
    }

    @Test
    void queryAddressLists_ShouldThrowException_WhenLongitudeIsBlank() {
        // Arrange
        String latitude = "39.98119";
        String longitude = "";
        Integer pageNo = 1;
        Integer pageSize = 10;

        // Act & Assert
        assertThrows(ConstraintViolationException.class, () -> {
            selffetchShopController.queryAddressLists(latitude, longitude, pageNo, pageSize);
        });
    }
} 