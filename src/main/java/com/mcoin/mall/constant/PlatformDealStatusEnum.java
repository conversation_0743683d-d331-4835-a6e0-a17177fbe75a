package com.mcoin.mall.constant;

public enum PlatformDealStatusEnum {

    PENDING(0, "审核中"),
    PROCESSING(1, "處理"),
    UNSUCCESSFUL(2, "不成功"),
    SUCCESSFUL(3, "成功");

    private final int code;
    private final String description;

    PlatformDealStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
