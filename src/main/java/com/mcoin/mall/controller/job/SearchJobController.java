package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.service.job.SearchJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/job/search")
@Slf4j
public class SearchJobController {

    @Resource
    private SearchJobService searchJobService;
    /**
     * 创建商品视图
     * 每隔一段時間更新一次db優惠券視圖
     *
     * @return
     */
    @PostMapping(value = "/createViewProduct")
    public Response<String> createViewProduct() {
        this.searchJobService.doCreateViewProduct();
        return Responses.ok("success");
    }
}
