package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.TradingCycleCreateRequest;
import com.mcoin.mall.model.job.TradingDataCreateFileRequest;
import com.mcoin.mall.model.job.TradingDataRequest;
import com.mcoin.mall.model.job.TradingDataSendEmailRequest;
import com.mcoin.mall.util.JodaTimeUtil;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.Date;

import static com.mcoin.mall.util.JodaTimeUtil.YYYY_MM_DD;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 TradeJobController
 * 使用真实服务和数据库，测试交易任务相关功能
 */
class TradeJobControllerTest extends BaseUnitTest {

    @Resource
    private TradeJobController tradeJobController;

    @Test
    void testCreateTradingData() {
        // 创建请求对象
        TradingDataRequest request = new TradingDataRequest();
        request.setCurrentDate(new Date());
        // 直接调用控制器方法
        Response<String> response = tradeJobController.createTradingData(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testCreateTradingCycle() {
        // 创建请求对象
        TradingCycleCreateRequest request = new TradingCycleCreateRequest();
        request.setCurrentDate(JodaTimeUtil.parse("2025-05-05", YYYY_MM_DD));
        // 直接调用控制器方法
        Response<String> response = tradeJobController.createTradingCycle(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testCreateExcel() {
        // 创建请求对象
        TradingDataCreateFileRequest request = new TradingDataCreateFileRequest();
        request.setCurrentDate(new Date());
        // 直接调用控制器方法
        Response<String> response = tradeJobController.createExcel(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testSendTradingEmail() {
        // 创建请求对象
        TradingDataSendEmailRequest request = new TradingDataSendEmailRequest();
        request.setCurrentDate(JodaTimeUtil.parse("2025-05-07", YYYY_MM_DD));
        // 直接调用控制器方法
        Response<String> response = tradeJobController.sendTradingEmail(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 