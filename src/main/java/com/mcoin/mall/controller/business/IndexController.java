package com.mcoin.mall.controller.business;

import java.util.List;
import java.util.Locale;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookBanner;
import com.mcoin.mall.bean.FookModule;
import com.mcoin.mall.bean.FookPlatformSuggest;
import com.mcoin.mall.bean.FookStoresKeyword;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.model.BannerRequest;
import com.mcoin.mall.model.BannerResponse;
import com.mcoin.mall.model.BannerResponseV2;
import com.mcoin.mall.model.HotKeywordsResponse;
import com.mcoin.mall.model.IndexModuleResponse;
import com.mcoin.mall.model.RecommendZoneRequest;
import com.mcoin.mall.model.RecommendZoneResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.ShopIntervalResponse;
import com.mcoin.mall.model.ShopTypeRequest;
import com.mcoin.mall.model.ShopTypeResponse;
import com.mcoin.mall.model.SnapUpMoreRequest;
import com.mcoin.mall.model.SnapUpMoreResponse;
import com.mcoin.mall.model.SnapUpNextRequest;
import com.mcoin.mall.model.SnapUpNextResponse;
import com.mcoin.mall.model.SnapUpRequest;
import com.mcoin.mall.model.SnapUpResponse;
import com.mcoin.mall.model.StoreKeywordsResponse;
import com.mcoin.mall.model.StoreTypeResponse;
import com.mcoin.mall.model.ZoneTypeResponse;
import com.mcoin.mall.model.ZoneTypeRequest;
import com.mcoin.mall.service.business.IndexService;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;
import com.mcoin.mall.util.MiniProgramDisplayUtil;
import com.mcoin.mall.util.OssUtil;

import cn.hutool.core.convert.Convert;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
public class IndexController {
    @Resource
    private IndexService indexService;

    @Resource
    private ContextHolder contextHolder;
    /**
     * 首页廣告圖片
     *
     * @return
     */
    @GetMapping(value = "/api/business/indexpostercache", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<BannerResponse> banner() {
        Locale locale = contextHolder.getLocale();
        String language = locale.getLanguage();
        BannerResponse response = new BannerResponse();
        response.setSnatchList(Lists.newArrayList());
        List<FookBanner> banners = indexService.getIndexPoster();
        for (FookBanner banner : banners) {
            BannerResponse.SnatchListItem item = new BannerResponse.SnatchListItem();
            item.setId(Convert.toStr(banner.getId()));
            String icon = banner.getIcon();
            if (language.equals(McoinMall.LANG_EN) && StringUtils.isNotBlank(banner.getIconEn())) {
                icon = banner.getIconEn();
            }
            item.setImg(OssUtil.initOssImage(icon));
            item.setSort(Convert.toStr(banner.getSort()));
            item.setSrc(banner.getSrc());
            item.setType(Convert.toStr(banner.getType()));
            item.setBroadcastStartTime(JodaTimeUtil.format(banner.getBroadcastStartTime(), JodaTimeUtil.DEFAULT_PATTERN));
            item.setBroadcastEndTime(JodaTimeUtil.format(banner.getBroadcastEndTime(), JodaTimeUtil.DEFAULT_PATTERN));
            item.setIsBroadcast(Convert.toStr(banner.getIsBroadcast()));
            item.setLocation(Convert.toStr(banner.getLocation()));
            item.setName(banner.getName());
            response.getSnatchList().add(item);
        }
        return Responses.ok(response);
    }

    /**
     * 搜索熱詞
     *
     * @return
     */
    @GetMapping(value = "/api/business/indexkeyscache", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<HotKeywordsResponse> hotKeywords(HttpServletRequest request) {
        HotKeywordsResponse response = new HotKeywordsResponse();
        HotKeywordsResponse.Suggest suggest = new HotKeywordsResponse.Suggest();
        List<HotKeywordsResponse.Suggest.SnatchListItem> suggestList = Lists.newArrayList();
        suggest.setSnatchList(suggestList);
        response.setSuggest(suggest);
        HotKeywordsResponse.Searches searches = new HotKeywordsResponse.Searches();
        List<HotKeywordsResponse.Searches.SnatchListItem> searchesList = Lists.newArrayList();
        searches.setSnatchList(searchesList);
        response.setSearches(searches);

        List<FookPlatformSuggest> popularSearches = indexService.getPopularSearches();
        if (CollectionUtils.isNotEmpty(popularSearches)) {
            for (FookPlatformSuggest popularSearch : popularSearches) {
                HotKeywordsResponse.Searches.SnatchListItem item = new HotKeywordsResponse.Searches.SnatchListItem();
                item.setLabel(popularSearch.getLabel());
                item.setPopularity(popularSearch.getPopularity());
                item.setLabelType(popularSearch.getLabelType());
                searchesList.add(item);
            }
        }
        List<FookPlatformSuggest> indexPopularSearches = indexService.getIndexPopularSearches();
        if (CollectionUtils.isNotEmpty(indexPopularSearches)) {
            for (FookPlatformSuggest popularSearch : indexPopularSearches) {
                HotKeywordsResponse.Suggest.SnatchListItem item = new HotKeywordsResponse.Suggest.SnatchListItem();
                item.setLabel(popularSearch.getLabel());
                item.setPopularity(popularSearch.getPopularity());
                item.setLabelType(popularSearch.getLabelType());
                suggestList.add(item);
            }
        }
        return Responses.ok(response);
    }

    /**
     * 專區類型
     *
     * @return
     */
    @GetMapping(value = "/api/business/zonetype", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    @ResponseBody
    public Response<List<ZoneTypeResponse>> zoneType(@Valid ZoneTypeRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        List<ZoneTypeResponse> listZone = indexService.getActiveZone();
        if (listZone != null && !listZone.isEmpty()) {
            // 取出第一个元素
            ZoneTypeResponse firstElement = listZone.get(0);
            RecommendZoneRequest recommendZoneRequest = new RecommendZoneRequest();
            recommendZoneRequest.setType(firstElement.getType());
            recommendZoneRequest.setZonne_id(Integer.parseInt(firstElement.getZoneId()));
            recommendZoneRequest.setLat(request.getLat());
            recommendZoneRequest.setLot(request.getLot());
            recommendZoneRequest.setShowLikeText(false);
            List<RecommendZoneResponse> productList = indexService.getActiveZoneProduct(recommendZoneRequest, clientType);
            firstElement.setProducts(productList);
        }
        return Responses.ok(listZone);
    }

    /**
     * 專區福利
     *
     * @param request 请求参数
     * @param clientType 客户端类型
     * @return 專區福利响应
     */
    @GetMapping(value = "/api/business/recommendzone", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取精选推荐列表", notes = "根据筛选条件获取專區福利商品列表")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type", 
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)", 
            required = false, 
            dataType = "string", 
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<List<RecommendZoneResponse>> recommendZone(
            @Valid RecommendZoneRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        request.setShowLikeText(false);
        List<RecommendZoneResponse> productList = indexService.getActiveZoneProduct(request, clientType);
        return Responses.ok(productList);
    }

    /**
     * 專區福利-第三方
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 專區福利响应
     */
    @GetMapping(value = "/api/third/party/recommendZone", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取專區福利列表(第三方)", notes = "根据筛选条件获取專區福利商品列表(第三方)")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<List<RecommendZoneResponse>> thirdPartyRecommendZone(@Valid RecommendZoneRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.setShowLikeText(false);
        List<RecommendZoneResponse> productList = indexService.getActiveZoneProduct(request, clientType);
        return Responses.ok(productList);
    }

    /**
     * 類別福利積分、金額區間
     *
     * @return
     */
    @GetMapping(value = "/api/business/indexshopinterval", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ShopIntervalResponse> shopInterval() {
        ShopIntervalResponse response = indexService.getShopInterval();
        return Responses.ok(response);
    }

 /**
     * 類別福利
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 類別福利响应
     */
    @PostMapping(value = "/api/business/indexshoptype", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取類別福利列表", notes = "根据筛选条件获取類別福利商品列表")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<ShopTypeResponse> shopType(@Valid @RequestBody ShopTypeRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        request.setStart((request.getPage() - 1) * request.getCount());
        ShopTypeResponse response = indexService.getShopType(request, clientType);
        return Responses.ok(response);
    }

    /**
     * 门店分类
     *
     * @return
     */
    @GetMapping(value = "/api/business/indexstorestypecache", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<StoreTypeResponse> storeType() {
        StoreTypeResponse storeTypeResponse = indexService.getStoresType();
        storeTypeResponse.setShopInterval(indexService.getShopInterval());
        return Responses.ok(storeTypeResponse);
    }

    /**
     * 搶購下期精选
     *
     * @return
     */
    @GetMapping(value = "/api/business/snapupnext", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "获取搶購下期精选列表", notes = "根据筛选条件获取搶購下期精选商品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "X-Client-Type",
                    value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
                    required = false,
                    dataType = "string",
                    paramType = "header",
                    example = "HARMONY"
            )
    })
    @ResponseBody
    public Response<SnapUpNextResponse> snapUpNext(SnapUpNextRequest request,
               @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        SnapUpNextResponse response = indexService.getSnapUpNext(request, clientType);
        return Responses.ok(response);
    }

    /**
     * 本期搶購
     *
     * @return
     */
    @GetMapping(value = "/api/business/indexsnapup", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "获取本期搶購列表", notes = "根据筛选条件获取本期搶購商品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "X-Client-Type",
                    value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
                    required = false,
                    dataType = "string",
                    paramType = "header",
                    example = "HARMONY"
            )
    })
    @ResponseBody
    public Response<SnapUpResponse> snapUp(SnapUpRequest request,
               @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        SnapUpResponse response = indexService.getSnapUp(request, clientType);
        return Responses.ok(response);
    }


    /**
     * 搶購更多
     *
     * @return
     */
    @GetMapping(value = "/api/business/snapup", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "获取搶購更多列表", notes = "根据筛选条件获取搶購更多商品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "X-Client-Type",
                    value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
                    required = false,
                    dataType = "string",
                    paramType = "header",
                    example = "HARMONY"
            )
    })
    @ResponseBody
    public Response<SnapUpMoreResponse> snapUpMore(SnapUpMoreRequest request,
               @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        SnapUpMoreResponse response = indexService.getSnapUpMore(request, clientType);
        return Responses.ok(response);
    }

    /**
     * 首页廣告圖片 v2
     *
     * @return
     */
    @GetMapping(value = "/api/v2/business/indexpostercache", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<BannerResponseV2> bannerV2(BannerRequest request) {
        Locale locale = contextHolder.getLocale();
        String language = locale.getLanguage();
        BannerResponseV2 response = new BannerResponseV2();
        response.setSnatchList(Lists.newArrayList());
        List<FookBanner> banners = indexService.getIndexPoster();
        for (FookBanner banner : banners) {
            String bannerStyle = Convert.toStr(banner.getBannerStyle());
            if (CollectionUtils.isEmpty(request.getBannerStyle()) || request.getBannerStyle().contains(bannerStyle)) {
                BannerResponseV2.SnatchListItem item = new BannerResponseV2.SnatchListItem();
                item.setId(Convert.toStr(banner.getId()));
                String icon = banner.getIcon();
                if (language.equals(McoinMall.LANG_EN) && StringUtils.isNotBlank(banner.getIconEn())) {
                    icon = banner.getIconEn();
                }
                item.setImg(OssUtil.initOssImage(icon));
                item.setSort(Convert.toStr(banner.getSort()));
                item.setSrc(banner.getSrc());
                item.setType(Convert.toStr(banner.getType()));
                item.setBroadcastStartTime(JodaTimeUtil.format(banner.getBroadcastStartTime(), JodaTimeUtil.DEFAULT_PATTERN));
                item.setBroadcastEndTime(JodaTimeUtil.format(banner.getBroadcastEndTime(), JodaTimeUtil.DEFAULT_PATTERN));
                item.setIsBroadcast(Convert.toStr(banner.getIsBroadcast()));
                item.setLocation(Convert.toStr(banner.getLocation()));
                item.setName(banner.getName());
                item.setBannerStyle(ObjectUtils.defaultIfNull(bannerStyle, ""));
                response.getSnatchList().add(item);
            }
        }
        return Responses.ok(response);
    }

    /**
     * 首页模块
     *
     * @return
     */
    @GetMapping(value = "/api/business/index", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "获取首页模块", notes = "根据筛选条件获取首页模块")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "X-Client-Type",
                    value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
                    required = false,
                    dataType = "string",
                    paramType = "header",
                    example = "HARMONY"
            )
    })
    @ResponseBody
    public Response<IndexModuleResponse> index(@RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        List<FookModule> fookModules = this.indexService.getIndex();
        IndexModuleResponse response = new IndexModuleResponse();
        List<IndexModuleResponse.SnatchListItem> snatchList = Lists.newArrayList();
        response.setSnatchList(snatchList);
        for (FookModule module : fookModules) {
            IndexModuleResponse.SnatchListItem item = new IndexModuleResponse.SnatchListItem();
            item.setModule(module.getBannerName());
            item.setBannerStyle(ObjectUtils.defaultIfNull(Convert.toStr(module.getBannerStyle()), ""));
            snatchList.add(item);
        }
        response.setHideHarmonyModule(MiniProgramDisplayUtil.filterMiniprogramProduct(clientType));
        return Responses.ok(response);
    }

    /**
     * 著数分类
     *
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 著数分类响应
     */
    @GetMapping(value = "/api/business/storeKeywords", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取著数分类列表", notes = "获取所有著数分类信息")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<StoreKeywordsResponse> storeKeywords(
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        // 检查是否为HARMONY设备且开关开启
        Boolean harmonySwitch = MiniProgramDisplayUtil.filterMiniprogramProduct(clientType);

        Locale locale = contextHolder.getLocale();
        String language = locale.getLanguage();
        List<FookStoresKeyword> storeKeyWords = this.indexService.getStoreKeyWordsV2(harmonySwitch);
        StoreKeywordsResponse response = new StoreKeywordsResponse();
        List<StoreKeywordsResponse.SnatchListItem> snatchList = Lists.newArrayList();
        response.setSnatchList(snatchList);
        if (CollectionUtils.isNotEmpty(storeKeyWords)) {
            for (FookStoresKeyword storeKeyWord : storeKeyWords) {
                StoreKeywordsResponse.SnatchListItem item = new StoreKeywordsResponse.SnatchListItem();
                item.setId(storeKeyWord.getId());
                item.setSrc(storeKeyWord.getSrc());
                item.setSrcType(Convert.toStr(storeKeyWord.getSrcType()));
                item.setImg(OssUtil.initOssImage(storeKeyWord.getIcon()));
                item.setSort(storeKeyWord.getSort());
                item.setExtendInfo(storeKeyWord.getExtendInfo());
                String name = storeKeyWord.getName();
                if (language.equals(McoinMall.LANG_EN) && StringUtils.isNotBlank(storeKeyWord.getEnglishName())) {
                    name = storeKeyWord.getEnglishName();
                }
                item.setName(name);
                snatchList.add(item);
            }
        }
        return Responses.ok(response);
    }

}
