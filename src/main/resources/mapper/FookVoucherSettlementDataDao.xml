<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookVoucherSettlementDataDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookVoucherSettlementData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="storeid" jdbcType="INTEGER" property="storeid" />
    <result column="ordercodeid" jdbcType="INTEGER" property="ordercodeid" />
    <result column="productid" jdbcType="INTEGER" property="productid" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="businessname" jdbcType="VARCHAR" property="businessname" />
    <result column="storename" jdbcType="VARCHAR" property="storename" />
    <result column="businesscode" jdbcType="VARCHAR" property="businesscode" />
    <result column="bankaccount" jdbcType="VARCHAR" property="bankaccount" />
    <result column="bankname" jdbcType="VARCHAR" property="bankname" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="usetime" jdbcType="TIMESTAMP" property="usetime" />
    <result column="billamount" jdbcType="DECIMAL" property="billamount" />
    <result column="userpaymentamount" jdbcType="DECIMAL" property="userpaymentamount" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="momecoinsamount" jdbcType="DECIMAL" property="momecoinsamount" />
    <result column="mpayintegral_exchange_amount" jdbcType="DECIMAL" property="mpayintegralExchangeAmount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="merchantsettleamount" jdbcType="DECIMAL" property="merchantsettleamount" />
    <result column="vouchercode" jdbcType="VARCHAR" property="vouchercode" />
    <result column="vouchername" jdbcType="VARCHAR" property="vouchername" />
    <result column="ordertransaction" jdbcType="VARCHAR" property="ordertransaction" />
    <result column="settlementtime" jdbcType="TIMESTAMP" property="settlementtime" />
    <result column="voucher_id" jdbcType="INTEGER" property="voucherId" />
    <result column="is_mpay" jdbcType="INTEGER" property="isMpay" />
    <result column="is_member" jdbcType="TINYINT" property="isMember" />
    <result column="memberintegral" jdbcType="DECIMAL" property="memberintegral" />
  </resultMap>
  <sql id="Base_Column_List">
    id, businessid, storeid, ordercodeid, productid, orderid, businessname, storename, 
    businesscode, bankaccount, bankname, bank, createtime, usetime, billamount, userpaymentamount, 
    mpayintegral, momecoinsamount, mpayintegral_exchange_amount, commission, merchantsettleamount, 
    vouchercode, vouchername, ordertransaction, settlementtime, voucher_id, is_mpay, 
    is_member, memberintegral
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_voucher_settlement_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_voucher_settlement_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookVoucherSettlementData">
    insert into fook_voucher_settlement_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="storeid != null">
        storeid,
      </if>
      <if test="ordercodeid != null">
        ordercodeid,
      </if>
      <if test="productid != null">
        productid,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="businessname != null">
        businessname,
      </if>
      <if test="storename != null">
        storename,
      </if>
      <if test="businesscode != null">
        businesscode,
      </if>
      <if test="bankaccount != null">
        bankaccount,
      </if>
      <if test="bankname != null">
        bankname,
      </if>
      <if test="bank != null">
        bank,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="usetime != null">
        usetime,
      </if>
      <if test="billamount != null">
        billamount,
      </if>
      <if test="userpaymentamount != null">
        userpaymentamount,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="momecoinsamount != null">
        momecoinsamount,
      </if>
      <if test="mpayintegralExchangeAmount != null">
        mpayintegral_exchange_amount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="merchantsettleamount != null">
        merchantsettleamount,
      </if>
      <if test="vouchercode != null">
        vouchercode,
      </if>
      <if test="vouchername != null">
        vouchername,
      </if>
      <if test="ordertransaction != null">
        ordertransaction,
      </if>
      <if test="settlementtime != null">
        settlementtime,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="isMpay != null">
        is_mpay,
      </if>
      <if test="isMember != null">
        is_member,
      </if>
      <if test="memberintegral != null">
        memberintegral,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="productid != null">
        #{productid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="businessname != null">
        #{businessname,jdbcType=VARCHAR},
      </if>
      <if test="storename != null">
        #{storename,jdbcType=VARCHAR},
      </if>
      <if test="businesscode != null">
        #{businesscode,jdbcType=VARCHAR},
      </if>
      <if test="bankaccount != null">
        #{bankaccount,jdbcType=VARCHAR},
      </if>
      <if test="bankname != null">
        #{bankname,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="usetime != null">
        #{usetime,jdbcType=TIMESTAMP},
      </if>
      <if test="billamount != null">
        #{billamount,jdbcType=DECIMAL},
      </if>
      <if test="userpaymentamount != null">
        #{userpaymentamount,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="momecoinsamount != null">
        #{momecoinsamount,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegralExchangeAmount != null">
        #{mpayintegralExchangeAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="merchantsettleamount != null">
        #{merchantsettleamount,jdbcType=DECIMAL},
      </if>
      <if test="vouchercode != null">
        #{vouchercode,jdbcType=VARCHAR},
      </if>
      <if test="vouchername != null">
        #{vouchername,jdbcType=VARCHAR},
      </if>
      <if test="ordertransaction != null">
        #{ordertransaction,jdbcType=VARCHAR},
      </if>
      <if test="settlementtime != null">
        #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=INTEGER},
      </if>
      <if test="isMpay != null">
        #{isMpay,jdbcType=INTEGER},
      </if>
      <if test="isMember != null">
        #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        #{memberintegral,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookVoucherSettlementData">
    update fook_voucher_settlement_data
    <set>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        storeid = #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="productid != null">
        productid = #{productid,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="businessname != null">
        businessname = #{businessname,jdbcType=VARCHAR},
      </if>
      <if test="storename != null">
        storename = #{storename,jdbcType=VARCHAR},
      </if>
      <if test="businesscode != null">
        businesscode = #{businesscode,jdbcType=VARCHAR},
      </if>
      <if test="bankaccount != null">
        bankaccount = #{bankaccount,jdbcType=VARCHAR},
      </if>
      <if test="bankname != null">
        bankname = #{bankname,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        bank = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="usetime != null">
        usetime = #{usetime,jdbcType=TIMESTAMP},
      </if>
      <if test="billamount != null">
        billamount = #{billamount,jdbcType=DECIMAL},
      </if>
      <if test="userpaymentamount != null">
        userpaymentamount = #{userpaymentamount,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="momecoinsamount != null">
        momecoinsamount = #{momecoinsamount,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegralExchangeAmount != null">
        mpayintegral_exchange_amount = #{mpayintegralExchangeAmount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="merchantsettleamount != null">
        merchantsettleamount = #{merchantsettleamount,jdbcType=DECIMAL},
      </if>
      <if test="vouchercode != null">
        vouchercode = #{vouchercode,jdbcType=VARCHAR},
      </if>
      <if test="vouchername != null">
        vouchername = #{vouchername,jdbcType=VARCHAR},
      </if>
      <if test="ordertransaction != null">
        ordertransaction = #{ordertransaction,jdbcType=VARCHAR},
      </if>
      <if test="settlementtime != null">
        settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=INTEGER},
      </if>
      <if test="isMpay != null">
        is_mpay = #{isMpay,jdbcType=INTEGER},
      </if>
      <if test="isMember != null">
        is_member = #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        memberintegral = #{memberintegral,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookVoucherSettlementData">
    update fook_voucher_settlement_data
    set businessid = #{businessid,jdbcType=INTEGER},
      storeid = #{storeid,jdbcType=INTEGER},
      ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      productid = #{productid,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      businessname = #{businessname,jdbcType=VARCHAR},
      storename = #{storename,jdbcType=VARCHAR},
      businesscode = #{businesscode,jdbcType=VARCHAR},
      bankaccount = #{bankaccount,jdbcType=VARCHAR},
      bankname = #{bankname,jdbcType=VARCHAR},
      bank = #{bank,jdbcType=VARCHAR},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      usetime = #{usetime,jdbcType=TIMESTAMP},
      billamount = #{billamount,jdbcType=DECIMAL},
      userpaymentamount = #{userpaymentamount,jdbcType=DECIMAL},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      momecoinsamount = #{momecoinsamount,jdbcType=DECIMAL},
      mpayintegral_exchange_amount = #{mpayintegralExchangeAmount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      merchantsettleamount = #{merchantsettleamount,jdbcType=DECIMAL},
      vouchercode = #{vouchercode,jdbcType=VARCHAR},
      vouchername = #{vouchername,jdbcType=VARCHAR},
      ordertransaction = #{ordertransaction,jdbcType=VARCHAR},
      settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      voucher_id = #{voucherId,jdbcType=INTEGER},
      is_mpay = #{isMpay,jdbcType=INTEGER},
      is_member = #{isMember,jdbcType=TINYINT},
      memberintegral = #{memberintegral,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>