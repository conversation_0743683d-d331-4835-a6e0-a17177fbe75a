<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookActiveZoneDao">
    <select id="getActiveZones" resultType="com.mcoin.mall.bean.FookActiveZone">
        select
        id, `name`, name_en as nameEn, `status`, `type`, sort, created_at as createdAt, updated_at as updateAt
        from fook_active_zone
        where
        status = 1
        and type = 1
        order by sort asc
    </select>

    <select id="getActiveZoneProducts" resultType="com.mcoin.mall.bo.ActiveZoneProductBo" timeout="5">
        select
        p.id as pid,p.businessid as businessId,p.title,p.zip_img as zipImg,p.img,p.price,p.only_point as onlyPoint,p.snap_up as snapUp,
        p.retail_price as retailPrice,p.stock,p.sales,p.orderBy,p.maximum_points as maximumPoints,b.system_type as systemType,b.name
        as businessName,p.buy_end_time as buyEndTime,p.buy_start_time as buyStartTime,p.min_point as minPoint,p.point_ratio as pointRatio,
                        p.type as type,p.href_url as hrefUrl, p.goods_id as goodsId
        from fook_active_zone_product_translations azpt
        LEFT JOIN fook_business_product p on azpt.business_product_id = p.id
        LEFT JOIN fook_business b on p.businessid = b.id
        where p.shelf_status = 1
          and p.enable = 1
          and p.buy_start_time <![CDATA[<=]]> now()
          and p.buy_end_time <![CDATA[>]]>  now()
          and azpt.active_zone_id = #{zoneId,jdbcType=INTEGER}
            <if test="harmonySwitch == true">
                and p.type <![CDATA[ <> ]]>  12
            </if>
        order by azpt.order_by asc
    </select>

    <select id="getActiveZoneStores"  resultType="com.mcoin.mall.bo.ActiveZoneStoreBo">
        SELECT bsp2.productid AS productId, s.enable, s.name AS storeName, s.longitude, s.dimension
        FROM fook_business_store_product bsp2
        LEFT JOIN fook_stores s ON
        s.id = bsp2.storeid
        WHERE s.enable = 1
        AND bsp2.id IN (
            SELECT MIN(bsp.id) FROM fook_business_store_product bsp
            WHERE bsp.productid IN
            <foreach item="productId" collection="productIds" open="(" separator="," close=")">
             #{productId}
            </foreach>
            GROUP BY bsp.productid
        )
    </select>

</mapper>
