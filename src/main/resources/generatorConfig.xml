<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
	<context id="Tables" defaultModelType="flat" targetRuntime="MyBatis3">

		<!-- 自动识别数据库关键字，默认false -->
		<property name="autoDelimitKeywords" value="true" />
		<!--可以使用``包括字段名，避免字段名与sql保留字冲突报错 -->
		<property name="beginningDelimiter" value="`" />
		<property name="endingDelimiter" value="`" />
		
		<!-- 整合 lombok 插件 -->
		<plugin type="com.mcoin.mall.gen.LombokPlugin" />
		<!--生成mapper.xml时覆盖原文件-->
		<plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin" />

		<!--自定义类的注释 -->
		<commentGenerator type="com.mcoin.mall.gen.CodeCommentGenerator">
			<property name="suppressDate" value="false"/>
			<property name="dateFormat" value="yyyy-MM-dd"/>
			<property name="addRemarkComments" value="true"/>
			<property name="suppressAllComments" value="false"/>
		</commentGenerator>

		<!--数据库连接 -->
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
						connectionURL="********************************************************************************"
						userId="mcoin" password="Zhu@123456">
		</jdbcConnection>
		<!--默认false Java type resolver will always use java.math.BigDecimal if 
			the database column is of type DECIMAL or NUMERIC. -->
		<javaTypeResolver type="com.mcoin.mall.gen.McoinJavaTypeResolver">
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>

		<!--生成实体类 指定包名 以及生成的地址 （可以自定义地址，但是路径不存在不会自动创建 使用Maven生成在target目录下，会自动创建）注意mac和win系统路径差异 -->
		<javaModelGenerator targetPackage="com.mcoin.mall.bean"
			targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="false" />
		</javaModelGenerator>
		<!--生成SQLMAP文件 注意mac和win系统路径差异-->
		<sqlMapGenerator targetPackage="mapper"
			targetProject="src/main/resources">
			<property name="enableSubPackages" value="false" />
		</sqlMapGenerator>
		<!--生成Dao文件 可以配置 type="XMLMAPPER"生成xml的dao实现 context id="DB2Tables" 修改targetRuntime="MyBatis3" 注意mac和win系统路径差异-->
		<javaClientGenerator type="XMLMAPPER"
			targetPackage="com.mcoin.mall.dao" targetProject="src/main/java">
			<property name="enableSubPackages" value="false" />
		</javaClientGenerator>

		<!--对应数据库表 mysql可以加入主键自增 字段命名 忽略某字段等 -->
		<table tableName="FOOK_BUSINESS_PRODUCT"  domainObjectName="FookBusinessProduct" mapperName="FookBusinessProductDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>
<!--		<table tableName="FOOK_BUSINESS" domainObjectName="FookBusiness" mapperName="FookBusinessDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_PRODUCT_TRANSLATIONS" domainObjectName="FookBusinessProductTranslations" mapperName="FookBusinessProductTranslationsDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_TRANSLATIONS" domainObjectName="FookBusinessTranslations" mapperName="FookBusinessTranslationsDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PRODUCT_STOCK_LOG" domainObjectName="FookProductStockLog" mapperName="FookProductStockLogDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_STORES" domainObjectName="FookStores" mapperName="FookStoresDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_STORES_KEYWORD" domainObjectName="FookStoresKeyword" mapperName="FookStoresKeywordDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_STORES_TYPE" domainObjectName="FookStoresType" mapperName="FookStoresTypeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_SUGGEST" domainObjectName="FookPlatformSuggest" mapperName="FookPlatformSuggestDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BANNER" domainObjectName="FookBanner" mapperName="FookBannerDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_STORE_PRODUCT" domainObjectName="FookBusinessStoreProduct" mapperName="FookBusinessStoreProductDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_STORES_TYPE_CLASS" domainObjectName="FookStoresTypeClass" mapperName="FookStoresTypeClassDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_STORES_KEYWORD_CLASS" domainObjectName="FookStoresKeywordClass" mapperName="FookStoresKeywordClassDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PRODUCT_DISCOUNT" domainObjectName="FookProductDiscount" mapperName="FookProductDiscountDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_COUPON_PRODUCT" domainObjectName="FookCouponProduct" mapperName="FookCouponProductDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_PRODUCT_LIMIT" domainObjectName="FookBusinessProductLimit" mapperName="FookBusinessProductLimitDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_MACAUPASS_USER" domainObjectName="FookMacaupassUser" mapperName="FookMacaupassUserDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_ORDER" domainObjectName="FookPlatformOrder" mapperName="FookPlatformOrderDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_ORDERCODE" domainObjectName="FookPlatformOrdercode" mapperName="FookPlatformOrdercodeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_ORDERINFO" domainObjectName="FookPlatformOrderinfo" mapperName="FookPlatformOrderinfoDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_EXTERNAL_VCODE" domainObjectName="FookExternalVcode" mapperName="FookExternalVcodeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_MACAUPASS_ORDER" domainObjectName="FookMacaupassOrder" mapperName="FookMacaupassOrderDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PAY_LOG" domainObjectName="FookPayLog" mapperName="FookPayLogDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_SCORE_LOG" domainObjectName="FookScoreLog" mapperName="FookScoreLogDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_PRODUCTCATEGORY" domainObjectName="FookBusinessProductcategory" mapperName="FookBusinessProductcategoryDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_PRODUCT_ORDERCODE" domainObjectName="FookBusinessProductOrdercode" mapperName="FookBusinessProductOrdercodeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_CLAUSE" domainObjectName="FookClause" mapperName="FookClauseDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_CLAUSE_CONFIRM" domainObjectName="FookClauseConfirm" mapperName="FookClauseConfirmDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_DAY_GAIN" domainObjectName="FookDayGain" mapperName="FookDayGainDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_MACAUPASS_TOKEN" domainObjectName="FookMacaupassToken" mapperName="FookMacaupassTokenDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_MACAUPASS_USER_LOGIN_LOG2" domainObjectName="FookMacaupassUserLoginLog2" mapperName="FookMacaupassUserLoginLog2Dao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_DAY_DISCOUNT" domainObjectName="FookDayDiscount" mapperName="FookDayDiscountDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_DAY_ITEM" domainObjectName="FookDayItem" mapperName="FookDayItemDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_USERINFO" domainObjectName="FookPlatformUserinfo" mapperName="FookPlatformUserinfoDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_ACTIVE_ZONE" domainObjectName="FookActiveZone" mapperName="FookActiveZoneDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_ACTIVE_ZONE_PRODUCT_TRANSLATIONS" domainObjectName="FookActiveZoneProductTranslations" mapperName="FookActiveZoneProductTranslationsDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_INFORMATION" domainObjectName="FookBusinessInformation" mapperName="FookBusinessInformationDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="SETTINGS" domainObjectName="Settings" mapperName="SettingsDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_STORES_TYPE_TRANSLATIONS" domainObjectName="FookStoresTypeTranslations" mapperName="FookStoresTypeTranslationsDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_STORES_TRANSLATIONS" domainObjectName="FookStoresTranslations" mapperName="FookStoresTranslationsDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_TEMPORARY_PRODUCT" domainObjectName="FookTemporaryProduct" mapperName="FookTemporaryProductDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PRODUCT_SNAPPING_RECORD" domainObjectName="FookProductSnappingRecord" mapperName="FookProductSnappingRecordDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_USERCOLLECTION" domainObjectName="FookPlatformUsercollection" mapperName="FookPlatformUsercollectionDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_ACTIVE_MERCHANT" domainObjectName="FookActiveMerchant" mapperName="FookActiveMerchantDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_ACTIVE_MERCHANT_DATA" domainObjectName="FookActiveMerchantData" mapperName="FookActiveMerchantDataDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_ORDERCODE_SETTLEMENT" domainObjectName="FookReportOrdercodeSettlement" mapperName="FookReportOrdercodeSettlementDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_BUSINESS_PRODUCTSTOCK" domainObjectName="FookBusinessProductstock" mapperName="FookBusinessProductstockDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_MOMECOINS_TASK" domainObjectName="FookMomecoinsTask" mapperName="FookMomecoinsTaskDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_USER_GROWTH" domainObjectName="FookPlatformUserGrowth" mapperName="FookPlatformUserGrowthDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_COUPON_SYN" domainObjectName="FookCouponSyn" mapperName="FookCouponSynDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_ORDERCODE_LOG" domainObjectName="FookPlatformOrdercodeLog" mapperName="FookPlatformOrdercodeLogDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_A_FEE_TYPE" domainObjectName="FookAFeeType" mapperName="FookAFeeTypeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_ORDERREFUND" domainObjectName="FookPlatformOrderrefund" mapperName="FookPlatformOrderrefundDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_UNUSUALORDER" domainObjectName="FookPlatformUnusualorder" mapperName="FookPlatformUnusualorderDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_PLATFORM_ORDERREFUND_RECORD" domainObjectName="FookPlatformOrderrefundRecord" mapperName="FookPlatformOrderrefundRecordDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_SHOW" domainObjectName="FookShow" mapperName="FookShowDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_SHOW_ZONE" domainObjectName="FookShowZone" mapperName="FookShowZoneDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_SHOW_ZONE_PRODUCT" domainObjectName="FookShowZoneProduct" mapperName="FookShowZoneProductDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_MACAUPASS_CODE" domainObjectName="FookMacaupassCode" mapperName="FookMacaupassCodeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_VOUCHER_ASSIGNMENT" domainObjectName="FookVoucherAssignment" mapperName="FookVoucherAssignmentDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_VOUCHER_ASSIGNMENT_DATA" domainObjectName="FookVoucherAssignmentData" mapperName="FookVoucherAssignmentDataDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_TRADING_SEND_EMAIL_PROCESS" domainObjectName="FookTradingSendEmailProcess" mapperName="FookTradingSendEmailProcessDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_TRADING" domainObjectName="FookTrading" mapperName="FookTradingDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_TRADING_DATA" domainObjectName="FookTradingData" mapperName="FookTradingDataDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_TRADING_DATA_CLASS" domainObjectName="FookTradingDataClass" mapperName="FookTradingDataClassDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_TRADING_WEEK" domainObjectName="FookTradingWeek" mapperName="FookTradingWeekDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_MERCHANT_SETTLEMENT" domainObjectName="FookReportMerchantSettlement" mapperName="FookReportMerchantSettlementDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_SHOP_SETTLENEMT" domainObjectName="FookReportShopSettlement" mapperName="FookReportShopSettlementDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_MERCHANT_SETTLEMENT_BATCH" domainObjectName="FookReportMerchantSettlementBatch" mapperName="FookReportMerchantSettlementBatchDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_MERCHANT_SETTLEMENT_PROCESSS" domainObjectName="FookReportMerchantSettlementProcesss" mapperName="FookReportMerchantSettlementProcesssDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_PROFIT" domainObjectName="FookReportProfit" mapperName="FookReportProfitDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_ORDERCODE_INTERNAL" domainObjectName="FookReportOrdercodeInternal" mapperName="FookReportOrdercodeInternalDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_ORDERCODE_INTERNAL_BATCH" domainObjectName="FookReportOrdercodeInternalBatch" mapperName="FookReportOrdercodeInternalBatchDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_MERCHANT_SETTLEMENT_OPTIONAL" domainObjectName="FookReportMerchantSettlementOptional" mapperName="FookReportMerchantSettlementOptionalDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_MERCHANT_SETTLEMENT_OPTIONAL_BATCH" domainObjectName="FookReportMerchantSettlementOptionalBatch" mapperName="FookReportMerchantSettlementOptionalBatchDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_SHOP_SETTLENEMT_OPTIONAL" domainObjectName="FookReportShopSettlenemtOptional" mapperName="FookReportShopSettlenemtOptionalDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_REPORT_MERCHANT_SETTLEMENT_OPTIONAL_PROCESSS" domainObjectName="FookReportMerchantSettlementOptionalProcesss" mapperName="FookReportMerchantSettlementOptionalProcesssDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--        <table tableName="FOOK_MQ_LOCAL" domainObjectName="FookMqLocal" mapperName="FookMqLocalDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="FOOK_MODULE" domainObjectName="fookModule" mapperName="fookModuleDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_report_mpaycoin_ordercode" domainObjectName="FookReportMpaycoinOrdercode" mapperName="FookReportMpaycoinOrdercodeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_report_mpaycoin_code" domainObjectName="FookReportMpaycoinCode" mapperName="FookReportMpaycoinCodeDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_report_mpaycoin_codelist" domainObjectName="FookReportMpaycoinCodeList" mapperName="FookReportMpaycoinCodeListDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_report_business" domainObjectName="FookReportBusiness" mapperName="FookReportBusinessDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_report_business_data" domainObjectName="FookReportBusinessData" mapperName="FookReportBusinessDataDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_order_settlement" domainObjectName="FookOrderSettlement" mapperName="FookOrderSettlementDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_order_settlement_data" domainObjectName="FookOrderSettlementData" mapperName="FookOrderSettlementDataDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_report_mpaycoin_all" domainObjectName="FookReportMpaycoinAll" mapperName="FookReportMpaycoinAllDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_report_mpaycoin_ordercode_all" domainObjectName="FookReportMpaycoinOrdercodeAll" mapperName="FookReportMpaycoinOrdercodeAllDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_refund_order" domainObjectName="FookRefundOrder" mapperName="FookRefundOrderDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_voucher_settlement" domainObjectName="FookVoucherSettlement" mapperName="FookVoucherSettlementDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_voucher_settlement_data" domainObjectName="FookVoucherSettlementData" mapperName="FookVoucherSettlementDataDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--			<table tableName="fook_report_mpaycoin" domainObjectName="FookReportMpaycoin" mapperName="FookReportMpaycoinDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>&ndash;&gt;-->
<!--		<table tableName="fook_show_snapup_session" domainObjectName="FookShowSnapupSession" mapperName="FookShowSnapupSessionDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_show_snapup_session_product" domainObjectName="FookShowSnapupSessionProduct" mapperName="FookShowSnapupSessionProductDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--			<table tableName="fook_mini_order_settlement" domainObjectName="FookMiniOrderSettlement" mapperName="FookMiniOrderSettlementDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--			<table tableName="fook_product_manual_recommend" domainObjectName="FookProductManualRecommend" mapperName="FookProductManualRecommendDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
<!--		<table tableName="fook_mini_order_sync_log" domainObjectName="FookMiniOrderSyncLog" mapperName="FookMiniOrderSyncLogDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>-->
		<!-- <table tableName="fook_order_env" domainObjectName="FookOrderEnv" mapperName="FookOrderEnvDao" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/> -->

	</context>
</generatorConfiguration>