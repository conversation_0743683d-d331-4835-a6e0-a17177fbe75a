<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTemporaryProductDao">
    <select id="selectTotol" resultType="java.lang.Integer">
        select count(1) from fook_temporary_product
    </select>
    <select id="selectSearchTerm" resultType="java.lang.Integer">
        select id
        from fook_temporary_product
        where (complex_name_en like concat('%', #{searchTerm,jdbcType=VARCHAR}, '%')
            OR complex_name like concat('%', #{searchTermSimple,jdbcType=VARCHAR}, '%')
            OR complex_name like concat('%', #{searchTermTraditional,jdbcType=VARCHAR}, '%')
            OR complex_name like concat('%', #{searchTermTwTraditional,jdbcType=VARCHAR}, '%'))
        <if test="harmonySwitch == true">
            <![CDATA[ AND type <> 12 ]]>
        </if>
        group by id ORDER BY id
    </select>
    <select id="selectOriginalTemporary" resultType="java.util.Map" timeout="5">
        SELECT
        p.id as "productId", s.id as "storeId"
        FROM
        fook_business_product AS p
        LEFT JOIN fook_business_store_product AS bsp ON p.id = bsp.productid
        LEFT JOIN fook_stores AS s ON s.id = bsp.storeid
        LEFT JOIN fook_business_information AS i ON i.id = s.business_information_id
        LEFT JOIN fook_business_product_translations pt ON p.id = pt.business_product_id
        WHERE
        p.is_mpay_open = 1
        AND s.enable = 1
        AND p.shelf_status = 1
        AND p.enable = 1
        AND p.is_redeem = 0
        and now() between p.buy_start_time and p.buy_end_time
        AND p.platform_type = 1
        <if test="harmonySwitch == true">
            <![CDATA[ AND p.type <> 12 ]]>
        </if>
        AND EXISTS ( SELECT * FROM fook_business WHERE p.businessid = fook_business.id AND fook_business.system_type in
        (1,3) )
        AND (
            CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,''), ifnull(i.name_en,''),
                    ifnull(pt.t_title,'')) like concat('%',#{searchTerm,jdbcType=VARCHAR},'%')
            OR CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,'')) like concat('%',#{searchTermSimple,jdbcType=VARCHAR},'%')
            OR CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,'')) like concat('%',#{searchTermTraditional,jdbcType=VARCHAR},'%')
            OR CONCAT( ifnull(p.title,''), ifnull(s.`name`,'') , ifnull(i.`name`,'')) like concat('%',#{searchTermTwTraditional,jdbcType=VARCHAR},'%')
            )
    </select>

    <sql id="searchProductSql">
        <where>
            <choose>
                <when test="types != null">
                    AND type in
                    <foreach collection="types" item="type" open="(" separator="," close=")">
                        #{type,jdbcType=INTEGER}
                    </foreach>
                </when>
                <when test="harmonySwitch == true">
                    <![CDATA[ AND type <> 12 ]]>
                </when>
            </choose>
            <if test="storesTypes != null">
                AND stores_type in
                <foreach collection="storesTypes" item="storesType" open="(" separator="," close=")">
                    #{storesType,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="informationIds != null">
                AND information_id in
                <foreach collection="informationIds" item="informationId" open="(" separator="," close=")">
                    #{informationId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="businessCategoriesIds != null and businessCategoriesIds.size > 0">
                AND business_categories_id in
                <foreach collection="businessCategoriesIds" item="businessCategoriesId" open="(" separator="," close=")">
                    #{businessCategoriesId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="search != null">
                AND (complex_name_en like concat('%',#{search,jdbcType=VARCHAR},'%')
                OR complex_name like concat('%',#{searchSimple,jdbcType=VARCHAR},'%')
                OR complex_name like concat('%',#{searchTraditional,jdbcType=VARCHAR},'%')
                OR complex_name like concat('%',#{searchTwTraditional,jdbcType=VARCHAR},'%'))
            </if>
        </where>

    </sql>
    <select id="searchProduct" parameterType="com.mcoin.mall.bean.SearchProductQuery"
            resultType="com.mcoin.mall.bean.FookTemporaryProduct">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fook_temporary_product
        WHERE temporary_id IN (
            SELECT MIN(temporary_id)
            from fook_temporary_product
            <include refid="searchProductSql"/>
            group by id ORDER BY id
        )
        <choose>
            <when test="orderType == 0 and sortType != 2">
                order by
                ACOS(SIN((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) *SIN((dimension * 3.1415) / 180 )
                + COS((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) * COS((dimension * 3.1415) / 180 )
                * COS((#{lot,jdbcType=VARCHAR} * 3.1415) / 180 - (longitude * 3.1415) / 180 ) ) * 6380 ASC
            </when>
            <when test="orderType == 0 and sortType == 2">
                order by
                ACOS(SIN((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) *SIN((dimension * 3.1415) / 180 )
                + COS((#{lat,jdbcType=VARCHAR} * 3.1415) / 180 ) * COS((dimension * 3.1415) / 180 )
                * COS((#{lot,jdbcType=VARCHAR} * 3.1415) / 180 - (longitude * 3.1415) / 180 ) ) * 6380 DESC
            </when>
            <when test="orderType == 1  and sortType != 2">
                order by price ASC,id
            </when>
            <when test="orderType == 1  and sortType == 2">
                order by price DESC,id
            </when>
            <when test="orderType == 2">
                order by actual_sales DESC,id
            </when>
        </choose>
        limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>
    <select id="isProductTempHasData" resultType="java.lang.Integer">
        select count(1) from fook_temporary_product limit 1
    </select>
    <select id="searchProductCount" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT(id))
        from fook_temporary_product
        <include refid="searchProductSql"/>
    </select>

    <sql id="batchInsertBase_Select">
        SELECT p.id, p.img, p.zip_img, p.businessid, p.title, p.type, p.retail_price, p.price, p.point_ratio, p.shelf_status
             , p.buy_start_time, p.buy_end_time, p.vaild_mode, p.vaild_start_time, p.vaild_end_time, p.day_number, p.snap_up
             , s.`name`, i.`name`, i.name_en, i.id, sss.st_id, sss.st_name, ifnull(bpt.t_title, ''), ifnull(fst.t_name, '')
             , ifnull(sss.str_tname, ''), CONCAT( ifnull(p.title, ''), ifnull(s.`name`, '') , ifnull(i.`name`, ''), ifnull(sss.st_name, '')
            , ifnull(skcs.sk_name, '') ), CONCAT( ifnull(p.title, ''), ifnull(s.`name`, '') , ifnull(i.`name`, ''), ifnull(i.name_en, '')
            , ifnull(bpt.t_title, '') , ifnull(fst.t_name, ''), ifnull(sss.str_tname, ''), ifnull(skcs.sk_name, '')
            , ifnull(skcs.sk_english_name, '') ), #{now,jdbcType=TIMESTAMP}, p.only_point, p.actual_sales, s.longitude, s.dimension, p.is_hot, p.is_great
             , ifnull(SUBSTRING_INDEX(p.category_path, '-', 1), NULL) AS business_categories_id
    </sql>
    <sql id="batchInsertBase_From">
        FROM `fook_business_product` AS p
        LEFT JOIN fook_business_store_product AS bsp ON p.id = bsp.productid
        LEFT JOIN fook_stores AS s ON s.id = bsp.storeid
        LEFT JOIN fook_business_information AS i ON i.id = s.business_information_id
        LEFT JOIN fook_business_product_translations AS bpt ON bpt.business_product_id = p.id
        LEFT JOIN fook_stores_translations AS fst ON fst.stores_id = s.id
        LEFT JOIN (
            SELECT st.id AS st_id, stc.stores_id, group_concat(st.name) AS st_name, group_concat(str.t_name) AS str_tname
            FROM fook_stores_type_class stc
            LEFT JOIN fook_stores_type AS st ON stc.stores_type_id = st.id
            LEFT JOIN fook_stores_type_translations AS str ON str.stores_type_id = st.id
            GROUP BY st.id, stc.stores_id ORDER BY st.id,stc.stores_id
        ) AS sss ON sss.stores_id = s.id
        LEFT JOIN (
            SELECT fskc.stores_id, fskc.id, group_concat(sk.name) AS sk_name, group_concat(sk.english_name) AS sk_english_name
            FROM fook_stores_keyword_class fskc
                     LEFT JOIN fook_stores_keyword AS sk ON
                sk.id = fskc.stores_keyword_id
            GROUP BY fskc.stores_id, fskc.id ORDER BY fskc.stores_id, fskc.id
        ) AS skcs ON skcs.stores_id = s.id
    </sql>

    <sql id="batchInsertBase_Where">
        WHERE p.`is_mpay_open` = 1
          AND s.`enable` = 1
          AND p.`shelf_status` = 1
          AND p.`enable` = 1
          AND p.`is_redeem` = 0
          AND p.`platform_type` = 1
          AND EXISTS ( SELECT `fook_business`.`id`
            FROM `fook_business`
            WHERE p.`businessid` = `fook_business`.`id`
            AND `fook_business`.`system_type` IN (1, 3) )
    </sql>

    <insert id="batchInsert">
        insert into fook_temporary_product (
        id,
        img,
        zip_img,
        businessid,
        title,
        type,
        retail_price,
        price,
        point_ratio,
        shelf_status,
        buy_start_time,
        buy_end_time,
        vaild_mode,
        vaild_start_time,
        vaild_end_time,
        day_number,
        snap_up,
        s_name,
        i_name,
        i_name_en,
        information_id,
        stores_type,
        stores_type_name,
        t_product_name,
        t_stores_name,
        t_stores_type_name,
        complex_name,
        complex_name_en,
        created_time,
        only_point,
        actual_sales,
        longitude,
        dimension,
        is_hot,
        is_great,
        business_categories_id
        )
        <include refid="batchInsertBase_Select"/>
        <include refid="batchInsertBase_From"/>
        <include refid="batchInsertBase_Where"/>
        AND p.`buy_end_time` > #{now,jdbcType=TIMESTAMP}
        AND p.`buy_start_time` <![CDATA[ < ]]> #{now,jdbcType=TIMESTAMP}
        union
        <!-- == 未开卖的抢购商品挂了近2天内秒杀场次（没有到可购买开始时间）允许被搜索 == -->
        <include refid="batchInsertBase_Select"/>
        <include refid="batchInsertBase_From"/>
        <include refid="batchInsertBase_Where"/>
        AND p.id in (
        select sp.product_id
        from fook_show_snapup_session_product sp
        join fook_show_snapup_session s on sp.session_id = s.id
        where date(s.start_time) >= CURDATE()
        and date(s.start_time) <![CDATA[<=]]> DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        and s.end_time > now()
        and s.valid = 1
        )
    </insert>
    <delete id="deleteAll">
        DELETE from fook_temporary_product
    </delete>

</mapper>