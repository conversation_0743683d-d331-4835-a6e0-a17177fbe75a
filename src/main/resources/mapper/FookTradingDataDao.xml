<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingDataDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookTradingData">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statistics_day" jdbcType="DATE" property="statisticsDay" />
    <result column="user_num" jdbcType="INTEGER" property="userNum" />
    <result column="user_pay_num" jdbcType="INTEGER" property="userPayNum" />
    <result column="use_no_pay_num" jdbcType="INTEGER" property="useNoPayNum" />
    <result column="business_num" jdbcType="INTEGER" property="businessNum" />
    <result column="trading_num" jdbcType="INTEGER" property="tradingNum" />
    <result column="integral_conversion_amount" jdbcType="DECIMAL" property="integralConversionAmount" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="payment_sum" jdbcType="DECIMAL" property="paymentSum" />
    <result column="settlement_business_num" jdbcType="INTEGER" property="settlementBusinessNum" />
    <result column="settlement_num" jdbcType="INTEGER" property="settlementNum" />
    <result column="settlement_conversion_amount" jdbcType="DECIMAL" property="settlementConversionAmount" />
    <result column="settlement_payment_amount" jdbcType="DECIMAL" property="settlementPaymentAmount" />
    <result column="settlement_amount_sum" jdbcType="DECIMAL" property="settlementAmountSum" />
    <result column="refund_conversion_amount" jdbcType="DECIMAL" property="refundConversionAmount" />
    <result column="refund_payment_amount" jdbcType="DECIMAL" property="refundPaymentAmount" />
    <result column="refund_amount_sum" jdbcType="VARCHAR" property="refundAmountSum" />
    <result column="refund_num" jdbcType="INTEGER" property="refundNum" />
    <result column="product_id_hot" jdbcType="INTEGER" property="productIdHot" />
    <result column="product_title_hot" jdbcType="VARCHAR" property="productTitleHot" />
    <result column="product_sales_amount" jdbcType="DECIMAL" property="productSalesAmount" />
    <result column="product_id_max" jdbcType="INTEGER" property="productIdMax" />
    <result column="product_title_max" jdbcType="VARCHAR" property="productTitleMax" />
    <result column="product_num" jdbcType="VARCHAR" property="productNum" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
    <result column="refund_subsidy_amount" jdbcType="DECIMAL" property="refundSubsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, statistics_day, user_num, user_pay_num, use_no_pay_num, business_num, trading_num, 
    integral_conversion_amount, payment_amount, payment_sum, settlement_business_num, 
    settlement_num, settlement_conversion_amount, settlement_payment_amount, settlement_amount_sum, 
    refund_conversion_amount, refund_payment_amount, refund_amount_sum, refund_num, product_id_hot, 
    product_title_hot, product_sales_amount, product_id_max, product_title_max, product_num, 
    created_at, updated_at, subsidy_amount, refund_subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_trading_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_trading_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookTradingData">
    insert into fook_trading_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="statisticsDay != null">
        statistics_day,
      </if>
      <if test="userNum != null">
        user_num,
      </if>
      <if test="userPayNum != null">
        user_pay_num,
      </if>
      <if test="useNoPayNum != null">
        use_no_pay_num,
      </if>
      <if test="businessNum != null">
        business_num,
      </if>
      <if test="tradingNum != null">
        trading_num,
      </if>
      <if test="integralConversionAmount != null">
        integral_conversion_amount,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="paymentSum != null">
        payment_sum,
      </if>
      <if test="settlementBusinessNum != null">
        settlement_business_num,
      </if>
      <if test="settlementNum != null">
        settlement_num,
      </if>
      <if test="settlementConversionAmount != null">
        settlement_conversion_amount,
      </if>
      <if test="settlementPaymentAmount != null">
        settlement_payment_amount,
      </if>
      <if test="settlementAmountSum != null">
        settlement_amount_sum,
      </if>
      <if test="refundConversionAmount != null">
        refund_conversion_amount,
      </if>
      <if test="refundPaymentAmount != null">
        refund_payment_amount,
      </if>
      <if test="refundAmountSum != null">
        refund_amount_sum,
      </if>
      <if test="refundNum != null">
        refund_num,
      </if>
      <if test="productIdHot != null">
        product_id_hot,
      </if>
      <if test="productTitleHot != null">
        product_title_hot,
      </if>
      <if test="productSalesAmount != null">
        product_sales_amount,
      </if>
      <if test="productIdMax != null">
        product_id_max,
      </if>
      <if test="productTitleMax != null">
        product_title_max,
      </if>
      <if test="productNum != null">
        product_num,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
      <if test="refundSubsidyAmount != null">
        refund_subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="statisticsDay != null">
        #{statisticsDay,jdbcType=DATE},
      </if>
      <if test="userNum != null">
        #{userNum,jdbcType=INTEGER},
      </if>
      <if test="userPayNum != null">
        #{userPayNum,jdbcType=INTEGER},
      </if>
      <if test="useNoPayNum != null">
        #{useNoPayNum,jdbcType=INTEGER},
      </if>
      <if test="businessNum != null">
        #{businessNum,jdbcType=INTEGER},
      </if>
      <if test="tradingNum != null">
        #{tradingNum,jdbcType=INTEGER},
      </if>
      <if test="integralConversionAmount != null">
        #{integralConversionAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentSum != null">
        #{paymentSum,jdbcType=DECIMAL},
      </if>
      <if test="settlementBusinessNum != null">
        #{settlementBusinessNum,jdbcType=INTEGER},
      </if>
      <if test="settlementNum != null">
        #{settlementNum,jdbcType=INTEGER},
      </if>
      <if test="settlementConversionAmount != null">
        #{settlementConversionAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementPaymentAmount != null">
        #{settlementPaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmountSum != null">
        #{settlementAmountSum,jdbcType=DECIMAL},
      </if>
      <if test="refundConversionAmount != null">
        #{refundConversionAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundPaymentAmount != null">
        #{refundPaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountSum != null">
        #{refundAmountSum,jdbcType=VARCHAR},
      </if>
      <if test="refundNum != null">
        #{refundNum,jdbcType=INTEGER},
      </if>
      <if test="productIdHot != null">
        #{productIdHot,jdbcType=INTEGER},
      </if>
      <if test="productTitleHot != null">
        #{productTitleHot,jdbcType=VARCHAR},
      </if>
      <if test="productSalesAmount != null">
        #{productSalesAmount,jdbcType=DECIMAL},
      </if>
      <if test="productIdMax != null">
        #{productIdMax,jdbcType=INTEGER},
      </if>
      <if test="productTitleMax != null">
        #{productTitleMax,jdbcType=VARCHAR},
      </if>
      <if test="productNum != null">
        #{productNum,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundSubsidyAmount != null">
        #{refundSubsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookTradingData">
    update fook_trading_data
    <set>
      <if test="statisticsDay != null">
        statistics_day = #{statisticsDay,jdbcType=DATE},
      </if>
      <if test="userNum != null">
        user_num = #{userNum,jdbcType=INTEGER},
      </if>
      <if test="userPayNum != null">
        user_pay_num = #{userPayNum,jdbcType=INTEGER},
      </if>
      <if test="useNoPayNum != null">
        use_no_pay_num = #{useNoPayNum,jdbcType=INTEGER},
      </if>
      <if test="businessNum != null">
        business_num = #{businessNum,jdbcType=INTEGER},
      </if>
      <if test="tradingNum != null">
        trading_num = #{tradingNum,jdbcType=INTEGER},
      </if>
      <if test="integralConversionAmount != null">
        integral_conversion_amount = #{integralConversionAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentSum != null">
        payment_sum = #{paymentSum,jdbcType=DECIMAL},
      </if>
      <if test="settlementBusinessNum != null">
        settlement_business_num = #{settlementBusinessNum,jdbcType=INTEGER},
      </if>
      <if test="settlementNum != null">
        settlement_num = #{settlementNum,jdbcType=INTEGER},
      </if>
      <if test="settlementConversionAmount != null">
        settlement_conversion_amount = #{settlementConversionAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementPaymentAmount != null">
        settlement_payment_amount = #{settlementPaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementAmountSum != null">
        settlement_amount_sum = #{settlementAmountSum,jdbcType=DECIMAL},
      </if>
      <if test="refundConversionAmount != null">
        refund_conversion_amount = #{refundConversionAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundPaymentAmount != null">
        refund_payment_amount = #{refundPaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountSum != null">
        refund_amount_sum = #{refundAmountSum,jdbcType=VARCHAR},
      </if>
      <if test="refundNum != null">
        refund_num = #{refundNum,jdbcType=INTEGER},
      </if>
      <if test="productIdHot != null">
        product_id_hot = #{productIdHot,jdbcType=INTEGER},
      </if>
      <if test="productTitleHot != null">
        product_title_hot = #{productTitleHot,jdbcType=VARCHAR},
      </if>
      <if test="productSalesAmount != null">
        product_sales_amount = #{productSalesAmount,jdbcType=DECIMAL},
      </if>
      <if test="productIdMax != null">
        product_id_max = #{productIdMax,jdbcType=INTEGER},
      </if>
      <if test="productTitleMax != null">
        product_title_max = #{productTitleMax,jdbcType=VARCHAR},
      </if>
      <if test="productNum != null">
        product_num = #{productNum,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundSubsidyAmount != null">
        refund_subsidy_amount = #{refundSubsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookTradingData">
    update fook_trading_data
    set statistics_day = #{statisticsDay,jdbcType=DATE},
      user_num = #{userNum,jdbcType=INTEGER},
      user_pay_num = #{userPayNum,jdbcType=INTEGER},
      use_no_pay_num = #{useNoPayNum,jdbcType=INTEGER},
      business_num = #{businessNum,jdbcType=INTEGER},
      trading_num = #{tradingNum,jdbcType=INTEGER},
      integral_conversion_amount = #{integralConversionAmount,jdbcType=DECIMAL},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      payment_sum = #{paymentSum,jdbcType=DECIMAL},
      settlement_business_num = #{settlementBusinessNum,jdbcType=INTEGER},
      settlement_num = #{settlementNum,jdbcType=INTEGER},
      settlement_conversion_amount = #{settlementConversionAmount,jdbcType=DECIMAL},
      settlement_payment_amount = #{settlementPaymentAmount,jdbcType=DECIMAL},
      settlement_amount_sum = #{settlementAmountSum,jdbcType=DECIMAL},
      refund_conversion_amount = #{refundConversionAmount,jdbcType=DECIMAL},
      refund_payment_amount = #{refundPaymentAmount,jdbcType=DECIMAL},
      refund_amount_sum = #{refundAmountSum,jdbcType=VARCHAR},
      refund_num = #{refundNum,jdbcType=INTEGER},
      product_id_hot = #{productIdHot,jdbcType=INTEGER},
      product_title_hot = #{productTitleHot,jdbcType=VARCHAR},
      product_sales_amount = #{productSalesAmount,jdbcType=DECIMAL},
      product_id_max = #{productIdMax,jdbcType=INTEGER},
      product_title_max = #{productTitleMax,jdbcType=VARCHAR},
      product_num = #{productNum,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      refund_subsidy_amount = #{refundSubsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>