<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresTypeTranslationsDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookStoresTypeTranslations">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="stores_type_id" jdbcType="INTEGER" property="storesTypeId" />
    <result column="t_name" jdbcType="VARCHAR" property="tName" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, stores_type_id, t_name, `locale`, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_stores_type_translations
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_stores_type_translations
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookStoresTypeTranslations">
    insert into fook_stores_type_translations
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="storesTypeId != null">
        stores_type_id,
      </if>
      <if test="tName != null">
        t_name,
      </if>
      <if test="locale != null">
        `locale`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="storesTypeId != null">
        #{storesTypeId,jdbcType=INTEGER},
      </if>
      <if test="tName != null">
        #{tName,jdbcType=VARCHAR},
      </if>
      <if test="locale != null">
        #{locale,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookStoresTypeTranslations">
    update fook_stores_type_translations
    <set>
      <if test="storesTypeId != null">
        stores_type_id = #{storesTypeId,jdbcType=INTEGER},
      </if>
      <if test="tName != null">
        t_name = #{tName,jdbcType=VARCHAR},
      </if>
      <if test="locale != null">
        `locale` = #{locale,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookStoresTypeTranslations">
    update fook_stores_type_translations
    set stores_type_id = #{storesTypeId,jdbcType=INTEGER},
      t_name = #{tName,jdbcType=VARCHAR},
      `locale` = #{locale,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>