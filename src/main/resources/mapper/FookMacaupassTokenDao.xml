<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMacaupassTokenDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMacaupassToken">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="expires_in" jdbcType="VARCHAR" property="expiresIn" />
    <result column="type" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, token, create_time, expires_in, `type`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_macaupass_token
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_macaupass_token
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMacaupassToken">
    insert into fook_macaupass_token
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="expiresIn != null">
        expires_in,
      </if>
      <if test="type != null">
        `type`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresIn != null">
        #{expiresIn,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMacaupassToken">
    update fook_macaupass_token
    <set>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expiresIn != null">
        expires_in = #{expiresIn,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMacaupassToken">
    update fook_macaupass_token
    set token = #{token,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      expires_in = #{expiresIn,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>