package com.mcoin.mall.util;

import static org.junit.jupiter.api.Assertions.*;

import com.mcoin.mall.constant.SettlementFileSuffixTypeEnum;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class TimeUtilsTest {

    private Date getDate(int year, int month, int day, int hour, int minute, int second) {
        LocalDateTime localDateTime = LocalDateTime.of(year, month, day, hour, minute, second);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    @Test
    public void testGetPreviousDayStartTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 11, 14, 0, 0, 0); // November 14, 2023
        Date actualDate = TimeUtils.getPreviousDayStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous day should be 00:00:00");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 10, 31, 0, 0, 0); // October 31, 2023
        actualDate = TimeUtils.getPreviousDayStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous day should be 00:00:00");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 10, 31, 0, 0, 0); // October 31, 2023
        actualDate = TimeUtils.getPreviousDayStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous day should be 00:00:00");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 12, 31, 0, 0, 0); // December 31, 2022
        actualDate = TimeUtils.getPreviousDayStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous day should be 00:00:00");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 12, 30, 0, 0, 0); // December 30, 2023
        actualDate = TimeUtils.getPreviousDayStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous day should be 00:00:00");
    }

    @Test
    public void testGetPreviousDayEndTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 11, 14, 23, 59, 59); // November 15, 2023
        Date actualDate = TimeUtils.getPreviousDayEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous day should be 23:59:59");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 10, 31, 23, 59, 59); // October 31, 2023
        actualDate = TimeUtils.getPreviousDayEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous day should be 23:59:59");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 30, 2023
        expectedDate = getDate(2023, 11, 29, 23, 59, 59); // November 29, 2023
        actualDate = TimeUtils.getPreviousDayEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous day should be 23:59:59");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 12, 31, 23, 59, 59); // December 31, 2022
        actualDate = TimeUtils.getPreviousDayEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous day should be 23:59:59");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 12, 30, 23, 59, 59); // December 30, 2023
        actualDate = TimeUtils.getPreviousDayEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous day should be 23:59:59");
    }

    @Test
    public void testGetCurrentMonthFirstHalfStartTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 11, 1, 0, 0, 0); // November 1, 2023
        Date actualDate = TimeUtils.getCurrentMonthFirstHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the first half of the current month should be 00:00:00");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 11, 1, 0, 0, 0); // November 1, 2023
        actualDate = TimeUtils.getCurrentMonthFirstHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the first half of the current month should be 00:00:00");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 30, 2023
        expectedDate = getDate(2023, 11, 1, 0, 0, 0); // November 1, 2023
        actualDate = TimeUtils.getCurrentMonthFirstHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the first half of the current month should be 00:00:00");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2023, 1, 1, 0, 0, 0); // January 1, 2023
        actualDate = TimeUtils.getCurrentMonthFirstHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the first half of the current month should be 00:00:00");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 12, 1, 0, 0, 0); // December 1, 2023
        actualDate = TimeUtils.getCurrentMonthFirstHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the first half of the current month should be 00:00:00");
    }

    @Test
    public void testGetPreviousMonthSecondHalfStartTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 10, 16, 0, 0, 0); // October 16, 2023
        Date actualDate = TimeUtils.getPreviousMonthSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous month should be 00:00:00");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 10, 16, 0, 0, 0); // October 16, 2023
        actualDate = TimeUtils.getPreviousMonthSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous month should be 00:00:00");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 30, 2023
        expectedDate = getDate(2023, 10, 16, 0, 0, 0); // October 16, 2023
        actualDate = TimeUtils.getPreviousMonthSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous month should be 00:00:00");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 12, 16, 0, 0, 0); // December 16, 2022
        actualDate = TimeUtils.getPreviousMonthSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous month should be 00:00:00");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 11, 16, 0, 0, 0); // November 16, 2023
        actualDate = TimeUtils.getPreviousMonthSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous month should be 00:00:00");
    }

    @Test
    public void testGetPreviousMonthEndTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 10, 31, 23, 59, 59); // October 31, 2023
        Date actualDate = TimeUtils.getPreviousMonthEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous month should be 23:59:59");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 10, 31, 23, 59, 59); // October 31, 2023
        actualDate = TimeUtils.getPreviousMonthEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous month should be 23:59:59");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 30, 2023
        expectedDate = getDate(2023, 10, 31, 23, 59, 59); // October 31, 2023
        actualDate = TimeUtils.getPreviousMonthEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous month should be 23:59:59");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 12, 31, 23, 59, 59); // December 31, 2022
        actualDate = TimeUtils.getPreviousMonthEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous month should be 23:59:59");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 11, 30, 23, 59, 59); // November 30, 2023
        actualDate = TimeUtils.getPreviousMonthEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous month should be 23:59:59");
    }

    @Test
    public void testGetPreviousMonthStartTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 10, 1, 0, 0, 0); // October 1, 2023
        Date actualDate = TimeUtils.getPreviousMonthStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous month should be 00:00:00");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 10, 1, 0, 0, 0); // October 1, 2023
        actualDate = TimeUtils.getPreviousMonthStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous month should be 00:00:00");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 30, 2023
        expectedDate = getDate(2023, 10, 1, 0, 0, 0); // October 1, 2023
        actualDate = TimeUtils.getPreviousMonthStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous month should be 00:00:00");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 12, 1, 0, 0, 0); // December 1, 2022
        actualDate = TimeUtils.getPreviousMonthStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous month should be 00:00:00");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 11, 1, 0, 0, 0); // November 1, 2023
        actualDate = TimeUtils.getPreviousMonthStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous month should be 00:00:00");
    }

    @Test
    public void testGetFirstHalfMonthDesc() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        String expectedDesc = "11" + SettlementFileSuffixTypeEnum.FIRST_HALF_MONTH.getDescription();
        String actualDesc = TimeUtils.getFirstHalfMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The first half month description should be '11'");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDesc = "11" + SettlementFileSuffixTypeEnum.FIRST_HALF_MONTH.getDescription();
        actualDesc = TimeUtils.getFirstHalfMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The first half month description should be '11'");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 31, 2023
        expectedDesc = "11" + SettlementFileSuffixTypeEnum.FIRST_HALF_MONTH.getDescription();
        actualDesc = TimeUtils.getFirstHalfMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The first half month description should be '11'");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDesc = "01" + SettlementFileSuffixTypeEnum.FIRST_HALF_MONTH.getDescription();
        actualDesc = TimeUtils.getFirstHalfMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The first half month description should be '01'");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDesc = "12" + SettlementFileSuffixTypeEnum.FIRST_HALF_MONTH.getDescription();
        actualDesc = TimeUtils.getFirstHalfMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The first half month description should be '12'");
    }

    @Test
    public void testGetPreviousMonthSecondHalfDesc() {
        // Test with a specific date in the middle of the month
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        String expected = "10" + SettlementFileSuffixTypeEnum.SECOND_HALF_MONTH.getDescription();
        String actual = TimeUtils.getPreviousMonthSecondHalfDesc(requestDate);
        assertEquals(expected, actual, "The second half month description should be '11'");

        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expected = "10" + SettlementFileSuffixTypeEnum.SECOND_HALF_MONTH.getDescription();
        actual = TimeUtils.getPreviousMonthSecondHalfDesc(requestDate);
        assertEquals(expected, actual, "The second half month description should be '10'");

        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 31, 2023
        expected = "10" + SettlementFileSuffixTypeEnum.SECOND_HALF_MONTH.getDescription();
        actual = TimeUtils.getPreviousMonthSecondHalfDesc(requestDate);
        assertEquals(expected, actual, "The second half month description should be '10'");

        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expected = "12" + SettlementFileSuffixTypeEnum.SECOND_HALF_MONTH.getDescription();
        actual = TimeUtils.getPreviousMonthSecondHalfDesc(requestDate);
        assertEquals(expected, actual, "The second half month description should be '12'");

        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expected = "11" + SettlementFileSuffixTypeEnum.SECOND_HALF_MONTH.getDescription();
        actual = TimeUtils.getPreviousMonthSecondHalfDesc(requestDate);
        assertEquals(expected, actual, "The second half month description should be '11'");
    }

    @Test
    public void testGetPreviousMonthDesc() {
        // Test with a specific date in the middle of the month
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        String expectedDesc = "10月結"; // October 1, 2023
        String actualDesc = TimeUtils.getPreviousMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The previous month description should be '1001'");
    
        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDesc = "10月結"; // October 1, 2023
        actualDesc = TimeUtils.getPreviousMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The previous month description should be '1001'");
    
        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 31, 2023
        expectedDesc = "10月結"; // October 1, 2023
        actualDesc = TimeUtils.getPreviousMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The previous month description should be '1001'");
    
        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDesc = "12月結"; // December 1, 2022
        actualDesc = TimeUtils.getPreviousMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The previous month description should be '1201'");
    
        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDesc = "11月結"; // November 1, 2022
        actualDesc = TimeUtils.getPreviousMonthDesc(requestDate);
        assertEquals(expectedDesc, actualDesc, "The previous month description should be '1101'");
    }

    @Test
    public void testGetCurrentYearStartTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 1, 1, 0, 0, 0); // January 1, 2023
        Date actualDate = TimeUtils.getCurrentYearStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the current year should be 00:00:00");
    
        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2023, 1, 1, 0, 0, 0); // January 1, 2023
        actualDate = TimeUtils.getCurrentYearStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the current year should be 00:00:00");
    
        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 1, 1, 0, 0, 0); // January 1, 2024
        actualDate = TimeUtils.getCurrentYearStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the current year should be 00:00:00");
    
        // Test with a date in the middle of the year
        requestDate = getDate(2023, 7, 15, 12, 0, 0); // July 15, 2023
        expectedDate = getDate(2023, 1, 1, 0, 0, 0); // January 1, 2024
        actualDate = TimeUtils.getCurrentYearStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the current year should be 00:00:00");
    }

    @Test
    public void testGetCurrentYearFirstHalfEndTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2023, 6, 30, 23, 59, 59); // July 31, 2023
        Date actualDate = TimeUtils.getCurrentYearFirstHalfEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the first half of the current year should be 23:59:59");
    
        // Test with a date at the beginning of the month
        requestDate = getDate(2023, 11, 1, 12, 0, 0); // November 1, 2023
        expectedDate = getDate(2023, 6, 30, 23, 59, 59); // July 31, 2023
        actualDate = TimeUtils.getCurrentYearFirstHalfEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the first half of the current year should be 23:59:59");
    
        // Test with a date at the end of the month
        requestDate = getDate(2023, 11, 30, 12, 0, 0); // November 31, 2023
        expectedDate = getDate(2023, 6, 30, 23, 59, 59); // July 31, 2023
        actualDate = TimeUtils.getCurrentYearFirstHalfEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the first half of the current year should be 23:59:59");
    
        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2023, 6, 30, 23, 59, 59); // July 31, 2023
        actualDate = TimeUtils.getCurrentYearFirstHalfEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the first half of the current year should be 23:59:59");
    
        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2023, 6, 30, 23, 59, 59); // July 31, 2023
        actualDate = TimeUtils.getCurrentYearFirstHalfEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the first half of the current year should be 23:59:59");
    }

    @Test
    public void testGetPreviousYearStartTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2022, 1, 1, 0, 0, 0); // November 1, 2022
        Date actualDate = TimeUtils.getPreviousYearStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous year should be 00:00:00");
    
        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 1, 1, 0, 0, 0); // January 1, 2022
        actualDate = TimeUtils.getPreviousYearStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous year should be 00:00:00");
    
        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2022, 1, 1, 0, 0, 0); // December 1, 2022
        actualDate = TimeUtils.getPreviousYearStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the previous year should be 00:00:00");
    }

    @Test
    public void testGetPreviousYearSecondHalfStartTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2022, 7, 1, 0, 0, 0); // July 1, 2022
        Date actualDate = TimeUtils.getPreviousYearSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous year should be 00:00:00");
    
        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 7, 1, 0, 0, 0); // July 1, 2022
        actualDate = TimeUtils.getPreviousYearSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous year should be 00:00:00");
    
        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2022, 7, 1, 0, 0, 0); // July 1, 2022
        actualDate = TimeUtils.getPreviousYearSecondHalfStartTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The start time of the second half of the previous year should be 00:00:00");
    }

    @Test
    public void testGetPreviousYearEndTime() {
        // Test with a specific date
        Date requestDate = getDate(2023, 11, 15, 12, 0, 0); // November 15, 2023
        Date expectedDate = getDate(2022, 12, 31, 23, 59, 59); // December 31, 2022
        Date actualDate = TimeUtils.getPreviousYearEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous year should be 23:59:59");
    
        // Test with a date at the beginning of the year
        requestDate = getDate(2023, 1, 1, 12, 0, 0); // January 1, 2023
        expectedDate = getDate(2022, 12, 31, 23, 59, 59); // December 31, 2022
        actualDate = TimeUtils.getPreviousYearEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous year should be 23:59:59");
    
        // Test with a date at the end of the year
        requestDate = getDate(2023, 12, 31, 12, 0, 0); // December 31, 2023
        expectedDate = getDate(2022, 12, 31, 23, 59, 59); // December 31, 2022
        actualDate = TimeUtils.getPreviousYearEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous year should be 23:59:59");
    
        // Test with a date in the middle of the year
        requestDate = getDate(2023, 6, 15, 12, 0, 0); // June 15, 2023
        expectedDate = getDate(2022, 12, 31, 23, 59, 59); // December 31, 2022
        actualDate = TimeUtils.getPreviousYearEndTime(requestDate);
        assertEquals(
                expectedDate.getTime(),
                actualDate.getTime(),
                "The end time of the previous year should be 23:59:59");
    }
}
