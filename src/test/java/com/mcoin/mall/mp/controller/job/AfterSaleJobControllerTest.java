package com.mcoin.mall.mp.controller.job;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.job.AfterSaleAutoCloseRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Transactional
class AfterSaleJobControllerTest extends BaseUnitTest {

    @Autowired
    private AfterSaleJobController afterSaleJobController;

    @Test
    void autoClose_ShouldTriggerAutoClose() {
        // Arrange
        AfterSaleAutoCloseRequest request = new AfterSaleAutoCloseRequest();
        request.setCurrentTime(new Date());

        // Act
        Response<String> response = afterSaleJobController.autoClose(request);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertEquals("success", response.getData());
    }
} 