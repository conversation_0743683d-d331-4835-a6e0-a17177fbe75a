<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookDayItemDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookDayItem">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="discount_id" jdbcType="INTEGER" property="discountId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="business_name_en" jdbcType="VARCHAR" property="businessNameEn" />
    <result column="discount_name" jdbcType="VARCHAR" property="discountName" />
    <result column="discount_name_en" jdbcType="VARCHAR" property="discountNameEn" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="logo_en" jdbcType="VARCHAR" property="logoEn" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="gain_id" jdbcType="INTEGER" property="gainId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, discount_id, business_name, business_name_en, discount_name, discount_name_en, 
    logo, logo_en, start_time, end_time, url, `status`, sort, created_at, updated_at,
    gain_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_day_item
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_day_item
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookDayItem">
    insert into fook_day_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="discountId != null">
        discount_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="businessNameEn != null">
        business_name_en,
      </if>
      <if test="discountName != null">
        discount_name,
      </if>
      <if test="discountNameEn != null">
        discount_name_en,
      </if>
      <if test="logo != null">
        logo,
      </if>
      <if test="logoEn != null">
        logo_en,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="gainId != null">
        gain_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="discountId != null">
        #{discountId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="businessNameEn != null">
        #{businessNameEn,jdbcType=VARCHAR},
      </if>
      <if test="discountName != null">
        #{discountName,jdbcType=VARCHAR},
      </if>
      <if test="discountNameEn != null">
        #{discountNameEn,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        #{logo,jdbcType=VARCHAR},
      </if>
      <if test="logoEn != null">
        #{logoEn,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="gainId != null">
        #{gainId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookDayItem">
    update fook_day_item
    <set>
      <if test="discountId != null">
        discount_id = #{discountId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="businessNameEn != null">
        business_name_en = #{businessNameEn,jdbcType=VARCHAR},
      </if>
      <if test="discountName != null">
        discount_name = #{discountName,jdbcType=VARCHAR},
      </if>
      <if test="discountNameEn != null">
        discount_name_en = #{discountNameEn,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        logo = #{logo,jdbcType=VARCHAR},
      </if>
      <if test="logoEn != null">
        logo_en = #{logoEn,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="gainId != null">
        gain_id = #{gainId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookDayItem">
    update fook_day_item
    set discount_id = #{discountId,jdbcType=INTEGER},
      business_name = #{businessName,jdbcType=VARCHAR},
      business_name_en = #{businessNameEn,jdbcType=VARCHAR},
      discount_name = #{discountName,jdbcType=VARCHAR},
      discount_name_en = #{discountNameEn,jdbcType=VARCHAR},
      logo = #{logo,jdbcType=VARCHAR},
      logo_en = #{logoEn,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      url = #{url,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      sort = #{sort,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      gain_id = #{gainId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>