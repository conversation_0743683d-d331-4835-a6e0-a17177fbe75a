package com.mcoin.mall.security;


import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class TokenManager {

    @Value("${security.jwt.secret}")
    private String jwtSecret;

    public DecodedJWT validateJwtToken(String token) {
        DecodedJWT decodedJWT;
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .build();
            decodedJWT = verifier.verify(token);
            return decodedJWT;
        } catch (JWTVerificationException exception){
            throw new BusinessException(Response.Code.BAD_REQUEST, "签名不正确");
        }
    }

    public String createJwtToken(Integer userId, String username) {
        try {
            long curTime = Instant.now().getEpochSecond();
            JwtPayload jwtPayload = new JwtPayload();
            jwtPayload.setIat(curTime);
            long expireTime = Long.parseLong(ConfigUtils.getProperty("security.jwt.expire", "7200"));
            jwtPayload.setExp(curTime + expireTime);
            jwtPayload.setSub(String.valueOf(userId));

            JwtPayload.Data data = new JwtPayload.Data();
            data.setId(userId);
            data.setUsername(username);

            jwtPayload.setData(data);

            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            return JWT.create()
                    .withPayload(JSON.toJSONString(jwtPayload))
                    .sign(algorithm);
        } catch (JWTVerificationException exception){
            throw new BusinessException(Response.Code.BAD_REQUEST, "签名不正确");
        }
    }


    public String createOrderJwtToken(String uuid, long expireTime) {
        try {
            long curTime = Instant.now().getEpochSecond();
            JwtPayload jwtPayload = new JwtPayload();
            jwtPayload.setIat(curTime);
            jwtPayload.setExp(curTime + expireTime);
            jwtPayload.setSub(uuid);

            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            return JWT.create()
                    .withPayload(JSON.toJSONString(jwtPayload))
                    .sign(algorithm);
        } catch (JWTVerificationException exception){
            throw new BusinessException(Response.Code.BAD_REQUEST, "签名不正确");
        }
    }

    public DecodedJWT getJwtToken(String token) {
        DecodedJWT decodedJWT;
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .build();
            decodedJWT = verifier.verify(token);
            return decodedJWT;
        } catch (JWTVerificationException exception){
            return null;
        }
    }
}
