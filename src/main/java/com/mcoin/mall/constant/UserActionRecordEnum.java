package com.mcoin.mall.constant;


import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户确认行为记录类型
 *
 * @author: laijinjie
 * @date: 2023/11/21
 */
@Getter
public enum UserActionRecordEnum {

    /**
     * 用户确认行为记录类型，type是类型，后续可以迭代新增其他类型
     */
    LIQUOR(1, BusinessProductClassEnum.LIQUOR.getType()),
    ;

    /**
     * 类型（类型需要唯一，不可修改）
     */
    private final int type;

    /**
     * 编码（编码需要唯一，不可修改）
     */
    private final String code;

    UserActionRecordEnum(int type, String code) {
        this.type = type;
        this.code = code;

    }

    public static UserActionRecordEnum getEnum(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (UserActionRecordEnum value : UserActionRecordEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
