<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMomecoinsTaskDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMomecoinsTask">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task" jdbcType="VARCHAR" property="task" />
    <result column="explain" jdbcType="VARCHAR" property="explain" />
    <result column="calcu_pattern" jdbcType="INTEGER" property="calcuPattern" />
    <result column="calcu_value" jdbcType="DECIMAL" property="calcuValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task, `explain`, calcu_pattern, calcu_value, create_time, `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_momecoins_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_momecoins_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMomecoinsTask">
    insert into fook_momecoins_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="task != null">
        task,
      </if>
      <if test="explain != null">
        `explain`,
      </if>
      <if test="calcuPattern != null">
        calcu_pattern,
      </if>
      <if test="calcuValue != null">
        calcu_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="task != null">
        #{task,jdbcType=VARCHAR},
      </if>
      <if test="explain != null">
        #{explain,jdbcType=VARCHAR},
      </if>
      <if test="calcuPattern != null">
        #{calcuPattern,jdbcType=INTEGER},
      </if>
      <if test="calcuValue != null">
        #{calcuValue,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMomecoinsTask">
    update fook_momecoins_task
    <set>
      <if test="task != null">
        task = #{task,jdbcType=VARCHAR},
      </if>
      <if test="explain != null">
        `explain` = #{explain,jdbcType=VARCHAR},
      </if>
      <if test="calcuPattern != null">
        calcu_pattern = #{calcuPattern,jdbcType=INTEGER},
      </if>
      <if test="calcuValue != null">
        calcu_value = #{calcuValue,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMomecoinsTask">
    update fook_momecoins_task
    set task = #{task,jdbcType=VARCHAR},
      `explain` = #{explain,jdbcType=VARCHAR},
      calcu_pattern = #{calcuPattern,jdbcType=INTEGER},
      calcu_value = #{calcuValue,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      `enable` = #{enable,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>