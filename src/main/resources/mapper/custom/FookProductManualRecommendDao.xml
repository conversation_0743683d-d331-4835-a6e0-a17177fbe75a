<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookProductManualRecommendDao">
  <select id="selectRecommendProductBos" resultType="com.mcoin.mall.bo.RecommendProductBo">
      select mr.product_id as "productId",
             p.type
      from fook_product_manual_recommend mr
       left join fook_business_product p on mr.product_id = p.id
       left join fook_business b on p.businessid = b.id
       left join fook_business_store_product sp on mr.product_id = sp.productid
       left join fook_stores s on sp.storeid = s.id
       left join fook_stores_type_class sc on  sc.stores_id = s.id
       left join fook_stores_type st on  sc.stores_type_id = st.id
      where  p.shelf_status = 1
        and p.stock > 0
        and p.snap_up = 0
        and b.system_type = 1
        and s.`enable` = 1
        and st.sort  <![CDATA[<>]]> 0
        and st.`enable` = 1
      <![CDATA[
            and p.buy_start_time <= #{now,jdbcType=TIMESTAMP}
      ]]>
      and p.buy_end_time > #{now,jdbcType=TIMESTAMP}
      group by mr.product_id,p.type,mr.product_sort
      order by mr.product_sort
  </select>
</mapper>