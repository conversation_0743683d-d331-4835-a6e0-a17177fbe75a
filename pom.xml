<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
		 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.14</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>mcoin-mall</groupId>
    <artifactId>mcoin-mall</artifactId>
    <version>2.0</version>

    <properties>
        <spring-boot.version>2.6.14</spring-boot.version>
    	<maven-jar-plugin.version>3.0.0</maven-jar-plugin.version>
        <java.version>1.8</java.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <redisson-spring-boot.version>3.15.0</redisson-spring-boot.version>
        <mybatis-spring-boot.version>2.1.4</mybatis-spring-boot.version>
        <!-- 指定版本升级，不依赖 spring-boot-starter-parent  start.....-->
        <spring-amqp.version>2.4.17</spring-amqp.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <!-- 指定版本升级，不依赖 spring-boot-starter-parent  end.....-->
        <mysql.version>5.1.49</mysql.version>
        <joda-time.version>2.7</joda-time.version>
        <dom4j.version>1.6.1</dom4j.version>
        <commons-codec>1.5</commons-codec>
        <jsch.version>0.1.55</jsch.version>
        <xmlsec.version>1.4.0</xmlsec.version>
        <commons-io.version>2.9.0</commons-io.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <fastjson.version>1.2.83</fastjson.version>
        <commons-csv.version>1.8</commons-csv.version>
        <mockito.version>3.8.0</mockito.version>
        <HikariCP.version>4.0.3</HikariCP.version>
        <knife4j.version>3.0.3</knife4j.version>
        <groovy.version>3.0.8</groovy.version>
        <junit-jupiter.version>5.8.1</junit-jupiter.version>
        <mpay-business-service-governance.version>2.0.0</mpay-business-service-governance.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok.version>1.18.30</lombok.version>
        <hutool.version>5.8.22</hutool.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <jxls.version>2.12.0</jxls.version>
        <sentinel.version>1.8.6</sentinel.version>
<!--        <netty.version>4.1.94.Final</netty.version>-->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-bom</artifactId>
                <version>2.0-groovy-3.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Add embedded-redis for testing -->
        <dependency>
            <groupId>com.github.codemonstur</groupId>
            <artifactId>embedded-redis</artifactId>
            <version>1.4.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.fridujo</groupId>
            <artifactId>rabbitmq-mock</artifactId>
            <version>1.1.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <!-- Feign声明式服务调用+客户端负载均衡(Ribbon) -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>archaius-core</artifactId>
                    <groupId>com.netflix.archaius</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
<!--                <exclusion>-->
<!--                    <artifactId>feign-hystrix</artifactId>-->
<!--                    <groupId>io.github.openfeign</groupId>-->
<!--                </exclusion>-->
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>simpleclient</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson-spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>byte-buddy</artifactId>
                    <groupId>net.bytebuddy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>HikariCP</artifactId>
                    <groupId>com.zaxxer</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>${HikariCP.version}</version>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>${commons-csv.version}</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>${lombok.version}</version>
        </dependency>


        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>byte-buddy</artifactId>
                    <groupId>net.bytebuddy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>byte-buddy</artifactId>
                    <groupId>net.bytebuddy</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.itextpdf/html2pdf -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>5.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.1.214</version>
            <scope>test</scope>
        </dependency>
        
        <dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>6.9.8</version>
			<scope>test</scope>
		</dependency>

        <!-- db -->
        <dependency>
            <groupId>ch.vorburger.mariaDB4j</groupId>
            <artifactId>mariaDB4j</artifactId>
            <version>2.5.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <version>3.4.1</version>
            <scope>test</scope>
        </dependency>
        <!-- spock依赖 -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>2.0-groovy-3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <version>2.0-groovy-3.0</version>
            <scope>test</scope>
        </dependency>
        <!-- Optional dependencies for using Spock -->
        <dependency> <!-- use a specific Groovy version rather than the one specified by spock-core -->
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>${groovy.version}</version>
        </dependency>

        <dependency> <!-- enables mocking of classes (in addition to interfaces) -->
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.11.0</version>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- enables mocking of classes without default constructor (together with ByteBuddy or CGLIB) -->
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
            <version>3.2</version>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- only required if Hamcrest matchers are used -->
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>2.2</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.4.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.mpay</groupId>
            <artifactId>mpay-business-service-governance-sentinel</artifactId>
            <version>${mpay-business-service-governance.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.csp</groupId>-->
<!--            <artifactId>sentinel-logging-slf4j</artifactId>-->
<!--            <version>${sentinel.version}</version>-->
<!--        </dependency>-->


        <dependency>
            <groupId>com.mpay</groupId>
            <artifactId>mpay-business-service-governance-trace</artifactId>
            <version>${mpay-business-service-governance.version}</version>
        </dependency>

        <dependency>
            <groupId>com.mpay</groupId>
            <artifactId>mpay-business-service-governance-gray</artifactId>
            <version>${mpay-business-service-governance.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.macaupay.plugins</groupId>-->
<!--            <artifactId>nacos-aliyun-kms-encryption-plugin</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->

        <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

        <!--阿里云SLS依赖-->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log-logback-appender</artifactId>
            <version>0.1.18</version>
        </dependency>

        <dependency>
            <groupId>org.jxls</groupId>
            <artifactId>jxls</artifactId>
            <version>${jxls.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>${commons-beanutils.version}</version>
            <exclusions>
<!--                <exclusion>-->
<!--                    <artifactId>commons-collections</artifactId>-->
<!--                    <groupId>commons-collections</groupId>-->
<!--                </exclusion>-->
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jxls</groupId>
            <artifactId>jxls-poi</artifactId>
            <version>${jxls.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.aliyun.oss/aliyun-sdk-oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.17.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.34.0.ALL</version>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.houbb/opencc4j -->
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>opencc4j</artifactId>
            <version>1.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>5.1.1</version>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>2.11.5</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${spring-boot.version}</version>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <!-- Package as an executable jar -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <!-- 解决Maven打包时修改了excel或者字体模板文件导致文件损坏 -->
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>id.package</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <copy todir="${basedir}/target" overwrite="true">
                                    <fileset dir="${basedir}">
                                        <include name="*.sh"/>
                                    </fileset>
                                </copy>
                                <chmod file="target/*.sh" perm="755"/>
                                <replace file="target/stop-${project.artifactId}.sh" token="8080" value="1${server.port}"></replace>
                                <replace file="target/start-${project.artifactId}.sh" token="-Xms" value="-Xms${jvm.Xms}"/>
                                <replace file="target/start-${project.artifactId}.sh" token="-Xmx" value="-Xmx${jvm.Xmx}"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Mandatory plugins for using Spock -->
            <plugin>
                <!-- The gmavenplus plugin is used to compile Groovy code. To learn more about this plugin,
                visit https://github.com/groovy/GMavenPlus/wiki -->
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.12.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>addSources</goal>
                            <goal>addTestSources</goal>
                            <goal>generateStubs</goal>
                            <goal>compile</goal>
                            <goal>generateTestStubs</goal>
                            <goal>compileTests</goal>
                            <goal>removeStubs</goal>
                            <goal>removeTestStubs</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <systemPropertyVariables>
                        <!-- Fixed: Spring cloud contract stubs not found in my local repo -->
                        <maven.repo.local>${settings.localRepository}</maven.repo.local>
                    </systemPropertyVariables>
                    <useModulePath>false</useModulePath> <!-- https://issues.apache.org/jira/browse/SUREFIRE-1809 -->
                    <useFile>false</useFile>
                    <includes>
                        <include>**/*Test*</include>
                        <include>**/*Spec</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.2</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>target/jacoco.exec</dataFile>
                            <outputDirectory>target/jacoco-ut</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>
                            com/mcoin/mall/bean/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/bo/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/client/model/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/model/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/mq/model/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/constant/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/vo/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/service/chennel/vo/**
                        </exclude>
                        <exclude>
                            com/mcoin/mall/config/rabbitmq/**
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>local</spring.profiles.active>
                <spring.cloud.nacos.discovery.username>nacos</spring.cloud.nacos.discovery.username>
                <spring.cloud.nacos.discovery.password>nacos</spring.cloud.nacos.discovery.password>
                <spring.cloud.nacos.discovery.server-addr>172.16.0.90:8858</spring.cloud.nacos.discovery.server-addr>
                <knife4j.production>false</knife4j.production>
                <server.port>8097</server.port>
                <spring.cloud.sentinel.transport.dashboard>127.0.0.1:8080</spring.cloud.sentinel.transport.dashboard>
                <jvm.Xms>2g</jvm.Xms>
                <jvm.Xmx>2g</jvm.Xmx>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tSD7cp1NgfCA1GF3M7u</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-dev</sls.project>
                <sls.logStore>mcoinmall-new-local</sls.logStore>
                <logs.dir>${user.home}/logs</logs.dir>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
                <spring.cloud.nacos.discovery.username>nacos</spring.cloud.nacos.discovery.username>
                <spring.cloud.nacos.discovery.password>nacos</spring.cloud.nacos.discovery.password>
                <spring.cloud.nacos.discovery.server-addr>gateway-kms-nacos-2-2-3-headless.b-middleware:8848</spring.cloud.nacos.discovery.server-addr>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
                <knife4j.production>false</knife4j.production>
                <server.port>8097</server.port>
                <jvm.Xms>2g</jvm.Xms>
                <jvm.Xmx>2g</jvm.Xmx>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tSD7cp1NgfCA1GF3M7u</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-dev</sls.project>
                <sls.logStore>mcoinmall-new-dev</sls.logStore>
                <logs.dir>/home/<USER>/logs</logs.dir>
            </properties>
        </profile>
        <profile>
            <id>sit</id>
            <properties>
                <spring.profiles.active>sit</spring.profiles.active>
                <spring.cloud.nacos.discovery.username>nacos</spring.cloud.nacos.discovery.username>
                <spring.cloud.nacos.discovery.password>nacos</spring.cloud.nacos.discovery.password>
                <spring.cloud.nacos.discovery.server-addr>gateway-kms-nacos-2-2-3-headless.b-middleware:8848</spring.cloud.nacos.discovery.server-addr>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
                <knife4j.production>false</knife4j.production>
                <server.port>8097</server.port>
                <jvm.Xms>3g</jvm.Xms>
                <jvm.Xmx>3g</jvm.Xmx>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tDtdKZkbH7Xsbgd8M4j</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-sit</sls.project>
                <sls.logStore>mcoinmall-new-sit</sls.logStore>
                <logs.dir>/home/<USER>/logs</logs.dir>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <spring.profiles.active>uat</spring.profiles.active>
                <spring.cloud.nacos.discovery.username>nacos</spring.cloud.nacos.discovery.username>
                <spring.cloud.nacos.discovery.password>Nacos@123</spring.cloud.nacos.discovery.password>
                <spring.cloud.nacos.discovery.server-addr>gateway-kms-nacos-2-2-3-headless.gateway-middleware:8848</spring.cloud.nacos.discovery.server-addr>
                <knife4j.production>false</knife4j.production>
                <server.port>8097</server.port>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
                <jvm.Xms>4g</jvm.Xms>
                <jvm.Xmx>4g</jvm.Xmx>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tSD7cp1NgfCA1GF3M7u</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-uat</sls.project>
                <sls.logStore>mcoinmall-new-uat</sls.logStore>
                <logs.dir>/home/<USER>/logs</logs.dir>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <spring.profiles.active>prd</spring.profiles.active>
                <spring.cloud.nacos.discovery.username>nacos</spring.cloud.nacos.discovery.username>
                <spring.cloud.nacos.discovery.password>Nacos@456</spring.cloud.nacos.discovery.password>
                <spring.cloud.nacos.discovery.server-addr>gateway-kms-nacos-2-2-3-headless.gateway-middleware:8848</spring.cloud.nacos.discovery.server-addr>
                <knife4j.production>true</knife4j.production>
                <server.port>8097</server.port>
                <spring.cloud.sentinel.transport.dashboard>sentinel-dashboard.gateway:8080</spring.cloud.sentinel.transport.dashboard>
                <jvm.Xms>8g</jvm.Xms>
                <jvm.Xmx>8g</jvm.Xmx>
                <sls.endpoint>cn-hongkong-intranet.log.aliyuncs.com</sls.endpoint>
                <sls.accessKeyId>LTAI5tSD7cp1NgfCA1GF3M7u</sls.accessKeyId>
                <sls.accessKeySecret>******************************</sls.accessKeySecret>
                <sls.project>mpay-prd</sls.project>
                <sls.logStore>mcoinmall-new-prd</sls.logStore>
                <logs.dir>/home/<USER>/logs</logs.dir>
            </properties>
        </profile>
    </profiles>

</project>
