package com.mcoin.mall.controller.business;

import com.mcoin.mall.service.business.ClauseService;
import com.mcoin.mall.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
public class ClauseController {

    @Autowired
    private ClauseService clauseService;

    /**
     * 獲取條款内容
     *
     * @return
     */
    @GetMapping(value = "/api/auth/clause", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<GetClauseResponse> getClause() throws Exception{
        GetClauseResponse response = clauseService.getClause();
        return Responses.ok(response);
    }

    /**
     * 用戶同意條款
     *
     * @return
     */
    @PostMapping(value = "/api/auth/setClause", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<SetClauseResponse> setClause(@Valid @RequestBody SetClauseRequest request) throws Exception{
        SetClauseResponse response = clauseService.saveClause(request);
        return Responses.ok(response);
    }
}
