<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookShowSnapupSessionDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookShowSnapupSession">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="cn_title" jdbcType="VARCHAR" property="cnTitle" />
    <result column="en_title" jdbcType="VARCHAR" property="enTitle" />
    <result column="session_type" jdbcType="BIT" property="sessionType" />
    <result column="show_id" jdbcType="INTEGER" property="showId" />
    <result column="show_snapup_id" jdbcType="INTEGER" property="showSnapupId" />
    <result column="valid" jdbcType="BIT" property="valid" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    id, start_time, end_time, cn_title, en_title, session_type,
    show_id, show_snapup_id, `valid`, created_at, updated_at, created_by, updated_by
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_show_snapup_session
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_show_snapup_session
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookShowSnapupSession">
    insert into fook_show_snapup_session
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="cnTitle != null">
        cn_title,
      </if>
      <if test="enTitle != null">
        en_title,
      </if>
      <if test="sessionType != null">
        session_type,
      </if>
      <if test="showId != null">
        show_id,
      </if>
      <if test="showSnapupId != null">
        show_snapup_id,
      </if>
      <if test="valid != null">
        `valid`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cnTitle != null">
        #{cnTitle,jdbcType=VARCHAR},
      </if>
      <if test="enTitle != null">
        #{enTitle,jdbcType=VARCHAR},
      </if>
      <if test="sessionType != null">
        #{sessionType,jdbcType=BIT},
      </if>
      <if test="showId != null">
        #{showId,jdbcType=INTEGER},
      </if>
      <if test="showSnapupId != null">
        #{showSnapupId,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookShowSnapupSession">
    update fook_show_snapup_session
    <set>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cnTitle != null">
        cn_title = #{cnTitle,jdbcType=VARCHAR},
      </if>
      <if test="enTitle != null">
        en_title = #{enTitle,jdbcType=VARCHAR},
      </if>
      <if test="sessionType != null">
        session_type = #{sessionType,jdbcType=BIT},
      </if>
      <if test="showId != null">
        show_id = #{showId,jdbcType=INTEGER},
      </if>
      <if test="showSnapupId != null">
        show_snapup_id = #{showSnapupId,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        `valid` = #{valid,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>