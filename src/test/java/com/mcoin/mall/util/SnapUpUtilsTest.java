package com.mcoin.mall.util;

import com.mcoin.mall.bo.SnapUpItemBo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

public class SnapUpUtilsTest {
    @Mock
    private SnapUpItemBo item;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetPoint_SystemType3_ReturnsMaxPoints() {
        // Arrange
        when(item.getSystemType()).thenReturn(3);
        when(item.getMaximumPoints()).thenReturn(100);
        when(item.getPointRatio()).thenReturn(10);
        when(item.getPrice()).thenReturn(BigDecimal.valueOf(10));
        when(item.getOnlyPoint()).thenReturn(1);

        // Act
        int result = SnapUpUtils.getPoint(item);

        // Assert
        assertEquals(100, result);
    }

    @Test
    public void testGetPoint_OnlyPoint1_ReturnsCalculatedPoints() {
        // Arrange
        when(item.getPointRatio()).thenReturn(10);
        when(item.getPrice()).thenReturn(BigDecimal.valueOf(10));
        when(item.getOnlyPoint()).thenReturn(1);

        // Act
        int result = SnapUpUtils.getPoint(item);

        // Assert
        assertEquals(100, result);
    }

    @Test
    public void testGetPoint_OnlyPoint2_ReturnsZero() {
        // Arrange
        when(item.getOnlyPoint()).thenReturn(2);

        // Act
        int result = SnapUpUtils.getPoint(item);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetPoint_Default_ReturnsMinPoint() {
        // Arrange
        when(item.getMinPoint()).thenReturn(5);
        when(item.getOnlyPoint()).thenReturn(0);

        // Act
        int result = SnapUpUtils.getPoint(item);

        // Assert
        assertEquals(5, result);
    }

    @Test
    public void testGetPoint_Default_MiniPointZero_ReturnsPointRatioDivide2() {
        // Arrange
        when(item.getPointRatio()).thenReturn(300);
        when(item.getMinPoint()).thenReturn(0);
        when(item.getOnlyPoint()).thenReturn(0);

        // Act
        int result = SnapUpUtils.getPoint(item);

        // Assert
        assertEquals(150, result);
    }

    @Test
    public void testGetPreferential_OnlyPoint1_ReturnsZero() {
        // Arrange
        when(item.getOnlyPoint()).thenReturn(1);
        when(item.getPrice()).thenReturn(BigDecimal.valueOf(10));

        // Act
        BigDecimal result = SnapUpUtils.getPreferential(item);

        // Assert
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    public void testGetPreferential_OnlyPoint2_ReturnsPrice() {
        // Arrange
        when(item.getOnlyPoint()).thenReturn(2);
        when(item.getPrice()).thenReturn(BigDecimal.valueOf(10));

        // Act
        BigDecimal result = SnapUpUtils.getPreferential(item);

        // Assert
        assertEquals(BigDecimal.valueOf(10), result);
    }

    @Test
    public void testGetPreferential_Default_ReturnsCalculatedPreferential() {
        // Arrange
        when(item.getPrice()).thenReturn(BigDecimal.valueOf(100));
        when(item.getMinPoint()).thenReturn(10);
        when(item.getPointRatio()).thenReturn(10);
        when(item.getOnlyPoint()).thenReturn(0);

        // Act
        BigDecimal result = SnapUpUtils.getPreferential(item);

        // Assert
        assertEquals(new BigDecimal("99.00"), result.setScale(2, RoundingMode.HALF_UP));
    }

    @Test
    public void testGetOccupy_NoSalesOrStock_ReturnsZero() {
        // Arrange
        when(item.getActualSales()).thenReturn(0);
        when(item.getStock()).thenReturn(0);

        // Act
        int result = SnapUpUtils.getOccupy(item);
        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetOccupy_ZeroStock_ReturnsZero() {
        // Arrange
        when(item.getActualSales()).thenReturn(10);
        when(item.getStock()).thenReturn(0);

        // Act
        int result = SnapUpUtils.getOccupy(item);

        // Assert
        assertEquals(0, result);
    }

    @Test
    public void testGetOccupy_WithSalesAndStock_ReturnsCalculatedOccupancy() {
        // Arrange
        when(item.getActualSales()).thenReturn(10);
        when(item.getStock()).thenReturn(90);

        // Act
        int result = SnapUpUtils.getOccupy(item);

        // Assert
        assertEquals(10, result);
    }

}
