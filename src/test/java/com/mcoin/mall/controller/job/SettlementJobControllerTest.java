package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.*;
import com.mcoin.mall.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.amqp.core.Message;

import javax.annotation.Resource;

import java.util.Date;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * 集成测试 SettlementJobController
 * 使用真实服务和数据库，测试结算任务相关功能
 */
class SettlementJobControllerTest extends BaseUnitTest {

    @Resource
    private SettlementJobController settlementJobController;

    @BeforeEach
    public void setUp() {
        // Mock locale
        Mockito.when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        Mockito.when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
        doNothing().when(settlementInternalTemplate).convertAndSend(any(Message.class));

    }
    @Test
    void testHalfMonth() {
        // 创建请求对象
        SettlementJobRequest request = new SettlementJobRequest();
        // 设置必要的请求参数
        request.setCurrentDate(new Date());
        // 直接调用控制器方法
        Response<String> response = settlementJobController.halfMonth(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testReportUpload() {
        // 创建请求对象
        SettlementReportUploadJobRequest request = new SettlementReportUploadJobRequest();
        // 设置必要的请求参数
        
        // 直接调用控制器方法
        Response<String> response = settlementJobController.reportUpload(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testReportSend() {
        // 创建请求对象
        SettlementReportSendJobRequest request = new SettlementReportSendJobRequest();
        // 设置必要的请求参数
        
        // 直接调用控制器方法
        Response<String> response = settlementJobController.reportSend(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testInternal() {
        // 创建请求对象
        SettlementInternalJobRequest request = new SettlementInternalJobRequest();
        // 设置必要的请求参数
        request.setCurrentDate(new Date());

        // 直接调用控制器方法
        Response<String> response = settlementJobController.internal(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testOptional() {
        // 创建请求对象
        SettlementOptionalJobRequest request = new SettlementOptionalJobRequest();
        // 设置必要的请求参数
        request.setCurrentDate(new Date());
        // 直接调用控制器方法
        Response<String> response = settlementJobController.optional(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testInternalReportOrderCode() {
        // 创建请求对象
        SettlementInternalReportOrderCodeJobRequest request = new SettlementInternalReportOrderCodeJobRequest();
        // 设置必要的请求参数
        
        // 直接调用控制器方法
        Response<String> response = settlementJobController.internalReportOrderCode(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testOptionalReportUpload() {
        // 创建请求对象
        SettlementOptionalReportUploadJobRequest request = new SettlementOptionalReportUploadJobRequest();
        // 设置必要的请求参数
        
        // 直接调用控制器方法
        Response<String> response = settlementJobController.optionalReportUpload(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testOptionalReportSend() {
        // 创建请求对象
        SettlementOptionalReportSendJobRequest request = new SettlementOptionalReportSendJobRequest();
        // 设置必要的请求参数
        
        // 直接调用控制器方法
        Response<String> response = settlementJobController.optionalReportSend(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testSumMcoinOrderInMpay() {
        // 创建请求对象
        SettlementJobRequest request = new SettlementJobRequest();
        // 设置必要的请求参数
        request.setCurrentDate(new Date());
        // 直接调用控制器方法
        Response<String> response = settlementJobController.sumMcoinOrderInMpay(request);
        
        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testSumBusinessData() {
        // 创建请求对象
        SettlementJobRequest request = new SettlementJobRequest();
        // 设置必要的请求参数
        
        // 直接调用控制器方法
        Response<String> response = settlementJobController.sumBusinessData(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 