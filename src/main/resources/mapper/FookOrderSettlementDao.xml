<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookOrderSettlementDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookOrderSettlement">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="settlement_cycle" jdbcType="VARCHAR" property="settlementCycle" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, start_time, end_time, total_amount, score, mpayintegral, settlement_time, settlement_cycle, 
    subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_order_settlement
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_order_settlement
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookOrderSettlement">
    insert into fook_order_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
      <if test="settlementCycle != null">
        settlement_cycle,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementCycle != null">
        #{settlementCycle,jdbcType=VARCHAR},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookOrderSettlement">
    update fook_order_settlement
    <set>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementCycle != null">
        settlement_cycle = #{settlementCycle,jdbcType=VARCHAR},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookOrderSettlement">
    update fook_order_settlement
    set start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      score = #{score,jdbcType=DECIMAL},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      settlement_cycle = #{settlementCycle,jdbcType=VARCHAR},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>