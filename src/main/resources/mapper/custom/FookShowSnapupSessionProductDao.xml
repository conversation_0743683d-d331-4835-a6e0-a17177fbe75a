<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookShowSnapupSessionProductDao">

    <update id="updateSessionProductBuyTime" >
        update fook_business_product
        set buy_start_time = #{startTime, jdbcType=TIMESTAMP},
            buy_end_time =  #{endTime, jdbcType=TIMESTAMP},
            updated_at = now()
        where id in (
            select product_id from fook_show_snapup_session_product where session_id = #{sessionId,jdbcType=INTEGER}
        )
    </update>

    <select id="countSessionTimeNotEqualBuyTime" resultType="int">
        select count(1)
        from fook_show_snapup_session_product sp
        left join fook_business_product p on sp.product_id = p.id
        left join fook_show_snapup_session ss on sp.session_id = ss.id
        where (ss.start_time != p.buy_start_time
        or ss.end_time != p.buy_end_time)
        and sp.session_id = #{sessionId,jdbcType=INTEGER}
    </select>

    <select id="selectMpUUIDsBySessionId" resultType="string">
        select distinct p.mpay_coupons_code_id
        from fook_show_snapup_session_product sp
        left join fook_business_product p on sp.product_id = p.id
        where sp.session_id = #{sessionId,jdbcType=INTEGER}
        and p.type = 12
        and p.mpay_coupons_code_id is not null;
    </select>


    <select id="selectValidProductBySessionIds" resultType="java.lang.Integer">
        select distinct(product_id) from fook_show_snapup_session_product
        where session_id in
        <foreach collection="sessionIds" item="sessionId" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </select>

</mapper>