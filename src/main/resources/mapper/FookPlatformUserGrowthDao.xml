<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformUserGrowthDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformUserGrowth">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="growth" jdbcType="INTEGER" property="growth" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="task_rule_id" jdbcType="INTEGER" property="taskRuleId" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="evaluationid" jdbcType="INTEGER" property="evaluationid" />
    <result column="mall_order_id" jdbcType="INTEGER" property="mallOrderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="ordercodeid" jdbcType="INTEGER" property="ordercodeid" />
    <result column="adminid" jdbcType="INTEGER" property="adminid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, userid, score, growth, `type`, task_rule_id, orderid, evaluationid, mall_order_id, 
    create_time, remarks, ordercodeid, adminid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_user_growth
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_user_growth
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformUserGrowth">
    insert into fook_platform_user_growth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="growth != null">
        growth,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="taskRuleId != null">
        task_rule_id,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="evaluationid != null">
        evaluationid,
      </if>
      <if test="mallOrderId != null">
        mall_order_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="ordercodeid != null">
        ordercodeid,
      </if>
      <if test="adminid != null">
        adminid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="growth != null">
        #{growth,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="taskRuleId != null">
        #{taskRuleId,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="evaluationid != null">
        #{evaluationid,jdbcType=INTEGER},
      </if>
      <if test="mallOrderId != null">
        #{mallOrderId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="ordercodeid != null">
        #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="adminid != null">
        #{adminid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformUserGrowth">
    update fook_platform_user_growth
    <set>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="growth != null">
        growth = #{growth,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="taskRuleId != null">
        task_rule_id = #{taskRuleId,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="evaluationid != null">
        evaluationid = #{evaluationid,jdbcType=INTEGER},
      </if>
      <if test="mallOrderId != null">
        mall_order_id = #{mallOrderId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="ordercodeid != null">
        ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="adminid != null">
        adminid = #{adminid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformUserGrowth">
    update fook_platform_user_growth
    set userid = #{userid,jdbcType=INTEGER},
      score = #{score,jdbcType=DECIMAL},
      growth = #{growth,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      task_rule_id = #{taskRuleId,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      evaluationid = #{evaluationid,jdbcType=INTEGER},
      mall_order_id = #{mallOrderId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      remarks = #{remarks,jdbcType=VARCHAR},
      ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      adminid = #{adminid,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>