<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductOrdercodeDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessProductOrdercode">
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessProductOrdercode">
    insert into fook_business_product_ordercode
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>