package com.mcoin.mall.service.chennel.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.Date;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookCouponSyn;
import com.mcoin.mall.bean.FookMacaupassOrder;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrdercode;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.client.MPayCouponAccessClient;
import com.mcoin.mall.client.MPayCouponClient;
import com.mcoin.mall.client.model.MPayCouponPayRequest;
import com.mcoin.mall.client.model.MPayCouponQueryRequest;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessProductTranslationsDao;
import com.mcoin.mall.dao.FookCouponSynDao;
import com.mcoin.mall.dao.FookMacaupassOrderDao;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.dao.FookPlatformOrderDao;
import com.mcoin.mall.dao.FookPlatformOrdercodeDao;
import com.mcoin.mall.dao.FookPlatformOrderinfoDao;
import com.mcoin.mall.dao.FookPlatformOrderrefundDao;
import com.mcoin.mall.dao.FookStoresDao;
import com.mcoin.mall.dao.FookStoresTranslationsDao;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.mq.model.CouponSyncMessage;
import com.mcoin.mall.util.ConfigUtils;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class MPayChannelServiceImplTest{

    @InjectMocks
    private MPayChannelServiceImpl mPayChannelService;

    private FookPlatformOrder order;

    private FookPlatformOrderinfo orderinfo;

    @Mock
    private FookMacaupassOrderDao fookMacaupassOrderDao;

    @Mock
    private FookMacaupassUserDao fookMacaupassUserDao;

    @Mock
    private FookBusinessProductDao fookBusinessProductDao;

    @Mock
    private MPayCouponClient mPayCouponClient;

    @Mock
    private FookPlatformOrdercodeDao fookPlatformOrdercodeDao;

    @Mock
    private FookCouponSynDao fookCouponSynDao;

    @Mock
    private FookPlatformOrderDao fookPlatformOrderDao;

    @Mock
    private MPayCouponAccessClient mPayCouponAccessClient;
    @Mock
    private FookPlatformOrderinfoDao fookPlatformOrderinfoDao;

    private MockedStatic<ConfigUtils> configUtilsMockedStatic;

    @Mock private FookStoresTranslationsDao fookStoresTranslationsDao;

    @Mock private FookBusinessProductTranslationsDao fookBusinessProductTranslationsDao;

    @Mock private FookPlatformOrderrefundDao fookPlatformOrderrefundDao;

    @Mock private FookStoresDao fookStoresDao;

    @BeforeEach
    public void setUp() throws BlockException {
        configUtilsMockedStatic = mockStatic(ConfigUtils.class);
        order = new FookPlatformOrder();
        order.setId(1);
        order.setUserid(100);
        order.setOrderNo("ORDER12345");
    
        orderinfo = new FookPlatformOrderinfo();
        orderinfo.setProdcutid(1000);
    
        FookBusinessProduct product = new FookBusinessProduct();
        product.setRetailPrice(new BigDecimal("100.00"));
        product.setMpayCouponsCodeId("COUPON12345");
    
//        when(fookMacaupassOrderDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(new FookMacaupassOrder());
//        when(fookBusinessProductDao.selectByPrimaryKey(1000)).thenReturn(product);
//        when(mPayCouponClient.pay(any(MPayCouponPayRequest.class))).thenReturn("SUCCESS");
    }

    @AfterEach
    public void tearDown() {
        configUtilsMockedStatic.close();
    }

    @Test
    public void testGetCouponSuccess() throws BlockException {
        orderinfo = new FookPlatformOrderinfo();
        orderinfo.setProdcutid(1000);

        FookBusinessProduct product = new FookBusinessProduct();
        product.setRetailPrice(new BigDecimal("100.00"));
        product.setMpayCouponsCodeId("COUPON12345");

        FookMacaupassUser user = new FookMacaupassUser();
        user.setUserId(100);
        user.setPhone("1234567890");
        user.setCustomid("CUSTOMID12345");
        FookMacaupassOrder fookMacaupassOrder = new FookMacaupassOrder();
        fookMacaupassOrder.setMpayintegral(100);
        when(fookMacaupassOrderDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassOrder);
        when(fookBusinessProductDao.selectByPrimaryKey(1000)).thenReturn(product);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(user);
        when(mPayCouponClient.pay(any(MPayCouponPayRequest.class))).thenReturn("SUCCESS");
        String result = mPayChannelService.getCoupon(order, orderinfo);
        assertNotNull(result);
        assertEquals("SUCCESS", result);
    }

    @Test
    public void testGetCouponNoMacaupassOrder() {
        when(fookMacaupassOrderDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        String result = mPayChannelService.getCoupon(order, orderinfo);
        assertNull(result);
    }

    @Test
    public void testGetCouponZeroMpayintegral() {
        FookMacaupassOrder macaupassOrder = new FookMacaupassOrder();
        macaupassOrder.setMpayintegral(0);
        when(fookMacaupassOrderDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(macaupassOrder);
        String result = mPayChannelService.getCoupon(order, orderinfo);
        assertNull(result);
    }

    @Test
    public void testGetCouponNoUser() {
        String result = mPayChannelService.getCoupon(order, orderinfo);
        assertNull(result);
    }

    @Test
    public void testGetCouponException() throws BlockException {
        FookMacaupassOrder fookMacaupassOrder = new FookMacaupassOrder();
        fookMacaupassOrder.setMpayintegral(100);
        when(fookMacaupassOrderDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookMacaupassOrder);
        FookBusinessProduct product = new FookBusinessProduct();
        product.setMpayCouponsCodeId("COUPON12345");
        product.setRetailPrice(new BigDecimal("100.00"));
        when(fookBusinessProductDao.selectByPrimaryKey(1000)).thenReturn(product);
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(new FookMacaupassUser());
        when(mPayCouponClient.pay(any(MPayCouponPayRequest.class))).thenThrow(new RuntimeException("Connection Error"));
        String result = mPayChannelService.getCoupon(order, orderinfo);
        assertNull(result);
    }

    @Test
    public void testQueryCouponUserNotFound() throws BlockException {
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        String result = mPayChannelService.queryCoupon(1, "ORDER12345");
        assertNull(result);
    }

    @Test
    public void testQueryCouponClientException() throws BlockException {
        FookMacaupassUser user = new FookMacaupassUser();
        user.setUserId(1);
        user.setCustomid("USER12345");
    
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(user);
        when(mPayCouponClient.query(any(MPayCouponQueryRequest.class))).thenThrow(new RuntimeException("Connection error"));
        String result = mPayChannelService.queryCoupon(1, "ORDER12345");
        assertNull(result);
    }

    @Test
    public void testQueryCouponSuccess() throws BlockException {
        FookMacaupassUser user = new FookMacaupassUser();
        user.setUserId(1);
        user.setCustomid("USER12345");
    
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(user);
        when(mPayCouponClient.query(any(MPayCouponQueryRequest.class))).thenReturn("SUCCESS");
    
        String result = mPayChannelService.queryCoupon(1, "ORDER12345");
        assertEquals("SUCCESS", result);
    }

    @Test
    public void testUpdSyncCouponOrderCodeSuccess() throws BlockException {
        CouponSyncMessage message = new CouponSyncMessage();
        message.setOrderCodeId(100);
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setOrderid(1);
        ordercode.setStatus(1);
        ordercode.setRefundStatus(2);
        ordercode.setUserTime(new Date());
        ordercode.setCode("CODE12345");
        ordercode.setApportionBillAmount(new BigDecimal("100.00"));
        ordercode.setApportionBillFinalAmount(new BigDecimal("100.00"));
        ordercode.setId(1);
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(1);
        order.setStatus(2);
        order.setIsMpay("1");
        order.setUserid(123);
        FookCouponSyn couponSyn = new FookCouponSyn();
        couponSyn.setStatus(0);
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(100)).thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(1)).thenReturn(order);
        when(fookCouponSynDao.selectOne(any())).thenReturn(couponSyn);
        FookPlatformOrderinfo fookPlatformOrderinfo = new FookPlatformOrderinfo();
        fookPlatformOrderinfo.setType(5);
        fookPlatformOrderinfo.setMilesMember(12345678901L);
        fookPlatformOrderinfo.setVaildEndTime(new Date());
        fookPlatformOrderinfo.setProdcutid(1);
        fookPlatformOrderinfo.setVaildStartTime(new Date());
        when(fookPlatformOrderinfoDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookPlatformOrderinfo);

        FookMacaupassUser user = new FookMacaupassUser();
        user.setPhone("12345678901");
        user.setCustomid("USER12345");
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(user);

        when(ConfigUtils.getProperty("macaupass.coupon.secret")).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");
        when(ConfigUtils.getProperty("APP_URL")).thenReturn("http://localhost:8080");
        when(mPayCouponAccessClient.updateCode(any())).thenReturn("{\"code\":\"200\", \"msg\":\"Success\"}");

        mPayChannelService.updSyncCouponOrderCode(message);

        verify(fookCouponSynDao).insert(any());
    }

    @Test
    public void testUpdSyncCouponOrderCodeRetry() throws BlockException {
        CouponSyncMessage message = new CouponSyncMessage();
        message.setOrderCodeId(100);
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setOrderid(1);
        ordercode.setStatus(1);
        ordercode.setRefundStatus(2);
        ordercode.setUserTime(new Date());
        ordercode.setCode("CODE12345");
        ordercode.setApportionBillAmount(new BigDecimal("100.00"));
        ordercode.setApportionBillFinalAmount(new BigDecimal("100.00"));
        ordercode.setId(1);
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(1);
        order.setStatus(2);
        order.setIsMpay("1");
        order.setUserid(123);
        FookCouponSyn couponSyn = new FookCouponSyn();
        couponSyn.setStatus(0);
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(100)).thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(1)).thenReturn(order);
        when(fookCouponSynDao.selectOne(any())).thenReturn(couponSyn);
        FookPlatformOrderinfo fookPlatformOrderinfo = new FookPlatformOrderinfo();
        fookPlatformOrderinfo.setType(4);
        fookPlatformOrderinfo.setMilesMember(12345678901L);
        fookPlatformOrderinfo.setVaildEndTime(new Date());
        fookPlatformOrderinfo.setProdcutid(1);
        fookPlatformOrderinfo.setVaildStartTime(new Date());
        when(fookPlatformOrderinfoDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(fookPlatformOrderinfo);

        FookMacaupassUser user = new FookMacaupassUser();
        user.setPhone("12345678901");
        user.setCustomid("USER12345");
        when(fookMacaupassUserDao.selectOne(any(LambdaQueryWrapper.class))).thenReturn(user);

        when(ConfigUtils.getProperty("macaupass.coupon.secret")).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");
        when(ConfigUtils.getProperty("APP_URL")).thenReturn("http://localhost:8080");
        when(mPayCouponAccessClient.updateCode(any())).thenReturn("");

        assertThrows(RetryException.class, () -> {
            mPayChannelService.updSyncCouponOrderCode(message);
        });
    }

    @Test
    public void testUpdSyncCouponOrderCodeOrderCodeNotFound() throws BlockException {
        CouponSyncMessage message = new CouponSyncMessage();
        message.setOrderCodeId(100);
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(100)).thenReturn(null);

        mPayChannelService.updSyncCouponOrderCode(message);

        verify(fookCouponSynDao, never()).selectOne(any());
        verify(fookPlatformOrderDao, never()).selectByPrimaryKey(any());
        verify(mPayCouponAccessClient, never()).updateCode(any());
    }

    @Test
    public void testUpdSyncCouponOrderCodeOrderNotFound() throws BlockException {
        CouponSyncMessage message = new CouponSyncMessage();
        message.setOrderCodeId(100);
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setOrderid(1);
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(100)).thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(1)).thenReturn(null);

        mPayChannelService.updSyncCouponOrderCode(message);

        verify(fookCouponSynDao, never()).selectOne(any());
        verify(mPayCouponAccessClient, never()).updateCode(any());
    }

    @Test
    public void testUpdSyncCouponOrderCodeOrderStatusNotValid() throws BlockException {
        CouponSyncMessage message = new CouponSyncMessage();
        message.setOrderCodeId(100);
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setOrderid(1);
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(1);
        order.setStatus(1);
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(100)).thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(1)).thenReturn(order);

        mPayChannelService.updSyncCouponOrderCode(message);

        verify(mPayCouponAccessClient, never()).updateCode(any());
    }

    @Test
    public void testUpdSyncCouponOrderCodeNotMpayOrder() throws BlockException {
        CouponSyncMessage message = new CouponSyncMessage();
        message.setOrderCodeId(100);
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setOrderid(1);
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(1);
        order.setStatus(2);
        order.setIsMpay("0");
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(100)).thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(1)).thenReturn(order);

        mPayChannelService.updSyncCouponOrderCode(message);

        verify(mPayCouponAccessClient, never()).updateCode(any());
    }

    @Test
    public void testUpdSyncCouponOrderCodeCouponSynAlreadySynced() throws BlockException {
        CouponSyncMessage message = new CouponSyncMessage();
        message.setOrderCodeId(100);
        FookPlatformOrdercode ordercode = new FookPlatformOrdercode();
        ordercode.setOrderid(1);
        FookPlatformOrder order = new FookPlatformOrder();
        order.setId(1);
        order.setStatus(2);
        order.setIsMpay("1");
        FookCouponSyn couponSyn = new FookCouponSyn();
        couponSyn.setStatus(1);
        when(fookPlatformOrdercodeDao.selectByPrimaryKey(100)).thenReturn(ordercode);
        when(fookPlatformOrderDao.selectByPrimaryKey(1)).thenReturn(order);
        when(fookCouponSynDao.selectOne(any())).thenReturn(couponSyn);

        mPayChannelService.updSyncCouponOrderCode(message);

        verify(mPayCouponAccessClient, never()).updateCode(any());
    }
}