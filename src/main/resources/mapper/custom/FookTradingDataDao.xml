<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookTradingDataDao">
  <select id="selectTradingDataByTradingId" resultMap="BaseResultMap">
    select d.id,
           d.statistics_day,
           d.user_num,
           d.user_pay_num,
           d.use_no_pay_num,
           d.business_num,
           d.trading_num,
           d.integral_conversion_amount,
           d.payment_amount,
           d.payment_sum,
           d.settlement_business_num,
           d.settlement_num,
           d.settlement_conversion_amount,
           d.settlement_payment_amount,
           d.settlement_amount_sum,
           d.refund_conversion_amount,
           d.refund_payment_amount,
           d.refund_amount_sum,
           d.refund_num,
           d.product_id_hot,
           d.product_title_hot,
           d.product_sales_amount,
           d.product_id_max,
           d.product_title_max,
           d.product_num,
           d.created_at,
           d.updated_at,
           d.subsidy_amount,
           d.refund_subsidy_amount
    from fook_trading_data_class c
    LEFT JOIN fook_trading_data d on c.trading_data_id = d.id
    where c.trading_id = #{tradingId,jdbcType=INTEGER}
    order by d.statistics_day asc
  </select>
</mapper>