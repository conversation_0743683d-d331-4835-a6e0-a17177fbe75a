package com.mcoin.mall.constant;

/**
 * 日志埋点事件类型枚举
 */
public enum LogEventType {
    // 防爬指纹相关埋点
    FINGERPRINT_VALIDATION_FAILURE("FINGER_001", "防爬指纹验证失败"),
    REQUEST_DELAY_EXCEEDED("FINGER_002", "请求延迟超过阈值");

    private final String eventCode;
    private final String description;

    LogEventType(String eventCode, String description) {
        this.eventCode = eventCode;
        this.description = description;
    }

    public String getEventCode() {
        return eventCode;
    }

    public String getDescription() {
        return description;
    }
}