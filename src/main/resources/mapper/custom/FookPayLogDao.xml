<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPayLogDao">
  <select id="sumPayAmount" resultType="com.mcoin.mall.bo.PayLogAmountBo">
    select SUM(amount) as totalAmount,
           SUM(fee) as totalFee,
           type
    from fook_pay_log
    where oparemtion = 'pay'
    and status = 1
    and updatetime between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
    group by type
  </select>
</mapper>