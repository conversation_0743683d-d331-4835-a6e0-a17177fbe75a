package com.mcoin.mall.controller.business;

import java.util.Locale;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.model.ApplyStoresRequest;
import com.mcoin.mall.model.ApplyStoresResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.StoreDetailCtx;
import com.mcoin.mall.model.StoreDetailRequest;
import com.mcoin.mall.model.StoreDetailResponse;
import com.mcoin.mall.model.StoresRequest;
import com.mcoin.mall.model.StoresResponse;
import com.mcoin.mall.service.business.StoreService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@RestController
public class StoreController {

    @Autowired
    private StoreService storeService;

    @Resource
    private ContextHolder contextHolder;
    /**
     * 門店詳情
     *
     * @param request 请求参数
     * @param clientType 客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)
     * @return 门店详情响应
     */
    @GetMapping(value = "/api/business/welfaredetails", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(value = "获取门店详情", notes = "根据门店ID获取门店详细信息")
    @ApiImplicitParams({
        @ApiImplicitParam(
            name = "X-Client-Type",
            value = "客户端类型(HARMONY/IOS/ANDROID/UNKNOWN)",
            required = false,
            dataType = "string",
            paramType = "header",
            example = "HARMONY"
        )
    })
    public Response<StoreDetailResponse> storeDetail(@Valid StoreDetailRequest request,
            @RequestHeader(value = "X-Client-Type", required = false) String clientType) {
        request.convertWsgToGsj();
        Locale locale = contextHolder.getLocale();
        String language = locale.getLanguage();
        StoreDetailCtx ctx = new StoreDetailCtx();
        ctx.setStoreDetailRequest(request);
        ctx.setLanguage(language);
        StoreDetailResponse storeDetail = this.storeService.getStoreDetail(ctx,clientType);
        return Responses.ok(storeDetail);
    }


    /**
     * 多門店查詢
     *
     * @return
     */
    @GetMapping(value = "/api/business/stores", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<StoresResponse> stores(@Valid StoresRequest request) throws Exception {
        request.convertWsgToGsj();
        StoresResponse response = storeService.getStores(request);
        return Responses.ok(response);
    }


    /**
     * 获取适用门店列表信息
     *
     * @return
     */
    @GetMapping(value = "/api/business/applystores", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ApplyStoresResponse> applyStores(@Valid ApplyStoresRequest request) {
        request.convertWsgToGsj();
        ApplyStoresResponse response = storeService.getApplyStores(request);
        return Responses.ok(response);
    }

}
