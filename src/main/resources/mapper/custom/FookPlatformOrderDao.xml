<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrderDao">

    <sql id="searchSettlementOrderSql">
        WHERE payment_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        AND status IN ( 2, 4)
        AND ( payment_type IN ( 1, 4 ) OR ( payment_type = 0 AND is_mpay = 1 ) )
    </sql>

    <select id="sumMileageNumber" resultType="integer">
        select sum(i.miles_milage)
        from fook_platform_order as o
        join fook_platform_orderinfo as i on i.orderid = o.id
        where o.create_time >= #{startTime,jdbcType=TIMESTAMP}
        <![CDATA[
      and o.create_time < #{endTime,jdbcType=TIMESTAMP}
    ]]>
        and o.status in (1, 2)
        and i.prodcutid = #{productId,jdbcType=INTEGER}
        and o.userid = #{userId,jdbcType=INTEGER}
    </select>

    <select id="getOrderIdByQuery" resultType="integer">
        SELECT id FROM fook_platform_order
        WHERE userid = #{userId,jdbcType=INTEGER}
        and status = #{status,jdbcType=INTEGER}
        and create_time between #{startTime,jdbcType=TIMESTAMP}
        and #{endTime,jdbcType=TIMESTAMP}
        and exists (
        SELECT * FROM fook_platform_ordercode WHERE orderid = fook_platform_order.id and status =
        #{status,jdbcType=INTEGER}
        )
    </select>


    <select id="sumUserBuyNumber" resultType="int">
        select sum(oi.number)
        from fook_platform_order as o FORCE INDEX (idx_userid_create_time)
        left join fook_platform_orderinfo as oi on o.id = oi.orderid
        where o.userid = #{userId,jdbcType=INTEGER}
        and o.create_time >= #{createTime,jdbcType=TIMESTAMP}
        and oi.prodcutid=#{productId,jdbcType=INTEGER}
        and o.status in (1, 2)
        <choose>
            <when test="sessionId != null">
                and o.session_id = #{sessionId,jdbcType=INTEGER}
            </when>
            <otherwise>
                and o.session_id is null
            </otherwise>
        </choose>
    </select>

    <select id="sumBuyNumber" resultType="int">
        select sum(oi.number)
        from fook_platform_order as o
        left join fook_platform_orderinfo as oi on o.id = oi.orderid
        where o.create_time >= #{orderTime,jdbcType=TIMESTAMP}
        and oi.prodcutid=#{productId,jdbcType=INTEGER}
        and o.status in (1, 2)
    </select>

    <select id="sumBuyNumberByProductId" resultType="int">
        select sum(oi.number)
        from fook_platform_order as o
        left join fook_platform_orderinfo as oi on o.id = oi.orderid
        left join fook_platform_ordercode as oc on o.id = oc.orderid
        where oi.prodcutid = #{productId,jdbcType=INTEGER}
        and o.status in (1, 2)
        and oc.is_a_open = 0
    </select>

    <update id="updateCompleteTime">
        update fook_platform_order
        set complete_time = #{completeTime,jdbcType=TIMESTAMP},
        ordercode_status = #{ordercodeStatus,jdbcType=INTEGER}
        where id = #{orderId,jdbcType=INTEGER}
    </update>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fook_platform_order
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectCodeForCheck" resultType="java.lang.String">
        select oc.code
        from fook_platform_order o
                 left join fook_platform_orderinfo oi on o.id = oi.orderid
                 left join fook_platform_ordercode oc on o.id = oc.orderid
                 left join fook_business_product p on oi.prodcutid = p.id
        where o.create_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          and o.status = 2
          and oi.miles_member is null
        and p.type !=6
        and p.isexport is null
        and oi.type !=5
    </select>

    <select id="sumOrderAmount" resultType="com.mcoin.mall.bo.OrderAmountBo">
        select SUM(order_amount) as totalAmount,
               SUM(if(mpayintegral > 0,0,score)) as totalScore,
               sum(if(mpayintegral is null,0,mpayintegral)) as totalMpayIntegral,
               SUM(total_amount) as payAmount,
               SUM(subsidy_amount) as subsidyAmount
        from fook_platform_order
        where payment_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and status in (2,4)
    </select>

    <select id="selectSettlementOrderList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM fook_platform_order
        <include refid="searchSettlementOrderSql"/>
        limit #{offset, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER};
    </select>


    <select id="sumSettlementMpayOrderAmount" resultType="com.mcoin.mall.bo.OrderAmountBo">
        select SUM(order_amount) as totalAmount,
               SUM(if(mpayintegral > 0,0,score)) as totalScore,
               SUM(mpayintegral) as totalMpayIntegral,
               SUM(total_amount) as payAmount,
               SUM(subsidy_amount) as subsidyAmount
        from fook_platform_order
        <include refid="searchSettlementOrderSql"/>
    </select>

    <select id="selectCountSettlementOrderList" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM fook_platform_order
        <include refid="searchSettlementOrderSql"/>
    </select>

    <select id="sumAllOrderAmount" resultType="com.mcoin.mall.bo.OrderAmountBo">
        select
               SUM(if(mpayintegral > 0,0,score)) as totalScore,
               SUM(mpayintegral) as totalMpayIntegral,
               SUM(total_amount) as payAmount,
               SUM(subsidy_amount) as subsidyAmount
        from fook_platform_order
        where payment_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
          and status in (2,4) and payment_type is not null
    </select>

    <select id="countOrderListByStatusAndTime" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM fook_platform_order
        where  payment_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP} and status in (2,4) and payment_type is not NULL
    </select>



    <select id="selectOrderListByStatusAndTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM fook_platform_order
        where  payment_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP} and status in (2,4) and payment_type is not NULL
        limit #{offset, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </select>

    <select id="queryBusinessIdsWithOrder" resultType="java.lang.Integer">
        SELECT o.sellerid
        FROM fook_platform_order o
        WHERE o.status = 2 AND o.refund_status = 0
          AND o.create_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        GROUP BY o.sellerid
    </select>

    <select id="queryBusinessOrderBo" resultType="com.mcoin.mall.bo.BusinessOrderBo">
        SELECT
            o.sellerid,
            SUM(IF(o.mpayintegral IS NULL, 0, o.mpayintegral)) AS mpayintegral,
            SUM(o.order_amount) AS orderAmount,
            SUM(o.total_amount) AS totalAmount,
            SUM(IF(o.mpayintegral > 0, 0, o.score)) AS score,
            sum(o.subsidy_amount) as subsidyAmount
        FROM
            fook_platform_order AS o
                JOIN
            fook_business AS b ON b.id = o.sellerid
        WHERE
            o.status = 2
          AND o.refund_status = 0
          AND o.create_time  BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        GROUP BY
            o.sellerid
        ORDER BY
            o.sellerid ASC
    </select>

    <select id="countBusiness" resultType="int">
        SELECT count(DISTINCT(o.sellerid))
        FROM fook_platform_order as o
        WHERE o.status = 2 AND o.refund_status = 0
          AND o.create_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
    </select>

    <select id="countOrderSettlementOrder" resultType="int">
        SELECT
        count(*)
        FROM
            fook_platform_order o
        WHERE
            o.payment_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}   and  o.`status` IN ( 2, 4 )
    </select>


    <select id="sumOrderSettlementOrderBo" resultType="com.mcoin.mall.bo.OrderSettlementOrderBo">
        SELECT
                    o.areaid as areaid,
                    o.userid as userid,
                    o.sellerid as sellerid,
                    o.`type` as orderType,
                    o.order_no as orderNo,
                    o.create_time as createTime,
                    o.payment_type as paymentType,
                    o.payment_transaction as paymentTransaction,
                    o.`status` as orderStatus,
                    o.complete_time as orderCompleteTime,
                    o.order_transaction as orderTransaction,
                    IFNULL( o.mpayintegral, 0) as mpayintegral,
                    IFNULL( o.score, 0) as score,
                    o.platform as platform,
                    o.order_amount as orderAmount,
                    o.currency as currency,
                    o.total_amount as totalAmount,
                    o.total_amount_currency as totalAmountCurrency,
                    o.transaction_amount as transactionAmount,
                    o.transaction_amount_currency as transactionAmountCurrency,
                    o.payment_amount as paymentAmount,
                    o.payment_amount_currency as paymentAmountCurrency,
                    o.payment_time as paymentTime,
                    o.bank_charges as bankCharges,
                    o.bank_settlement_amount as bankSettlementAmount,
                    o.refundid as refundid,
                    o.ordercode_status as orderCodeStatus,
                    o.refund_status as refundStatus,
                    o.is_mpay as isMpay,
                    pl.tradeno as payLogTradeNo,
                    sl.tradeno as scoreTradeNo,
                    o.id as orderId,
                    o.point_ratio as pointRatio
        FROM
            fook_platform_order o
                LEFT JOIN fook_platform_ordercode AS oc ON o.id = oc.orderid
                LEFT JOIN (select orderid,tradeno from fook_pay_log where `status` = 1) AS pl ON o.id = pl.orderid
                LEFT JOIN (select orderid,tradeno from fook_score_log where `status` = 1 )  AS sl ON o.id = sl.orderid
        WHERE
            o.payment_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}   and  o.`status` IN ( 2, 4 )  GROUP BY
        o.id,
        o.areaid,
        o.userid,
        o.sellerid,
        o.`type`,
        o.order_no,
        o.create_time,
        o.payment_type,
        o.payment_transaction,
        o.`status`,
        o.complete_time,
        o.order_transaction,
        o.mpayintegral,
        o.score,
        o.platform,
        o.order_amount,
        o.currency,
        o.total_amount,
        o.total_amount_currency,
        o.transaction_amount,
        o.transaction_amount_currency,
        o.payment_amount,
        o.payment_amount_currency,
        o.payment_time,
        o.bank_charges,
        o.bank_settlement_amount,
        o.refundid,
        o.ordercode_status,
        o.refund_status,
        o.is_mpay,
        o.point_ratio,pl.tradeno, sl.tradeno
        order by o.id limit #{offset, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </select>

    <select id="sumOrderSettlementSumOrderBo" resultType="com.mcoin.mall.bo.OrderSettlementOrderSumBo">
        SELECT
        orderid,
        SUM(CASE WHEN oc.is_settlement = 1 THEN merch_settle_amount ELSE 0 END) AS settlementAmount,
        SUM(CASE WHEN oc.is_settlement != 1 AND oc.refundid IS NULL THEN merch_settle_amount ELSE 0 END) AS pendingAmount,
        SUM(CASE WHEN oc.`status` = 2 THEN apportion_bill_amount ELSE 0 END) AS settlementAccountingAmount,
        SUM(CASE WHEN oc.`status` = 1 THEN apportion_bill_amount ELSE 0 END) AS pendingAccountingAmount,
        SUM(CASE WHEN oc.`status` = 3 THEN apportion_bill_amount ELSE 0 END) AS refundAmount,
        SUM(CASE WHEN oc.`refund_status` = 2 THEN apportion_bill_amount ELSE 0 END) AS approvalAmount,
        SUM(oc.subsidy_amount) AS subsidyAmount,
        0 - SUM(IF(oc.apportion_commission IS NULL, 0, oc.apportion_commission)) AS commission,
        SUM(IF(oc.merch_settle_amount IS NULL, 0, oc.merch_settle_amount)) AS totalSettlementAmount
        FROM
        fook_platform_ordercode oc where orderid in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach> group by orderid order by orderid desc
    </select>
</mapper>