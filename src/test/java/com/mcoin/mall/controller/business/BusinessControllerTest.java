package com.mcoin.mall.controller.business;

import com.alibaba.fastjson.JSON;
import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.constant.YesNo;
import com.mcoin.mall.model.BusinessConfigRequest;
import com.mcoin.mall.model.BusinessConfigResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.security.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

/**
 * 集成测试 BusinessController
 * 使用真实服务和数据库，测试基础业务功能
 */
@Slf4j
class BusinessControllerTest extends BaseUnitTest {

    @Resource
    private BusinessController businessController;

    @Resource
    private RedissonClient redissonClient;

    @BeforeEach
    void setUp() {
        when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(1);
        when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
    }

    @Test
    void testInformation() {
        // 直接调用控制器方法
        Response<?> response = businessController.information();

        // 验证响应结构
        assertNotNull(response);
    }

    @Test
    void testConfig() {
        // 初始化缓存值
        redissonClient.getBucket("sign_image_url").set("https://example.com/image.png");
        redissonClient.getBucket("sign_http_url").set("https://example.com/image.html");
        // 直接调用控制器方法
        BusinessConfigRequest request = new BusinessConfigRequest();
        request.setShowImg(YesNo.YES.getValue());
        Response<BusinessConfigResponse> response = businessController.config(request);

        log.info("response:{}", JSON.toJSONString(response));
        // 验证响应结构
        assertNotNull(response);
        assertNotNull(response.getData().getSignConfig());
        assertNotNull(response.getData().getSignConfig().getHttpUrl());
        assertEquals("https://example.com/image.png", response.getData().getSignConfig().getImageUrl());
        assertNotNull(response.getData().getSignConfig().getImageUrl());
        assertEquals("https://example.com/image.html", response.getData().getSignConfig().getHttpUrl());
        assertNotNull(response.getData().getClause());
        assertEquals(YesNo.NO.getValue(), response.getData().getClause().getStatus());

        // 删除缓存值
        redissonClient.getBucket("sign_image_url").set("");
        redissonClient.getBucket("sign_http_url").set("");
    }

    @Test
    void testConfig_ShowImg_0() {
        // 初始化缓存值
        redissonClient.getBucket("sign_image_url").set("https://example.com/image.png");
        redissonClient.getBucket("sign_http_url").set("https://example.com/image.html");
        // 直接调用控制器方法
        BusinessConfigRequest request = new BusinessConfigRequest();
        request.setShowImg(YesNo.NO.getValue());
        Response<BusinessConfigResponse> response = businessController.config(request);

        log.info("response:{}", JSON.toJSONString(response));
        // 验证响应结构
        assertNotNull(response);
        assertNotNull(response.getData().getSignConfig());
        assertNotNull(response.getData().getSignConfig().getHttpUrl());
        assertEquals("", response.getData().getSignConfig().getImageUrl());
        assertNotNull(response.getData().getSignConfig().getImageUrl());
        assertEquals("", response.getData().getSignConfig().getHttpUrl());
        assertNotNull(response.getData().getClause());
        assertEquals(YesNo.NO.getValue(), response.getData().getClause().getStatus());

        // 删除缓存值
        redissonClient.getBucket("sign_image_url").set("");
        redissonClient.getBucket("sign_http_url").set("");
    }

    @Test
    void testConfig_ShowImg_0_UserId_2() {

        when(contextHolder.getLocale()).thenReturn(Locale.TRADITIONAL_CHINESE);
        UserInfo mockUserInfo = new UserInfo();
        mockUserInfo.setUserId(2);
        when(contextHolder.getAuthUserInfo()).thenReturn(mockUserInfo);
        // 初始化缓存值
        redissonClient.getBucket("sign_image_url").set("https://example.com/image.png");
        redissonClient.getBucket("sign_http_url").set("https://example.com/image.html");
        // 直接调用控制器方法
        BusinessConfigRequest request = new BusinessConfigRequest();
        request.setShowImg(YesNo.NO.getValue());
        Response<BusinessConfigResponse> response = businessController.config(request);

        log.info("response:{}", JSON.toJSONString(response));
        // 验证响应结构
        assertNotNull(response);
        assertNotNull(response.getData().getSignConfig());
        assertNotNull(response.getData().getSignConfig().getHttpUrl());
        assertEquals("", response.getData().getSignConfig().getImageUrl());
        assertNotNull(response.getData().getSignConfig().getImageUrl());
        assertEquals("", response.getData().getSignConfig().getHttpUrl());
        assertNotNull(response.getData().getClause());
        assertEquals(YesNo.YES.getValue(), response.getData().getClause().getStatus());

        // 删除缓存值
        redissonClient.getBucket("sign_image_url").set("");
        redissonClient.getBucket("sign_http_url").set("");
    }
}