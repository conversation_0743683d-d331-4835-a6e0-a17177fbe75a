<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportOrdercodeSettlementDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportOrdercodeSettlement">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="storeid" jdbcType="INTEGER" property="storeid" />
    <result column="ordercodeid" jdbcType="INTEGER" property="ordercodeid" />
    <result column="businessname" jdbcType="VARCHAR" property="businessname" />
    <result column="storename" jdbcType="VARCHAR" property="storename" />
    <result column="bankaccount" jdbcType="VARCHAR" property="bankaccount" />
    <result column="bankname" jdbcType="VARCHAR" property="bankname" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="usetime" jdbcType="TIMESTAMP" property="usetime" />
    <result column="billamount" jdbcType="DECIMAL" property="billamount" />
    <result column="userpaymentamount" jdbcType="DECIMAL" property="userpaymentamount" />
    <result column="momecoinsamount" jdbcType="DECIMAL" property="momecoinsamount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="merchantsettleamount" jdbcType="DECIMAL" property="merchantsettleamount" />
    <result column="vouchercode" jdbcType="VARCHAR" property="vouchercode" />
    <result column="vouchername" jdbcType="VARCHAR" property="vouchername" />
    <result column="ordertransaction" jdbcType="VARCHAR" property="ordertransaction" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="settlementtime" jdbcType="TIMESTAMP" property="settlementtime" />
    <result column="settlementbusinessid" jdbcType="INTEGER" property="settlementbusinessid" />
    <result column="internalid" jdbcType="INTEGER" property="internalid" />
    <result column="mpaysettlement" jdbcType="INTEGER" property="mpaysettlement" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="is_mpay" jdbcType="INTEGER" property="isMpay" />
    <result column="is_member" jdbcType="TINYINT" property="isMember" />
    <result column="memberintegral" jdbcType="DECIMAL" property="memberintegral" />
    <result column="optionalid" jdbcType="INTEGER" property="optionalid" />
    <result column="is_voucher" jdbcType="TINYINT" property="isVoucher" />
    <result column="is_settlement" jdbcType="TINYINT" property="isSettlement" />
    <result column="storeMid" jdbcType="VARCHAR" property="storemid" />
    <result column="is_a_open" jdbcType="BIT" property="isAOpen" />
    <result column="third_party_settlement_price" jdbcType="DECIMAL" property="thirdPartySettlementPrice" />
    <result column="a_fee_type" jdbcType="VARCHAR" property="aFeeType" />
    <result column="a_fee_rate" jdbcType="DECIMAL" property="aFeeRate" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="third_party_pay" jdbcType="DECIMAL" property="thirdPartyPay" />
    <result column="channel_commission" jdbcType="DECIMAL" property="channelCommission" />
    <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
    <result column="delivery_type" jdbcType="INTEGER" property="deliveryType" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
    <result column="tracking_no" jdbcType="VARCHAR" property="trackingNo" />
    <result column="business_redeem_time" jdbcType="TIMESTAMP" property="businessRedeemTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, businessid, storeid, ordercodeid, businessname, storename, bankaccount, bankname, 
    bank, createtime, usetime, billamount, userpaymentamount, momecoinsamount, commission, 
    merchantsettleamount, vouchercode, vouchername, ordertransaction, `status`, settlementtime, 
    settlementbusinessid, internalid, mpaysettlement, mpayintegral, is_mpay, is_member, 
    memberintegral, optionalid, is_voucher, is_settlement, storeMid, is_a_open, third_party_settlement_price, 
    a_fee_type, a_fee_rate, discount_amount, third_party_pay, channel_commission, retail_price, 
    delivery_type, subsidy_amount, tracking_no, business_redeem_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_ordercode_settlement
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_ordercode_settlement
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportOrdercodeSettlement">
    insert into fook_report_ordercode_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="storeid != null">
        storeid,
      </if>
      <if test="ordercodeid != null">
        ordercodeid,
      </if>
      <if test="businessname != null">
        businessname,
      </if>
      <if test="storename != null">
        storename,
      </if>
      <if test="bankaccount != null">
        bankaccount,
      </if>
      <if test="bankname != null">
        bankname,
      </if>
      <if test="bank != null">
        bank,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="usetime != null">
        usetime,
      </if>
      <if test="billamount != null">
        billamount,
      </if>
      <if test="userpaymentamount != null">
        userpaymentamount,
      </if>
      <if test="momecoinsamount != null">
        momecoinsamount,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="merchantsettleamount != null">
        merchantsettleamount,
      </if>
      <if test="vouchercode != null">
        vouchercode,
      </if>
      <if test="vouchername != null">
        vouchername,
      </if>
      <if test="ordertransaction != null">
        ordertransaction,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="settlementtime != null">
        settlementtime,
      </if>
      <if test="settlementbusinessid != null">
        settlementbusinessid,
      </if>
      <if test="internalid != null">
        internalid,
      </if>
      <if test="mpaysettlement != null">
        mpaysettlement,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="isMpay != null">
        is_mpay,
      </if>
      <if test="isMember != null">
        is_member,
      </if>
      <if test="memberintegral != null">
        memberintegral,
      </if>
      <if test="optionalid != null">
        optionalid,
      </if>
      <if test="isVoucher != null">
        is_voucher,
      </if>
      <if test="isSettlement != null">
        is_settlement,
      </if>
      <if test="storemid != null">
        storeMid,
      </if>
      <if test="isAOpen != null">
        is_a_open,
      </if>
      <if test="thirdPartySettlementPrice != null">
        third_party_settlement_price,
      </if>
      <if test="aFeeType != null">
        a_fee_type,
      </if>
      <if test="aFeeRate != null">
        a_fee_rate,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="thirdPartyPay != null">
        third_party_pay,
      </if>
      <if test="channelCommission != null">
        channel_commission,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
      <if test="trackingNo != null">
        tracking_no,
      </if>
      <if test="businessRedeemTime != null">
        business_redeem_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="businessname != null">
        #{businessname,jdbcType=VARCHAR},
      </if>
      <if test="storename != null">
        #{storename,jdbcType=VARCHAR},
      </if>
      <if test="bankaccount != null">
        #{bankaccount,jdbcType=VARCHAR},
      </if>
      <if test="bankname != null">
        #{bankname,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="usetime != null">
        #{usetime,jdbcType=TIMESTAMP},
      </if>
      <if test="billamount != null">
        #{billamount,jdbcType=DECIMAL},
      </if>
      <if test="userpaymentamount != null">
        #{userpaymentamount,jdbcType=DECIMAL},
      </if>
      <if test="momecoinsamount != null">
        #{momecoinsamount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="merchantsettleamount != null">
        #{merchantsettleamount,jdbcType=DECIMAL},
      </if>
      <if test="vouchercode != null">
        #{vouchercode,jdbcType=VARCHAR},
      </if>
      <if test="vouchername != null">
        #{vouchername,jdbcType=VARCHAR},
      </if>
      <if test="ordertransaction != null">
        #{ordertransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="settlementtime != null">
        #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementbusinessid != null">
        #{settlementbusinessid,jdbcType=INTEGER},
      </if>
      <if test="internalid != null">
        #{internalid,jdbcType=INTEGER},
      </if>
      <if test="mpaysettlement != null">
        #{mpaysettlement,jdbcType=INTEGER},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="isMpay != null">
        #{isMpay,jdbcType=INTEGER},
      </if>
      <if test="isMember != null">
        #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="optionalid != null">
        #{optionalid,jdbcType=INTEGER},
      </if>
      <if test="isVoucher != null">
        #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="isSettlement != null">
        #{isSettlement,jdbcType=TINYINT},
      </if>
      <if test="storemid != null">
        #{storemid,jdbcType=VARCHAR},
      </if>
      <if test="isAOpen != null">
        #{isAOpen,jdbcType=BIT},
      </if>
      <if test="thirdPartySettlementPrice != null">
        #{thirdPartySettlementPrice,jdbcType=DECIMAL},
      </if>
      <if test="aFeeType != null">
        #{aFeeType,jdbcType=VARCHAR},
      </if>
      <if test="aFeeRate != null">
        #{aFeeRate,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="thirdPartyPay != null">
        #{thirdPartyPay,jdbcType=DECIMAL},
      </if>
      <if test="channelCommission != null">
        #{channelCommission,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="trackingNo != null">
        #{trackingNo,jdbcType=VARCHAR},
      </if>
      <if test="businessRedeemTime != null">
        #{businessRedeemTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportOrdercodeSettlement">
    update fook_report_ordercode_settlement
    <set>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        storeid = #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="businessname != null">
        businessname = #{businessname,jdbcType=VARCHAR},
      </if>
      <if test="storename != null">
        storename = #{storename,jdbcType=VARCHAR},
      </if>
      <if test="bankaccount != null">
        bankaccount = #{bankaccount,jdbcType=VARCHAR},
      </if>
      <if test="bankname != null">
        bankname = #{bankname,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        bank = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="usetime != null">
        usetime = #{usetime,jdbcType=TIMESTAMP},
      </if>
      <if test="billamount != null">
        billamount = #{billamount,jdbcType=DECIMAL},
      </if>
      <if test="userpaymentamount != null">
        userpaymentamount = #{userpaymentamount,jdbcType=DECIMAL},
      </if>
      <if test="momecoinsamount != null">
        momecoinsamount = #{momecoinsamount,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="merchantsettleamount != null">
        merchantsettleamount = #{merchantsettleamount,jdbcType=DECIMAL},
      </if>
      <if test="vouchercode != null">
        vouchercode = #{vouchercode,jdbcType=VARCHAR},
      </if>
      <if test="vouchername != null">
        vouchername = #{vouchername,jdbcType=VARCHAR},
      </if>
      <if test="ordertransaction != null">
        ordertransaction = #{ordertransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="settlementtime != null">
        settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementbusinessid != null">
        settlementbusinessid = #{settlementbusinessid,jdbcType=INTEGER},
      </if>
      <if test="internalid != null">
        internalid = #{internalid,jdbcType=INTEGER},
      </if>
      <if test="mpaysettlement != null">
        mpaysettlement = #{mpaysettlement,jdbcType=INTEGER},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="isMpay != null">
        is_mpay = #{isMpay,jdbcType=INTEGER},
      </if>
      <if test="isMember != null">
        is_member = #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        memberintegral = #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="optionalid != null">
        optionalid = #{optionalid,jdbcType=INTEGER},
      </if>
      <if test="isVoucher != null">
        is_voucher = #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="isSettlement != null">
        is_settlement = #{isSettlement,jdbcType=TINYINT},
      </if>
      <if test="storemid != null">
        storeMid = #{storemid,jdbcType=VARCHAR},
      </if>
      <if test="isAOpen != null">
        is_a_open = #{isAOpen,jdbcType=BIT},
      </if>
      <if test="thirdPartySettlementPrice != null">
        third_party_settlement_price = #{thirdPartySettlementPrice,jdbcType=DECIMAL},
      </if>
      <if test="aFeeType != null">
        a_fee_type = #{aFeeType,jdbcType=VARCHAR},
      </if>
      <if test="aFeeRate != null">
        a_fee_rate = #{aFeeRate,jdbcType=DECIMAL},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=DECIMAL},
      </if>
      <if test="thirdPartyPay != null">
        third_party_pay = #{thirdPartyPay,jdbcType=DECIMAL},
      </if>
      <if test="channelCommission != null">
        channel_commission = #{channelCommission,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="trackingNo != null">
        tracking_no = #{trackingNo,jdbcType=VARCHAR},
      </if>
      <if test="businessRedeemTime != null">
        business_redeem_time = #{businessRedeemTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportOrdercodeSettlement">
    update fook_report_ordercode_settlement
    set businessid = #{businessid,jdbcType=INTEGER},
      storeid = #{storeid,jdbcType=INTEGER},
      ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      businessname = #{businessname,jdbcType=VARCHAR},
      storename = #{storename,jdbcType=VARCHAR},
      bankaccount = #{bankaccount,jdbcType=VARCHAR},
      bankname = #{bankname,jdbcType=VARCHAR},
      bank = #{bank,jdbcType=VARCHAR},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      usetime = #{usetime,jdbcType=TIMESTAMP},
      billamount = #{billamount,jdbcType=DECIMAL},
      userpaymentamount = #{userpaymentamount,jdbcType=DECIMAL},
      momecoinsamount = #{momecoinsamount,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      merchantsettleamount = #{merchantsettleamount,jdbcType=DECIMAL},
      vouchercode = #{vouchercode,jdbcType=VARCHAR},
      vouchername = #{vouchername,jdbcType=VARCHAR},
      ordertransaction = #{ordertransaction,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      settlementbusinessid = #{settlementbusinessid,jdbcType=INTEGER},
      internalid = #{internalid,jdbcType=INTEGER},
      mpaysettlement = #{mpaysettlement,jdbcType=INTEGER},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      is_mpay = #{isMpay,jdbcType=INTEGER},
      is_member = #{isMember,jdbcType=TINYINT},
      memberintegral = #{memberintegral,jdbcType=DECIMAL},
      optionalid = #{optionalid,jdbcType=INTEGER},
      is_voucher = #{isVoucher,jdbcType=TINYINT},
      is_settlement = #{isSettlement,jdbcType=TINYINT},
      storeMid = #{storemid,jdbcType=VARCHAR},
      is_a_open = #{isAOpen,jdbcType=BIT},
      third_party_settlement_price = #{thirdPartySettlementPrice,jdbcType=DECIMAL},
      a_fee_type = #{aFeeType,jdbcType=VARCHAR},
      a_fee_rate = #{aFeeRate,jdbcType=DECIMAL},
      discount_amount = #{discountAmount,jdbcType=DECIMAL},
      third_party_pay = #{thirdPartyPay,jdbcType=DECIMAL},
      channel_commission = #{channelCommission,jdbcType=DECIMAL},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      delivery_type = #{deliveryType,jdbcType=INTEGER},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      tracking_no = #{trackingNo,jdbcType=VARCHAR},
      business_redeem_time = #{businessRedeemTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>