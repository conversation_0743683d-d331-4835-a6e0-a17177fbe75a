package com.mcoin.mall.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import com.mcoin.mall.model.PageV2;

public class PageUtilTest{

    @Test
    public void testGetPageNum_LargeDataCount() {
        int dataCount = 1000;
        int pageSize = 10;
        int expectedPageNum = 100;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 100 for data count of 1000");
    }

    @Test
    public void testGetPageNum_LargeDataCountAndLargePageSize() {
        int dataCount = 1000;
        int pageSize = 100;
        int expectedPageNum = 10;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 10 for data count of 1000 and page size of 100");
    }

    @Test
    public void testGetPageNum_ZeroData() {
        int dataCount = 0;
        int pageSize = 10;
        int expectedPageNum = 1;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 1 for zero data count");
    }

    @Test
    public void testGetPageNum_MultipleOfPageSize() {
        int dataCount = 20;
        int pageSize = 10;
        int expectedPageNum = 2;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 2 for data count of 20");
    }

    @Test
    public void testGetPageNum_NotMultipleOfPageSize() {
        int dataCount = 23;
        int pageSize = 10;
        int expectedPageNum = 3;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 3 for data count of 23");
    }

    @Test
    public void testGetPageNum_ExactlyMultipleOfPageSize() {
        int dataCount = 30;
        int pageSize = 10;
        int expectedPageNum = 3;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 3 for data count of 30");
    }

    @Test
    public void testGetPageNum_SmallDataCount() {
        int dataCount = 5;
        int pageSize = 10;
        int expectedPageNum = 1;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 1 for data count of 5");
    }

    @Test
    public void testGetPageNum_LargePageSize() {
        int dataCount = 10;
        int pageSize = 100;
        int expectedPageNum = 1;
        int actualPageNum = PageUtil.getPageNum(dataCount, pageSize);
        assertEquals(expectedPageNum, actualPageNum, "Page number should be 1 for data count of 10 and page size of 100");
    }

    @Test
    public void testGetOffset_NormalCase() {
        int curPage = 3;
        int pageSize = 10;
        int expectedOffset = 20;
        int actualOffset = PageUtil.getOffset(curPage, pageSize);
        assertEquals(expectedOffset, actualOffset, "Offset should be 20 for current page 3 and page size 10");
    }

    @Test
    public void testGetOffset_FirstPage() {
        int curPage = 1;
        int pageSize = 10;
        int expectedOffset = 0;
        int actualOffset = PageUtil.getOffset(curPage, pageSize);
        assertEquals(expectedOffset, actualOffset, "Offset should be 0 for current page 1 and page size 10");
    }

    @Test
    public void testGetOffset_LargePageSize() {
        int curPage = 5;
        int pageSize = 100;
        int expectedOffset = 400;
        int actualOffset = PageUtil.getOffset(curPage, pageSize);
        assertEquals(expectedOffset, actualOffset, "Offset should be 400 for current page 5 and page size 100");
    }

    @Test
    public void testGetOffset_ZeroPageSize() {
        int curPage = 5;
        int pageSize = 0;
        int expectedOffset = 0;
        int actualOffset = PageUtil.getOffset(curPage, pageSize);
        assertEquals(expectedOffset, actualOffset, "Offset should be 0 for current page 5 and page size 0");
    }

    @Test
    public void testGetOffset_NegativePageSize() {
        int curPage = 5;
        int pageSize = -10;
        int expectedOffset = -40;
        int actualOffset = PageUtil.getOffset(curPage, pageSize);
        assertEquals(expectedOffset, actualOffset, "Offset should be -40 for current page 5 and page size -10");
    }


    @Test
    public void testCreatePageV2_LargeDataCount() {
        int dataCount = 1000;
        int page = 1;
        int expectedLastPage = 100;
        int expectedCurrentPage = 1;
    
        PageV2 pageV2 = PageUtil.createPageV2(page, dataCount);
    
        assertEquals(expectedLastPage, pageV2.getLastPage(), "Last page should be 100 for data count of 1000");
        assertEquals(expectedCurrentPage, pageV2.getCurrentPage(), "Current page should be 1");
    }

    @Test
    public void testCreatePageV2_SmallDataCount() {
        int dataCount = 50;
        int page = 1;
        int expectedLastPage = 5;
        int expectedCurrentPage = 1;
    
        PageV2 pageV2 = PageUtil.createPageV2(page, dataCount);
    
        assertEquals(expectedLastPage, pageV2.getLastPage(), "Last page should be 2 for data count of 50");
        assertEquals(expectedCurrentPage, pageV2.getCurrentPage(), "Current page should be 1");
    }

    @Test
    public void testCreatePageV2_ZeroDataCount() {
        int dataCount = 0;
        int page = 1;
        int expectedLastPage = 1;
        int expectedCurrentPage = 1;
    
        PageV2 pageV2 = PageUtil.createPageV2(page, dataCount);
    
        assertEquals(expectedLastPage, pageV2.getLastPage(), "Last page should be 1 for data count of 0");
        assertEquals(expectedCurrentPage, pageV2.getCurrentPage(), "Current page should be 1");
    }

}