<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrdercodeDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformOrdercode">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="shopid" jdbcType="INTEGER" property="shopid" />
    <result column="user_time" jdbcType="TIMESTAMP" property="userTime" />
    <result column="verification_mode" jdbcType="INTEGER" property="verificationMode" />
    <result column="is_comment" jdbcType="INTEGER" property="isComment" />
    <result column="is_settlement" jdbcType="INTEGER" property="isSettlement" />
    <result column="settlement_time" jdbcType="TIMESTAMP" property="settlementTime" />
    <result column="settlement_id" jdbcType="INTEGER" property="settlementId" />
    <result column="merch_settle_amount" jdbcType="DECIMAL" property="merchSettleAmount" />
    <result column="merch_settle_amount_currency" jdbcType="VARCHAR" property="merchSettleAmountCurrency" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="apportion_bill_amount" jdbcType="DECIMAL" property="apportionBillAmount" />
    <result column="apportion_bill_final_amount" jdbcType="DECIMAL" property="apportionBillFinalAmount" />
    <result column="apportion_mpayintegral" jdbcType="INTEGER" property="apportionMpayintegral" />
    <result column="apportion_momecoins" jdbcType="DECIMAL" property="apportionMomecoins" />
    <result column="apportion_commission" jdbcType="DECIMAL" property="apportionCommission" />
    <result column="orderinfo_id" jdbcType="INTEGER" property="orderinfoId" />
    <result column="refundid" jdbcType="INTEGER" property="refundid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="apportion_memberintegral" jdbcType="DECIMAL" property="apportionMemberintegral" />
    <result column="is_voucher" jdbcType="TINYINT" property="isVoucher" />
    <result column="preferentialAmount" jdbcType="DECIMAL" property="preferentialamount" />
    <result column="requestOrderNo" jdbcType="VARCHAR" property="requestorderno" />
    <result column="requestOrderNoStatus" jdbcType="TINYINT" property="requestordernostatus" />
    <result column="is_a_open" jdbcType="BIT" property="isAOpen" />
    <result column="is_untie" jdbcType="TINYINT" property="isUntie" />
    <result column="is_exported" jdbcType="TINYINT" property="isExported" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
    <result column="tracking_no" jdbcType="VARCHAR" property="trackingNo" />
    <result column="business_redeem_time" jdbcType="TIMESTAMP" property="businessRedeemTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, orderid, code, `status`, shopid, user_time, verification_mode, is_comment, is_settlement, 
    settlement_time, settlement_id, merch_settle_amount, merch_settle_amount_currency, 
    refund_status, apportion_bill_amount, apportion_bill_final_amount, apportion_mpayintegral, 
    apportion_momecoins, apportion_commission, orderinfo_id, refundid, userid, apportion_memberintegral, 
    is_voucher, preferentialAmount, requestOrderNo, requestOrderNoStatus, is_a_open, 
    is_untie, is_exported, subsidy_amount, tracking_no, business_redeem_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_ordercode
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_ordercode
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformOrdercode">
    insert into fook_platform_ordercode
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="shopid != null">
        shopid,
      </if>
      <if test="userTime != null">
        user_time,
      </if>
      <if test="verificationMode != null">
        verification_mode,
      </if>
      <if test="isComment != null">
        is_comment,
      </if>
      <if test="isSettlement != null">
        is_settlement,
      </if>
      <if test="settlementTime != null">
        settlement_time,
      </if>
      <if test="settlementId != null">
        settlement_id,
      </if>
      <if test="merchSettleAmount != null">
        merch_settle_amount,
      </if>
      <if test="merchSettleAmountCurrency != null">
        merch_settle_amount_currency,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="apportionBillAmount != null">
        apportion_bill_amount,
      </if>
      <if test="apportionBillFinalAmount != null">
        apportion_bill_final_amount,
      </if>
      <if test="apportionMpayintegral != null">
        apportion_mpayintegral,
      </if>
      <if test="apportionMomecoins != null">
        apportion_momecoins,
      </if>
      <if test="apportionCommission != null">
        apportion_commission,
      </if>
      <if test="orderinfoId != null">
        orderinfo_id,
      </if>
      <if test="refundid != null">
        refundid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="apportionMemberintegral != null">
        apportion_memberintegral,
      </if>
      <if test="isVoucher != null">
        is_voucher,
      </if>
      <if test="preferentialamount != null">
        preferentialAmount,
      </if>
      <if test="requestorderno != null">
        requestOrderNo,
      </if>
      <if test="requestordernostatus != null">
        requestOrderNoStatus,
      </if>
      <if test="isAOpen != null">
        is_a_open,
      </if>
      <if test="isUntie != null">
        is_untie,
      </if>
      <if test="isExported != null">
        is_exported,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
      <if test="trackingNo != null">
        tracking_no,
      </if>
      <if test="businessRedeemTime != null">
        business_redeem_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="shopid != null">
        #{shopid,jdbcType=INTEGER},
      </if>
      <if test="userTime != null">
        #{userTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verificationMode != null">
        #{verificationMode,jdbcType=INTEGER},
      </if>
      <if test="isComment != null">
        #{isComment,jdbcType=INTEGER},
      </if>
      <if test="isSettlement != null">
        #{isSettlement,jdbcType=INTEGER},
      </if>
      <if test="settlementTime != null">
        #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementId != null">
        #{settlementId,jdbcType=INTEGER},
      </if>
      <if test="merchSettleAmount != null">
        #{merchSettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="merchSettleAmountCurrency != null">
        #{merchSettleAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="apportionBillAmount != null">
        #{apportionBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="apportionBillFinalAmount != null">
        #{apportionBillFinalAmount,jdbcType=DECIMAL},
      </if>
      <if test="apportionMpayintegral != null">
        #{apportionMpayintegral,jdbcType=INTEGER},
      </if>
      <if test="apportionMomecoins != null">
        #{apportionMomecoins,jdbcType=DECIMAL},
      </if>
      <if test="apportionCommission != null">
        #{apportionCommission,jdbcType=DECIMAL},
      </if>
      <if test="orderinfoId != null">
        #{orderinfoId,jdbcType=INTEGER},
      </if>
      <if test="refundid != null">
        #{refundid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="apportionMemberintegral != null">
        #{apportionMemberintegral,jdbcType=DECIMAL},
      </if>
      <if test="isVoucher != null">
        #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="preferentialamount != null">
        #{preferentialamount,jdbcType=DECIMAL},
      </if>
      <if test="requestorderno != null">
        #{requestorderno,jdbcType=VARCHAR},
      </if>
      <if test="requestordernostatus != null">
        #{requestordernostatus,jdbcType=TINYINT},
      </if>
      <if test="isAOpen != null">
        #{isAOpen,jdbcType=BIT},
      </if>
      <if test="isUntie != null">
        #{isUntie,jdbcType=TINYINT},
      </if>
      <if test="isExported != null">
        #{isExported,jdbcType=TINYINT},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="trackingNo != null">
        #{trackingNo,jdbcType=VARCHAR},
      </if>
      <if test="businessRedeemTime != null">
        #{businessRedeemTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformOrdercode">
    update fook_platform_ordercode
    <set>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="shopid != null">
        shopid = #{shopid,jdbcType=INTEGER},
      </if>
      <if test="userTime != null">
        user_time = #{userTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verificationMode != null">
        verification_mode = #{verificationMode,jdbcType=INTEGER},
      </if>
      <if test="isComment != null">
        is_comment = #{isComment,jdbcType=INTEGER},
      </if>
      <if test="isSettlement != null">
        is_settlement = #{isSettlement,jdbcType=INTEGER},
      </if>
      <if test="settlementTime != null">
        settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementId != null">
        settlement_id = #{settlementId,jdbcType=INTEGER},
      </if>
      <if test="merchSettleAmount != null">
        merch_settle_amount = #{merchSettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="merchSettleAmountCurrency != null">
        merch_settle_amount_currency = #{merchSettleAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="apportionBillAmount != null">
        apportion_bill_amount = #{apportionBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="apportionBillFinalAmount != null">
        apportion_bill_final_amount = #{apportionBillFinalAmount,jdbcType=DECIMAL},
      </if>
      <if test="apportionMpayintegral != null">
        apportion_mpayintegral = #{apportionMpayintegral,jdbcType=INTEGER},
      </if>
      <if test="apportionMomecoins != null">
        apportion_momecoins = #{apportionMomecoins,jdbcType=DECIMAL},
      </if>
      <if test="apportionCommission != null">
        apportion_commission = #{apportionCommission,jdbcType=DECIMAL},
      </if>
      <if test="orderinfoId != null">
        orderinfo_id = #{orderinfoId,jdbcType=INTEGER},
      </if>
      <if test="refundid != null">
        refundid = #{refundid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="apportionMemberintegral != null">
        apportion_memberintegral = #{apportionMemberintegral,jdbcType=DECIMAL},
      </if>
      <if test="isVoucher != null">
        is_voucher = #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="preferentialamount != null">
        preferentialAmount = #{preferentialamount,jdbcType=DECIMAL},
      </if>
      <if test="requestorderno != null">
        requestOrderNo = #{requestorderno,jdbcType=VARCHAR},
      </if>
      <if test="requestordernostatus != null">
        requestOrderNoStatus = #{requestordernostatus,jdbcType=TINYINT},
      </if>
      <if test="isAOpen != null">
        is_a_open = #{isAOpen,jdbcType=BIT},
      </if>
      <if test="isUntie != null">
        is_untie = #{isUntie,jdbcType=TINYINT},
      </if>
      <if test="isExported != null">
        is_exported = #{isExported,jdbcType=TINYINT},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="trackingNo != null">
        tracking_no = #{trackingNo,jdbcType=VARCHAR},
      </if>
      <if test="businessRedeemTime != null">
        business_redeem_time = #{businessRedeemTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformOrdercode">
    update fook_platform_ordercode
    set orderid = #{orderid,jdbcType=INTEGER},
      code = #{code,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      shopid = #{shopid,jdbcType=INTEGER},
      user_time = #{userTime,jdbcType=TIMESTAMP},
      verification_mode = #{verificationMode,jdbcType=INTEGER},
      is_comment = #{isComment,jdbcType=INTEGER},
      is_settlement = #{isSettlement,jdbcType=INTEGER},
      settlement_time = #{settlementTime,jdbcType=TIMESTAMP},
      settlement_id = #{settlementId,jdbcType=INTEGER},
      merch_settle_amount = #{merchSettleAmount,jdbcType=DECIMAL},
      merch_settle_amount_currency = #{merchSettleAmountCurrency,jdbcType=VARCHAR},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      apportion_bill_amount = #{apportionBillAmount,jdbcType=DECIMAL},
      apportion_bill_final_amount = #{apportionBillFinalAmount,jdbcType=DECIMAL},
      apportion_mpayintegral = #{apportionMpayintegral,jdbcType=INTEGER},
      apportion_momecoins = #{apportionMomecoins,jdbcType=DECIMAL},
      apportion_commission = #{apportionCommission,jdbcType=DECIMAL},
      orderinfo_id = #{orderinfoId,jdbcType=INTEGER},
      refundid = #{refundid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      apportion_memberintegral = #{apportionMemberintegral,jdbcType=DECIMAL},
      is_voucher = #{isVoucher,jdbcType=TINYINT},
      preferentialAmount = #{preferentialamount,jdbcType=DECIMAL},
      requestOrderNo = #{requestorderno,jdbcType=VARCHAR},
      requestOrderNoStatus = #{requestordernostatus,jdbcType=TINYINT},
      is_a_open = #{isAOpen,jdbcType=BIT},
      is_untie = #{isUntie,jdbcType=TINYINT},
      is_exported = #{isExported,jdbcType=TINYINT},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      tracking_no = #{trackingNo,jdbcType=VARCHAR},
      business_redeem_time = #{businessRedeemTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>