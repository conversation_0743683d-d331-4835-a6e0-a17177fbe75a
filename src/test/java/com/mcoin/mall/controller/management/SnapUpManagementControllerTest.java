package com.mcoin.mall.controller.management;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.management.SyncSessionInfoRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertThrows;

class SnapUpManagementControllerTest extends BaseUnitTest {

    @Autowired
    private SnapUpManagementController snapUpManagementController;

    @Test
    void testSyncSessionInfo() {
        SyncSessionInfoRequest request = new SyncSessionInfoRequest();


        assertThrows(BusinessException.class, () -> {
            snapUpManagementController.syncSessionInfo(request);        });
    }
}