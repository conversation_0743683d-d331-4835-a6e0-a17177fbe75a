package com.mcoin.mall.service.order;

import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookMiniOrderSettlement;
import com.mcoin.mall.bean.FookMiniOrderSyncLog;
import com.mcoin.mall.mq.model.MiniOrderSyncMessage;

public interface MiniOrderSyncService {

    void createOrderCodeByMiniProgram(FookMiniOrderSettlement fookMiniOrderSettlement, FookBusinessProduct product);

    FookMiniOrderSyncLog createOrUpdateOrderByMiniProgram(MiniOrderSyncMessage miniOrderSyncMessage);

    void createRefundByMiniProgram(FookMiniOrderSettlement fookMiniOrderSettlement, FookBusinessProduct fookBusinessProduct);
}
