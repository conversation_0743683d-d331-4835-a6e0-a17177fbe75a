<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMpaycoinOrdercodeAllDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportMpaycoinOrdercodeAll">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mpaycoinid" jdbcType="INTEGER" property="mpaycoinid" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="settlementtime" jdbcType="TIMESTAMP" property="settlementtime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="order_money" jdbcType="DECIMAL" property="orderMoney" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="payment_type" jdbcType="BIT" property="paymentType" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="payment_transaction" jdbcType="VARCHAR" property="paymentTransaction" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="operationtime" jdbcType="TIMESTAMP" property="operationtime" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="refund_mpayintegral" jdbcType="DECIMAL" property="refundMpayintegral" />
    <result column="refund_score" jdbcType="DECIMAL" property="refundScore" />
    <result column="is_mpay" jdbcType="TINYINT" property="isMpay" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mpaycoinid, businessid, userid, createtime, settlementtime, order_no, order_type, 
    pay_type, order_money, mpayintegral, score, total_amount, payment_type, payment_amount, 
    platform, payment_time, complete_time, payment_transaction, orderid, operationtime, 
    refund_amount, refund_mpayintegral, refund_score, is_mpay, subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_mpaycoin_ordercode_all
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_mpaycoin_ordercode_all
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinOrdercodeAll">
    insert into fook_report_mpaycoin_ordercode_all
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mpaycoinid != null">
        mpaycoinid,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="settlementtime != null">
        settlementtime,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="orderMoney != null">
        order_money,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="paymentTransaction != null">
        payment_transaction,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="operationtime != null">
        operationtime,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundMpayintegral != null">
        refund_mpayintegral,
      </if>
      <if test="refundScore != null">
        refund_score,
      </if>
      <if test="isMpay != null">
        is_mpay,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="mpaycoinid != null">
        #{mpaycoinid,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementtime != null">
        #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="orderMoney != null">
        #{orderMoney,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=BIT},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentTransaction != null">
        #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="operationtime != null">
        #{operationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundMpayintegral != null">
        #{refundMpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="refundScore != null">
        #{refundScore,jdbcType=DECIMAL},
      </if>
      <if test="isMpay != null">
        #{isMpay,jdbcType=TINYINT},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinOrdercodeAll">
    update fook_report_mpaycoin_ordercode_all
    <set>
      <if test="mpaycoinid != null">
        mpaycoinid = #{mpaycoinid,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementtime != null">
        settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="orderMoney != null">
        order_money = #{orderMoney,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=BIT},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentTransaction != null">
        payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="operationtime != null">
        operationtime = #{operationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundMpayintegral != null">
        refund_mpayintegral = #{refundMpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="refundScore != null">
        refund_score = #{refundScore,jdbcType=DECIMAL},
      </if>
      <if test="isMpay != null">
        is_mpay = #{isMpay,jdbcType=TINYINT},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportMpaycoinOrdercodeAll">
    update fook_report_mpaycoin_ordercode_all
    set mpaycoinid = #{mpaycoinid,jdbcType=INTEGER},
      businessid = #{businessid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=INTEGER},
      order_money = #{orderMoney,jdbcType=DECIMAL},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      score = #{score,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      payment_type = #{paymentType,jdbcType=BIT},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      platform = #{platform,jdbcType=VARCHAR},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      orderid = #{orderid,jdbcType=INTEGER},
      operationtime = #{operationtime,jdbcType=TIMESTAMP},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      refund_mpayintegral = #{refundMpayintegral,jdbcType=DECIMAL},
      refund_score = #{refundScore,jdbcType=DECIMAL},
      is_mpay = #{isMpay,jdbcType=TINYINT},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>