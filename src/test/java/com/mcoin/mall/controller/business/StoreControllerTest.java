package com.mcoin.mall.controller.business;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

import java.util.Locale;

import javax.annotation.Resource;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.model.ApplyStoresRequest;
import com.mcoin.mall.model.ApplyStoresResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.StoreDetailRequest;
import com.mcoin.mall.model.StoreDetailResponse;
import com.mcoin.mall.model.StoresRequest;
import com.mcoin.mall.model.StoresResponse;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.service.business.StoreService;

/**
 * 集成测试 StoreController
 * 使用真实服务和数据库，测试店铺相关功能
 */
class StoreControllerTest extends BaseUnitTest {

    @Resource
    private StoreController storeController;

    @Resource
    private StoreService storeService;

    @SpyBean
    private ContextHolder contextHolder;

    private static final int TEST_USER_ID = 1001;
    private static final String TEST_STORE_ID = "40"; // 使用已知存在的门店ID

    @BeforeEach
    void setUp() {
        // 模拟Locale
        doReturn(Locale.TRADITIONAL_CHINESE).when(contextHolder).getLocale();

        // 创建测试用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(TEST_USER_ID);
        userInfo.setUsername("test_user");

        // 设置认证上下文
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userInfo, null, userInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 为ContextHolder提供模拟的用户信息
        doReturn(userInfo).when(contextHolder).getAuthUserInfo();
    }

    /**
     * 测试获取门店详情 - 使用有效的门店ID
     * 应返回正确的门店信息
     */
    @Test
    void getStoreDetail_withValidStoreId_shouldReturnStoreDetails() {
        // 创建请求对象
        StoreDetailRequest request = new StoreDetailRequest();
        request.setWelfareId(TEST_STORE_ID); // 使用测试数据库中存在的店铺ID
        request.setLat("22.3193039"); // 香港某地的纬度
        request.setLot("114.1693611"); // 香港某地的经度

        // 直接调用控制器方法
        Response<StoreDetailResponse> response = storeController.storeDetail(request, "HARMONY");
        
        // 验证响应结构和内容
        assertNotNull(response);
        assertEquals(Response.Code.SUCCESS.get(), response.getCode());
        
        // 如果没有门店数据，跳过详细验证
        if (response.getData() != null) {
            StoreDetailResponse storeDetail = response.getData();
            assertNotNull(storeDetail.getId());
            assertNotNull(storeDetail.getName());
            assertNotNull(storeDetail.getAddress());
            assertNotNull(storeDetail.getBusinessTime());
        }
    }

    /**
     * 测试获取门店详情 - 使用不同客户端类型
     * 应根据客户端类型返回适当的内容
     */
    @Test
    void getStoreDetail_withDifferentClientTypes_shouldReturnAppropriateContent() {
        // 创建请求对象
        StoreDetailRequest request = new StoreDetailRequest();
        request.setWelfareId(TEST_STORE_ID); // 使用测试数据库中存在的店铺ID
        
        // 使用不同的客户端类型
        Response<StoreDetailResponse> responseHarmony = storeController.storeDetail(request, "HARMONY");
        Response<StoreDetailResponse> responseIOS = storeController.storeDetail(request, "IOS");
        Response<StoreDetailResponse> responseAndroid = storeController.storeDetail(request, "ANDROID");
        
        // 验证所有响应都成功返回
        assertNotNull(responseHarmony);
        assertNotNull(responseIOS);
        assertNotNull(responseAndroid);
        
        assertEquals(Response.Code.SUCCESS.get(), responseHarmony.getCode());
        assertEquals(Response.Code.SUCCESS.get(), responseIOS.getCode());
        assertEquals(Response.Code.SUCCESS.get(), responseAndroid.getCode());
    }

    /**
     * 测试获取门店详情 - 使用英文语言环境
     * 应返回英文版本的门店信息
     */
    @Test
    void getStoreDetail_withEnglishLocale_shouldReturnEnglishContent() {
        // 创建请求对象
        StoreDetailRequest request = new StoreDetailRequest();
        request.setWelfareId(TEST_STORE_ID); // 使用测试数据库中存在的店铺ID
        
        // 设置英文语言环境
        doReturn(Locale.ENGLISH).when(contextHolder).getLocale();
        
        // 直接调用控制器方法
        Response<StoreDetailResponse> response = storeController.storeDetail(request, "HARMONY");
        
        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.SUCCESS.get(), response.getCode());
    }

    /**
     * 测试获取门店详情 - 包含位置信息
     * 应返回带有距离信息的门店详情
     */
    @Test
    void getStoreDetail_withLocationInfo_shouldReturnDistanceInfo() {
        // 创建请求对象，包含经纬度
        StoreDetailRequest request = new StoreDetailRequest();
        request.setWelfareId(TEST_STORE_ID);
        request.setLat("22.3193039"); // 香港某地的纬度
        request.setLot("114.1693611"); // 香港某地的经度
        
        // 直接调用控制器方法
        Response<StoreDetailResponse> response = storeController.storeDetail(request, "HARMONY");
        
        // 验证响应中包含距离信息
        assertNotNull(response);
        assertEquals(Response.Code.SUCCESS.get(), response.getCode());
        
        // 如果没有门店数据，跳过详细验证
        if (response.getData() != null) {
            StoreDetailResponse storeDetail = response.getData();
            assertNotNull(storeDetail);
            // 不严格检查距离信息，因为可能为空
        }
    }

    /**
     * 测试获取门店详情 - 使用分页
     * 应返回分页相关信息
     */
    @Test
    void getStoreDetail_withPagination_shouldReturnPageInfo() {
        // 创建请求对象，包含分页信息
        StoreDetailRequest request = new StoreDetailRequest();
        request.setWelfareId(TEST_STORE_ID);
        request.setPage(1); // 第一页
        
        // 直接调用控制器方法
        Response<StoreDetailResponse> response = storeController.storeDetail(request, "HARMONY");
        
        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.SUCCESS.get(), response.getCode());
    }

    /**
     * 测试获取门店列表 - 使用有效的业务ID
     * 应返回正确的门店列表信息
     */
    @Test
    void stores_withValidBusinessId_shouldReturnStoresList() throws Exception {
        // 创建请求对象
        StoresRequest request = new StoresRequest();
        request.setBusinessId("1"); // 设置业务ID
        
        // 直接调用控制器方法
        Response<StoresResponse> response = storeController.stores(request);
        
        // 验证响应结构和内容
        assertNotNull(response);
        assertEquals(Response.Code.SUCCESS.get(), response.getCode());
        assertNotNull(response.getData());
        assertNotNull(response.getData().getSnatchList());
        assertTrue(response.getData().getSnatchList().size() >= 0);
    }

    /**
     * 测试申请门店 - 使用有效的产品ID
     * 应返回申请结果信息
     */
    @Test
    void applyStores_withValidProductId_shouldReturnApplyResult() {
        // 创建请求对象
        ApplyStoresRequest request = new ApplyStoresRequest();
        request.setProductId("1"); // 使用测试数据库中存在的产品ID
        
        // 直接调用控制器方法
        Response<ApplyStoresResponse> response = storeController.applyStores(request);
        
        // 验证响应结构和内容
        assertNotNull(response);
        assertEquals(Response.Code.SUCCESS.get(), response.getCode());
    }
} 