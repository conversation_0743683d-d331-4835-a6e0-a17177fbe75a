package com.mcoin.mall;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import com.mcoin.mall.client.AmapClient;
import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.MpMcoinMallManagementClient;
import com.mcoin.mall.client.PointProdClient;
import com.mcoin.mall.component.ContextHolder;

import ch.vorburger.exec.ManagedProcessException;
import ch.vorburger.mariadb4j.DBConfigurationBuilder;
import ch.vorburger.mariadb4j.springframework.MariaDB4jSpringService;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@SpringBootTest(classes = McoinMallApplicationTest.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class BaseUnitTest {

    @MockBean
    protected ContextHolder contextHolder;

    @MockBean
    protected AmapClient amapClient;

    @MockBean
    protected MPayClient mPayClient;

    @MockBean
    protected MpMcoinMallManagementClient managementClient;

    @MockBean(name = "miniOrderSyncTemplateDelay")
    protected RabbitTemplate miniOrderSyncTemplateDelay;

    @MockBean(name = "miniOrderSyncTemplate")
    protected RabbitTemplate miniOrderSyncTemplate;


    @MockBean(name = "settlementInternalTemplate")
    protected RabbitTemplate settlementInternalTemplate;

    @MockBean(name = "refundTemplate")
    protected RabbitTemplate refundTemplate;

    @MockBean
    protected PointProdClient pointProdClient;




    protected static MariaDB4jSpringService DB;
    private static boolean setUpIsDone = false;
    private static File tempDir;
    private static final Object lock = new Object();

    @BeforeAll
    public static void setup() throws ManagedProcessException {
        log.info("setup");
        synchronized (lock) {
            if (!setUpIsDone) {
                try {
                    tempDir = Files.createTempDirectory("MariaDB4j").toFile();
                    tempDir.deleteOnExit();
                } catch (IOException e) {
                    e.printStackTrace();
                }

                startMariaDB4j();
                setUpIsDone = true;
            }
        }
    }

    @AfterAll
    public static void tearDown() {
        log.info("tearDown");
    }

    private static void startMariaDB4j() throws ManagedProcessException {
        log.info("执行startMariaDB4j方法");
        DB = new MariaDB4jSpringService() {
            @Override
            public DBConfigurationBuilder getConfiguration() {
                DBConfigurationBuilder builder = super.getConfiguration();
                builder.addArg("--character-set-server=utf8mb4");
                builder.addArg("--collation_server=utf8mb4_unicode_ci");
                builder.addArg("--innodb_lock_wait_timeout=500");
                builder.addArg("--sql_mode=ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION");
                if (!builder._getArgs().contains("--user=root")) {
                    builder.addArg("--user=root");
                }
                return builder;
            }
        };
        DB.setDefaultPort(18120);
        DB.setDefaultBaseDir(tempDir.getAbsolutePath()+"/base");
        DB.setDefaultDataDir(tempDir.getAbsolutePath()+"/data");
        DB.start();
        DB.getDB().createDB("fooku");
        
        // Load schema files
        List<File> schemeFiles = FileUtil.loopFiles(new ClassPathResource("db/schema").getFile(), file -> file.getName().endsWith(".sql"));
        for (File sqlFile : schemeFiles) {
            DB.getDB().source("db/schema/" + sqlFile.getName(), "fooku");
        }

        // Load test data files
        List<File> sqlFiles = FileUtil.loopFiles(new ClassPathResource("db/data").getFile(), file -> file.getName().endsWith(".sql"));
        for (File sqlFile : sqlFiles) {
            DB.getDB().source("db/data/" + sqlFile.getName(), "fooku");
        }
    }

    private static void stopMariaDB4j() {
        log.info("执行stopMariaDB4j方法");
        if (DB != null) DB.stop();
    }
}