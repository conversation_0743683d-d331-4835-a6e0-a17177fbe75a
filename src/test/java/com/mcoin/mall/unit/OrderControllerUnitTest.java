package com.mcoin.mall.unit;

import com.mcoin.mall.controller.order.OrderController;
import com.mcoin.mall.model.OrderDetailRequest;
import com.mcoin.mall.model.OrderDetailResponse;
import com.mcoin.mall.service.order.OrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class OrderControllerUnitTest extends ControllerMock {
    @Mock
    OrderService orderService;
    
    @InjectMocks
    OrderController orderController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @ParameterizedTest
    @MethodSource("orderDetailTestCases")
    void testOrderDetail(OrderDetailRequest request, String expectedResult) {
        // given
        OrderDetailResponse response = new OrderDetailResponse();
        response.setCode("A");
        when(orderService.getOrderDetail(any(OrderDetailRequest.class))).thenReturn(response);

        // when / then
        assertEquals(expectedResult, orderController.orderDetail(request).getData().getCode());
    }

    static Stream<Arguments> orderDetailTestCases() {
        return Stream.of(
            Arguments.of(new OrderDetailRequest(), "A")
        );
    }
}