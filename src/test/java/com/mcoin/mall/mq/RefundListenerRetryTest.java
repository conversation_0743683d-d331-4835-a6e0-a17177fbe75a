package com.mcoin.mall.mq;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import com.alibaba.fastjson.JSON;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.mq.model.RefundMessage;
import com.mcoin.mall.service.base.MqLocalService;
import com.mcoin.mall.service.chennel.MPayChannelService;
import com.mcoin.mall.service.chennel.RefundService;

import lombok.extern.slf4j.Slf4j;

/**
 * RefundListener 重试逻辑测试
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class RefundListenerRetryTest {

    @Mock
    private RabbitTemplate refundTemplateDelay;

    @Mock
    private RabbitTemplate refundTemplate;

    @Mock
    private MPayChannelService mPayChannelService;

    @Mock
    private RefundService refundService;

    @Mock
    private MqLocalService mqLocalService;

    @InjectMocks
    private RefundListener refundListener;

    @Captor
    private ArgumentCaptor<Message> messageCaptor;

    @BeforeEach
    void setUp() {
        // Static mocks should be handled in each test method
    }

    /**
     * 测试退款操作失败时触发重试逻辑
     * 注意：基于实际代码，RefundListener不直接处理重试逻辑，重试是通过RetryException抛出来触发的
     */
    @Test
    @DisplayName("测试退款失败抛出RetryException")
    void testRetryOnFailure() {
        // 准备测试数据
        RefundMessage refundMessage = new RefundMessage();
        refundMessage.setRefundId(10001);
        refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO");
        // 不设置delayCount和delayMax，因为这些字段在实际代码中不存在

        // 创建RabbitMQ消息
        MessageProperties messageProperties = new MessageProperties();
        Message message = new Message(JSON.toJSONBytes(refundMessage), messageProperties);

        // Mock MPayChannelService.updRefund抛出RetryException，模拟退款失败需要重试
        when(mPayChannelService.updRefund(any(RefundMessage.class))).thenThrow(new RetryException("退款重试"));

        // 调用被测试方法 - 应该不抛出异常，因为RetryException被捕获了
        refundListener.onMessage(message);
        
        // 验证updRefund方法被调用
        verify(mPayChannelService, times(1)).updRefund(any(RefundMessage.class));
        // 验证mqLocalService.updateMqLocalFinish没有被调用，因为抛出了异常
        verify(mqLocalService, times(0)).updateMqLocalFinish(any(Long.class));
    }
    
    /**
     * 测试退款成功的场景
     */
    @Test
    @DisplayName("测试退款成功")
    void testRefundSuccess() {
        // 准备测试数据
        RefundMessage refundMessage = new RefundMessage();
        refundMessage.setRefundId(10001);
        refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO");
        refundMessage.setMqLocalId(100L); // 设置mqLocalId

        // 创建RabbitMQ消息
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setReceivedDelay(2000);  // 设置延迟时间
        Message message = new Message(JSON.toJSONBytes(refundMessage), messageProperties);

        // Mock MPayChannelService.updRefund返回成功
        when(mPayChannelService.updRefund(any(RefundMessage.class))).thenReturn(1);

        // 调用被测试方法
        refundListener.onMessage(message);
        
        // 验证退款操作被调用
        verify(mPayChannelService, times(1)).updRefund(any(RefundMessage.class));
        // 验证mqLocalService.updateMqLocalFinish被调用
        verify(mqLocalService, times(1)).updateMqLocalFinish(100L);
    }
    
    /**
     * 测试退款成功但没有mqLocalId的场景
     */
    @Test
    @DisplayName("测试退款成功但没有mqLocalId")
    void testRefundSuccessWithoutMqLocalId() {
        // 准备测试数据 - 不设置mqLocalId
        RefundMessage refundMessage = new RefundMessage();
        refundMessage.setRefundId(10001);
        refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO");
        // mqLocalId为null

        // 创建RabbitMQ消息
        MessageProperties messageProperties = new MessageProperties();
        Message message = new Message(JSON.toJSONBytes(refundMessage), messageProperties);

        // Mock MPayChannelService.updRefund返回成功
        when(mPayChannelService.updRefund(any(RefundMessage.class))).thenReturn(1);

        // 调用被测试方法
        refundListener.onMessage(message);
        
        // 验证退款操作被调用
        verify(mPayChannelService, times(1)).updRefund(any(RefundMessage.class));
        // 验证mqLocalService.updateMqLocalFinish没有被调用，因为mqLocalId为null
        verify(mqLocalService, times(0)).updateMqLocalFinish(any(Long.class));
    }

    /**
     * 测试处理非RetryException异常
     */
    @Test
    @DisplayName("测试处理非RetryException异常")
    void testHandleNonRetryException() {
        // 准备测试数据
        RefundMessage refundMessage = new RefundMessage();
        refundMessage.setRefundId(10001);
        refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO");

        // 创建RabbitMQ消息
        MessageProperties messageProperties = new MessageProperties();
        Message message = new Message(JSON.toJSONBytes(refundMessage), messageProperties);

        // Mock MPayChannelService.updRefund抛出其他类型的异常
        doThrow(new RuntimeException("一般异常")).when(mPayChannelService).updRefund(any(RefundMessage.class));

        // 调用被测试方法
        refundListener.onMessage(message);
        
        // 验证updRefund方法被调用
        verify(mPayChannelService, times(1)).updRefund(any(RefundMessage.class));
        // 验证mqLocalService.updateMqLocalFinish没有被调用，因为抛出了异常
        verify(mqLocalService, times(0)).updateMqLocalFinish(any(Long.class));
    }

    /**
     * 测试事务提交后发送消息
     */
    @Test
    @DisplayName("测试事务提交后发送消息")
    void testTransactionalEventListener() {
        // 准备测试数据
        RefundMessage refundMessage = new RefundMessage();
        refundMessage.setRefundId(10001);
        refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO");
        refundMessage.setDelayTime(5000);  // 设置延迟时间
        
        // 调用被测试方法 - 这是TransactionalEventListener方法
        refundListener.onMessage(refundMessage);
        
        // 验证延迟消息被发送
        verify(refundTemplateDelay, times(1)).convertAndSend(messageCaptor.capture());
        
        // 验证消息内容和延迟时间正确
        Message capturedMessage = messageCaptor.getValue();
        assertEquals(5000, capturedMessage.getMessageProperties().getDelay());
        
        // 验证消息内容正确
        RefundMessage capturedRefundMessage = JSON.parseObject(new String(capturedMessage.getBody()), RefundMessage.class);
        assertEquals(10001, capturedRefundMessage.getRefundId());
        assertEquals("TEST_OUT_REQUEST_NO", capturedRefundMessage.getOutRequestNo());
    }

    /**
     * 测试事务提交后发送无延迟消息
     */
    @Test
    @DisplayName("测试事务提交后发送无延迟消息")
    void testTransactionalEventListenerNoDelay() {
        // 准备测试数据
        RefundMessage refundMessage = new RefundMessage();
        refundMessage.setRefundId(10002);
        refundMessage.setOutRequestNo("TEST_OUT_REQUEST_NO_2");
        refundMessage.setDelayTime(0);  // 无延迟
        
        // 调用被测试方法
        refundListener.onMessage(refundMessage);
        
        // 验证普通消息被发送
        verify(refundTemplate, times(1)).convertAndSend(messageCaptor.capture());
        
        // 验证延迟消息没有被发送
        verify(refundTemplateDelay, times(0)).convertAndSend(any(Message.class));
        
        // 验证消息内容正确
        Message capturedMessage = messageCaptor.getValue();
        RefundMessage capturedRefundMessage = JSON.parseObject(new String(capturedMessage.getBody()), RefundMessage.class);
        assertEquals(10002, capturedRefundMessage.getRefundId());
        assertEquals("TEST_OUT_REQUEST_NO_2", capturedRefundMessage.getOutRequestNo());
        assertEquals(0, capturedRefundMessage.getDelayTime());
    }

    /**
     * 测试JSON解析异常
     */
    @Test
    @DisplayName("测试JSON解析异常")
    void testJsonParseException() {
        // 创建无效JSON的消息
        MessageProperties messageProperties = new MessageProperties();
        Message message = new Message("invalid json".getBytes(), messageProperties);

        // 调用被测试方法 - 应该不抛出异常，因为异常被捕获了
        refundListener.onMessage(message);
        
        // 验证updRefund方法没有被调用，因为JSON解析失败
        verify(mPayChannelService, times(0)).updRefund(any(RefundMessage.class));
        // 验证mqLocalService.updateMqLocalFinish没有被调用
        verify(mqLocalService, times(0)).updateMqLocalFinish(any(Long.class));
    }
} 