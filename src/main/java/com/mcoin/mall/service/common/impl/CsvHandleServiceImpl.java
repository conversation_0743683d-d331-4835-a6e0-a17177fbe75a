package com.mcoin.mall.service.common.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookMiniOrderSettlement;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.MiniOrderSettlementRequest;
import com.mcoin.mall.service.common.AliyunOSSService;
import com.mcoin.mall.service.common.CsvHandleService;
import com.mcoin.mall.service.order.MiniOrderSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileReader;
import java.io.Reader;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.mcoin.mall.util.McoinMall.MINI_REFUND_ORDER_NO_PREFIX;

/**
 * @author: laijinjie
 * @date: 2023/11/16
 */
@Slf4j
@Service
public class CsvHandleServiceImpl implements CsvHandleService {

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    @Resource
    private MiniOrderSyncService miniOrderSyncService;

    @Resource
    private AliyunOSSService aliyunOSSService;

    /**
     * dev环境文件全路径：https://mpay-miniprogram-dev.oss-cn-hongkong.aliyuncs.com/lingshou/uploads/settlement/merchant_settlement_2023-11-15.csv
     *
     * @param request
     */
    @Override
    public void handleByMiniSettlement(MiniOrderSettlementRequest request) {
        log.info("零售小程序结算数据同步开始：{}", JSON.toJSONString(request));
        // 用于手动触发定时任务
        if (CollectionUtils.isNotEmpty(request.getSettlementDates())) {
            List<String> fileNames = Lists.newArrayListWithCapacity(request.getSettlementDates().size());
            String fileNamePrefix = "merchant_settlement_";
            for (String settlementDate : request.getSettlementDates()) {
                fileNames.add(fileNamePrefix + settlementDate + ".csv");
            }

            for (String name : fileNames) {
                File file = aliyunOSSService.downloadMiniFile(name);
                this.handleFile(file);
            }
        } else if (StringUtils.isBlank(request.getTestFilename())) {
            LocalDate yesterday = LocalDate.now().minusDays(1); // 获取当前日期并减去一天
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd"); // 创建日期格式化对象
            String formattedYesterday = yesterday.format(formatter); // 将前一天的日期格式化为字符串

            String fileName = "merchant_settlement_" + formattedYesterday + ".csv";
            // 读取oss的文件
            this.handleFile(aliyunOSSService.downloadMiniFile(fileName));
        } else {
            // 本地测试
            this.handleFile(new File(request.getTestFilename()));
        }
        log.info("零售小程序结算数据同步结束：{}", JSON.toJSONString(request));
    }

    private void handleFile(File file) {
        if (file == null) {
            log.error("文件为空，处理失败");
            return;
        }
        List<FookMiniOrderSettlement> miniProgramSettlementBos = Lists.newArrayList();
        List<FookMiniOrderSettlement> refundSettlementBos = Lists.newArrayList();
        try (Reader reader = new FileReader(file); CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT)) {

            List<String> headerList = Lists.newArrayList();

            int row = 0;
            for (CSVRecord cSVRecord : csvParser) {
                row++;
                if (row < 2) {
                    cSVRecord.forEach(headerList::add);
                    log.info("表头信息：{}", JSON.toJSONString(headerList));
                    continue;
                }
                String orderNo = cSVRecord.get(1);
                FookMiniOrderSettlement fookMiniOrderSettlement = new FookMiniOrderSettlement()
                        .setUuid(cSVRecord.get(0))
                        .setOrderNo(orderNo)
                        .setProductPrice(cSVRecord.get(2))
                        .setGoodsNum(StringUtils.isNotBlank(cSVRecord.get(3)) ? Integer.valueOf(cSVRecord.get(3)) : null)
                        .setBillAmount(cSVRecord.get(4))
                        .setPayAmount(cSVRecord.get(5))
                        .setPayIntegral(cSVRecord.get(6))
                        .setFeeRate(cSVRecord.get(7))
                        .setIntegralRatio(cSVRecord.get(8))
                        .setCode(cSVRecord.get(9))
                        .setCreateTime(cSVRecord.get(10))
                        .setCompleteTime(cSVRecord.get(11))
                        .setDeliveryType(StringUtils.isNotBlank(cSVRecord.get(12)) ? Integer.valueOf(cSVRecord.get(12)) : null)
                        .setSubsidyAmount(Convert.toBigDecimal(cSVRecord.get(13)));
                if (orderNo.startsWith(MINI_REFUND_ORDER_NO_PREFIX)) {
                    refundSettlementBos.add(fookMiniOrderSettlement);
                } else {
                    miniProgramSettlementBos.add(fookMiniOrderSettlement);
                }
            }
        } catch (Exception e) {
            log.error("解析零售小程序的结算文件失败", e);
        }
        log.info("解析结果,count:{}", miniProgramSettlementBos.size());
        log.info("解析退款单结果,count:{}", refundSettlementBos.size());
        this.checkAndCreate(miniProgramSettlementBos);
        this.checkAndCreateRefund(refundSettlementBos);
    }

    private void checkAndCreate(List<FookMiniOrderSettlement> fookMiniOrderSettlements) {
        if (CollectionUtils.isEmpty(fookMiniOrderSettlements)) {
            log.warn("本次解析的零售小程序的结算数据为空");
            return;
        }
        int successCount = 0;
        for (FookMiniOrderSettlement fookMiniOrderSettlement : fookMiniOrderSettlements) {
            List<FookBusinessProduct> fookBusinessProducts = fookBusinessProductDao.getBusinessProductByMpayCouponsCodeId(fookMiniOrderSettlement.getUuid());
            if (CollectionUtils.isEmpty(fookBusinessProducts)) {
                log.error("根据零售小程序唯一标识：{}查询福利不存在", fookMiniOrderSettlement.getUuid());
                continue;
            }
            if (fookBusinessProducts.size() > 1) {
                // 理论上不会出现这样的数据，如果有应该是系统异常
                log.error("根据零售小程序唯一标识：{}查询福利时，发现有多个福利有异常", fookMiniOrderSettlement.getUuid());
            }
            try {
                miniOrderSyncService.createOrderCodeByMiniProgram(fookMiniOrderSettlement, fookBusinessProducts.get(0));
                successCount++;
            } catch (BusinessException e) {
                log.warn("根据零售小程序唯一标识：{}新增订单及结算数据失败", fookMiniOrderSettlement.getUuid(), e);
            } catch (Exception e) {
                log.error("根据零售小程序唯一标识：{}新增订单及结算数据失败", fookMiniOrderSettlement.getUuid(), e);
            }
        }
        log.info("本次解析零售小程序的结算数据完成，总数：{}，成功：{}，失败：{}", fookMiniOrderSettlements.size(), successCount, fookMiniOrderSettlements.size() - successCount);
    }

    private void checkAndCreateRefund(List<FookMiniOrderSettlement> refundSettlementBos) {
        if (CollectionUtils.isEmpty(refundSettlementBos)) {
            log.info("本次解析的零售小程序的结算无退款数据");
            return;
        }
        int successCount = 0;
        for (FookMiniOrderSettlement fookMiniOrderSettlement : refundSettlementBos) {
            List<FookBusinessProduct> fookBusinessProducts = fookBusinessProductDao.getBusinessProductByMpayCouponsCodeId(fookMiniOrderSettlement.getUuid());
            if (CollectionUtils.isEmpty(fookBusinessProducts)) {
                log.error("根据零售小程序唯一标识：{}查询福利不存在", fookMiniOrderSettlement.getUuid());
                continue;
            }
            if (fookBusinessProducts.size() > 1) {
                // 理论上不会出现这样的数据，如果有应该是系统异常
                log.error("根据零售小程序唯一标识：{}查询福利时，发现有多个福利有异常", fookMiniOrderSettlement.getUuid());
            }
            try {
                miniOrderSyncService.createRefundByMiniProgram(fookMiniOrderSettlement, fookBusinessProducts.get(0));
                successCount++;
            } catch (BusinessException e) {
                log.warn("根据零售小程序唯一标识：{}新增订单及结算数据失败", fookMiniOrderSettlement.getUuid(), e);
            } catch (Exception e) {
                log.error("根据零售小程序唯一标识：{}新增订单及结算退款数据失败", fookMiniOrderSettlement.getUuid(), e);
            }
        }
        log.info("本次解析零售小程序的退款结算数据完成，总数：{}，成功：{}，失败：{}", refundSettlementBos.size(), successCount, refundSettlementBos.size() - successCount);
    }
}
