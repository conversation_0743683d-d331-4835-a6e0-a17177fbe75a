<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMacaupassCodeDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMacaupassCode">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="storeid" jdbcType="INTEGER" property="storeid" />
    <result column="macaupass_businesscode" jdbcType="VARCHAR" property="macaupassBusinesscode" />
    <result column="macaupass_storecode" jdbcType="VARCHAR" property="macaupassStorecode" />
    <result column="macaupass_terminalcode" jdbcType="VARCHAR" property="macaupassTerminalcode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, businessid, storeid, macaupass_businesscode, macaupass_storecode, macaupass_terminalcode
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_macaupass_code
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_macaupass_code
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMacaupassCode">
    insert into fook_macaupass_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="storeid != null">
        storeid,
      </if>
      <if test="macaupassBusinesscode != null">
        macaupass_businesscode,
      </if>
      <if test="macaupassStorecode != null">
        macaupass_storecode,
      </if>
      <if test="macaupassTerminalcode != null">
        macaupass_terminalcode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        #{storeid,jdbcType=INTEGER},
      </if>
      <if test="macaupassBusinesscode != null">
        #{macaupassBusinesscode,jdbcType=VARCHAR},
      </if>
      <if test="macaupassStorecode != null">
        #{macaupassStorecode,jdbcType=VARCHAR},
      </if>
      <if test="macaupassTerminalcode != null">
        #{macaupassTerminalcode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMacaupassCode">
    update fook_macaupass_code
    <set>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        storeid = #{storeid,jdbcType=INTEGER},
      </if>
      <if test="macaupassBusinesscode != null">
        macaupass_businesscode = #{macaupassBusinesscode,jdbcType=VARCHAR},
      </if>
      <if test="macaupassStorecode != null">
        macaupass_storecode = #{macaupassStorecode,jdbcType=VARCHAR},
      </if>
      <if test="macaupassTerminalcode != null">
        macaupass_terminalcode = #{macaupassTerminalcode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMacaupassCode">
    update fook_macaupass_code
    set businessid = #{businessid,jdbcType=INTEGER},
      storeid = #{storeid,jdbcType=INTEGER},
      macaupass_businesscode = #{macaupassBusinesscode,jdbcType=VARCHAR},
      macaupass_storecode = #{macaupassStorecode,jdbcType=VARCHAR},
      macaupass_terminalcode = #{macaupassTerminalcode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>