package com.mcoin.mall.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bo.UserInfoBo;
import com.mcoin.mall.constant.YesNo;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.mapping.ApiControllerMapping;
import com.mcoin.mall.model.CollectionRequest;
import com.mcoin.mall.model.CollectionResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.api.ApiCollectCntResponse;
import com.mcoin.mall.model.api.ApiCollectionCntRequest;
import com.mcoin.mall.model.api.ApiCollectionRequest;
import com.mcoin.mall.model.api.ApiCollectionResponse;
import com.mcoin.mall.service.user.UserService;
import com.mcoin.mall.util.SettlementUtil;
import com.mcoin.mall.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
public class ApiProductController {

    @Resource
    private UserService userService;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    /**
     * 收藏商品，提供给小程序
     *
     * @return
     */
    @PostMapping(value = "/api/service/auth/collection", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ApiCollectionResponse> collection(@Valid @RequestBody ApiCollectionRequest request) {
        if (Boolean.FALSE.equals(SignUtil.checkCouponSign("macaupass.coupon.secret", request))) {
            return Responses.ok(new ApiCollectionResponse(), Response.Status.FAILED.ordinal(), Response.Code.SIGNATURE_FAILED, SettlementUtil.getCodeMessage(Response.Code.SIGNATURE_FAILED.get()));
        }
        UserInfoBo userInfoBo = userService.getUserInfoByCustomId(request.getCustId());
        if (null == userInfoBo) {
            return Responses.ok(new ApiCollectionResponse(), Response.Status.FAILED.ordinal(), Response.Code.UNAUTHORIZED, SettlementUtil.getCodeMessage(Response.Code.UNAUTHORIZED.get()));
        }
        CollectionRequest req = new CollectionRequest();
        List<FookBusinessProduct> products = fookBusinessProductDao.selectList(new LambdaQueryWrapper<FookBusinessProduct>()
                .eq(FookBusinessProduct::getGoodsId, request.getGoodsId())
                .eq(FookBusinessProduct::getEnable, YesNo.YES.getValue())
        );
        if (CollectionUtils.isEmpty(products)) {
            return Responses.ok(new ApiCollectionResponse(), Response.Status.FAILED.ordinal(), Response.Code.BP_NOT_EXISTS, SettlementUtil.getCodeMessage(Response.Code.BP_NOT_EXISTS.get()));
        }
        if (products.size() > 1) {
            log.error("Multi products bind one mini goods! {}", request.getGoodsId());
            return Responses.ok(new ApiCollectionResponse(), Response.Status.FAILED.ordinal(), Response.Code.UNKNOWN_ERROR, SettlementUtil.getCodeMessage(Response.Code.UNKNOWN_ERROR.get()));
        }
        req.setId(products.get(0).getId());
        req.setType(request.getType());
        req.setIsCollect(request.getIsCollect());
        CollectionResponse response = userService.saveCollection(req, userInfoBo.getUserId());
        ApiCollectionResponse res = ApiControllerMapping.INSTANCE.toApiCollectionResponse(response);
        return Responses.ok(res);
    }

    @PostMapping(value = "/api/service/auth/collectCount", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ApiCollectCntResponse> collectCount(@Valid @RequestBody ApiCollectionCntRequest request) {
        if (Boolean.FALSE.equals(SignUtil.checkCouponSign("macaupass.coupon.secret", request))) {
            return Responses.ok(new ApiCollectCntResponse(), Response.Status.FAILED.ordinal(), Response.Code.SIGNATURE_FAILED, SettlementUtil.getCodeMessage(Response.Code.SIGNATURE_FAILED.get()));
        }
        Integer userId = null;
        if (null != request.getCustId()) {
            UserInfoBo userInfoBo = userService.getUserInfoByCustomId(request.getCustId());
            if (null != userInfoBo) {
                userId = userInfoBo.getUserId();
            } else {
                return Responses.ok(new ApiCollectCntResponse(), Response.Status.FAILED.ordinal(), Response.Code.UNAUTHORIZED, SettlementUtil.getCodeMessage(Response.Code.UNAUTHORIZED.get()));
            }
        }
        ApiCollectCntResponse res = userService.getCollections(request, userId);
        return Responses.ok(res);
    }


}
