<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookMqLocalDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookMqLocal">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="try_count" jdbcType="INTEGER" property="tryCount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="next_retry" jdbcType="TIMESTAMP" property="nextRetry" />
    <result column="max_try_count" jdbcType="INTEGER" property="maxTryCount" />
    <result column="mq_config" jdbcType="VARCHAR" property="mqConfig" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mcoin.mall.bean.FookMqLocal">
    <result column="message_body" jdbcType="LONGVARCHAR" property="messageBody" />
    <result column="attachment" jdbcType="LONGVARCHAR" property="attachment" />
  </resultMap>
  <sql id="Base_Column_List">
    id, resource_id, resource_type, template_name, try_count, `status`, create_time, 
    update_time, next_retry, max_try_count, mq_config
  </sql>
  <sql id="Blob_Column_List">
    message_body, attachment
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fook_mq_local
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fook_mq_local
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookMqLocal">
    insert into fook_mq_local
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="tryCount != null">
        try_count,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="nextRetry != null">
        next_retry,
      </if>
      <if test="maxTryCount != null">
        max_try_count,
      </if>
      <if test="mqConfig != null">
        mq_config,
      </if>
      <if test="messageBody != null">
        message_body,
      </if>
      <if test="attachment != null">
        attachment,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="tryCount != null">
        #{tryCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextRetry != null">
        #{nextRetry,jdbcType=TIMESTAMP},
      </if>
      <if test="maxTryCount != null">
        #{maxTryCount,jdbcType=INTEGER},
      </if>
      <if test="mqConfig != null">
        #{mqConfig,jdbcType=VARCHAR},
      </if>
      <if test="messageBody != null">
        #{messageBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="attachment != null">
        #{attachment,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookMqLocal">
    update fook_mq_local
    <set>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="tryCount != null">
        try_count = #{tryCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextRetry != null">
        next_retry = #{nextRetry,jdbcType=TIMESTAMP},
      </if>
      <if test="maxTryCount != null">
        max_try_count = #{maxTryCount,jdbcType=INTEGER},
      </if>
      <if test="mqConfig != null">
        mq_config = #{mqConfig,jdbcType=VARCHAR},
      </if>
      <if test="messageBody != null">
        message_body = #{messageBody,jdbcType=LONGVARCHAR},
      </if>
      <if test="attachment != null">
        attachment = #{attachment,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.mcoin.mall.bean.FookMqLocal">
    update fook_mq_local
    set resource_id = #{resourceId,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      try_count = #{tryCount,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      next_retry = #{nextRetry,jdbcType=TIMESTAMP},
      max_try_count = #{maxTryCount,jdbcType=INTEGER},
      mq_config = #{mqConfig,jdbcType=VARCHAR},
      message_body = #{messageBody,jdbcType=LONGVARCHAR},
      attachment = #{attachment,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookMqLocal">
    update fook_mq_local
    set resource_id = #{resourceId,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      try_count = #{tryCount,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      next_retry = #{nextRetry,jdbcType=TIMESTAMP},
      max_try_count = #{maxTryCount,jdbcType=INTEGER},
      mq_config = #{mqConfig,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>