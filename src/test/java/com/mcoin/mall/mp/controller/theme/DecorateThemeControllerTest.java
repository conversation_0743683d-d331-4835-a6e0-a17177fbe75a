package com.mcoin.mall.mp.controller.theme;

import com.alibaba.fastjson.JSONObject;
import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.theme.DecorateThemePageResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@Transactional
class DecorateThemeControllerTest extends BaseUnitTest {

    @Autowired
    private DecorateThemeController decorateThemeController;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
    }


    @Test
    void queryThemeConfig_ShouldReturnThemeConfig() {
        // Act
        Response<JSONObject> response = decorateThemeController.queryThemeConfig();

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void queryThemePage_ShouldReturnThemePage_WhenTypeIsValid() {
        // Arrange
        Integer type = 1;
        Integer goodsId = null;

        // Act
        Response<DecorateThemePageResponse> response = decorateThemeController.queryThemePage(type, goodsId);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void queryThemePage_ShouldReturnThemePage_WhenTypeAndGoodsIdAreValid() {
        // Arrange
        Integer type = 1;
        Integer goodsId = 123;

        // Act
        Response<DecorateThemePageResponse> response = decorateThemeController.queryThemePage(type, goodsId);

        // Assert
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
    }

    @Test
    void queryThemePage_ShouldThrowException_WhenTypeIsNull() {
        // Arrange
        Integer goodsId = 123;

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            decorateThemeController.queryThemePage(null, goodsId);
        });
    }
} 