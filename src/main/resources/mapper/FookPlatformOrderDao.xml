<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookPlatformOrderDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookPlatformOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="areaid" jdbcType="INTEGER" property="areaid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="sellerid" jdbcType="INTEGER" property="sellerid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="payment_type" jdbcType="INTEGER" property="paymentType" />
    <result column="payment_transaction" jdbcType="VARCHAR" property="paymentTransaction" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="order_transaction" jdbcType="VARCHAR" property="orderTransaction" />
    <result column="mpayintegral" jdbcType="INTEGER" property="mpayintegral" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="point_ratio" jdbcType="INTEGER" property="pointRatio" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="total_amount_currency" jdbcType="VARCHAR" property="totalAmountCurrency" />
    <result column="transaction_amount" jdbcType="DECIMAL" property="transactionAmount" />
    <result column="transaction_amount_currency" jdbcType="VARCHAR" property="transactionAmountCurrency" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="payment_amount_currency" jdbcType="VARCHAR" property="paymentAmountCurrency" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="query_mark" jdbcType="INTEGER" property="queryMark" />
    <result column="account_suffix" jdbcType="VARCHAR" property="accountSuffix" />
    <result column="expiration_month" jdbcType="INTEGER" property="expirationMonth" />
    <result column="expiration_year" jdbcType="INTEGER" property="expirationYear" />
    <result column="card_type" jdbcType="VARCHAR" property="cardType" />
    <result column="credit_cardid" jdbcType="INTEGER" property="creditCardid" />
    <result column="reason_code" jdbcType="VARCHAR" property="reasonCode" />
    <result column="process_type" jdbcType="INTEGER" property="processType" />
    <result column="bank_charges" jdbcType="DECIMAL" property="bankCharges" />
    <result column="bank_settlement_amount" jdbcType="DECIMAL" property="bankSettlementAmount" />
    <result column="ordercode_id" jdbcType="INTEGER" property="ordercodeId" />
    <result column="refundid" jdbcType="INTEGER" property="refundid" />
    <result column="ordercode_status" jdbcType="INTEGER" property="ordercodeStatus" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="is_mpay" jdbcType="VARCHAR" property="isMpay" />
    <result column="is_member" jdbcType="TINYINT" property="isMember" />
    <result column="memberintegral" jdbcType="DECIMAL" property="memberintegral" />
    <result column="is_voucher" jdbcType="TINYINT" property="isVoucher" />
    <result column="mpay_coupons_status" jdbcType="INTEGER" property="mpayCouponsStatus" />
    <result column="mpay_coupons_code" jdbcType="VARCHAR" property="mpayCouponsCode" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
    <result column="session_id" jdbcType="INTEGER" property="sessionId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, areaid, userid, sellerid, `type`, order_no, create_time, payment_type, payment_transaction, 
    `status`, complete_time, order_transaction, mpayintegral, score, platform, order_amount, 
    point_ratio, currency, total_amount, total_amount_currency, transaction_amount, transaction_amount_currency, 
    payment_amount, payment_amount_currency, payment_time, query_mark, account_suffix, 
    expiration_month, expiration_year, card_type, credit_cardid, reason_code, process_type, 
    bank_charges, bank_settlement_amount, ordercode_id, refundid, ordercode_status, refund_status, 
    is_mpay, is_member, memberintegral, is_voucher, mpay_coupons_status, mpay_coupons_code, 
    subsidy_amount, session_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_platform_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_platform_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookPlatformOrder">
    insert into fook_platform_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areaid != null">
        areaid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="sellerid != null">
        sellerid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="paymentTransaction != null">
        payment_transaction,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="orderTransaction != null">
        order_transaction,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="pointRatio != null">
        point_ratio,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="totalAmountCurrency != null">
        total_amount_currency,
      </if>
      <if test="transactionAmount != null">
        transaction_amount,
      </if>
      <if test="transactionAmountCurrency != null">
        transaction_amount_currency,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="paymentAmountCurrency != null">
        payment_amount_currency,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="queryMark != null">
        query_mark,
      </if>
      <if test="accountSuffix != null">
        account_suffix,
      </if>
      <if test="expirationMonth != null">
        expiration_month,
      </if>
      <if test="expirationYear != null">
        expiration_year,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="creditCardid != null">
        credit_cardid,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="processType != null">
        process_type,
      </if>
      <if test="bankCharges != null">
        bank_charges,
      </if>
      <if test="bankSettlementAmount != null">
        bank_settlement_amount,
      </if>
      <if test="ordercodeId != null">
        ordercode_id,
      </if>
      <if test="refundid != null">
        refundid,
      </if>
      <if test="ordercodeStatus != null">
        ordercode_status,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="isMpay != null">
        is_mpay,
      </if>
      <if test="isMember != null">
        is_member,
      </if>
      <if test="memberintegral != null">
        memberintegral,
      </if>
      <if test="isVoucher != null">
        is_voucher,
      </if>
      <if test="mpayCouponsStatus != null">
        mpay_coupons_status,
      </if>
      <if test="mpayCouponsCode != null">
        mpay_coupons_code,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areaid != null">
        #{areaid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="sellerid != null">
        #{sellerid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentTransaction != null">
        #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderTransaction != null">
        #{orderTransaction,jdbcType=VARCHAR},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountCurrency != null">
        #{totalAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="transactionAmount != null">
        #{transactionAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionAmountCurrency != null">
        #{transactionAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmountCurrency != null">
        #{paymentAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="queryMark != null">
        #{queryMark,jdbcType=INTEGER},
      </if>
      <if test="accountSuffix != null">
        #{accountSuffix,jdbcType=VARCHAR},
      </if>
      <if test="expirationMonth != null">
        #{expirationMonth,jdbcType=INTEGER},
      </if>
      <if test="expirationYear != null">
        #{expirationYear,jdbcType=INTEGER},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="creditCardid != null">
        #{creditCardid,jdbcType=INTEGER},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="processType != null">
        #{processType,jdbcType=INTEGER},
      </if>
      <if test="bankCharges != null">
        #{bankCharges,jdbcType=DECIMAL},
      </if>
      <if test="bankSettlementAmount != null">
        #{bankSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="ordercodeId != null">
        #{ordercodeId,jdbcType=INTEGER},
      </if>
      <if test="refundid != null">
        #{refundid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeStatus != null">
        #{ordercodeStatus,jdbcType=INTEGER},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="isMpay != null">
        #{isMpay,jdbcType=VARCHAR},
      </if>
      <if test="isMember != null">
        #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="isVoucher != null">
        #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="mpayCouponsStatus != null">
        #{mpayCouponsStatus,jdbcType=INTEGER},
      </if>
      <if test="mpayCouponsCode != null">
        #{mpayCouponsCode,jdbcType=VARCHAR},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookPlatformOrder">
    update fook_platform_order
    <set>
      <if test="areaid != null">
        areaid = #{areaid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="sellerid != null">
        sellerid = #{sellerid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentTransaction != null">
        payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderTransaction != null">
        order_transaction = #{orderTransaction,jdbcType=VARCHAR},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="pointRatio != null">
        point_ratio = #{pointRatio,jdbcType=INTEGER},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountCurrency != null">
        total_amount_currency = #{totalAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="transactionAmount != null">
        transaction_amount = #{transactionAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionAmountCurrency != null">
        transaction_amount_currency = #{transactionAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmountCurrency != null">
        payment_amount_currency = #{paymentAmountCurrency,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="queryMark != null">
        query_mark = #{queryMark,jdbcType=INTEGER},
      </if>
      <if test="accountSuffix != null">
        account_suffix = #{accountSuffix,jdbcType=VARCHAR},
      </if>
      <if test="expirationMonth != null">
        expiration_month = #{expirationMonth,jdbcType=INTEGER},
      </if>
      <if test="expirationYear != null">
        expiration_year = #{expirationYear,jdbcType=INTEGER},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="creditCardid != null">
        credit_cardid = #{creditCardid,jdbcType=INTEGER},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="processType != null">
        process_type = #{processType,jdbcType=INTEGER},
      </if>
      <if test="bankCharges != null">
        bank_charges = #{bankCharges,jdbcType=DECIMAL},
      </if>
      <if test="bankSettlementAmount != null">
        bank_settlement_amount = #{bankSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="ordercodeId != null">
        ordercode_id = #{ordercodeId,jdbcType=INTEGER},
      </if>
      <if test="refundid != null">
        refundid = #{refundid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeStatus != null">
        ordercode_status = #{ordercodeStatus,jdbcType=INTEGER},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="isMpay != null">
        is_mpay = #{isMpay,jdbcType=VARCHAR},
      </if>
      <if test="isMember != null">
        is_member = #{isMember,jdbcType=TINYINT},
      </if>
      <if test="memberintegral != null">
        memberintegral = #{memberintegral,jdbcType=DECIMAL},
      </if>
      <if test="isVoucher != null">
        is_voucher = #{isVoucher,jdbcType=TINYINT},
      </if>
      <if test="mpayCouponsStatus != null">
        mpay_coupons_status = #{mpayCouponsStatus,jdbcType=INTEGER},
      </if>
      <if test="mpayCouponsCode != null">
        mpay_coupons_code = #{mpayCouponsCode,jdbcType=VARCHAR},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookPlatformOrder">
    update fook_platform_order
    set areaid = #{areaid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      sellerid = #{sellerid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      payment_type = #{paymentType,jdbcType=INTEGER},
      payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      order_transaction = #{orderTransaction,jdbcType=VARCHAR},
      mpayintegral = #{mpayintegral,jdbcType=INTEGER},
      score = #{score,jdbcType=DECIMAL},
      platform = #{platform,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      point_ratio = #{pointRatio,jdbcType=INTEGER},
      currency = #{currency,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      total_amount_currency = #{totalAmountCurrency,jdbcType=VARCHAR},
      transaction_amount = #{transactionAmount,jdbcType=DECIMAL},
      transaction_amount_currency = #{transactionAmountCurrency,jdbcType=VARCHAR},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      payment_amount_currency = #{paymentAmountCurrency,jdbcType=VARCHAR},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      query_mark = #{queryMark,jdbcType=INTEGER},
      account_suffix = #{accountSuffix,jdbcType=VARCHAR},
      expiration_month = #{expirationMonth,jdbcType=INTEGER},
      expiration_year = #{expirationYear,jdbcType=INTEGER},
      card_type = #{cardType,jdbcType=VARCHAR},
      credit_cardid = #{creditCardid,jdbcType=INTEGER},
      reason_code = #{reasonCode,jdbcType=VARCHAR},
      process_type = #{processType,jdbcType=INTEGER},
      bank_charges = #{bankCharges,jdbcType=DECIMAL},
      bank_settlement_amount = #{bankSettlementAmount,jdbcType=DECIMAL},
      ordercode_id = #{ordercodeId,jdbcType=INTEGER},
      refundid = #{refundid,jdbcType=INTEGER},
      ordercode_status = #{ordercodeStatus,jdbcType=INTEGER},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      is_mpay = #{isMpay,jdbcType=VARCHAR},
      is_member = #{isMember,jdbcType=TINYINT},
      memberintegral = #{memberintegral,jdbcType=DECIMAL},
      is_voucher = #{isVoucher,jdbcType=TINYINT},
      mpay_coupons_status = #{mpayCouponsStatus,jdbcType=INTEGER},
      mpay_coupons_code = #{mpayCouponsCode,jdbcType=VARCHAR},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      session_id = #{sessionId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>