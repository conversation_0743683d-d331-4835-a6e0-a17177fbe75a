<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookProductStockLogDao">
  <select id="selectFirstOrderById" resultMap="BaseResultMap" >
    select <include refid="Base_Column_List"/>
    from fook_product_stock_log
    where product_id = #{productId,jdbcType=INTEGER}
    order by id desc
    limit 1
  </select>
</mapper>