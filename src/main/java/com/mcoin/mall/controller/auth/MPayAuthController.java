package com.mcoin.mall.controller.auth;

import java.util.Map;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import com.mcoin.mall.model.ConfigMPayJSApiRequest;
import com.mcoin.mall.model.ConfigMPayJSApiResponse;
import com.mcoin.mall.model.CouponLoginRequest;
import com.mcoin.mall.model.CouponLoginResponse;
import com.mcoin.mall.model.GetTokenRequest;
import com.mcoin.mall.model.GetTokenResponse;
import com.mcoin.mall.model.GetUserInfoResponse;
import com.mcoin.mall.model.MPayIndexLoginRequest;
import com.mcoin.mall.model.MPayLoginRequest;
import com.mcoin.mall.model.RefreshTokenResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.UserProfileResponse;
import com.mcoin.mall.service.auth.MPayAuthService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.StrUtil;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@RestController
public class MPayAuthController {

    @Autowired
    private MPayAuthService mPayAuthService;
    @Resource
    private RetryTemplate retryTemplate;

    /**
     * 授權登錄
     *
     * @return
     */
    @GetMapping(value = "/macaupasslogin", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RedirectView authLogin(@Valid MPayLoginRequest request, RedirectAttributes attributes, @RequestParam(required = false) Map<String, String> paramsMap) {
        String scope = ConfigUtils.getProperty("macaupass.SCOPE");
        String redirectUrl = mPayAuthService.getRedirectUrl(scope, StrUtil.generateCode(32, 0), request.getSource(), request.getOauthParams(),paramsMap);
        log.info("MacauPassLogin url={}", redirectUrl);
        return new RedirectView(redirectUrl);
    }

    /**
     * 券详情授权登录
     *
     * @return
     */
    @PostMapping(value = "/couponlogin", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<CouponLoginResponse> couponLogin(@Valid @RequestBody CouponLoginRequest request) {
        CouponLoginResponse response = mPayAuthService.couponLogin(request);
        return Responses.ok(response);
    }

    /**
     * MPay首頁推廣 登錄
     *
     * @return
     */
    @GetMapping(value = "/macaupasspromote", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RedirectView mPayIndexLogin(@Valid MPayIndexLoginRequest request ,@RequestParam(required = false) Map<String, String> paramsMap) {

        String type = request.getType();
        String id = request.getId();
        String state = null;
        Integer skipIndex = request.getSkipIndex();
        //tabNum不為空，前端做區分是banner跳轉或消息推送跳轉進入mcoin，作返回首頁判斷用
        if ("mycoupon".equals(type)) {
            state = type;
        } else {
            if (null != skipIndex && 1 == skipIndex) {
                state = type + "_link/" + id;
            } else {
                state = type + "/" + id;
            }
        }

        String scope = ConfigUtils.getProperty("macaupass.SCOPE");
        String redirectUrl = mPayAuthService.getRedirectUrl(scope, state, request.getSource(), request.getOauthParams(),paramsMap);
        log.info("mPayIndexLogin url={}", redirectUrl);
        return new RedirectView(redirectUrl);
    }

    /**
     * 獲取用戶信息
     *
     * @return
     */
    @GetMapping(value = "/api/auth/basicinformation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<GetUserInfoResponse> getUserInfo() {
        GetUserInfoResponse response = retryTemplate.execute((e) -> mPayAuthService.updAndGetUserInfo());
        return Responses.ok(response);
    }

    /**
     * 獲取JSAPI列表
     *
     * @return
     */
    @PostMapping(value = "/macaupassconfig", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<ConfigMPayJSApiResponse> configJSApi(@Valid @RequestBody ConfigMPayJSApiRequest request) {
        ConfigMPayJSApiResponse response = mPayAuthService.saveAndGetJSApi(request.getUrl());
        return Responses.ok(response);
    }

    /**
     * 保存用戶信息並獲取Token
     *
     * @return
     */
    @GetMapping(value = "/macaupasssave", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<GetTokenResponse> getToken(GetTokenRequest request) {
        GetTokenResponse response = mPayAuthService.saveAndGetToken(request);
        return Responses.ok(response);
    }

    /**
     * 刷新Token
     *
     * @return
     */
    @PostMapping(value = "/api/business/refresh", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<RefreshTokenResponse> refreshToken() {
        RefreshTokenResponse response = mPayAuthService.refreshToken();
        return Responses.ok(response);
    }

    /**
     * 获取用户昵称和头像
     *
     * @return 用户昵称和头像信息
     */
    @PostMapping(value = "/api/auth/userinfo", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<UserProfileResponse> getUserProfile() {
        UserProfileResponse response = mPayAuthService.updAndGetUserProfile();
        return Responses.ok(response);
    }

}
