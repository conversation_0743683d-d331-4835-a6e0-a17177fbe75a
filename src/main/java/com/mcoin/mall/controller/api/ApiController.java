package com.mcoin.mall.controller.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mcoin.mall.bean.FookBusinessProduct;
import com.mcoin.mall.bean.FookMacaupassCode;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookBusinessProductDao;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.dao.FookMacaupassCodeDao;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.SettlementCtx;
import com.mcoin.mall.model.api.*;
import com.mcoin.mall.service.api.OrderApiService;
import com.mcoin.mall.service.order.OrderCodeRedeemService;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.SettlementUtil;
import com.mcoin.mall.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.MessageSource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
public class ApiController {

    @Resource
    private OrderCodeRedeemService orderCodeRedeemService;

    @Resource
    private MessageSource messageSource;

    @Resource
    private ContextHolder contextHolder;

    @Resource
    private FookMacaupassCodeDao fookMacaupassCodeDao;

    @Resource
    private OrderApiService orderApiService;

    @Resource
    private FookBusinessProductDao fookBusinessProductDao;

    @Resource
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Resource
    private FookMacaupassUserDao fookMacaupassUserDao;
    @Resource
    private ObjectMapper jacksonObjectMapper;


    /**
     * 核销
     *
     * @return
     */
    @PostMapping(value = "/mpay/redeemvoucher")
    @ResponseBody
    public Response<RedeemVoucherResponse> redeemvoucher(HttpServletRequest req) {
        RedeemVoucherRequest request = null;
        String contentType = req.getContentType();
        if (contentType != null && contentType.contains("application/json")) {
            try {
                request = jacksonObjectMapper.readValue(req.getInputStream(), RedeemVoucherRequest.class);
            } catch (IOException e) {
                log.error("read input stream error!", e);
                throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
            }
        } else if (contentType != null && contentType.contains("application/x-www-form-urlencoded")) {
            Map<String, String> formParams = ServletUtil.getParamMap(req);
            try {
                String json = jacksonObjectMapper.writeValueAsString(formParams);
                request = jacksonObjectMapper.readValue(json, RedeemVoucherRequest.class);
            } catch (IOException e) {
                log.error("read input stream error!", e);
                throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
            }
        } else {
            log.error("Unsupport content type ! {}", contentType);
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }
        RedeemVoucherResponse response = new RedeemVoucherResponse();
        if (Boolean.FALSE.equals(SignUtil.checkSign("api.redeem.SECRET", request))) {
            return Responses.ok(response, Response.Status.FAILED.ordinal(), Response.Code.SIGNATURE_FAILED, SettlementUtil.getCodeMessage(Response.Code.SIGNATURE_FAILED.get()));
        }
        String branchCode = StrUtil.replaceFirst(request.getBranchCode(), request.getMerchantCode(), "");
        FookMacaupassCode passCode = this.fookMacaupassCodeDao.selectOne(new LambdaQueryWrapper<FookMacaupassCode>()
                .eq(FookMacaupassCode::getMacaupassBusinesscode, request.getMerchantCode())
                .eq(FookMacaupassCode::getMacaupassStorecode, branchCode)
                .last("limit 1")
        );
        if (null == passCode) {
            return Responses.ok(response, Response.Status.FAILED.ordinal(), Response.Code.ST_STORE_NOT_EXIST, SettlementUtil.getCodeMessage(Response.Code.ST_STORE_NOT_EXIST.get()));
        }
        SettlementCtx ctx = new SettlementCtx();
        ctx.setCode(request.getVoucherCode());
        ctx.setStoresId(passCode.getStoreid());
        ctx.setExternalRefNo(request.getExternalRefNo());
        ctx.setBranchCode(request.getBranchCode());
        ctx.setMerchantCode(request.getMerchantCode());
        ctx.setCallbackMsg(request.getCallbackMsg());
        ctx.setTerminalCode(request.getTerminalCode());
        ctx.setUserId(-1);
        ctx.setMode(4);
        ctx.setPassCheck(this.orderApiService.getPassCheck(request.getVoucherCode()));
        ctx.setTrackingNo(request.getTrackingNo());
        ctx.setBusinessRedeemTime(request.getBusinessRedeemTime());
        ctx.setWithDraw(false);
        return this.orderCodeRedeemService.settlementWithDetail(ctx);
    }

    /**
     * 优惠券查询
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/mpay/checkvoucher")
    @ResponseBody
    public Response<CheckvoucherResponse> checkvoucher(HttpServletRequest req) {
        CheckvoucherRequest request = null;
        String contentType = req.getContentType();
        if (contentType != null && contentType.contains("application/json")) {
            try {
                request = jacksonObjectMapper.readValue(req.getInputStream(), CheckvoucherRequest.class);
            } catch (IOException e) {
                log.error("read input stream error!", e);
                throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
            }
        } else if (contentType != null && contentType.contains("application/x-www-form-urlencoded")) {
            Map<String, String> formParams = ServletUtil.getParamMap(req);
            try {
                String json = jacksonObjectMapper.writeValueAsString(formParams);
                request = jacksonObjectMapper.readValue(json, CheckvoucherRequest.class);
            } catch (IOException e) {
                log.error("read input stream error!", e);
                throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
            }
        } else {
            log.error("Unsupport content type ! {}", contentType);
            throw new BusinessException(Response.Code.UNKNOWN_ERROR, "system error");
        }

        if (Boolean.FALSE.equals(SignUtil.checkSign("api.redeem.SECRET", request))) {
            return Responses.ok(new CheckvoucherResponse(), Response.Status.FAILED.ordinal(), Response.Code.SIGNATURE_FAILED, SettlementUtil.getCodeMessage(Response.Code.SIGNATURE_FAILED.get()));
        }
        CheckvoucherResponse response = this.orderApiService.getOrderDetail(request.getVoucherCode());
        return Responses.ok(response);
    }

    /**
     * 核销通知
     * @param request
     * @return
     */
    @PostMapping(value = "/mcoin/mpaycouponsoffcode")
    @ResponseBody
    public MpayRedeemVoucherResponse mpayRedeemVoucher(@Valid MpayRedeemVoucherRequest request) {
        String secret = ConfigUtils.getProperty("api.mpay.redeem.SECRET");
        String tmpSig = request.getSign();
        request.setSign(null);
        boolean signResult = SignUtil.checkSign(secret, request, tmpSig);
        request.setSign(tmpSig);
        if (Boolean.FALSE.equals(signResult)) {
            MpayRedeemVoucherResponse res = new MpayRedeemVoucherResponse();
            res.setRspcod(Response.Code.SIGNATURE_FAILED.get());
            res.setRspmsg(SettlementUtil.getCodeMessage(Response.Code.SIGNATURE_FAILED.get()));
            MpayRedeemVoucherResponse.Ddata d = new MpayRedeemVoucherResponse.Ddata();
            d.setResultcode("0");
            d.setResultmsg(SettlementUtil.getCodeMessage(Response.Code.SIGNATURE_FAILED.get()));
            res.setData(d);
            return res;
        }
        Integer userid = null;
        FookMacaupassUser customUser = this.fookMacaupassUserDao.getFirstMpayUserByCustomId(request.getUserid());
        if (null != customUser) {
            userid = customUser.getUserId();
        } else {
            MpayRedeemVoucherResponse res = new MpayRedeemVoucherResponse();
            res.setRspcod(Response.Code.ST_NOT_EXIST.get());
            res.setRspmsg(SettlementUtil.getCodeMessage(Response.Code.ST_NOT_EXIST.get()));
            MpayRedeemVoucherResponse.Ddata d = new MpayRedeemVoucherResponse.Ddata();
            d.setResultcode("0");
            d.setResultmsg(SettlementUtil.getCodeMessage(Response.Code.ST_NOT_EXIST.get()));
            res.setData(d);
            return res;
        }
        FookBusinessProduct product = this.fookBusinessProductDao.selectOne(new LambdaQueryWrapper<FookBusinessProduct>()
                .eq(FookBusinessProduct::getMpayCouponsCodeId, request.getProductid())
                .last("limit 1"));
        MpayRedeemVoucherResponse response = new MpayRedeemVoucherResponse();
        if (null != product) {
            List<Integer> storesIdByProductIds = this.fookBusinessStoreProductDao.getStoreIdsByBusinessId(product.getBusinessid());
            if (CollectionUtils.isNotEmpty(storesIdByProductIds)) {
                if (storesIdByProductIds.size() > 1) {
                    log.error("More than 1 stores in this productid {}", request.getProductid());
                }
                Integer storeid = storesIdByProductIds.get(0);
                SettlementCtx ctx = new SettlementCtx();
                ctx.setCode(request.getCode());
                ctx.setUserId(userid);
                ctx.setMode(6);
                ctx.setStoresId(storeid);
                ctx.setPassCheck(true);
                ctx.setSettlementTime(DateUtil.parse(request.getSettlement_time(), "yyyy-MM-dd HH:mm:ss"));
                if (request.getStatus() == 2) {
                    ctx.setWithDraw(false);
                } else if (request.getStatus() == 1) {
                    ctx.setWithDraw(true);
                }
                response = this.orderCodeRedeemService.mpaySettlementWithDetail(ctx);
            } else {
                log.error("Store does not exists!. business id {}", product.getBusinessid());
                response.setRspcod(Response.Code.ST_STORE_NOT_EXIST.get());
                response.setRspmsg(SettlementUtil.getCodeMessage(Response.Code.ST_STORE_NOT_EXIST.get()));
                MpayRedeemVoucherResponse.Ddata d = new MpayRedeemVoucherResponse.Ddata();
                d.setResultcode("0");
                d.setResultmsg(SettlementUtil.getCodeMessage(Response.Code.ST_STORE_NOT_EXIST.get()));
                response.setData(d);
            }
        } else {
            log.error("product does not exists!. product id {}", request.getProductid());
            response.setRspcod(Response.Code.ST_STORE_NOT_EXIST.get());
            response.setRspmsg(SettlementUtil.getCodeMessage(Response.Code.ST_STORE_NOT_EXIST.get()));
            MpayRedeemVoucherResponse.Ddata d = new MpayRedeemVoucherResponse.Ddata();
            d.setResultcode("0");
            d.setResultmsg(SettlementUtil.getCodeMessage(Response.Code.ST_STORE_NOT_EXIST.get()));
            response.setData(d);
        }
        return response;
    }

}
