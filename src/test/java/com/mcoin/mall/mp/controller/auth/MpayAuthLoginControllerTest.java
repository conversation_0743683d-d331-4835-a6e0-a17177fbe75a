package com.mcoin.mall.mp.controller.auth;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.fastjson.JSON;
import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.client.model.MPayAccessTokenResponse;
import com.mcoin.mall.mp.client.model.MPayUserInfoResponse;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.auth.MpayAuthRequest;
import com.mcoin.mall.mp.model.auth.MpayAuthResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Transactional
public class MpayAuthLoginControllerTest extends BaseUnitTest {

    @Autowired
    private MpayAuthLoginController mpayAuthLoginController;

    @BeforeEach
    public void setUp() throws BlockException {
        // Mock MPayClient responses
        MPayAccessTokenResponse accessTokenResponse = new MPayAccessTokenResponse();
        accessTokenResponse.setAccessToken("mock_access_token");
        accessTokenResponse.setOpenid("mock_openid");
        when(mPayClient.accessToken(anyString(), anyString(), anyString()))
                .thenReturn(JSON.toJSONString(accessTokenResponse));

        MPayUserInfoResponse userInfoResponse = new MPayUserInfoResponse();
        userInfoResponse.setOpenid("mock_openid");
        userInfoResponse.setCustId("mock_cust_id");
        userInfoResponse.setUnionid("mock_unionid");
        userInfoResponse.setNickname("mock_nickname");
        userInfoResponse.setHeadimgurl("mock_headimgurl");
        when(mPayClient.getUserInfo(anyString(), anyString()))
                .thenReturn(JSON.toJSONString(userInfoResponse));

        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setUserId(1);
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
    }

    @Test
    @Order(1)
    void mpayAuthLogin_ShouldReturnSuccess_WhenValidCode() throws BlockException {
        // 准备请求数据
        MpayAuthRequest request = new MpayAuthRequest();
        request.setCode("valid_code");

        // 执行请求
        Response<MpayAuthResponse> response = mpayAuthLoginController.mpayAuthLogin(request);

        // 验证响应
        assertNotNull(response);
        assertEquals(Response.Code.OK.getCode(), response.getCode());
        assertNotNull(response.getData());
        
        // 验证用户信息
        MpayAuthResponse data = response.getData();
        assertNotNull(data.getId());
        assertNotNull(data.getSn());
        assertNotNull(data.getCode());
        assertEquals("mock_nickname", data.getNickname());
        assertEquals("mock_headimgurl", data.getAvatar());
        assertNotNull(data.getToken());
        assertNotNull(data.getUpdateTime());
        assertNotNull(data.getCreateTime());
        assertNotNull(data.getSid());
        assertEquals(8, data.getRegisterSource());
        assertEquals(1, data.getIsNewUser());
    }

    @Test
    @Order(2)
    void mpayAuthLogin_ShouldThrowException_WhenCodeIsEmpty() throws BlockException {
        // 准备请求数据 - code为空
        MpayAuthRequest request = new MpayAuthRequest();
        request.setCode("");

        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> mpayAuthLoginController.mpayAuthLogin(request));
        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals("code缺少", exception.getMessage());
    }

    @Test
    @Order(3)
    void mpayAuthLogin_ShouldThrowException_WhenCodeIsNull() throws BlockException {
        // 准备请求数据 - code为null
        MpayAuthRequest request = new MpayAuthRequest();
        request.setCode(null);

        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> mpayAuthLoginController.mpayAuthLogin(request));
        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals("code缺少", exception.getMessage());
    }

    @Test
    @Order(4)
    void mpayAuthLogin_ShouldHandleAccessTokenFailure() throws BlockException {
        // Mock accessToken failure
        when(mPayClient.accessToken(anyString(), anyString(), anyString()))
                .thenReturn(null);

        // 准备请求数据
        MpayAuthRequest request = new MpayAuthRequest();
        request.setCode("valid_code");

        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> mpayAuthLoginController.mpayAuthLogin(request));
        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals("not login", exception.getMessage());
    }

    @Test
    @Order(5)
    void mpayAuthLogin_ShouldHandleUserInfoFailure() throws BlockException {
        // Mock getUserInfo failure
        when(mPayClient.getUserInfo(anyString(), anyString()))
                .thenReturn(null);

        // 准备请求数据
        MpayAuthRequest request = new MpayAuthRequest();
        request.setCode("valid_code");

        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> mpayAuthLoginController.mpayAuthLogin(request));
        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals("not login", exception.getMessage());
    }

    @Test
    @Order(6)
    void mpayAuthLogin_ShouldHandleInvalidAccessTokenResponse() throws BlockException {
        // Mock invalid accessToken response
        MPayAccessTokenResponse accessTokenResponse = new MPayAccessTokenResponse();
        accessTokenResponse.setAccessToken("");
        accessTokenResponse.setOpenid("");
        when(mPayClient.accessToken(anyString(), anyString(), anyString()))
                .thenReturn(JSON.toJSONString(accessTokenResponse));

        // 准备请求数据
        MpayAuthRequest request = new MpayAuthRequest();
        request.setCode("valid_code");

        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> mpayAuthLoginController.mpayAuthLogin(request));
        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals("not login", exception.getMessage());
    }

    @Test
    @Order(7)
    void mpayAuthLogin_ShouldHandleInvalidUserInfoResponse() throws BlockException {
        // Mock invalid userInfo response
        MPayUserInfoResponse userInfoResponse = new MPayUserInfoResponse();
        userInfoResponse.setOpenid("");
        when(mPayClient.getUserInfo(anyString(), anyString()))
                .thenReturn(JSON.toJSONString(userInfoResponse));

        // 准备请求数据
        MpayAuthRequest request = new MpayAuthRequest();
        request.setCode("valid_code");

        // 执行请求并验证异常
        Response<MpayAuthResponse> response = mpayAuthLoginController.mpayAuthLogin(request);
        assertEquals(Response.Code.OK.getCode(), response.getCode());

    }
}