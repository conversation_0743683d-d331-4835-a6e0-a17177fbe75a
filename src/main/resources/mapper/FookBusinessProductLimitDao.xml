<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductLimitDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessProductLimit">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="productid" jdbcType="INTEGER" property="productid" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="stock" jdbcType="INTEGER" property="stock" />
    <result column="limit_number" jdbcType="INTEGER" property="limitNumber" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, productid, `status`, stock, limit_number, start_time, end_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_product_limit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_product_limit
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessProductLimit">
    insert into fook_business_product_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productid != null">
        productid,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="limitNumber != null">
        limit_number,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="productid != null">
        #{productid,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=INTEGER},
      </if>
      <if test="limitNumber != null">
        #{limitNumber,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessProductLimit">
    update fook_business_product_limit
    <set>
      <if test="productid != null">
        productid = #{productid,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=INTEGER},
      </if>
      <if test="limitNumber != null">
        limit_number = #{limitNumber,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessProductLimit">
    update fook_business_product_limit
    set productid = #{productid,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      stock = #{stock,jdbcType=INTEGER},
      limit_number = #{limitNumber,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>