# åºç¨åç§°
spring.application.name=mcoin-mall
# ç¯å¢
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
spring.main.allow-circular-references=true
spring.profiles.active=@spring.profiles.active@
# Actuator Web è®¿é®ç«¯å£
management.server.port=<EMAIL>@
management.endpoints.web.base-path=/${spring.application.name}/actuator
management.endpoints.jmx.exposure.include=*
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.metrics.tags.application=${spring.application.name}
management.health.mail.enabled=false
# æ¯å¦åè®¸ææçHTTPè¯·æ±(GET|POST|PUT|DELETEç­)è¿è¡éè¯, é»è®¤å¼æ¯false, å³åªåè®¸GETè¯·æ±éè¯
spring.cloud.loadbalancer.retry.enabled=false
# å¯¹å½åå®ä¾æå¤§éè¯æ¬¡æ°, é»è®¤å¼ä¸º0(éè¯æ¬¡æ°ä¸åæ¬ç¬¬ä¸æ¬¡è¯·æ±)
spring.cloud.loadbalancer.retry.max-retries-on-same-service-instance=0
# åæ¢å®ä¾æå¤§éè¯æ¬¡æ°, é»è®¤å¼1
# å¦æè®¿é®å½åå®ä¾å¼å¸¸, ä¼åæ¬¡å°è¯è®¿é®å½åå®ä¾(æ¬¡æ°ç±MaxAutoRetrieså³å®), å¦æè¿ä¸è¡, å°±ä¼è®¿é®ä¸ä¸ä¸ªå®ä¾; å¦æä»ç¶ä¸è¡, ä¼æä¸ä¸ä¸ªå®ä¾ä½ä¸ºå½åå®ä¾å¹¶éè¯(æ¬¡æ°ç±MaxAutoRetrieså³å®)...ä¾æ­¤ç±»æ¨, ç´å°åæ¢å®ä¾æ¬¡æ°è¾¾å°ä¸é(ç±MaxAutoRetriesNextServerå³å®)
spring.cloud.loadbalancer.retry.max-retries-on-next-service-instance=1
# Connect timeout used by Apache HttpClient, default 1000ms
feign.client.config.default.connect-timeout=2000
# Read timeout used by Apache HttpClient, default 1000ms
feign.client.config.default.read-timeout=5000

feign.client.config.mPayCouponClient.logger-level=FULL
feign.client.config.mPayCouponAccessClient.logger-level=FULL
feign.client.config.mPayClient.logger-level=FULL
feign.client.config.miniOrderClient.logger-level=FULL
feign.client.config.mCoinClient.logger-level=FULL
feign.client.config.taskUploadClient.logger-level=FULL
feign.client.config.miniUserAddressClient.logger-level=FULL
feign.client.config.miniGoodsClient.logger-level=FULL
feign.client.config.dingDingClient.logger-level=BASIC
feign.client.config.mpMcoinMallManagementClient.logger-level=FULL
feign.client.config.amapClient.logger-level=FULL
feign.client.config.pointProdClient.logger-level=FULL

# æå¡åè¡¨å·æ°å¨æ
spring.cloud.loadbalancer.cache.ttl=10s
# åºç¨æå¡ WEB è®¿é®ç«¯å£
server.port=@server.port@
server.servlet.context-path=/mcoin-mall
server.shutdown=graceful
# Tomcatåæ°éç½®
server.tomcat.accept-count=500
server.tomcat.threads.max=300
server.tomcat.threads.min-spare=50
server.tomcat.mbeanregistry.enabled=true
# Nacoså¸®å©ææ¡£: https://nacos.io/zh-cn/docs/concepts.html
# Nacosè®¤è¯ä¿¡æ¯
spring.cloud.nacos.discovery.username=@spring.cloud.nacos.discovery.username@
spring.cloud.nacos.discovery.password=@spring.cloud.nacos.discovery.password@
# Nacos æå¡åç°ä¸æ³¨åéç½®ï¼å¶ä¸­å­å±æ§ server-addr æå® Nacos æå¡å¨ä¸»æºåç«¯å£
spring.cloud.nacos.discovery.server-addr=@spring.cloud.nacos.discovery.server-addr@
# æ³¨åå° nacos çæå® namespaceï¼é»è®¤ä¸º public
spring.cloud.nacos.discovery.namespace=@spring.profiles.active@
# Sentinel æ§å¶å°å°å
spring.cloud.sentinel.transport.dashboard=@spring.cloud.sentinel.transport.dashboard@
# Sentinel metricæ¥å¿æä»¶æ°é
spring.cloud.sentinel.metric.file-total-count=2
# åæ¶Sentinelæ§å¶å°æå è½½
# é»è®¤æåµä¸ Sentinel ä¼å¨å®¢æ·ç«¯é¦æ¬¡è°ç¨çæ¶åè¿è¡åå§åï¼å¼å§åæ§å¶å°åéå¿è·³å
# éç½® sentinel.eager=true æ¶ï¼åæ¶Sentinelæ§å¶å°æå è½½åè½
spring.cloud.sentinel.eager=true
# å¦ææå¤å¥ç½ç»ï¼åæ æ³æ­£ç¡®è·åæ¬æºIPï¼åéè¦ä½¿ç¨ä¸é¢çåæ°è®¾ç½®å½åæºå¨å¯è¢«å¤é¨è®¿é®çIPå°åï¼ä¾adminæ§å¶å°ä½¿ç¨
# spring.cloud.sentinel.transport.client-ip=
# æ¯å¦åè®¸Beanè¦å(è§£å³Feignè°ç¨å¯å¨æ¥é)
spring.main.allow-bean-definition-overriding=true
# freemarkeræ¨¡æ¿å¼æéç½®
#spring.freemarker.template-loader-path=classpath:/templates/
spring.freemarker.settings.classic_compatible=true
# å¼å¯feignæµæ§
feign.sentinel.enabled=true

# SLSæ¥å¿
sls.endpoint=@sls.endpoint@
sls.accessKeyId=@sls.accessKeyId@
sls.accessKeySecret=@sls.accessKeySecret@
sls.project=@sls.project@
sls.logStore=@sls.logStore@

# æ¥å¿ç®å½
logs.dir=@logs.dir@



mybatis.config-location=classpath:mapper/config/mybatis-config.xml
mybatis.mapper-locations=classpath:mapper/custom/*.xml,classpath:mapper/*.xml

# knife4jéç½®
knife4j.enable=true
knife4j.production=@knife4j.production@
knife4j.basic.enable=true
knife4j.basic.username=knife4j
knife4j.basic.password=knife4j

# jackson éç½®
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.deserialization.fail-on-ignored-properties=false
spring.jackson.deserialization.fail-on-null-for-primitives=false
spring.jackson.deserialization.accept-single-value-as-array=true
spring.jackson.serialization.fail-on-empty-beans=false

spring.jackson.mapper.sort-properties-alphabetically=true
spring.jackson.parser.allow-single-quotes=true

spring.jackson.default-property-inclusion=non_null

# ç°åº¦ï¼ç°åº¦èç¹æ è¯
spring.cloud.nacos.discovery.metadata.gray-version=
service.governance.gray.messaging.nacos.enabled=false

spring.config.import=classpath:bootstrap.properties,\
    classpath:<EMAIL>@/it-security-share.properties,\
    classpath:<EMAIL>@/jdbc-mysql-share.properties,\
    classpath:<EMAIL>@/rabbitmq-share.properties,\
    classpath:<EMAIL>@/redisson-share.properties,\
    classpath:<EMAIL>@/jdbc-mcoin-mall-ext.properties,\
    classpath:<EMAIL>@/rabbitmq-mcoin-mall-ext.properties,\
    classpath:<EMAIL>@/redisson-mcoin-mall-ext.properties,\
    classpath:<EMAIL>@/task-mcoin-mall-ext.properties,\
    classpath:<EMAIL>@/mcoin-mall-job.properties,\
    classpath:<EMAIL>@/mcoin-mall-report-settlement-opt-tpl.properties,\
    classpath:<EMAIL>@/mcoin-mall-report-settlement-req-tpl.properties,\
    classpath:<EMAIL>@/mcoin-mall.properties,\
    nacos:it-security-share.properties?refreshEnabled=true,\
    nacos:jdbc-mysql-share.properties?refreshEnabled=true,\
    nacos:rabbitmq-share.properties?refreshEnabled=true,\
    nacos:redisson-share.properties?refreshEnabled=true,\
    nacos:service-governance-share.properties?refreshEnabled=true,\
    nacos:jdbc-mcoin-mall-ext.properties?refreshEnabled=true,\
    nacos:rabbitmq-mcoin-mall-ext.properties?refreshEnabled=true,\
    nacos:redisson-mcoin-mall-ext.properties?refreshEnabled=true,\
    nacos:task-mcoin-mall-ext.properties?refreshEnabled=true,\
    nacos:mcoin-mall-job.properties?refreshEnabled=true,\
    nacos:mcoin-mall-report-settlement-opt-tpl.properties?refreshEnabled=true,\
    nacos:mcoin-mall-report-settlement-req-tpl.properties?refreshEnabled=true,\
    nacos:mcoin-mall.properties?refreshEnabled=true
  