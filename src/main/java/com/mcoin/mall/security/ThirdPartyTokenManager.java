package com.mcoin.mall.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ThirdPartyTokenManager {

    @Value("${third.party.salt:mgame}")
    private String salt;

    /**
     * 检验合法性，其中secret参数就应该传入的是用户的id
     *
     * @param token
     * @throws
     */
    public DecodedJWT verifyToken(String token) {
        try {
            String secret = getAudience(token);
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(secret + salt)).build();
            return verifier.verify(token);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取签发对象
     */
    private String getAudience(String token) {
        String audience = null;
        try {
            audience = JWT.decode(token).getAudience().get(0);
        } catch (JWTDecodeException j) {
            throw new BusinessException(Response.Code.BAD_REQUEST, "签名不正确");
        }
        return audience;
    }
}
