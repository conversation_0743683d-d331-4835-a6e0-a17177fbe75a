package com.mcoin.mall.controller.job;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.service.base.MqLocalService;

import lombok.extern.slf4j.Slf4j;

/**
 * MQ重试
 */
@RestController
@RequestMapping("/api/job/mq")
@Slf4j
public class MqJobController {

    @Resource
    private MqLocalService mqLocalService;


    @PostMapping(value = "/resendMq")
    public Response<String> resendMq() {
        this.mqLocalService.resendMq();
        return Responses.ok("success");
    }

    @PostMapping(value = "/resendGradientMq")
    public Response<String> resendGradientMq() {
        this.mqLocalService.resendGradientMq();
        return Responses.ok("success");
    }

}
