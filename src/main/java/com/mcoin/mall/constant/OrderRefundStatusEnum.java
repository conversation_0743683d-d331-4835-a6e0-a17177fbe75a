package com.mcoin.mall.constant;

public enum OrderRefundStatusEnum {

    SUBMITTED(1, "提交申請/平台審核中"),
    AGREED(2, "同意退款/第三方審核中"),
    DISAGREED(3, "不同意退款"),
    THIRD_PARTY_APPROVED(4, "第三方通過/處理中"),
    THIRD_PARTY_DISAPPROVED(5, "第三方不通過"),
    COMPLETED(6, "已到賬"),
    FAILED(7, "到賬失敗"),
    CANCELLED(8, "取消退款"),
    MANUAL_REFUND_PENDING(9, "轉入人工退款"),
    MANUAL_REFUND_SUCCESSFUL(10, "人工退款成功");

    private final int code;
    private final String description;

    OrderRefundStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
