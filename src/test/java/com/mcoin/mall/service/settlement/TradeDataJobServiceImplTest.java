package com.mcoin.mall.service.settlement;

import com.mcoin.mall.bo.TradingDataBo;
import com.mcoin.mall.bo.TradingProductHotDataBo;
import com.mcoin.mall.bo.TradingProductMaxDataBo;
import com.mcoin.mall.bo.TradingRefundDataBo;
import com.mcoin.mall.bo.TradingSettlementDataBo;
import com.mcoin.mall.component.ExplicitTransaction;
import com.mcoin.mall.dao.FookMacaupassUserLoginLog2Dao;
import com.mcoin.mall.dao.FookTradingDao;
import com.mcoin.mall.dao.FookTradingDataDao;
import com.mcoin.mall.dao.SettingsDao;
import com.mcoin.mall.model.job.TradingDataRequest;
import com.mcoin.mall.service.job.impl.TradeDataJobServiceImpl;
import com.mcoin.mall.util.ConfigUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.Callable;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/12/27
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TradeDataJobServiceImplTest {
    @Mock
    private ExplicitTransaction batchTransaction;

    @Mock
    private SettingsDao settingsDao;

    @Mock
    private FookMacaupassUserLoginLog2Dao fookMacaupassUserLoginLog2Dao;

    @Mock
    private FookTradingDao fookTradingDao;

    @Mock
    private FookTradingDataDao fookTradingDataDao;

    @InjectMocks
    private TradeDataJobServiceImpl tradeDataJobService; // 替换为实际的服务类


    MockedStatic<ConfigUtils> configUtilsStatic;

    @BeforeEach
    public void setUp() {
        configUtilsStatic = mockStatic(ConfigUtils.class);
        MockitoAnnotations.openMocks(this);
    }
    @AfterEach
    public void tearDown() {
        configUtilsStatic.close();
    }

    @Test
    public void testDoCreateTradingData() {
        // 初始化 Mockito
        MockitoAnnotations.openMocks(this);

        // 准备测试数据
        TradingDataRequest request = new TradingDataRequest();
        request.setCurrentDate(new Date());

        // 模拟设置值
        when(settingsDao.getValueDirect("site.kernel_orderreturn")).thenReturn("1");
        when(settingsDao.getValueDirect("site.kernel_reportorder")).thenReturn("1");

        // 模拟 DAO 方法
        when(fookMacaupassUserLoginLog2Dao.countDistinctUserId(any(Date.class), any(Date.class))).thenReturn(10);
        when(fookTradingDao.selectTradingData(any(Date.class), any(Date.class))).thenReturn(mockTradingDataBo());
        when(fookTradingDao.selectTradingSettlementData(any(Date.class), any(Date.class))).thenReturn(mockTradingSettlementDataBo());
        when(fookTradingDao.selectTradingRefundData(any(Date.class), any(Date.class))).thenReturn(mockTradingRefundDataBo());
        when(fookTradingDao.selectTradingProductMaxData(any(Date.class), any(Date.class))).thenReturn(mockTradingProductMaxDataBo());
        when(fookTradingDao.selectTradingProductHotData(any(Date.class), any(Date.class))).thenReturn(mockTradingProductHotDataBo());

        // 模拟 batchTransaction.invokeWithNewTransaction 方法的行为
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run(); // 执行传入的 runnable
            return null; // 返回值可以根据需要调整
        }).when(batchTransaction).invokeWithNewTransaction(any(Callable.class));

        // 执行方法
        tradeDataJobService.doCreateTradingData(request);
    }

    // 模拟方法
    private TradingDataBo mockTradingDataBo() {
        TradingDataBo tradingDataBo = new TradingDataBo();
        tradingDataBo.setUserPayNum(5);
        tradingDataBo.setTradingBusinessNum(3);
        tradingDataBo.setTradingNum(20);
        tradingDataBo.setIntegralConversionAmount(new BigDecimal(500));
        tradingDataBo.setPaymentAmountSum(new BigDecimal(500));
        tradingDataBo.setTradingTotalAmount(new BigDecimal(500));
        tradingDataBo.setSubsidyAmount(new BigDecimal(500));
        return tradingDataBo;
    }

    private TradingSettlementDataBo mockTradingSettlementDataBo() {
        TradingSettlementDataBo tradingSettlementDataBo = new TradingSettlementDataBo();
        tradingSettlementDataBo.setSettlementBusinessNum(2);
        tradingSettlementDataBo.setSettlementNum(15);
        tradingSettlementDataBo.setMomecoinsamountSum(new BigDecimal(500));
        tradingSettlementDataBo.setUserpaymentamountSum(new BigDecimal(500));
        tradingSettlementDataBo.setBillamountSum(new BigDecimal(500));
        return tradingSettlementDataBo;
    }

    private TradingRefundDataBo mockTradingRefundDataBo() {
        TradingRefundDataBo tradingRefundDataBo = new TradingRefundDataBo();
        tradingRefundDataBo.setRefundConversionAmount(new BigDecimal(500));
        tradingRefundDataBo.setRefundAmount(new BigDecimal(500));
        tradingRefundDataBo.setOrderAmount(new BigDecimal(500));
        tradingRefundDataBo.setTradingNum(5);
        tradingRefundDataBo.setRefundSubsidyAmount(new BigDecimal(500));
        return tradingRefundDataBo;
    }

    private TradingProductMaxDataBo mockTradingProductMaxDataBo() {
        TradingProductMaxDataBo tradingProductMaxDataBo = new TradingProductMaxDataBo();
        tradingProductMaxDataBo.setProdcutid(1234);
        tradingProductMaxDataBo.setProductTitle("Max Product");
        tradingProductMaxDataBo.setOrderAmountSum(new BigDecimal(500));
        return tradingProductMaxDataBo;
    }

    private TradingProductHotDataBo mockTradingProductHotDataBo() {
        TradingProductHotDataBo tradingProductHotDataBo = new TradingProductHotDataBo();
        tradingProductHotDataBo.setProdcutid(1);
        tradingProductHotDataBo.setProductTitle("Hot Product");
        tradingProductHotDataBo.setProductNum(100);
        return tradingProductHotDataBo;
    }
}
