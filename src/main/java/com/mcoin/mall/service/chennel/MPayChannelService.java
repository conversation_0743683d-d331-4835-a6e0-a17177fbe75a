package com.mcoin.mall.service.chennel;

import com.mcoin.mall.bean.FookPayLog;
import com.mcoin.mall.bean.FookPlatformOrder;
import com.mcoin.mall.bean.FookPlatformOrderinfo;
import com.mcoin.mall.constant.CouponSynStatus;
import com.mcoin.mall.mq.model.CouponSyncMessage;
import com.mcoin.mall.mq.model.RefundMessage;
import com.mcoin.mall.service.chennel.vo.MPayPaySuccessVo;

public interface MPayChannelService {

    /**
     * mpay 劵平台领券
     */
    String getCoupon(FookPlatformOrder order, FookPlatformOrderinfo orderinfo);

    /**
     * mpay 劵平台查券
     */
    String queryCoupon(Integer userId, String orderNo);

    /**
     * mpay 劵平台同步
     */
    void updSyncCouponOrderCode(CouponSyncMessage message);

    /**
     *
     * @param message
     * @return 1:成功 0:重试 -1:未查询到退款订单 -2:支付平台處理狀態不正确 -3:未查询到订单 -4:无退款金額
     */
    int updRefund(RefundMessage message);

    void updRefundForInvalidOrder(FookPlatformOrder order, FookPayLog payLog, String tradeNo);

    int insertCouponSync(Integer orderId, Integer codeId, CouponSyncMessage.Operation operation, String msg);

    int insertCouponSync(Integer orderId, Integer codeId, CouponSyncMessage.Operation operation, CouponSynStatus status, String msg);

    void updPaySuccess(MPayPaySuccessVo payPaySuccessData);

    /**
     * 查询支付
     */
    String queryPay(String outTradeNo, String orderNo);
}
