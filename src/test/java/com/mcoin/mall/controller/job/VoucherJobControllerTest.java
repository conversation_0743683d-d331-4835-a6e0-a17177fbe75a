package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.VoucherAssignRequest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 VoucherJobController
 * 使用真实服务和数据库，测试优惠券派发任务
 */
class VoucherJobControllerTest extends BaseUnitTest {

    @Resource
    private VoucherJobController voucherJobController;

    @Test
    void testAssignVoucher() {
        // 创建请求对象
        VoucherAssignRequest request = new VoucherAssignRequest();
        
        // 直接调用控制器方法
        Response<String> response = voucherJobController.assignVoucher(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 