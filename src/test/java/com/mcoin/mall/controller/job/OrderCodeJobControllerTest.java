package com.mcoin.mall.controller.job;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.job.OrderCodeCheckRepeatRequest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 集成测试 OrderCodeJobController
 * 使用真实服务和数据库，测试订单码相关功能
 */
class OrderCodeJobControllerTest extends BaseUnitTest {

    @Resource
    private OrderCodeJobController orderCodeJobController;

    @Test
    void testCheckRepeat() {
        // 创建请求对象
        OrderCodeCheckRepeatRequest request = new OrderCodeCheckRepeatRequest();
        
        // 直接调用控制器方法
        Response<String> response = orderCodeJobController.checkRepeat(request);
        
        // 验证响应结构
        assertNotNull(response);
    }
} 