<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBannerDao">
  <select id="getIndexBanners" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fook_banner
    where
        enable = 1
        and icon != ''
        and if_index_show = 1
      order by sort asc
  </select>
</mapper>
