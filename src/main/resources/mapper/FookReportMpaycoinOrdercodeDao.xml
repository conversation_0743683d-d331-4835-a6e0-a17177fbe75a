<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookReportMpaycoinOrdercodeDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookReportMpaycoinOrdercode">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="businessid" jdbcType="INTEGER" property="businessid" />
    <result column="storeid" jdbcType="INTEGER" property="storeid" />
    <result column="ordercodeid" jdbcType="INTEGER" property="ordercodeid" />
    <result column="userid" jdbcType="INTEGER" property="userid" />
    <result column="businessname" jdbcType="VARCHAR" property="businessname" />
    <result column="storename" jdbcType="VARCHAR" property="storename" />
    <result column="bankaccount" jdbcType="VARCHAR" property="bankaccount" />
    <result column="bankname" jdbcType="VARCHAR" property="bankname" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="usetime" jdbcType="TIMESTAMP" property="usetime" />
    <result column="billamount" jdbcType="DECIMAL" property="billamount" />
    <result column="userpaymentamount" jdbcType="DECIMAL" property="userpaymentamount" />
    <result column="momecoinsamount" jdbcType="DECIMAL" property="momecoinsamount" />
    <result column="mpaycoins" jdbcType="DECIMAL" property="mpaycoins" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="merchantsettleamount" jdbcType="DECIMAL" property="merchantsettleamount" />
    <result column="vouchercode" jdbcType="VARCHAR" property="vouchercode" />
    <result column="vouchername" jdbcType="VARCHAR" property="vouchername" />
    <result column="ordertransaction" jdbcType="VARCHAR" property="ordertransaction" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="settlementtime" jdbcType="TIMESTAMP" property="settlementtime" />
    <result column="mpaycoinid" jdbcType="INTEGER" property="mpaycoinid" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="order_money" jdbcType="DECIMAL" property="orderMoney" />
    <result column="mpayintegral" jdbcType="DECIMAL" property="mpayintegral" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="payment_type" jdbcType="INTEGER" property="paymentType" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="payment_transaction" jdbcType="VARCHAR" property="paymentTransaction" />
    <result column="mpaycoinids" jdbcType="INTEGER" property="mpaycoinids" />
    <result column="orderid" jdbcType="INTEGER" property="orderid" />
    <result column="operationtime" jdbcType="TIMESTAMP" property="operationtime" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="refund_mpayintegral" jdbcType="DECIMAL" property="refundMpayintegral" />
    <result column="subsidy_amount" jdbcType="DECIMAL" property="subsidyAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, businessid, storeid, ordercodeid, userid, businessname, storename, bankaccount, 
    bankname, bank, createtime, usetime, billamount, userpaymentamount, momecoinsamount, 
    mpaycoins, commission, merchantsettleamount, vouchercode, vouchername, ordertransaction, 
    `status`, settlementtime, mpaycoinid, order_no, order_type, pay_type, order_money, 
    mpayintegral, total_amount, payment_type, payment_amount, platform, payment_time, 
    complete_time, payment_transaction, mpaycoinids, orderid, operationtime, refund_amount, 
    refund_mpayintegral, subsidy_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_report_mpaycoin_ordercode
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_report_mpaycoin_ordercode
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinOrdercode">
    insert into fook_report_mpaycoin_ordercode
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessid != null">
        businessid,
      </if>
      <if test="storeid != null">
        storeid,
      </if>
      <if test="ordercodeid != null">
        ordercodeid,
      </if>
      <if test="userid != null">
        userid,
      </if>
      <if test="businessname != null">
        businessname,
      </if>
      <if test="storename != null">
        storename,
      </if>
      <if test="bankaccount != null">
        bankaccount,
      </if>
      <if test="bankname != null">
        bankname,
      </if>
      <if test="bank != null">
        bank,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="usetime != null">
        usetime,
      </if>
      <if test="billamount != null">
        billamount,
      </if>
      <if test="userpaymentamount != null">
        userpaymentamount,
      </if>
      <if test="momecoinsamount != null">
        momecoinsamount,
      </if>
      <if test="mpaycoins != null">
        mpaycoins,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="merchantsettleamount != null">
        merchantsettleamount,
      </if>
      <if test="vouchercode != null">
        vouchercode,
      </if>
      <if test="vouchername != null">
        vouchername,
      </if>
      <if test="ordertransaction != null">
        ordertransaction,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="settlementtime != null">
        settlementtime,
      </if>
      <if test="mpaycoinid != null">
        mpaycoinid,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="orderMoney != null">
        order_money,
      </if>
      <if test="mpayintegral != null">
        mpayintegral,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="paymentTransaction != null">
        payment_transaction,
      </if>
      <if test="mpaycoinids != null">
        mpaycoinids,
      </if>
      <if test="orderid != null">
        orderid,
      </if>
      <if test="operationtime != null">
        operationtime,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundMpayintegral != null">
        refund_mpayintegral,
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=INTEGER},
      </if>
      <if test="businessname != null">
        #{businessname,jdbcType=VARCHAR},
      </if>
      <if test="storename != null">
        #{storename,jdbcType=VARCHAR},
      </if>
      <if test="bankaccount != null">
        #{bankaccount,jdbcType=VARCHAR},
      </if>
      <if test="bankname != null">
        #{bankname,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="usetime != null">
        #{usetime,jdbcType=TIMESTAMP},
      </if>
      <if test="billamount != null">
        #{billamount,jdbcType=DECIMAL},
      </if>
      <if test="userpaymentamount != null">
        #{userpaymentamount,jdbcType=DECIMAL},
      </if>
      <if test="momecoinsamount != null">
        #{momecoinsamount,jdbcType=DECIMAL},
      </if>
      <if test="mpaycoins != null">
        #{mpaycoins,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="merchantsettleamount != null">
        #{merchantsettleamount,jdbcType=DECIMAL},
      </if>
      <if test="vouchercode != null">
        #{vouchercode,jdbcType=VARCHAR},
      </if>
      <if test="vouchername != null">
        #{vouchername,jdbcType=VARCHAR},
      </if>
      <if test="ordertransaction != null">
        #{ordertransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="settlementtime != null">
        #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mpaycoinid != null">
        #{mpaycoinid,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="orderMoney != null">
        #{orderMoney,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentTransaction != null">
        #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="mpaycoinids != null">
        #{mpaycoinids,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=INTEGER},
      </if>
      <if test="operationtime != null">
        #{operationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundMpayintegral != null">
        #{refundMpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="subsidyAmount != null">
        #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookReportMpaycoinOrdercode">
    update fook_report_mpaycoin_ordercode
    <set>
      <if test="businessid != null">
        businessid = #{businessid,jdbcType=INTEGER},
      </if>
      <if test="storeid != null">
        storeid = #{storeid,jdbcType=INTEGER},
      </if>
      <if test="ordercodeid != null">
        ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      </if>
      <if test="userid != null">
        userid = #{userid,jdbcType=INTEGER},
      </if>
      <if test="businessname != null">
        businessname = #{businessname,jdbcType=VARCHAR},
      </if>
      <if test="storename != null">
        storename = #{storename,jdbcType=VARCHAR},
      </if>
      <if test="bankaccount != null">
        bankaccount = #{bankaccount,jdbcType=VARCHAR},
      </if>
      <if test="bankname != null">
        bankname = #{bankname,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        bank = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="usetime != null">
        usetime = #{usetime,jdbcType=TIMESTAMP},
      </if>
      <if test="billamount != null">
        billamount = #{billamount,jdbcType=DECIMAL},
      </if>
      <if test="userpaymentamount != null">
        userpaymentamount = #{userpaymentamount,jdbcType=DECIMAL},
      </if>
      <if test="momecoinsamount != null">
        momecoinsamount = #{momecoinsamount,jdbcType=DECIMAL},
      </if>
      <if test="mpaycoins != null">
        mpaycoins = #{mpaycoins,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="merchantsettleamount != null">
        merchantsettleamount = #{merchantsettleamount,jdbcType=DECIMAL},
      </if>
      <if test="vouchercode != null">
        vouchercode = #{vouchercode,jdbcType=VARCHAR},
      </if>
      <if test="vouchername != null">
        vouchername = #{vouchername,jdbcType=VARCHAR},
      </if>
      <if test="ordertransaction != null">
        ordertransaction = #{ordertransaction,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="settlementtime != null">
        settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mpaycoinid != null">
        mpaycoinid = #{mpaycoinid,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="orderMoney != null">
        order_money = #{orderMoney,jdbcType=DECIMAL},
      </if>
      <if test="mpayintegral != null">
        mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentTransaction != null">
        payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      </if>
      <if test="mpaycoinids != null">
        mpaycoinids = #{mpaycoinids,jdbcType=INTEGER},
      </if>
      <if test="orderid != null">
        orderid = #{orderid,jdbcType=INTEGER},
      </if>
      <if test="operationtime != null">
        operationtime = #{operationtime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundMpayintegral != null">
        refund_mpayintegral = #{refundMpayintegral,jdbcType=DECIMAL},
      </if>
      <if test="subsidyAmount != null">
        subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookReportMpaycoinOrdercode">
    update fook_report_mpaycoin_ordercode
    set businessid = #{businessid,jdbcType=INTEGER},
      storeid = #{storeid,jdbcType=INTEGER},
      ordercodeid = #{ordercodeid,jdbcType=INTEGER},
      userid = #{userid,jdbcType=INTEGER},
      businessname = #{businessname,jdbcType=VARCHAR},
      storename = #{storename,jdbcType=VARCHAR},
      bankaccount = #{bankaccount,jdbcType=VARCHAR},
      bankname = #{bankname,jdbcType=VARCHAR},
      bank = #{bank,jdbcType=VARCHAR},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      usetime = #{usetime,jdbcType=TIMESTAMP},
      billamount = #{billamount,jdbcType=DECIMAL},
      userpaymentamount = #{userpaymentamount,jdbcType=DECIMAL},
      momecoinsamount = #{momecoinsamount,jdbcType=DECIMAL},
      mpaycoins = #{mpaycoins,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      merchantsettleamount = #{merchantsettleamount,jdbcType=DECIMAL},
      vouchercode = #{vouchercode,jdbcType=VARCHAR},
      vouchername = #{vouchername,jdbcType=VARCHAR},
      ordertransaction = #{ordertransaction,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      settlementtime = #{settlementtime,jdbcType=TIMESTAMP},
      mpaycoinid = #{mpaycoinid,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=INTEGER},
      order_money = #{orderMoney,jdbcType=DECIMAL},
      mpayintegral = #{mpayintegral,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      payment_type = #{paymentType,jdbcType=INTEGER},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      platform = #{platform,jdbcType=VARCHAR},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      payment_transaction = #{paymentTransaction,jdbcType=VARCHAR},
      mpaycoinids = #{mpaycoinids,jdbcType=INTEGER},
      orderid = #{orderid,jdbcType=INTEGER},
      operationtime = #{operationtime,jdbcType=TIMESTAMP},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      refund_mpayintegral = #{refundMpayintegral,jdbcType=DECIMAL},
      subsidy_amount = #{subsidyAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>