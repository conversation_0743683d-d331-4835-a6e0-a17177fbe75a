package com.mcoin.mall.controller.management;

import com.mcoin.mall.client.model.GatewayAccessTokenResponse;
import com.mcoin.mall.client.model.GatewayAppTicketResponse;
import com.mcoin.mall.client.model.GatewayAppTokenResponse;
import com.mcoin.mall.service.gateway.token.GatewayTokenManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@RestController
@RequestMapping("/api/management")
@Slf4j
public class TokenManageController {

    @Resource
    private GatewayTokenManageService gatewayTokenManageService;

    @GetMapping("/user/token")
    public GatewayAccessTokenResponse manageUserToken(@RequestParam @Valid String code) {
        return gatewayTokenManageService.updateUserToken(code);
    }

    @GetMapping("/app/token")
    public GatewayAppTokenResponse manageAppToken() {
        return gatewayTokenManageService.manageAppToken();
    }


    @GetMapping("/app/ticket")
    public GatewayAppTicketResponse manageAppTicket() {
        return gatewayTokenManageService.manageAppTicket();
    }

}
