package com.mcoin.mall.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class HtmlUtilTest{

    @Test
    public void testReplaceNewLine_withTab() {
        String input = "This is a test\twith tab.";
        String expectedOutput = "This is a test with tab.";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }


    @Test
    public void testReplaceNewLine_withCarriageReturnAndSpace() {
        String input = "This is a test\r\n\twith carriage return and space.";
        String expectedOutput = "This is a test<br/> with carriage return and space.";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testReplaceNewLine_withEmptyString() {
        String input = "";
        String expectedOutput = "";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testReplaceNewLine_withNewLine() {
        String input = "This is a test\nwith new line.";
        String expectedOutput = "This is a test<br/>with new line.";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testReplaceNewLine_withMultipleNewLines() {
        String input = "This is a test\nwith multiple\nnew lines.";
        String expectedOutput = "This is a test<br/>with multiple<br/>new lines.";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testReplaceNewLine_withCarriageReturnAndNewLine() {
        String input = "This is a test\r\nwith carriage return and new line.";
        String expectedOutput = "This is a test<br/>with carriage return and new line.";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testReplaceNewLine_withWhitespaceString() {
        String input = "   ";
        String expectedOutput = "   ";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }


    @Test
    public void testReplaceNewLine_withTabAndNewLine() {
        String input = "This is a test\t\nwith tab and new line.";
        String expectedOutput = "This is a test <br/>with tab and new line.";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testReplaceNewLine_withNull() {
        String input = null;
        String expectedOutput = null;
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void testReplaceNewLine_withSpecialCharacters() {
        String input = "This is a test\nwith special characters!";
        String expectedOutput = "This is a test<br/>with special characters!";
        String actualOutput = HtmlUtil.replaceNewLine(input);
        assertEquals(expectedOutput, actualOutput);
    }

}