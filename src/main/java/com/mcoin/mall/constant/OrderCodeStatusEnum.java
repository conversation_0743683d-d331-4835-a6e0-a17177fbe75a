package com.mcoin.mall.constant;


import lombok.Getter;

/**
 * 券码状态枚举
 *
 */
@Getter
public enum OrderCodeStatusEnum {

    /**
     * 券码状态枚举
     */
    UNUSED(1, "未使用"),
    USED(2, "已使用"),
    INVALID(3, "已失效"),
    EXPIRED(4, "已過期"),
    REFUNDING(5, "退款中"),
    NOT_START(6, "券未到开始使用时间"),
    ;

    /**
     * 类型（类型需要唯一，不可修改）
     */
    private final int type;

    /**
     * 描述
     */
    private final String desc;

    OrderCodeStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;

    }
}
