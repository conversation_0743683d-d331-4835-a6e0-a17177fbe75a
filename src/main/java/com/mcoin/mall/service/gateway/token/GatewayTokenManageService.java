package com.mcoin.mall.service.gateway.token;

import com.mcoin.mall.client.model.GatewayAccessTokenResponse;
import com.mcoin.mall.client.model.GatewayAppTicketResponse;
import com.mcoin.mall.client.model.GatewayAppTokenResponse;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
public interface GatewayTokenManageService{
    /**
     * 更新用户token
     * @param code code
     * @return MPayAccessTokenResponse response
     */
    GatewayAccessTokenResponse updateUserToken(String code);

    /**
     * 获取appToken
     * @return MPayAppTokenResponse response
     */
    GatewayAppTokenResponse manageAppToken();

    /**
     * 获取appTicket
     *
     * @return MPayAppTokenResponse response
     */
    GatewayAppTicketResponse manageAppTicket();


}
