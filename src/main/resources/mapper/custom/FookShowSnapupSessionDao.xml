<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookShowSnapupSessionDao">

  <select id="selectCurrentSession"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    <include refid="Base_From_where_order_by"/>
    limit 1
  </select>

  <sql id="Base_From_where_order_by">
    from fook_show_snapup_session
    where valid = 1
    and session_type in (0, 2)
    and (
         <!-- 抢购中 -->
        (start_time <![CDATA[<=]]> #{now,jdbcType=TIMESTAMP}  and end_time > #{now,jdbcType=TIMESTAMP})
        or
        <!-- 今日即将推出 -->
        (date(start_time) = date(#{now,jdbcType=TIMESTAMP}) and start_time > #{now,jdbcType=TIMESTAMP})
        or
        <!-- 明日即将推出 -->
        (date(start_time) = #{tomorrow,jdbcType=TIMESTAMP})
    )
    order by start_time
  </sql>

  <select id="selectCurrentAndNextSession"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    <include refid="Base_From_where_order_by"/>
    limit #{limit,jdbcType=INTEGER}
  </select>

    <sql id="Base_Column_List_alias_ss">
        ss.id, ss.start_time, ss.end_time, ss.cn_title, ss.en_title, ss.session_type,
    ss.show_id, ss.show_snapup_id, ss.`valid`, ss.created_at, ss.updated_at, ss.created_by, ss.updated_by
    </sql>

    <select id="selectSessionByProductIdAndSessionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_alias_ss"/>
        from fook_show_snapup_session ss
        join fook_show_snapup_session_product sp on ss.id = sp.session_id
        where sp.product_id = #{productId,jdbcType=INTEGER}
        and ss.id = #{sessionId,jdbcType=INTEGER}
        order by ss.start_time asc
        limit 1
    </select>

    <select id="selectClosetSessionByProductId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_alias_ss"/>
        from fook_show_snapup_session ss
        join fook_show_snapup_session_product sp on ss.id = sp.session_id
        where sp.product_id = #{productId,jdbcType=INTEGER}
        and ss.end_time > #{now,jdbcType=TIMESTAMP}
        order by ss.start_time asc, ss.valid desc
        limit 1
    </select>

    <select id="selectLastSessionByProductId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List_alias_ss"/>
        from fook_show_snapup_session ss
        join fook_show_snapup_session_product sp on ss.id = sp.session_id
        where sp.product_id = #{productId,jdbcType=INTEGER}
        and ss.start_time = #{startTime,jdbcType=TIMESTAMP}
        and ss.end_time = #{endTime,jdbcType=TIMESTAMP}
        order by ss.start_time asc, ss.valid desc
        limit 1
    </select>
</mapper>