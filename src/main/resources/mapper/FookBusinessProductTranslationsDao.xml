<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessProductTranslationsDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessProductTranslations">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_product_id" jdbcType="INTEGER" property="businessProductId" />
    <result column="t_title" jdbcType="VARCHAR" property="tTitle" />
    <result column="t_tnc" jdbcType="VARCHAR" property="tTnc" />
    <result column="t_receive_method" jdbcType="VARCHAR" property="tReceiveMethod" />
    <result column="locale" jdbcType="VARCHAR" property="locale" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, business_product_id, t_title, t_tnc, t_receive_method, `locale`, created_at,
    updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_product_translations
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_product_translations
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessProductTranslations">
    insert into fook_business_product_translations
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessProductId != null">
        business_product_id,
      </if>
      <if test="tTitle != null">
        t_title,
      </if>
      <if test="tTnc != null">
        t_tnc,
      </if>
      <if test="tReceiveMethod != null">
        t_receive_method,
      </if>
      <if test="locale != null">
        `locale`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessProductId != null">
        #{businessProductId,jdbcType=INTEGER},
      </if>
      <if test="tTitle != null">
        #{tTitle,jdbcType=VARCHAR},
      </if>
      <if test="tTnc != null">
        #{tTnc,jdbcType=VARCHAR},
      </if>
      <if test="tReceiveMethod != null">
        #{tReceiveMethod,jdbcType=VARCHAR},
      </if>
      <if test="locale != null">
        #{locale,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessProductTranslations">
    update fook_business_product_translations
    <set>
      <if test="businessProductId != null">
        business_product_id = #{businessProductId,jdbcType=INTEGER},
      </if>
      <if test="tTitle != null">
        t_title = #{tTitle,jdbcType=VARCHAR},
      </if>
      <if test="tTnc != null">
        t_tnc = #{tTnc,jdbcType=VARCHAR},
      </if>
      <if test="tReceiveMethod != null">
        t_receive_method = #{tReceiveMethod,jdbcType=VARCHAR},
      </if>
      <if test="locale != null">
        `locale` = #{locale,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessProductTranslations">
    update fook_business_product_translations
    set business_product_id = #{businessProductId,jdbcType=INTEGER},
      t_title = #{tTitle,jdbcType=VARCHAR},
      t_tnc = #{tTnc,jdbcType=VARCHAR},
      t_receive_method = #{tReceiveMethod,jdbcType=VARCHAR},
      `locale` = #{locale,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>