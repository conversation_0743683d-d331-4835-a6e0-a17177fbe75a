package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookClause;
import com.mcoin.mall.bean.FookClauseConfirm;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.dao.FookClauseConfirmDao;
import com.mcoin.mall.dao.FookClauseDao;
import com.mcoin.mall.model.GetClauseResponse;
import com.mcoin.mall.model.SetClauseRequest;
import com.mcoin.mall.model.SetClauseResponse;
import com.mcoin.mall.service.business.ClauseService;
import com.mcoin.mall.util.JodaTimeUtil;
import com.mcoin.mall.util.McoinMall;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Locale;

@Slf4j
@Service
public class ClauseServiceImpl implements ClauseService {
    @Autowired
    private FookClauseDao fookClauseDao;
    @Autowired
    private FookClauseConfirmDao fookClauseConfirmDao;
    @Resource
    private ContextHolder contextHolder;
    @Resource
    private MessageSource messageSource;
    @Override
    public GetClauseResponse getClause() {
        int userId = contextHolder.getAuthUserInfo().getUserId();
        String msgAgree = "同意";
        String msgNotAgress = "不同意";
        Locale locale = contextHolder.getLocale();
        String lang = locale.getLanguage();
        if (McoinMall.LANG_EN.equalsIgnoreCase(lang)){
            msgAgree = "agree";
            msgNotAgress = "Not agreed";
        }
        int status = 0;
        String msg = msgAgree;
        GetClauseResponse clauseResponse = new GetClauseResponse();
        FookClause fookClause = fookClauseDao.selectLatestClause();
        if (fookClause != null){
            clauseResponse.setClauseid(String.valueOf(fookClause.getId()));
            FookClauseConfirm fookClauseConfirm = fookClauseConfirmDao.selectClauseConfirm(userId, fookClause.getId(),1);
            if (fookClauseConfirm == null){
                status = 1;
                msg = msgNotAgress;
                Date date = fookClause.getUpdatedAt() == null ? fookClause.getCreatedAt() : fookClause.getUpdatedAt();
                clauseResponse.setCreate_at(JodaTimeUtil.format4Mcoin(date, lang));
                if (McoinMall.LANG_EN.equalsIgnoreCase(lang)){
                    clauseResponse.setContent(fookClause.getContentEn());
                } else {
                    clauseResponse.setContent(fookClause.getContent());
                }
            }
        }
        clauseResponse.setStatus(status);
        clauseResponse.setMsg(msg);
        return clauseResponse;
    }

    @Override
    public SetClauseResponse saveClause(SetClauseRequest request) {
        int userId = contextHolder.getAuthUserInfo().getUserId();
        String clauseid = request.getClauseid();
        String status = request.getStatus();
        FookClauseConfirm fookClauseConfirm = fookClauseConfirmDao.selectClauseConfirm(userId, Integer.parseInt(clauseid),null);
        FookClauseConfirm fookClauseConfirmSplit = new FookClauseConfirm();
        fookClauseConfirmSplit.setStatus(Integer.valueOf(status));
        fookClauseConfirmSplit.setUpdatedAt(new Date());
        if (fookClauseConfirm != null){
            fookClauseConfirmSplit.setId(fookClauseConfirm.getId());
            fookClauseConfirmDao.updateByPrimaryKeySelective(fookClauseConfirmSplit);
        } else {
            fookClauseConfirmSplit.setUserid(userId);
            fookClauseConfirmSplit.setClauseid(Integer.parseInt(clauseid));
            fookClauseConfirmSplit.setCreatedAt(new Date());
            fookClauseConfirmDao.insertSelective(fookClauseConfirmSplit);
        }
        SetClauseResponse response = new SetClauseResponse();
        response.setStatus(McoinMall.RESPONSE_STATUS_SUCCESS);
        response.setMsg(messageSource.getMessage("message.basic.successful", null, contextHolder.getLocale()));
        return response;
    }
}
