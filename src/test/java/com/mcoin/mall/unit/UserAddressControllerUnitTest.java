    package com.mcoin.mall.unit;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.bean.FookMacaupassUser;
import com.mcoin.mall.client.MiniUserAddressClient;
import com.mcoin.mall.client.model.MiniOrdersHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressDetailHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressEditHttpRequest;
import com.mcoin.mall.client.model.MiniUserAddressLocationHttpRequest;
import com.mcoin.mall.component.ContextHolder;
import com.mcoin.mall.controller.user.UserAddressController;
import com.mcoin.mall.dao.FookMacaupassUserDao;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.UserAddressAddRequest;
import com.mcoin.mall.model.UserAddressListResponse;
import com.mcoin.mall.model.UserAddressLocationRequest;
import com.mcoin.mall.model.UserAddressRequest;
import com.mcoin.mall.model.UserAddressSetRequest;
import com.mcoin.mall.security.UserInfo;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.JsonTestUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Locale;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class UserAddressControllerUnitTest extends ControllerMock {
    @Mock
    ContextHolder contextHolder;
    
    @Mock
    FookMacaupassUserDao fookMacaupassUserDao;
    
    @Mock
    MiniUserAddressClient miniUserAddressClient;
    
    @InjectMocks
    UserAddressController userAddressController;
    
    private MockedStatic<ConfigUtils> mockedStaticConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @AfterEach
    void cleanupConfig() {
        if (mockedStaticConfig != null) {
            mockedStaticConfig.close();
        }
    }

    @ParameterizedTest
    @MethodSource("detailTestCases")
    void testDetail(UserAddressRequest request, String expectedResult) throws BlockException {
        // given
        when(contextHolder.getAuthUserInfo()).thenReturn(createUserInfo(0));
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookMacaupassUser.json", "singleObj", FookMacaupassUser.class));
        when(miniUserAddressClient.detail(any(MiniUserAddressDetailHttpRequest.class))).thenReturn(
                JsonTestUtils.getStringFromJson("json/miniUserAddress.json", "singleObj"));
        mockedStaticConfig = Mockito.mockStatic(ConfigUtils.class);
        mockedStaticConfig.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");

        // when / then
        assertEquals(expectedResult, userAddressController.detail(request).getData().getAddress());
    }
    
    @ParameterizedTest
    @MethodSource("addTestCases")
    void testAdd(UserAddressAddRequest request, int expectedResult) throws BlockException {
        // given
        when(contextHolder.getLocale()).thenReturn(new Locale("language", "country", "variant"));
        when(contextHolder.getAuthUserInfo()).thenReturn(createUserInfo(0));
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookMacaupassUser.json", "singleObj", FookMacaupassUser.class));
        when(miniUserAddressClient.add(any(MiniUserAddressEditHttpRequest.class))).thenReturn(
                JsonTestUtils.getStringFromJson("json/miniUserAddress.json", "addObj"));
        mockedStaticConfig = Mockito.mockStatic(ConfigUtils.class);
        mockedStaticConfig.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");

        // when / then
        assertEquals(expectedResult, userAddressController.add(request).getCode());
    }
    
    @ParameterizedTest
    @MethodSource("editTestCases")
    void testEdit(UserAddressAddRequest request, int expectedResult) throws BlockException {
        // given
        when(contextHolder.getLocale()).thenReturn(new Locale("language", "country", "variant"));
        when(contextHolder.getAuthUserInfo()).thenReturn(createUserInfo(0));
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookMacaupassUser.json", "singleObj", FookMacaupassUser.class));
        when(miniUserAddressClient.edit(any(MiniUserAddressEditHttpRequest.class))).thenReturn(
                JsonTestUtils.getStringFromJson("json/miniUserAddress.json", "addObj"));
        mockedStaticConfig = Mockito.mockStatic(ConfigUtils.class);
        mockedStaticConfig.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");

        // when / then
        assertEquals(expectedResult, userAddressController.edit(request).getCode());
    }
    
    @Test
    void testList() throws BlockException {
        // given
        when(contextHolder.getAuthUserInfo()).thenReturn(createUserInfo(0));
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookMacaupassUser.json", "singleObj", FookMacaupassUser.class));
        when(miniUserAddressClient.lists(any(MiniOrdersHttpRequest.class))).thenReturn(
                JsonTestUtils.getStringFromJson("json/miniUserAddress.json", "listObj"));
        mockedStaticConfig = Mockito.mockStatic(ConfigUtils.class);
        mockedStaticConfig.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");

        // when
        Response<UserAddressListResponse> response = userAddressController.list();
        
        // then
        assertEquals(20, response.getData().getSnatchList().size());
    }
    
    @ParameterizedTest
    @MethodSource("setDefaultTestCases")
    void testSetDefault(UserAddressSetRequest request, int expectedResult) throws BlockException {
        // given
        when(contextHolder.getAuthUserInfo()).thenReturn(createUserInfo(0));
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookMacaupassUser.json", "singleObj", FookMacaupassUser.class));
        when(miniUserAddressClient.setDefault(any(MiniUserAddressDetailHttpRequest.class))).thenReturn(
                JsonTestUtils.getStringFromJson("json/miniUserAddress.json", "addObj"));
        mockedStaticConfig = Mockito.mockStatic(ConfigUtils.class);
        mockedStaticConfig.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");

        // when / then
        assertEquals(expectedResult, userAddressController.setDefault(request).getCode());
    }
    
    @ParameterizedTest
    @MethodSource("delTestCases")
    void testDel(UserAddressSetRequest request, int expectedResult) throws BlockException {
        // given
        when(contextHolder.getAuthUserInfo()).thenReturn(createUserInfo(0));
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookMacaupassUser.json", "singleObj", FookMacaupassUser.class));
        when(miniUserAddressClient.del(any(MiniUserAddressDetailHttpRequest.class))).thenReturn(
                JsonTestUtils.getStringFromJson("json/miniUserAddress.json", "addObj"));
        mockedStaticConfig = Mockito.mockStatic(ConfigUtils.class);
        mockedStaticConfig.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");

        // when / then
        assertEquals(expectedResult, userAddressController.del(request).getCode());
    }
    
    @ParameterizedTest
    @MethodSource("geocoderCoordinateTestCases")
    void testGeocoderCoordinate(UserAddressLocationRequest request, int expectedResult) throws BlockException {
        // given
        when(contextHolder.getAuthUserInfo()).thenReturn(createUserInfo(0));
        when(fookMacaupassUserDao.getFirstMpayUserByUserId(anyInt())).thenReturn(
                JsonTestUtils.getObjectFromJson("json/fookMacaupassUser.json", "singleObj", FookMacaupassUser.class));
        when(miniUserAddressClient.geocoderCoordinate(any(MiniUserAddressLocationHttpRequest.class))).thenReturn(
                JsonTestUtils.getStringFromJson("json/miniUserAddress.json", "geocoderCoordinate"));
        mockedStaticConfig = Mockito.mockStatic(ConfigUtils.class);
        mockedStaticConfig.when(() -> ConfigUtils.getProperty(anyString())).thenReturn("7TBjvXTMtMK9f08KkKKWegOOPe4USpms");

        // when / then
        assertEquals(expectedResult, userAddressController.geocoderCoordinate(request).getCode());
    }
    
    static Stream<Arguments> detailTestCases() {
        return Stream.of(
            Arguments.of(new UserAddressRequest(), "浙江省杭州市餘杭區五常街道阿裡巴巴西谿A區(A8樓)")
        );
    }
    
    static Stream<Arguments> addTestCases() {
        return Stream.of(
            Arguments.of(new UserAddressAddRequest(), 200)
        );
    }
    
    static Stream<Arguments> editTestCases() {
        return Stream.of(
            Arguments.of(new UserAddressAddRequest(), 200)
        );
    }
    
    static Stream<Arguments> setDefaultTestCases() {
        return Stream.of(
            Arguments.of(new UserAddressSetRequest(), 200)
        );
    }
    
    static Stream<Arguments> delTestCases() {
        return Stream.of(
            Arguments.of(new UserAddressSetRequest(), 200)
        );
    }
    
    static Stream<Arguments> geocoderCoordinateTestCases() {
        return Stream.of(
            Arguments.of(new UserAddressLocationRequest(), 200)
        );
    }
    
    private UserInfo createUserInfo(int userId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        return userInfo;
    }
} 