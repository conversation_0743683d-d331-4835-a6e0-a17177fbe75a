<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookVoucherSettlementDataDao">

  <select id="sumVoucherSettlementData" resultType="com.mcoin.mall.bo.VoucherSettlementAmountBo">
    SELECT
      SUM(billamount) as totalamount,
      SUM(merchantsettleamount) as totalsettlement,
      SUM(commission) as totalcommission,
      SUM(momecoinsamount) as totalmomecoins,
      SUM(mpayintegral) as totalmpayintegral
    FROM
      fook_voucher_settlement_data
    WHERE
      usetime BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
  </select>
</mapper>