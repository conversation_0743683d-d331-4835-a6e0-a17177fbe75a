package com.mcoin.mall.unit;

import com.mcoin.mall.constant.RefundSceneEnum;
import com.mcoin.mall.controller.management.OrderRefundController;
import com.mcoin.mall.model.OrderRefundApprovalRequest;
import com.mcoin.mall.model.OrderRefundApprovalResponse;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.service.order.OrderService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 订单退款控制器测试
 */
class OrderRefundControllerTest extends ControllerMock {

    @InjectMocks
    @Resource
    private OrderRefundController orderRefundController;

    @Mock
    OrderService orderService;


    @Test
    void testRefundApprovalProcess() {
        // when
        OrderRefundApprovalRequest req = new OrderRefundApprovalRequest();
        req.setId(243629);
        req.setType(1);
        req.setOperationType(1);
        req.setOperationId(0);
        req.setRefundScene(RefundSceneEnum.APPROVAL_REFUND.getCode());

        OrderRefundApprovalResponse response = new OrderRefundApprovalResponse();
        response.setCode(1);

        when(orderService.approvalRefund(any(OrderRefundApprovalRequest.class))).thenReturn(response);

        Response<OrderRefundApprovalResponse> result = orderRefundController.refundApprovalProcess(req);
        
        // then
        assertEquals(1, result.getStatus());
    }
} 