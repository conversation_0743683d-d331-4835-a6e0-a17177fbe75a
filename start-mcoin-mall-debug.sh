#!/bin/bash

APP_NAME="mcoin-mall"
LOG_DIR="/home/<USER>/logs/$APP_NAME"

if [ ! -d "$LOG_DIR" ] 
then
mkdir -p $LOG_DIR
fi

JAVA_OPTS=""
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=400"
JAVA_OPTS="$JAVA_OPTS -Xms4g"
JAVA_OPTS="$JAVA_OPTS -Xmx8g"
JAVA_OPTS="$JAVA_OPTS -Xss256K"
JAVA_OPTS="$JAVA_OPTS -XX:MetaspaceSize=256m"
JAVA_OPTS="$JAVA_OPTS -XX:MaxMetaspaceSize=1g"
JAVA_OPTS="$JAVA_OPTS -XX:SoftRefLRUPolicyMSPerMB=0"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCDetails"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCTimeStamps"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCApplicationStoppedTime"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=$LOG_DIR/$APP_NAME.memory.dump"
JAVA_OPTS="$JAVA_OPTS -Xloggc:$LOG_DIR/$APP_NAME.gc.log"
JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=19000"

SPRING_CONFIG="--spring.config.location=classpath:application.properties,classpath:bootstrap.properties"

nohup java -jar $JAVA_OPTS $APP_NAME.jar $SPRING_CONFIG >> $LOG_DIR/nohup.log 2>&1 &