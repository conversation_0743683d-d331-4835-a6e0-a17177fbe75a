package com.mcoin.mall.controller.job;

import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.Responses;
import com.mcoin.mall.model.job.SynVoucherCode2PlatformRequest;
import com.mcoin.mall.service.job.CouponSynJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/job/coupon")
@Slf4j
public class CouponJobController {
    @Resource
    private CouponSynJob couponSynJob;

    /**
     * 同步券碼信息到券平台
     * PHP(code:syn)
     * @return
     */
    @PostMapping(value = "/synCoupon")
    public Response<String> synCoupon(@Valid SynVoucherCode2PlatformRequest request) {
        couponSynJob.updSynVoucherCode2Platform(request);
        return Responses.ok("success");
    }
}
