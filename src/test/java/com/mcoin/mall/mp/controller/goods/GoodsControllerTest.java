package com.mcoin.mall.mp.controller.goods;

import com.mcoin.mall.mp.BaseUnitTest;
import com.mcoin.mall.mp.exception.BusinessException;
import com.mcoin.mall.mp.model.Response;
import com.mcoin.mall.mp.model.goods.GoodsDetailResponse;
import com.mcoin.mall.mp.security.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@Transactional
public class GoodsControllerTest extends BaseUnitTest {

    @Autowired
    private GoodsController goodsController;

    @BeforeEach
    public void setUp() {
        UserInfo user = new UserInfo();
        user.setCustId("1");
        user.setSid(65);
        when(contextHolder.getAuthUserInfo()).thenReturn(user);
        when(contextHolder.getSid()).thenReturn(65);
        
        // Mock RabbitTemplate behavior
        doNothing().when(goodsDetailTemplate).convertAndSend(any(Message.class));
    }

    @Test
    public void queryGoodsDetailById_ShouldReturnValidDetail() {
        // 执行请求
        Response<GoodsDetailResponse> response = goodsController.queryGoodsDetailById(64, 1, null);

        // 验证响应
        assertNotNull(response);
        assertEquals(1, response.getCode());
        assertNotNull(response.getData());

        GoodsDetailResponse detail = response.getData();
        assertNotNull(detail.getSpecValueList());
        assertFalse(detail.getSpecValueList().isEmpty());
        assertNotNull(detail.getSpecValue());
        assertFalse(detail.getSpecValue().isEmpty());
    }

    @Test
    public void queryGoodsDetailById_ShouldThrowException_WhenIdIsNull() {
        // 执行请求并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            goodsController.queryGoodsDetailById(null, 1, null);
        });

        assertEquals(Response.Code.FAIL.getCode(), exception.getRespCode().getCode());
        assertEquals(Response.Code.FAIL.getMsg(), exception.getMessage());
    }

    @Test
    public void queryGoodsDetailById_ShouldReturnNull_WhenGoodsNotFound() {
        // 执行请求
        assertThrows(BusinessException.class, () -> {
            Response<GoodsDetailResponse> response = goodsController.queryGoodsDetailById(999999, 1, null);
        });
    }

    @Test
    public void queryGoodsDetailById_ShouldHandleSessionId() {
        // 执行请求
        Response<GoodsDetailResponse> response = goodsController.queryGoodsDetailById(64, 1, 136);

        // 验证响应
        assertNotNull(response);
        assertEquals(1, response.getCode());
        assertNotNull(response.getData());
    }
}