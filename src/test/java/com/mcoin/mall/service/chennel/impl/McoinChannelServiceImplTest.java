package com.mcoin.mall.service.chennel.impl;

import static com.mcoin.mall.util.CacheNameUtils.getMcoinScoreToken;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.mcoin.mall.client.McoinClient;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.exception.RetryException;
import com.mcoin.mall.util.ConfigUtils;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

@ExtendWith(MockitoExtension.class)
public class McoinChannelServiceImplTest{

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private McoinClient mcoinClient;

    @InjectMocks
    private McoinChannelServiceImpl mcoinChannelService;

    private MockedStatic<ConfigUtils> configUtilsMockedStatic;

    @BeforeEach
    public void setUp() {
        configUtilsMockedStatic = mockStatic(ConfigUtils.class);
        when(ConfigUtils.getProperty("spring.application.name")).thenReturn("mcoin-mall");
        when(ConfigUtils.getProperty("spring.profiles.active")).thenReturn("test");
        when(ConfigUtils.getProperty("mcoin.appid")).thenReturn("testAppId");
        when(ConfigUtils.getProperty("mcoin.privatekey")).thenReturn("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAg" +
                "EAAoIBAQDf++HcqMvHnzDRcL3yMTZfhuYF7Xf4Qx3PhoKR6lrlSTtgxfCEfxvcFJQ+q8LP7NicyuE/9FSy07vWEC6mlPtJlq1yiYg" +
                "hDgvJynEiw5fwAk7b7MggcAfsGgTY+OLcxLGsKGt3Q9dOj4nVc3tMI69tci/6rmrzO0PPsonffgooAghFC7s+cGiON7/blfZRvBBQ" +
                "2NBh8rdRVZMpQZb8nlYAZ29Zx1HkLktsOd6FrCNO7kHY4P1RtroD95vArl3YwxGpAa6CzZs1E4jk16bT1m0zoHkMt8eBuz9mqmcyq" +
                "F5w85EmZsHrpVvhnIFY/ptrUP6EK9UIBPXh54amHo0lgpqrAgMBAAECggEBAJ8QtFAOl9mGVhcHJ/3tsR2aIZUoFcD7eRo9/lA9zJ" +
                "t0rHSHXc3aryBWhQkU1d7v5s1Cz0Cp9dShxY26JEctGmAiX78tqL1AymJeIIZ9vVM3cGWC/IT8ysODntmvtvztuvf2JIuoZCloiox" +
                "J3NAvr4/cPfKbF1zxQ7Emq/9J9VB+/LTWyFmiFGaS4bb11FW6QHY4MaCzBoiAXIKJ860Nr8i7lix3BErF2TlGm/odeKg+0xn6RVjN" +
                "wQ8RPOU6ZQRP73eMNq2OhDNG8F8LNGXBvktIdo1KXGI4SBFeQADXMRGRMlB+G3HPg7MfrcYnbA4lx8MNyL9hw0zJxlpCbzt3MMECg" +
                "YEA9xJmc4l1c95DOf616vXFpYgIo/43sCZWnYthxfS/DDg7DKaY26gpra5lKH2OyQtxxp0qX8qtzSckmVC4/jW8H2eCn8G1R3hFOgX" +
                "7Y3Wj06s4esoWi9hY7lF6WSwudkoa9RPWWsCfzu6WFt739W1RTg/rxCqnDjaBZvLIOcWfV3sCgYEA6BPmsgcOFgb27jTXF/UlTTNdD" +
                "PoyNw6esl0e9cSYQFdGhLx1qn1OnmJnbt0J2tqNZKqJ/R5gx00hRQAu353OuOwkiqsyY63IlB7rpTcF4ZJQVNO5vqJZ0pB2C6HSuln" +
                "mQyeVlbDipc7CP/s++LAMQ4IoZKma7DiIknX7OD9BypECgYAVYSM6Zi+iqh35G8BUJ5ZFv6K3xhy9gmPGWDRKs+YAQbFiY9wgTcnlf" +
                "IzGVy8O2I2s2Ra8mUY21WdGWQTZAn9X3FYiStnL6G1dGv1o2tolS9CkV25iBYOUg7ppkvgmRj1U7bWDvt1VQ7H7IqokM6Rwc9I79FD" +
                "mWvMRnHqU/TPPlwKBgH8z0UGI7maSYKwFmFOQUWa0HW9sfzOANumKctrAa7bwXz2H0nKlBf937jtsuecT3WXst39eNCtpEjAwvoBgj" +
                "Zr8C7dZyF+sNAFDxWMj+nw95vvnpKphcBwihCEyDD+J4NS0EKAgeMnqvru06ToDvGUQJTWvZLPO9MRaGF0nBteRAoGBAJ61axSDUhK" +
                "136MCBv2q+wye2AnX80XwH5xp5O70+Tn1+v10TlsF3bRs4RvpXTZ5Zi6EF/TVilZpDbmqCieI0rKpmF0fCNNFMo7xYKke0Lx7wTsp5" +
                "vnsqYuD2lGS4HWfkmBLGue0PLvoK9C7yFyERGJGih46tnz8+yVtOBkCPrXt");
        when(ConfigUtils.getProperty("mcoin.publickey")).thenReturn("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQE" +
                "AxuiAcGf/DZASDToHatxeZmpwIp712DwWeIEwaQCdsmY9hGILb8jJOl3qD9bU4gD8znFGK9SLhvRAQn044wY7+xs6is+R1vGVpg6KY" +
                "zIz71i8AHcPvKkMuEz9BSQJp6HjVmK48fLaIlwZBqMHq5/G6wfFi6N+2utuoqNgTJQW42eo2wyQoE+L4NKsusm25EIN7ZMfuK1ZXWv" +
                "BFZ0bIQJVyRvo0gIuneZEL1RXPEGwDKnFpsWWDKc/pPDMqKRD2QNkeI61c/4hFID5q9Tb9CfWk3UZbCWH6O3UnoPqQDJMPnFVaOhpo" +
                "BHqYH1acowE8FcXM0ZSePswJkUChfrBJpFdXwIDAQAB");
    }
    @AfterEach
    public void tearDown() {
        configUtilsMockedStatic.close();
    }

    @SuppressWarnings("rawtypes")
    @Test
    public void testGetPointTokenExists() throws RetryException, BlockException {
        String custId = "testUser";
        String tokenCacheKey = getMcoinScoreToken(custId);
        RBucket tokenBucket = mock(RBucket.class);
        when(redissonClient.getBucket(tokenCacheKey)).thenReturn(tokenBucket);
        when(tokenBucket.get()).thenReturn("existingToken");
        when(mcoinClient.getBalance(any())).thenReturn("{\"code\":\"0000\",\"data\":{\"integral\":\"63535\"," +
                "\"userStatus\":\"0\"},\"msg\":\"success\",\"sign\":\"hTkhw60Fhfp6Rq62a2o3/z70fZfSc4CvBToAI7wAJ7PZ" +
                "K3fB/n96wOheVnFhush+nPH1v4P4BZyabFpg5t+ApOqLKPJuIx1Jslf4TSlwSC6ku1I8bjT4q5CZdC0BxPA14MS6s2yw+GKN64" +
                "ojGm0hRRXi7+Ea+hFPDVTBztjqhwDAk3DPXgueLS2eHwxl2NJHyTij3P0B3EV4eGUbv3+unrFp4r8mFCHLNVm4nowOxDp/g0YE6" +
                "kcMrohlNbw0AziphofRYtxh6l7m01nxnVJiv9+vLnQ+FeHqo6SNNtwklqzLPNOiuM5OUn0DP5ROz3yhNKdmCi3Brw8E3qYK7IHRTQ==\"}");
    
        Integer result = mcoinChannelService.getPoint(custId);
    
        assertNotNull(result);
        verify(tokenBucket, times(1)).get();
        verify(mcoinClient, times(0)).getToken(any());
        verify(mcoinClient, times(1)).getBalance(any());
    }

    @Test
    public void testGetPointTokenNotExistsFetchToken() throws RetryException, BlockException {
        String custId = "testUser";
        String tokenCacheKey = getMcoinScoreToken(custId);
        RBucket tokenBucket = mock(RBucket.class);
        when(redissonClient.getBucket(tokenCacheKey)).thenReturn(tokenBucket);
        when(tokenBucket.get()).thenReturn(null);
        when(mcoinClient.getToken(any())).thenReturn("tokenResponse");
        when(mcoinClient.getBalance(any())).thenReturn("{\"code\":\"0000\",\"data\":{\"integral\":\"63535\"," +
                "\"userStatus\":\"0\"},\"msg\":\"success\",\"sign\":\"hTkhw60Fhfp6Rq62a2o3/z70fZfSc4CvBToAI7wAJ7PZ" +
                "K3fB/n96wOheVnFhush+nPH1v4P4BZyabFpg5t+ApOqLKPJuIx1Jslf4TSlwSC6ku1I8bjT4q5CZdC0BxPA14MS6s2yw+GKN64" +
                "ojGm0hRRXi7+Ea+hFPDVTBztjqhwDAk3DPXgueLS2eHwxl2NJHyTij3P0B3EV4eGUbv3+unrFp4r8mFCHLNVm4nowOxDp/g0YE6" +
                "kcMrohlNbw0AziphofRYtxh6l7m01nxnVJiv9+vLnQ+FeHqo6SNNtwklqzLPNOiuM5OUn0DP5ROz3yhNKdmCi3Brw8E3qYK7IHRTQ==\"}");
        when(mcoinClient.getToken(any())).thenReturn("{\"code\":\"0000\",\"data\":{\"expires\":\"7200\"," +
                "\"token\":\"e906676d66ceb41caf73d8369eab2929\",\"userId\":\"mcidqb1pg64wrkTpDNrY1A\"}," +
                "\"msg\":\"success\",\"sign\":\"fWIkLqpqSkz6uEYbDAW8mdiV+qTT0eqDmhgADKv7N3ySnkV4m6kvjTTcGhcl+" +
                "X9P+y4RV5YUoi1Mb1eIUZYqa3UcTXsgFi6e5q4vYLleERfCDSew0S3RduNej2DhU5lw+66QEy6WlupLwA01bkwp6TUhf" +
                "CO60OT+Uh1eIcIs13jhvzPq4IV3KSsWYkLkReFdDTDoZhYVC1WNSnth4ugIRuMtlkeoUsNcNTG2Stom6Y5MAszwdtGgFx" +
                "wNhYhbXWUN0TDbCLIyA9wLo7xnx+K383330TqWrB8Y04CUiPYQXjZxE0rJtnJhCZ9Pc+E+WmbMrYis63jSNEzJW1EfBHZbbA==\"}");
    
        Integer result = mcoinChannelService.getPoint(custId);
    
        assertNotNull(result);
        verify(tokenBucket, times(1)).get();
        verify(mcoinClient, times(1)).getToken(any());
        verify(mcoinClient, times(1)).getBalance(any());
    }

    @Test
    public void testGetPointTokenInvalidThrowRetryException() throws RetryException, BlockException {
        String custId = "testUser";
        String tokenCacheKey = getMcoinScoreToken(custId);
        RBucket tokenBucket = mock(RBucket.class);
        when(redissonClient.getBucket(tokenCacheKey)).thenReturn(tokenBucket);
        when(tokenBucket.get()).thenReturn("existingToken");
        when(mcoinClient.getBalance(any())).thenReturn("{\"code\":\"9996\"}");
    
        assertThrows(RetryException.class, () -> {
            mcoinChannelService.getPoint(custId);
        });
    
        verify(tokenBucket, times(1)).get();
        verify(tokenBucket, times(1)).delete();
        verify(mcoinClient, times(1)).getBalance(any());
    }

    @Test
    public void testGetPointSignVerificationFailsThrowBusinessException() throws RetryException, BlockException {
        String custId = "testUser";
        String tokenCacheKey = getMcoinScoreToken(custId);
        RBucket tokenBucket = mock(RBucket.class);
        when(redissonClient.getBucket(tokenCacheKey)).thenReturn(tokenBucket);
        when(tokenBucket.get()).thenReturn("existingToken");
        when(mcoinClient.getBalance(any())).thenReturn("{\"code\":\"0000\",\"data\":{\"integral\":\"63535\"," +
                "\"userStatus\":\"0\"},\"msg\":\"success\",\"sign\":\"h1Tkhw60Fhfp6Rq62a2o3/z70fZfSc4CvBToAI7wAJ7PZ" +
                "K3fB/n96wOheVnFhush+nPH1v4P4BZyabFpg5t+ApOqLKPJuIx1Jslf4TSlwSC6ku1I8bjT4q5CZdC0BxPA14MS6s2yw+GKN64" +
                "ojGm0hRRXi7+Ea+hFPDVTBztjqhwDAk3DPXgueLS2eHwxl2NJHyTij3P0B3EV4eGUbv3+unrFp4r8mFCHLNVm4nowOxDp/g0YE6" +
                "kcMrohlNbw0AziphofRYtxh6l7m01nxnVJiv9+vLnQ+FeHqo6SNNtwklqzLPNOiuM5OUn0DP5ROz3yhNKdmCi3Brw8E3qYK7IHRTQ==\"}");
    
        assertThrows(BusinessException.class, () -> {
            mcoinChannelService.getPoint(custId);
        });
    
        verify(tokenBucket, times(1)).get();
        verify(mcoinClient, times(1)).getBalance(any());
    }

    @Test
    public void testGetPointTokenFetchFailsThrowBusinessException() throws RetryException, BlockException {
        String custId = "testUser";
        String tokenCacheKey = getMcoinScoreToken(custId);
        RBucket tokenBucket = mock(RBucket.class);
        when(redissonClient.getBucket(tokenCacheKey)).thenReturn(tokenBucket);
        when(tokenBucket.get()).thenReturn(null);
        when(mcoinClient.getToken(any())).thenReturn("");
    
        assertThrows(BusinessException.class, () -> {
            mcoinChannelService.getPoint(custId);
        });
    
        verify(tokenBucket, times(1)).get();
        verify(mcoinClient, times(1)).getToken(any());
    }

}