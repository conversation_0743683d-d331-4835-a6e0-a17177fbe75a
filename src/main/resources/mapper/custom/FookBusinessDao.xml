<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessDao">
  <select id="getByIds" resultType="com.mcoin.mall.bean.FookBusiness">
    SELECT
    <include refid="Base_Column_List" />
    FROM fook_business
    WHERE id in
    <foreach item="item" collection="businessIds" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="selectByPrimaryKeyTranslations" resultType="com.mcoin.mall.bean.FookBusiness">
    SELECT a.id,
           a.areas_id,
           a.code,
           if('en' = #{lang,jdbcType=VARCHAR}, IFNULL(b.t_name, a.name), a.name) as name,
           a.status,
           a.system_type
    FROM fook_business a
           left join fook_business_translations b
                     on a.id = b.business_id
    WHERE a.id = #{id,jdbcType=INTEGER}
  </select>

    <select id="selectSettlementOptionalBusiness" resultType="com.mcoin.mall.bo.SettlementOptionalBusinessBo">
        select id, optional_type as optionalType
        from fook_business
        where status = 1
        and optional_type > 0
    </select>
</mapper>