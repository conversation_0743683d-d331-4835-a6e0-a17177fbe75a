package com.mcoin.mall.controller.business;

import cn.hutool.core.lang.Validator;
import com.mcoin.mall.exception.BusinessException;
import com.mcoin.mall.model.*;
import com.mcoin.mall.service.business.AsiaMilesService;
import com.mcoin.mall.util.AsiaMilesUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Locale;

@RestController
public class AsiaMilesController {


    @Resource
    private AsiaMilesService asiaMilesService;

    @Resource
    private MessageSource messageSource;

    /**
     * 获取万里通会员信息
     *
     * @return
     */
    @PostMapping(value = "/api/auth/amlist", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    }, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<AsiaMilesUserInfoListResponse> getUserInfo() {
        AsiaMilesUserInfoListResponse response = asiaMilesService.getAsiaMilesList();
        return Responses.ok(response);
    }

    /**
     * 万里通下单前校验
     *
     * @return
     */
    @PostMapping(value = "/api/auth/judgeasiamiles", consumes = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            MediaType.TEXT_PLAIN_VALUE
    })
    public Response<Object> judgeAsiaMiles(@Valid @RequestBody AsiaMilesFromRequest request) {
        AsiaMilesFromResponse response = new AsiaMilesFromResponse();
        // 亚洲万里通会员号码
        String milesMember = request.getMiles_member();
        if (StringUtils.isBlank(milesMember)) {
            throw getBusinessException("message.ordersaveinfo.miles_member");
        }
        // 亚洲万里通会员号码
        String milesMemberRepeat = request.getMiles_member_repeat();
        if (StringUtils.isBlank(milesMemberRepeat)) {
            // 请再次输入会员号码
            throw getBusinessException("message.ordersaveinfo.miles_member_repeat");
        }
        if (!milesMember.equals(milesMemberRepeat)) {
            // 两次输入的会员号码不一致
            throw getBusinessException("message.ordersaveinfo.no_miles_member");
        }
        // 会员姓氏
        String milesNameFamily = request.getMiles_name_family();
        if (StringUtils.isBlank(milesNameFamily) || StringUtils.isBlank(milesNameFamily.trim())) {
            // 请输入会员姓氏
            throw getBusinessException("message.ordersaveinfo.miles_name_family");
        }
        milesNameFamily = milesNameFamily.trim();
        // 会员名字
        String milesNameGiven = request.getMiles_name_given();
        if (StringUtils.isBlank(milesNameGiven) || StringUtils.isBlank(milesNameGiven.trim())) {
            // 请输入会员名字
            throw getBusinessException("message.ordersaveinfo.miles_name_given");
        }
        milesNameGiven = milesNameGiven.trim();
        if (Validator.hasChinese(milesNameFamily)) {
            // 会员姓氏不能含有中文
            throw getBusinessException("message.ordersaveinfo.last_name_no_chinese");
        }
        if (Validator.hasChinese(milesNameGiven)) {
            // 会员名字不能含有中文
            throw getBusinessException("message.ordersaveinfo.first_name_no_chinese");
        }
        if (Validator.hasNumber(milesNameFamily)) {
            // 会员姓氏不能含有数字
            throw getBusinessException("message.ordersaveinfo.last_name_no_num");
        }
        if (Validator.hasNumber(milesNameGiven)) {
            // 会员名字不能含有数字
            throw getBusinessException("message.ordersaveinfo.first_name_no_num");
        }
        if (milesNameFamily.length() > 40) {
            // 会员姓氏长度不能超过40位
            throw getBusinessException("message.ordersaveinfo.last_name_length_40");
        }
        if (milesNameGiven.length() > 40) {
            // 会员姓名长度不能超过40位
            throw getBusinessException("message.ordersaveinfo.first_name_length_40");
        }
        // 验证会员号码是否符合亚洲万里通验证规则
        if (!AsiaMilesUtils.verification(milesMember)) {
            // 会员号码不符合亚洲万里通验证规则
            throw getBusinessException("message.ordersaveinfo.miles_member_verification");
        }
        return Responses.okStatus200(response);
    }

    private BusinessException getBusinessException(String code) {
        Locale locale = LocaleContextHolder.getLocale();
        return new BusinessException(Response.Code.BAD_REQUEST, messageSource.getMessage(code, null, locale));
    }


}
