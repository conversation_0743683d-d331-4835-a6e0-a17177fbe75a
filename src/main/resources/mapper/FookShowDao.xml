<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookShowDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookShow">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cn_name" jdbcType="VARCHAR" property="cnName" />
    <result column="en_name" jdbcType="VARCHAR" property="enName" />
    <result column="banner_img" jdbcType="CHAR" property="bannerImg" />
    <result column="href_url" jdbcType="VARCHAR" property="hrefUrl" />
    <result column="clause_content" jdbcType="VARCHAR" property="clauseContent" />
    <result column="zone_name_switch" jdbcType="TINYINT" property="zoneNameSwitch" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="show_backgroud" jdbcType="VARCHAR" property="showBackgroud" />
    <result column="show_theme" jdbcType="INTEGER" property="showTheme" />
    <result column="banner_title" jdbcType="VARCHAR" property="bannerTitle" />
    <result column="url_type" jdbcType="INTEGER" property="urlType" />
    <result column="banner_src" jdbcType="VARCHAR" property="bannerSrc" />
  </resultMap>
  <sql id="Base_Column_List">
    id, cn_name, en_name, banner_img, href_url, clause_content, zone_name_switch, created_at, updated_at,
    created_by, updated_by, show_backgroud, show_theme, banner_title, url_type, banner_src
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_show
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_show
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookShow">
    insert into fook_show
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cnName != null">
        cn_name,
      </if>
      <if test="enName != null">
        en_name,
      </if>
      <if test="bannerImg != null">
        banner_img,
      </if>
      <if test="hrefUrl != null">
        href_url,
      </if>
      <if test="zoneNameSwitch != null">
        zone_name_switch,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="showBackgroud != null">
        show_backgroud,
      </if>
      <if test="showTheme != null">
        show_theme,
      </if>
      <if test="bannerTitle != null">
        banner_title,
      </if>
      <if test="urlType != null">
        url_type,
      </if>
      <if test="bannerSrc != null">
        banner_src,
      </if>
      <if test="clauseContent != null">
        clause_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="cnName != null">
        #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="enName != null">
        #{enName,jdbcType=VARCHAR},
      </if>
      <if test="bannerImg != null">
        #{bannerImg,jdbcType=CHAR},
      </if>
      <if test="hrefUrl != null">
        #{hrefUrl,jdbcType=VARCHAR},
      </if>
      <if test="zoneNameSwitch != null">
        #{zoneNameSwitch,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="showBackgroud != null">
        #{showBackgroud,jdbcType=VARCHAR},
      </if>
      <if test="showTheme != null">
        #{showTheme,jdbcType=INTEGER},
      </if>
      <if test="bannerTitle != null">
        #{bannerTitle,jdbcType=VARCHAR},
      </if>
      <if test="urlType != null">
        #{urlType,jdbcType=INTEGER},
      </if>
      <if test="bannerSrc != null">
        #{bannerSrc,jdbcType=VARCHAR},
      </if>
      <if test="clauseContent != null">
        #{clauseContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookShow">
    update fook_show
    <set>
      <if test="cnName != null">
        cn_name = #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="enName != null">
        en_name = #{enName,jdbcType=VARCHAR},
      </if>
      <if test="bannerImg != null">
        banner_img = #{bannerImg,jdbcType=CHAR},
      </if>
      <if test="hrefUrl != null">
        href_url = #{hrefUrl,jdbcType=VARCHAR},
      </if>
      <if test="zoneNameSwitch != null">
        zone_name_switch = #{zoneNameSwitch,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="showBackgroud != null">
        show_backgroud = #{showBackgroud,jdbcType=VARCHAR},
      </if>
      <if test="showTheme != null">
        show_theme = #{showTheme,jdbcType=INTEGER},
      </if>
      <if test="bannerTitle != null">
        banner_title = #{bannerTitle,jdbcType=VARCHAR},
      </if>
      <if test="urlType != null">
        url_type = #{urlType,jdbcType=INTEGER},
      </if>
      <if test="bannerSrc != null">
        banner_src = #{bannerSrc,jdbcType=VARCHAR},
      </if>
      <if test="clauseContent != null">
        clause_content = #{clauseContent,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookShow">
    update fook_show
    set cn_name = #{cnName,jdbcType=VARCHAR},
      en_name = #{enName,jdbcType=VARCHAR},
      banner_img = #{bannerImg,jdbcType=CHAR},
      href_url = #{hrefUrl,jdbcType=VARCHAR},
      clause_content = #{clauseContent,jdbcType=VARCHAR},
      zone_name_switch = #{zoneNameSwitch,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      show_backgroud = #{showBackgroud,jdbcType=VARCHAR},
      show_theme = #{showTheme,jdbcType=INTEGER},
      banner_title = #{bannerTitle,jdbcType=VARCHAR},
      url_type = #{urlType,jdbcType=INTEGER},
      banner_src = #{bannerSrc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>