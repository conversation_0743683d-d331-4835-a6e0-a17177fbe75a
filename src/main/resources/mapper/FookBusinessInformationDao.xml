<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBusinessInformationDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBusinessInformation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="area_id" jdbcType="SMALLINT" property="areaId" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_zh" jdbcType="VARCHAR" property="nameZh" />
    <result column="name_en" jdbcType="VARCHAR" property="nameEn" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="if_index_show" jdbcType="BIT" property="ifIndexShow" />
    <result column="if_hot" jdbcType="BIT" property="ifHot" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="longitude" jdbcType="VARCHAR" property="longitude" />
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
    <result column="google_longitude" jdbcType="VARCHAR" property="googleLongitude" />
    <result column="google_dimension" jdbcType="VARCHAR" property="googleDimension" />
  </resultMap>
  <sql id="Base_Column_List">
    id, area_id, pid, `type`, `name`, name_zh, name_en, sort, icon, `enable`, if_index_show,
    if_hot, code, longitude, dimension, google_longitude, google_dimension
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_business_information
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_business_information
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBusinessInformation">
    insert into fook_business_information
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="nameZh != null">
        name_zh,
      </if>
      <if test="nameEn != null">
        name_en,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="ifIndexShow != null">
        if_index_show,
      </if>
      <if test="ifHot != null">
        if_hot,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="googleLongitude != null">
        google_longitude,
      </if>
      <if test="googleDimension != null">
        google_dimension,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=SMALLINT},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameZh != null">
        #{nameZh,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="ifIndexShow != null">
        #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="ifHot != null">
        #{ifHot,jdbcType=BIT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="googleLongitude != null">
        #{googleLongitude,jdbcType=VARCHAR},
      </if>
      <if test="googleDimension != null">
        #{googleDimension,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBusinessInformation">
    update fook_business_information
    <set>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=SMALLINT},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameZh != null">
        name_zh = #{nameZh,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        name_en = #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="ifIndexShow != null">
        if_index_show = #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="ifHot != null">
        if_hot = #{ifHot,jdbcType=BIT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="googleLongitude != null">
        google_longitude = #{googleLongitude,jdbcType=VARCHAR},
      </if>
      <if test="googleDimension != null">
        google_dimension = #{googleDimension,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBusinessInformation">
    update fook_business_information
    set area_id = #{areaId,jdbcType=SMALLINT},
      pid = #{pid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      name_zh = #{nameZh,jdbcType=VARCHAR},
      name_en = #{nameEn,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      icon = #{icon,jdbcType=VARCHAR},
      `enable` = #{enable,jdbcType=BIT},
      if_index_show = #{ifIndexShow,jdbcType=BIT},
      if_hot = #{ifHot,jdbcType=BIT},
      code = #{code,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      dimension = #{dimension,jdbcType=VARCHAR},
      google_longitude = #{googleLongitude,jdbcType=VARCHAR},
      google_dimension = #{googleDimension,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>