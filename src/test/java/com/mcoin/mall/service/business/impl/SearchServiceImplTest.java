package com.mcoin.mall.service.business.impl;

import com.mcoin.mall.bean.FookBusinessStoreProduct;
import com.mcoin.mall.constant.ClientTypeEnum;
import com.mcoin.mall.dao.FookBusinessStoreProductDao;
import com.mcoin.mall.dao.FookTemporaryProductDao;
import com.mcoin.mall.model.CheckSearchValueRequest;
import com.mcoin.mall.model.CheckSearchValueResponse;
import com.mcoin.mall.util.ConfigUtils;
import com.mcoin.mall.util.MiniProgramDisplayUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SearchServiceImplTest {

    @Mock
    private FookTemporaryProductDao fookTemporaryProductDao;

    @Mock
    private FookBusinessStoreProductDao fookBusinessStoreProductDao;

    @Mock
    private com.mcoin.mall.dao.FookStoresDao fookStoresDao;

    @InjectMocks
    private SearchServiceImpl searchService;

    private MockedStatic<MiniProgramDisplayUtil> mockedMiniProgramDisplayUtil;
    private MockedStatic<ConfigUtils> mockedConfigUtils;

    @BeforeEach
    public void setUp() {
        mockedMiniProgramDisplayUtil = Mockito.mockStatic(MiniProgramDisplayUtil.class);
        mockedConfigUtils = Mockito.mockStatic(ConfigUtils.class);
    }

    @AfterEach
    public void tearDown() {
        mockedMiniProgramDisplayUtil.close();
        mockedConfigUtils.close();
    }

    @Test
    public void testCheckSearchValue_WithHarmonyClientAndSwitchEnabled() {
        // Arrange
        CheckSearchValueRequest request = new CheckSearchValueRequest();
        request.setSearchTerm("test");
        String clientType = ClientTypeEnum.HARMONY.toString();

        // Mock the MiniProgramDisplayUtil to return TRUE for HARMONY client type
        mockedMiniProgramDisplayUtil.when(() -> MiniProgramDisplayUtil.filterMiniprogramProduct(eq(ClientTypeEnum.HARMONY.toString())))
                .thenReturn(Boolean.TRUE);

        // Mock the temporary product count
        when(fookTemporaryProductDao.selectTotol()).thenReturn(10);

        // Mock the product IDs (filtered to exclude type=12)
        List<Integer> filteredProductIds = Arrays.asList(1, 2, 3);
        when(fookTemporaryProductDao.selectSearchTerm(anyString(), anyString(), anyString(), anyString(), eq(Boolean.TRUE)))
                .thenReturn(filteredProductIds);

        // Mock the store products
        List<FookBusinessStoreProduct> storeProducts = new ArrayList<>();
        FookBusinessStoreProduct storeProduct1 = new FookBusinessStoreProduct();
        storeProduct1.setStoreid(101);
        storeProducts.add(storeProduct1);
        FookBusinessStoreProduct storeProduct2 = new FookBusinessStoreProduct();
        storeProduct2.setStoreid(102);
        storeProducts.add(storeProduct2);
        when(fookBusinessStoreProductDao.getStoresIdByProductIds(eq(filteredProductIds))).thenReturn(storeProducts);

        // Mock the stores
        List<Integer> storeIds = Arrays.asList(101, 102);
        List<com.mcoin.mall.bean.FookStores> stores = new ArrayList<>();
        com.mcoin.mall.bean.FookStores store1 = new com.mcoin.mall.bean.FookStores();
        store1.setId(101);
        store1.setEnable(1);
        stores.add(store1);
        com.mcoin.mall.bean.FookStores store2 = new com.mcoin.mall.bean.FookStores();
        store2.setId(102);
        store2.setEnable(1);
        stores.add(store2);
        when(fookStoresDao.getStores(anyList())).thenReturn(stores);

        // Act
        CheckSearchValueResponse response = searchService.checkSearchValue(request, clientType);

        // Assert
        assertEquals(3, response.getProductCount());
        assertEquals(2, response.getStoresCount());

        // Verify that selectSearchTerm was called with the correct parameters
        verify(fookTemporaryProductDao).selectSearchTerm(anyString(), anyString(), anyString(), anyString(), eq(Boolean.TRUE));
    }

    @Test
    public void testCheckSearchValue_WithNonHarmonyClient() {
        // Arrange
        CheckSearchValueRequest request = new CheckSearchValueRequest();
        request.setSearchTerm("test");
        String clientType = ClientTypeEnum.IOS.toString();

        // Mock the MiniProgramDisplayUtil to return FALSE for non-HARMONY client type
        mockedMiniProgramDisplayUtil.when(() -> MiniProgramDisplayUtil.filterMiniprogramProduct(eq(ClientTypeEnum.IOS.toString())))
                .thenReturn(Boolean.FALSE);

        // Mock the temporary product count
        when(fookTemporaryProductDao.selectTotol()).thenReturn(10);

        // Mock the product IDs (not filtered)
        List<Integer> allProductIds = Arrays.asList(1, 2, 3, 4);
        when(fookTemporaryProductDao.selectSearchTerm(anyString(), anyString(), anyString(), anyString(), eq(Boolean.FALSE)))
                .thenReturn(allProductIds);

        // Mock the store products
        List<FookBusinessStoreProduct> storeProducts = new ArrayList<>();
        FookBusinessStoreProduct storeProduct1 = new FookBusinessStoreProduct();
        storeProduct1.setStoreid(101);
        storeProducts.add(storeProduct1);
        FookBusinessStoreProduct storeProduct2 = new FookBusinessStoreProduct();
        storeProduct2.setStoreid(102);
        storeProducts.add(storeProduct2);
        FookBusinessStoreProduct storeProduct3 = new FookBusinessStoreProduct();
        storeProduct3.setStoreid(103);
        storeProducts.add(storeProduct3);
        when(fookBusinessStoreProductDao.getStoresIdByProductIds(eq(allProductIds))).thenReturn(storeProducts);

        // Mock the stores
        List<Integer> storeIds = Arrays.asList(101, 102, 103);
        List<com.mcoin.mall.bean.FookStores> stores = new ArrayList<>();
        com.mcoin.mall.bean.FookStores store1 = new com.mcoin.mall.bean.FookStores();
        store1.setId(101);
        store1.setEnable(1);
        stores.add(store1);
        com.mcoin.mall.bean.FookStores store2 = new com.mcoin.mall.bean.FookStores();
        store2.setId(102);
        store2.setEnable(1);
        stores.add(store2);
        com.mcoin.mall.bean.FookStores store3 = new com.mcoin.mall.bean.FookStores();
        store3.setId(103);
        store3.setEnable(1);
        stores.add(store3);
        when(fookStoresDao.getStores(anyList())).thenReturn(stores);

        // Act
        CheckSearchValueResponse response = searchService.checkSearchValue(request, clientType);

        // Assert
        assertEquals(4, response.getProductCount());
        assertEquals(3, response.getStoresCount());

        // Verify that selectSearchTerm was called with the correct parameters
        verify(fookTemporaryProductDao).selectSearchTerm(anyString(), anyString(), anyString(), anyString(), eq(Boolean.FALSE));
    }

    @Test
    public void testCheckSearchValue_WithEmptyTemporaryTable_QueryOriginalTable() {
        // Arrange
        CheckSearchValueRequest request = new CheckSearchValueRequest();
        request.setSearchTerm("test");
        String clientType = ClientTypeEnum.HARMONY.toString();

        // Mock the MiniProgramDisplayUtil to return TRUE for HARMONY client type
        mockedMiniProgramDisplayUtil.when(() -> MiniProgramDisplayUtil.filterMiniprogramProduct(eq(ClientTypeEnum.HARMONY.toString())))
                .thenReturn(Boolean.TRUE);

        // Mock the temporary product count to be 0 (empty table)
        when(fookTemporaryProductDao.selectTotol()).thenReturn(0);

        // Mock the ConfigUtils to enable querying the original table
        mockedConfigUtils.when(() -> ConfigUtils.getProperty(eq("cache.invalidation.original.table.search.switch"), anyString()))
                .thenReturn("true");

        // Mock the original table query results (filtered to exclude type=12)
        List<Map<String, Integer>> filteredOriginalResults = new ArrayList<>();
        Map<String, Integer> result1 = new HashMap<>();
        result1.put("productId", 1);
        result1.put("storeId", 101);
        filteredOriginalResults.add(result1);
        Map<String, Integer> result2 = new HashMap<>();
        result2.put("productId", 2);
        result2.put("storeId", 102);
        filteredOriginalResults.add(result2);
        when(fookTemporaryProductDao.selectOriginalTemporary(anyString(), anyString(), anyString(), anyString(), eq(Boolean.TRUE)))
                .thenReturn(filteredOriginalResults);

        // Act
        CheckSearchValueResponse response = searchService.checkSearchValue(request, clientType);

        // Assert
        assertEquals(2, response.getProductCount());
        assertEquals(2, response.getStoresCount());

        // Verify that selectOriginalTemporary was called with the correct parameters
        verify(fookTemporaryProductDao).selectOriginalTemporary(anyString(), anyString(), anyString(), anyString(), eq(Boolean.TRUE));
    }
}
