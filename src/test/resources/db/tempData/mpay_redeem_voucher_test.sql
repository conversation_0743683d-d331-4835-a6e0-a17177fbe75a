-- Test data for MpayRedeemVoucherTest

-- Clean up existing test data to avoid duplicate key errors
DELETE FROM `fook_platform_orderinfo` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_platform_order` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_platform_ordercode` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_macaupass_code` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_business_store_product` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_business_product` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_stores` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_business` WHERE `id` IN (1001, 1002);
DELETE FROM `fook_macaupass_user` WHERE `id` IN (1001, 1002);

-- Insert test users
INSERT INTO `fook_macaupass_user` (
    `id`, `user_id`, `openid`, `access_token`, `refresh_token`,
    `point`, `phone`, `area`, `status`, `create_time`,
    `update_time`, `code`, `customid`
) VALUES
(1001, 1001, 'test_openid_001', 'test_access_token_001', 'test_refresh_token_001',
 1000, '********', '853', 1, NOW(),
 NOW(), 'test_code_001', 'TEST_USER_001'),
(1002, 1002, 'test_openid_002', 'test_access_token_002', 'test_refresh_token_002',
 2000, '********', '853', 1, NOW(),
 NOW(), 'test_code_002', 'TEST_USER_002');

-- Insert test businesses
INSERT INTO `fook_business` (
    `id`, `areas_id`, `code`, `name`, `company_name`,
    `account`, `status`, `tel`, `address`, `sex`,
    `enbale`, `system_type`
) VALUES
(1001, 1, 'TEST_BIZ_001', 'Test Business 1', 'Test Company 1',
 'TEST_BIZ_001', 1, '********', 'Test Address 1', 1,
 1, 1),
(1002, 1, 'TEST_BIZ_002', 'Test Business 2', 'Test Company 2',
 'TEST_BIZ_002', 1, '********', 'Test Address 2', 1,
 1, 1);

-- Insert test stores
INSERT INTO `fook_stores` (
    `id`, `store_number`, `store_type_id`, `business_information_id`,
    `area_id`, `user_id`, `business_id`, `name`,
    `phone`, `address`, `enable`
) VALUES
(1001, 'TEST_STORE_001', '1', '1',
 1, 1001, 1001, 'Test Store 1',
 '********', 'Test Store Address 1', 1),
(1002, 'TEST_STORE_002', '1', '1',
 1, 1002, 1002, 'Test Store 2',
 '********', 'Test Store Address 2', 1);

-- Insert test products
INSERT INTO `fook_business_product` (
    `id`, `areaid`, `type`, `businessid`,
    `img`, `title`, `price`, `retail_price`,
    `stock`, `history_stock`, `sales`, `shelf_status`,
    `buy_start_time`, `buy_end_time`, `created_at`,
    `updated_at`, `enable`, `is_hot`, `is_great`,
    `mpay_coupons_code_id`
) VALUES
(1001, 1, 1, 1001,
 'image/test/product1.jpg', 'Test Product 1', 10.00, 20.00,
 100, 100, 0, 1,
 '2023-01-01 00:00:00', '2030-12-31 23:59:59', '2023-01-01 00:00:00',
 '2023-01-01 00:00:00', 1, 1, 1,
 'TEST_PRODUCT_001'),
(1002, 1, 1, 1002,
 'image/test/product2.jpg', 'Test Product 2', 15.00, 30.00,
 200, 200, 0, 1,
 '2023-01-01 00:00:00', '2030-12-31 23:59:59', '2023-01-01 00:00:00',
 '2023-01-01 00:00:00', 1, 1, 1,
 'TEST_PRODUCT_002');

-- Associate products with stores
INSERT INTO `fook_business_store_product` (
    `id`, `productid`, `storeid`, `type`, `businessid`
) VALUES
(1001, 1001, 1001, 1, 1001),
(1002, 1002, 1002, 1, 1002);

-- Insert MacauPass codes
INSERT INTO `fook_macaupass_code` (
    `id`, `businessid`, `storeid`, `macaupass_businesscode`,
    `macaupass_storecode`, `macaupass_terminalcode`
) VALUES
(1001, 1001, 1001, 'TEST_MPAY_BIZ_001',
 'TEST_MPAY_STORE_001', 'TEST_MPAY_TERMINAL_001'),
(1002, 1002, 1002, 'TEST_MPAY_BIZ_002',
 'TEST_MPAY_STORE_002', 'TEST_MPAY_TERMINAL_002');

-- Insert test order codes for redemption
INSERT INTO `fook_platform_ordercode` (
    `id`, `orderid`, `code`, `status`,
    `user_time`, `shopid`, `userid`
) VALUES
(1001, 1001, 'TEST_CODE_001', 1,
 NULL, NULL, 1001),
(1002, 1002, 'TEST_CODE_002', 1,
 NULL, NULL, 1002);

-- Insert test orders
INSERT INTO `fook_platform_order` (
    `id`, `userid`, `order_no`, `payment_type`,
    `status`, `create_time`, `total_amount`, `score`
) VALUES
(1001, 1001, 'TEST_ORDER_001', 1,
 2, '2023-01-01 00:00:00', 10.00, 0),
(1002, 1002, 'TEST_ORDER_002', 1,
 2, '2023-01-01 00:00:00', 15.00, 0);

-- Insert order info
INSERT INTO `fook_platform_orderinfo` (
    `id`, `orderid`, `prodcutid`, `product_price`,
    `title_snapshots`, `number`
) VALUES
(1001, 1001, 1001, 10.00,
 'Test Product 1', 1),
(1002, 1002, 1002, 15.00,
 'Test Product 2', 1);
