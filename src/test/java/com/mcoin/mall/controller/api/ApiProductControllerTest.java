package com.mcoin.mall.controller.api;

import com.mcoin.mall.BaseUnitTest;
import com.mcoin.mall.model.Response;
import com.mcoin.mall.model.api.ApiCollectCntResponse;
import com.mcoin.mall.model.api.ApiCollectionCntRequest;
import com.mcoin.mall.model.api.ApiCollectionRequest;
import com.mcoin.mall.model.api.ApiCollectionResponse;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Integration test for the ApiProductController
 * Tests collection-related functionality using real services and databases
 */
class ApiProductControllerTest extends BaseUnitTest {

    @Resource
    private ApiProductController apiProductController;

    @Test
    void testCollection() {
        // Create request object with valid test data
        ApiCollectionRequest request = new ApiCollectionRequest();
        request.setCustId("test_cust_id"); // 使用测试数据库中存在的custId
        request.setGoodsId(1); // 使用测试数据库中存在的商品ID
        request.setType(3); // 3 for physical products
        request.setIsCollect(1); // 1 for add collection
        
        // 生成有效的签名 - 在真实集成测试中，需要计算实际签名
        // 这里假设测试环境已配置为接受此签名或验证被绕过
        request.setSignature("valid_test_signature");
        
        // 调用控制器方法
        Response<ApiCollectionResponse> response = apiProductController.collection(request);
        
        // 因为是集成测试，只验证基本的响应结构
        assertNotNull(response);
        // 注意：真实响应代码可能因测试数据而异
    }

    @Test
    void testCollectCount() {
        // Create request object with valid test data
        ApiCollectionCntRequest request = new ApiCollectionCntRequest();
        request.setCustId("test_cust_id"); // 使用测试数据库中存在的custId
        request.setGoodsId(1); // 使用测试数据库中存在的商品ID
        
        // 生成有效的签名
        request.setSignature("valid_test_signature");
        
        // 调用控制器方法
        Response<ApiCollectCntResponse> response = apiProductController.collectCount(request);
        
        // 因为是集成测试，只验证基本的响应结构
        assertNotNull(response);
        // 实际值取决于测试数据库中的状态
        if (response.getCode() == 200 && response.getData() != null) {
            assertNotNull(response.getData().getCollectCount());
            // 当收藏数据存在时，goodsId应该与请求匹配
            if (response.getData().getGoodsId() != null) {
                assertEquals(1, response.getData().getGoodsId());
            }
        }
    }
} 