package com.mcoin.mall.constant;

public enum SessionType {
    SNATCH(0, "搶購專場"),
    H5(1, "H5專場"),
    ALL(2, "所有");

    private final int code;
    private final String description;

    SessionType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取对应的枚举类型，如果没有匹配项，则返回null
    public static SessionType fromCode(int code) {
        for (SessionType type : SessionType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
