package com.mcoin.mall;

import com.mcoin.mall.client.MPayClient;
import com.mcoin.mall.client.MPayCouponClient;
import com.mcoin.mall.client.MiniUserAddressClient;
import com.mcoin.mall.client.TaskUploadClient;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

@SpringBootApplication
@EnableDiscoveryClient
@ImportResource(value = {"classpath*:context-*.xml"})
@MapperScan("com.mcoin.mall.dao")
@Slf4j
@EnableFeignClients(basePackageClasses = {
        MPayClient.class,
        MPayCouponClient.class,
        TaskUploadClient.class,
        MiniUserAddressClient.class
})
@EnableAspectJAutoProxy(exposeProxy = true)
public class McoinMallApplication {

    public static void main(String[] args) throws UnknownHostException {
        ApplicationContext application = SpringApplication.run(McoinMallApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        log.info("\n----------------------------------------------------------\n\t" +
                         "Application McoinMall is running! Access URLs:\n\t" +
                         "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
                         "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
                         "Swagger文档: \thttp://" + ip + ":" + port + path + "/doc.html\n" +
                         "----------------------------------------------------------");
    }

}
