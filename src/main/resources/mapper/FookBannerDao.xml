<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookBannerDao">
  <resultMap id="BaseResultMap" type="com.mcoin.mall.bean.FookBanner">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="zip_icon" jdbcType="VARCHAR" property="zipIcon" />
    <result column="icon_en" jdbcType="VARCHAR" property="iconEn" />
    <result column="zip_icon_en" jdbcType="VARCHAR" property="zipIconEn" />
    <result column="if_index_show" jdbcType="BIT" property="ifIndexShow" />
    <result column="if_hot" jdbcType="BIT" property="ifHot" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="src" jdbcType="VARCHAR" property="src" />
    <result column="promote_src" jdbcType="VARCHAR" property="promoteSrc" />
    <result column="uuid" jdbcType="INTEGER" property="uuid" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="location" jdbcType="TINYINT" property="location" />
    <result column="is_broadcast" jdbcType="TINYINT" property="isBroadcast" />
    <result column="broadcast_start_time" jdbcType="TIMESTAMP" property="broadcastStartTime" />
    <result column="broadcast_end_time" jdbcType="TIMESTAMP" property="broadcastEndTime" />
    <result column="banner_style" jdbcType="INTEGER" property="bannerStyle" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, sort, `enable`, icon, zip_icon, icon_en, zip_icon_en, if_index_show, 
    if_hot, code, created_at, updated_at, src, promote_src, uuid, `type`, `location`, 
    is_broadcast, broadcast_start_time, broadcast_end_time, banner_style
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fook_banner
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fook_banner
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.mcoin.mall.bean.FookBanner">
    insert into fook_banner
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="zipIcon != null">
        zip_icon,
      </if>
      <if test="iconEn != null">
        icon_en,
      </if>
      <if test="zipIconEn != null">
        zip_icon_en,
      </if>
      <if test="ifIndexShow != null">
        if_index_show,
      </if>
      <if test="ifHot != null">
        if_hot,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="src != null">
        src,
      </if>
      <if test="promoteSrc != null">
        promote_src,
      </if>
      <if test="uuid != null">
        uuid,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="location != null">
        `location`,
      </if>
      <if test="isBroadcast != null">
        is_broadcast,
      </if>
      <if test="broadcastStartTime != null">
        broadcast_start_time,
      </if>
      <if test="broadcastEndTime != null">
        broadcast_end_time,
      </if>
      <if test="bannerStyle != null">
        banner_style,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="zipIcon != null">
        #{zipIcon,jdbcType=VARCHAR},
      </if>
      <if test="iconEn != null">
        #{iconEn,jdbcType=VARCHAR},
      </if>
      <if test="zipIconEn != null">
        #{zipIconEn,jdbcType=VARCHAR},
      </if>
      <if test="ifIndexShow != null">
        #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="ifHot != null">
        #{ifHot,jdbcType=BIT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="src != null">
        #{src,jdbcType=VARCHAR},
      </if>
      <if test="promoteSrc != null">
        #{promoteSrc,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="location != null">
        #{location,jdbcType=TINYINT},
      </if>
      <if test="isBroadcast != null">
        #{isBroadcast,jdbcType=TINYINT},
      </if>
      <if test="broadcastStartTime != null">
        #{broadcastStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="broadcastEndTime != null">
        #{broadcastEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bannerStyle != null">
        #{bannerStyle,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.mcoin.mall.bean.FookBanner">
    update fook_banner
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="zipIcon != null">
        zip_icon = #{zipIcon,jdbcType=VARCHAR},
      </if>
      <if test="iconEn != null">
        icon_en = #{iconEn,jdbcType=VARCHAR},
      </if>
      <if test="zipIconEn != null">
        zip_icon_en = #{zipIconEn,jdbcType=VARCHAR},
      </if>
      <if test="ifIndexShow != null">
        if_index_show = #{ifIndexShow,jdbcType=BIT},
      </if>
      <if test="ifHot != null">
        if_hot = #{ifHot,jdbcType=BIT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="src != null">
        src = #{src,jdbcType=VARCHAR},
      </if>
      <if test="promoteSrc != null">
        promote_src = #{promoteSrc,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="location != null">
        `location` = #{location,jdbcType=TINYINT},
      </if>
      <if test="isBroadcast != null">
        is_broadcast = #{isBroadcast,jdbcType=TINYINT},
      </if>
      <if test="broadcastStartTime != null">
        broadcast_start_time = #{broadcastStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="broadcastEndTime != null">
        broadcast_end_time = #{broadcastEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bannerStyle != null">
        banner_style = #{bannerStyle,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.mcoin.mall.bean.FookBanner">
    update fook_banner
    set `name` = #{name,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      `enable` = #{enable,jdbcType=BIT},
      icon = #{icon,jdbcType=VARCHAR},
      zip_icon = #{zipIcon,jdbcType=VARCHAR},
      icon_en = #{iconEn,jdbcType=VARCHAR},
      zip_icon_en = #{zipIconEn,jdbcType=VARCHAR},
      if_index_show = #{ifIndexShow,jdbcType=BIT},
      if_hot = #{ifHot,jdbcType=BIT},
      code = #{code,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      src = #{src,jdbcType=VARCHAR},
      promote_src = #{promoteSrc,jdbcType=VARCHAR},
      uuid = #{uuid,jdbcType=INTEGER},
      `type` = #{type,jdbcType=TINYINT},
      `location` = #{location,jdbcType=TINYINT},
      is_broadcast = #{isBroadcast,jdbcType=TINYINT},
      broadcast_start_time = #{broadcastStartTime,jdbcType=TIMESTAMP},
      broadcast_end_time = #{broadcastEndTime,jdbcType=TIMESTAMP},
      banner_style = #{bannerStyle,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>