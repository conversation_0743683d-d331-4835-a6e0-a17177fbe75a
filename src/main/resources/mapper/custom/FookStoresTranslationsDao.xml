<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mcoin.mall.dao.FookStoresTranslationsDao">
  <select id="getTranslationsByStoreId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from fook_stores_translations
    where stores_id = #{storeId,jdbcType=INTEGER}
    and locale = #{language,jdbcType=VARCHAR}
  </select>
</mapper>