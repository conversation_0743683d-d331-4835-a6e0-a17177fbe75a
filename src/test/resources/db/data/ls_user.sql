INSERT INTO ls_user
(id, sn, nickname, avatar, real_name, mobile, `level`, group_id, sex, birthday, user_money, user_integral, total_order_amount, total_order_num, total_recharge_amount, account, password, pay_password, login_time, login_ip, disable, user_growth, remark, first_leader, second_leader, third_leader, ancestor_relation, code, user_earnings, register_source, inviter_id, create_time, update_time, delete_time, sid, is_new_user)
VALUES(315, '********', '就', 'https://sit-mpay.macaupass.com/commimages/userPhoto/H84159431e32c95b724cab14c3ffb972820240306150358.jpg', NULL, '', 17, NULL, 0, NULL, 0.00, 0, 228.00, 24, 0.00, '', NULL, NULL, **********, '*************', 0, 0, '', 0, 0, 0, NULL, 'PWKQGD', 0.00, 8, 0, **********, **********, NULL, 78, 1);
INSERT INTO `ls_user` (
    `id`, `sn`, `nickname`, `avatar`, `real_name`, `mobile`, `level`,
    `user_money`, `user_integral`, `account`, `password`, `disable`,
    `code`, `register_source`, `create_time`, `update_time`, `sid`
) VALUES (
             90, 'USER001', '测试用户', 'https://example.com/avatar.jpg', '张三',
             '***********', 1, 100.00, 1000, 'test_user', 'password', 0,
             'TEST001', 1, **********, **********, 65
         );


-- 插入测试用户
INSERT INTO `ls_user` (
    `id`, `sn`, `nickname`, `avatar`, `real_name`, `mobile`, `level`,
    `user_money`, `user_integral`, `account`, `password`, `disable`,
    `code`, `register_source`, `create_time`, `update_time`, `sid`
) VALUES (
             1, 'TEST001', '测试用户', 'https://example.com/avatar.jpg', '张三',
             '***********', 1, 100.00, 1000, 'test_user', 'password', 0,
             'TEST001', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 65
         );